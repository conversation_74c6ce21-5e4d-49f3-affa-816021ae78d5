const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/secp256k1-vwi1ufCg.js","assets/index-2wea5Wgv.js","assets/index-CnEfukNX.css","assets/events-B2jzgt6q.js","assets/index-nibyPLVP.js","assets/core-C0olQNtY.js"])))=>i.map(i=>d[i]);
import{k as th,_ as rh,g as ih,j as vt}from"./index-2wea5Wgv.js";import{e as rt,N as Oo}from"./events-B2jzgt6q.js";import{c as Ld,a as kd,k as qd,d as Md,s as Fd,g as sa}from"./index-nibyPLVP.js";var na=function(t,e,r){if(r||arguments.length===2)for(var i=0,s=e.length,n;i<s;i++)(n||!(i in e))&&(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},zd=function(){function t(e,r,i){this.name=e,this.version=r,this.os=i,this.type="browser"}return t}(),Hd=function(){function t(e){this.version=e,this.type="node",this.name="node",this.os=process.platform}return t}(),Vd=function(){function t(e,r,i,s){this.name=e,this.version=r,this.os=i,this.bot=s,this.type="bot-device"}return t}(),Kd=function(){function t(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null}return t}(),Wd=function(){function t(){this.type="react-native",this.name="react-native",this.version=null,this.os=null}return t}(),Gd=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,Yd=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,oa=3,Jd=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",Gd]],aa=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function Zd(t){return typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"?new Wd:typeof navigator<"u"?Xd(navigator.userAgent):tp()}function Qd(t){return t!==""&&Jd.reduce(function(e,r){var i=r[0],s=r[1];if(e)return e;var n=s.exec(t);return!!n&&[i,n]},!1)}function Xd(t){var e=Qd(t);if(!e)return null;var r=e[0],i=e[1];if(r==="searchbot")return new Kd;var s=i[1]&&i[1].split(".").join("_").split("_").slice(0,3);s?s.length<oa&&(s=na(na([],s,!0),rp(oa-s.length),!0)):s=[];var n=s.join("."),o=ep(t),a=Yd.exec(t);return a&&a[1]?new Vd(r,n,o,a[1]):new zd(r,n,o)}function ep(t){for(var e=0,r=aa.length;e<r;e++){var i=aa[e],s=i[0],n=i[1],o=n.exec(t);if(o)return s}return null}function tp(){var t=typeof process<"u"&&process.version;return t?new Hd(process.version.slice(1)):null}function rp(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}var j={};/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var jn=function(t,e){return jn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var s in i)i.hasOwnProperty(s)&&(r[s]=i[s])},jn(t,e)};function ip(t,e){jn(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var Bn=function(){return Bn=Object.assign||function(e){for(var r,i=1,s=arguments.length;i<s;i++){r=arguments[i];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bn.apply(this,arguments)};function sp(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,i=Object.getOwnPropertySymbols(t);s<i.length;s++)e.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(t,i[s])&&(r[i[s]]=t[i[s]]);return r}function np(t,e,r,i){var s=arguments.length,n=s<3?e:i===null?i=Object.getOwnPropertyDescriptor(e,r):i,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(t,e,r,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(n=(s<3?o(n):s>3?o(e,r,n):o(e,r))||n);return s>3&&n&&Object.defineProperty(e,r,n),n}function op(t,e){return function(r,i){e(r,i,t)}}function ap(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function cp(t,e,r,i){function s(n){return n instanceof r?n:new r(function(o){o(n)})}return new(r||(r=Promise))(function(n,o){function a(h){try{l(i.next(h))}catch(u){o(u)}}function c(h){try{l(i.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):s(h.value).then(a,c)}l((i=i.apply(t,e||[])).next())})}function lp(t,e){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,s,n,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(l){return function(h){return c([l,h])}}function c(l){if(i)throw new TypeError("Generator is already executing.");for(;r;)try{if(i=1,s&&(n=l[0]&2?s.return:l[0]?s.throw||((n=s.return)&&n.call(s),0):s.next)&&!(n=n.call(s,l[1])).done)return n;switch(s=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,s=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){r.label=l[1];break}if(l[0]===6&&r.label<n[1]){r.label=n[1],n=l;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(l);break}n[2]&&r.ops.pop(),r.trys.pop();continue}l=e.call(t,r)}catch(h){l=[6,h],s=0}finally{i=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function hp(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}function up(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function Dn(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function sh(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var i=r.call(t),s,n=[],o;try{for(;(e===void 0||e-- >0)&&!(s=i.next()).done;)n.push(s.value)}catch(a){o={error:a}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return n}function dp(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(sh(arguments[e]));return t}function pp(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var i=Array(t),s=0,e=0;e<r;e++)for(var n=arguments[e],o=0,a=n.length;o<a;o++,s++)i[s]=n[o];return i}function Si(t){return this instanceof Si?(this.v=t,this):new Si(t)}function fp(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=r.apply(t,e||[]),s,n=[];return s={},o("next"),o("throw"),o("return"),s[Symbol.asyncIterator]=function(){return this},s;function o(d){i[d]&&(s[d]=function(f){return new Promise(function(p,y){n.push([d,f,p,y])>1||a(d,f)})})}function a(d,f){try{c(i[d](f))}catch(p){u(n[0][3],p)}}function c(d){d.value instanceof Si?Promise.resolve(d.value.v).then(l,h):u(n[0][2],d)}function l(d){a("next",d)}function h(d){a("throw",d)}function u(d,f){d(f),n.shift(),n.length&&a(n[0][0],n[0][1])}}function gp(t){var e,r;return e={},i("next"),i("throw",function(s){throw s}),i("return"),e[Symbol.iterator]=function(){return this},e;function i(s,n){e[s]=t[s]?function(o){return(r=!r)?{value:Si(t[s](o)),done:s==="return"}:n?n(o):o}:n}}function yp(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof Dn=="function"?Dn(t):t[Symbol.iterator](),r={},i("next"),i("throw"),i("return"),r[Symbol.asyncIterator]=function(){return this},r);function i(n){r[n]=t[n]&&function(o){return new Promise(function(a,c){o=t[n](o),s(a,c,o.done,o.value)})}}function s(n,o,a,c){Promise.resolve(c).then(function(l){n({value:l,done:a})},o)}}function mp(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function wp(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function bp(t){return t&&t.__esModule?t:{default:t}}function vp(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function Ep(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}const _p=Object.freeze(Object.defineProperty({__proto__:null,get __assign(){return Bn},__asyncDelegator:gp,__asyncGenerator:fp,__asyncValues:yp,__await:Si,__awaiter:cp,__classPrivateFieldGet:vp,__classPrivateFieldSet:Ep,__createBinding:hp,__decorate:np,__exportStar:up,__extends:ip,__generator:lp,__importDefault:bp,__importStar:wp,__makeTemplateObject:mp,__metadata:ap,__param:op,__read:sh,__rest:sp,__spread:dp,__spreadArrays:pp,__values:Dn},Symbol.toStringTag,{value:"Module"})),xs=th(_p);var Ys={},oi={},ca;function Ip(){if(ca)return oi;ca=1,Object.defineProperty(oi,"__esModule",{value:!0}),oi.delay=void 0;function t(e){return new Promise(r=>{setTimeout(()=>{r(!0)},e)})}return oi.delay=t,oi}var Gt={},Js={},Yt={},la;function $p(){return la||(la=1,Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.ONE_THOUSAND=Yt.ONE_HUNDRED=void 0,Yt.ONE_HUNDRED=100,Yt.ONE_THOUSAND=1e3),Yt}var Zs={},ha;function Sp(){return ha||(ha=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.ONE_YEAR=t.FOUR_WEEKS=t.THREE_WEEKS=t.TWO_WEEKS=t.ONE_WEEK=t.THIRTY_DAYS=t.SEVEN_DAYS=t.FIVE_DAYS=t.THREE_DAYS=t.ONE_DAY=t.TWENTY_FOUR_HOURS=t.TWELVE_HOURS=t.SIX_HOURS=t.THREE_HOURS=t.ONE_HOUR=t.SIXTY_MINUTES=t.THIRTY_MINUTES=t.TEN_MINUTES=t.FIVE_MINUTES=t.ONE_MINUTE=t.SIXTY_SECONDS=t.THIRTY_SECONDS=t.TEN_SECONDS=t.FIVE_SECONDS=t.ONE_SECOND=void 0,t.ONE_SECOND=1,t.FIVE_SECONDS=5,t.TEN_SECONDS=10,t.THIRTY_SECONDS=30,t.SIXTY_SECONDS=60,t.ONE_MINUTE=t.SIXTY_SECONDS,t.FIVE_MINUTES=t.ONE_MINUTE*5,t.TEN_MINUTES=t.ONE_MINUTE*10,t.THIRTY_MINUTES=t.ONE_MINUTE*30,t.SIXTY_MINUTES=t.ONE_MINUTE*60,t.ONE_HOUR=t.SIXTY_MINUTES,t.THREE_HOURS=t.ONE_HOUR*3,t.SIX_HOURS=t.ONE_HOUR*6,t.TWELVE_HOURS=t.ONE_HOUR*12,t.TWENTY_FOUR_HOURS=t.ONE_HOUR*24,t.ONE_DAY=t.TWENTY_FOUR_HOURS,t.THREE_DAYS=t.ONE_DAY*3,t.FIVE_DAYS=t.ONE_DAY*5,t.SEVEN_DAYS=t.ONE_DAY*7,t.THIRTY_DAYS=t.ONE_DAY*30,t.ONE_WEEK=t.SEVEN_DAYS,t.TWO_WEEKS=t.ONE_WEEK*2,t.THREE_WEEKS=t.ONE_WEEK*3,t.FOUR_WEEKS=t.ONE_WEEK*4,t.ONE_YEAR=t.ONE_DAY*365}(Zs)),Zs}var ua;function nh(){return ua||(ua=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=xs;e.__exportStar($p(),t),e.__exportStar(Sp(),t)}(Js)),Js}var da;function Op(){if(da)return Gt;da=1,Object.defineProperty(Gt,"__esModule",{value:!0}),Gt.fromMiliseconds=Gt.toMiliseconds=void 0;const t=nh();function e(i){return i*t.ONE_THOUSAND}Gt.toMiliseconds=e;function r(i){return Math.floor(i/t.ONE_THOUSAND)}return Gt.fromMiliseconds=r,Gt}var pa;function Pp(){return pa||(pa=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=xs;e.__exportStar(Ip(),t),e.__exportStar(Op(),t)}(Ys)),Ys}var br={},fa;function xp(){if(fa)return br;fa=1,Object.defineProperty(br,"__esModule",{value:!0}),br.Watch=void 0;class t{constructor(){this.timestamps=new Map}start(r){if(this.timestamps.has(r))throw new Error(`Watch already started for label: ${r}`);this.timestamps.set(r,{started:Date.now()})}stop(r){const i=this.get(r);if(typeof i.elapsed<"u")throw new Error(`Watch already stopped for label: ${r}`);const s=Date.now()-i.started;this.timestamps.set(r,{started:i.started,elapsed:s})}get(r){const i=this.timestamps.get(r);if(typeof i>"u")throw new Error(`No timestamp found for label: ${r}`);return i}elapsed(r){const i=this.get(r);return i.elapsed||Date.now()-i.started}}return br.Watch=t,br.default=t,br}var Qs={},ai={},ga;function Ap(){if(ga)return ai;ga=1,Object.defineProperty(ai,"__esModule",{value:!0}),ai.IWatch=void 0;class t{}return ai.IWatch=t,ai}var ya;function Tp(){return ya||(ya=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),xs.__exportStar(Ap(),t)}(Qs)),Qs}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=xs;e.__exportStar(Pp(),t),e.__exportStar(xp(),t),e.__exportStar(Tp(),t),e.__exportStar(nh(),t)})(j);var oe={};Object.defineProperty(oe,"__esModule",{value:!0});oe.getLocalStorage=oe.getLocalStorageOrThrow=oe.getCrypto=oe.getCryptoOrThrow=oh=oe.getLocation=oe.getLocationOrThrow=Po=oe.getNavigator=oe.getNavigatorOrThrow=ar=oe.getDocument=oe.getDocumentOrThrow=oe.getFromWindowOrThrow=oe.getFromWindow=void 0;function pr(t){let e;return typeof window<"u"&&typeof window[t]<"u"&&(e=window[t]),e}oe.getFromWindow=pr;function Qr(t){const e=pr(t);if(!e)throw new Error(`${t} is not defined in Window`);return e}oe.getFromWindowOrThrow=Qr;function Cp(){return Qr("document")}oe.getDocumentOrThrow=Cp;function Rp(){return pr("document")}var ar=oe.getDocument=Rp;function Np(){return Qr("navigator")}oe.getNavigatorOrThrow=Np;function jp(){return pr("navigator")}var Po=oe.getNavigator=jp;function Bp(){return Qr("location")}oe.getLocationOrThrow=Bp;function Dp(){return pr("location")}var oh=oe.getLocation=Dp;function Up(){return Qr("crypto")}oe.getCryptoOrThrow=Up;function Lp(){return pr("crypto")}oe.getCrypto=Lp;function kp(){return Qr("localStorage")}oe.getLocalStorageOrThrow=kp;function qp(){return pr("localStorage")}oe.getLocalStorage=qp;var xo={};Object.defineProperty(xo,"__esModule",{value:!0});var ah=xo.getWindowMetadata=void 0;const ma=oe;function Mp(){let t,e;try{t=ma.getDocumentOrThrow(),e=ma.getLocationOrThrow()}catch{return null}function r(){const u=t.getElementsByTagName("link"),d=[];for(let f=0;f<u.length;f++){const p=u[f],y=p.getAttribute("rel");if(y&&y.toLowerCase().indexOf("icon")>-1){const w=p.getAttribute("href");if(w)if(w.toLowerCase().indexOf("https:")===-1&&w.toLowerCase().indexOf("http:")===-1&&w.indexOf("//")!==0){let v=e.protocol+"//"+e.host;if(w.indexOf("/")===0)v+=w;else{const b=e.pathname.split("/");b.pop();const E=b.join("/");v+=E+"/"+w}d.push(v)}else if(w.indexOf("//")===0){const v=e.protocol+w;d.push(v)}else d.push(w)}}return d}function i(...u){const d=t.getElementsByTagName("meta");for(let f=0;f<d.length;f++){const p=d[f],y=["itemprop","property","name"].map(w=>p.getAttribute(w)).filter(w=>w?u.includes(w):!1);if(y.length&&y){const w=p.getAttribute("content");if(w)return w}}return""}function s(){let u=i("name","og:site_name","og:title","twitter:title");return u||(u=t.title),u}function n(){return i("description","og:description","twitter:description","keywords")}const o=s(),a=n(),c=e.origin,l=r();return{description:a,url:c,icons:l,name:o}}ah=xo.getWindowMetadata=Mp;function Oi(t,{strict:e=!0}={}){return!t||typeof t!="string"?!1:e?/^0x[0-9a-fA-F]*$/.test(t):t.startsWith("0x")}function wa(t){return Oi(t,{strict:!1})?Math.ceil((t.length-2)/2):t.length}const ch="2.23.2";let ci={getDocsUrl:({docsBaseUrl:t,docsPath:e="",docsSlug:r})=>e?`${t??"https://viem.sh"}${e}${r?`#${r}`:""}`:void 0,version:`viem@${ch}`};class cr extends Error{constructor(e,r={}){var a;const i=(()=>{var c;return r.cause instanceof cr?r.cause.details:(c=r.cause)!=null&&c.message?r.cause.message:r.details})(),s=r.cause instanceof cr&&r.cause.docsPath||r.docsPath,n=(a=ci.getDocsUrl)==null?void 0:a.call(ci,{...r,docsPath:s}),o=[e||"An error occurred.","",...r.metaMessages?[...r.metaMessages,""]:[],...n?[`Docs: ${n}`]:[],...i?[`Details: ${i}`]:[],...ci.version?[`Version: ${ci.version}`]:[]].join(`
`);super(o,r.cause?{cause:r.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metaMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),this.details=i,this.docsPath=s,this.metaMessages=r.metaMessages,this.name=r.name??this.name,this.shortMessage=e,this.version=ch}walk(e){return lh(this,e)}}function lh(t,e){return e!=null&&e(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause!==void 0?lh(t.cause,e):e?null:t}class hh extends cr{constructor({size:e,targetSize:r,type:i}){super(`${i.charAt(0).toUpperCase()}${i.slice(1).toLowerCase()} size (${e}) exceeds padding size (${r}).`,{name:"SizeExceedsPaddingSizeError"})}}function Xr(t,{dir:e,size:r=32}={}){return typeof t=="string"?Fp(t,{dir:e,size:r}):zp(t,{dir:e,size:r})}function Fp(t,{dir:e,size:r=32}={}){if(r===null)return t;const i=t.replace("0x","");if(i.length>r*2)throw new hh({size:Math.ceil(i.length/2),targetSize:r,type:"hex"});return`0x${i[e==="right"?"padEnd":"padStart"](r*2,"0")}`}function zp(t,{dir:e,size:r=32}={}){if(r===null)return t;if(t.length>r)throw new hh({size:t.length,targetSize:r,type:"bytes"});const i=new Uint8Array(r);for(let s=0;s<r;s++){const n=e==="right";i[n?s:r-s-1]=t[n?s:t.length-s-1]}return i}class Hp extends cr{constructor({max:e,min:r,signed:i,size:s,value:n}){super(`Number "${n}" is not in safe ${s?`${s*8}-bit ${i?"signed":"unsigned"} `:""}integer range ${e?`(${r} to ${e})`:`(above ${r})`}`,{name:"IntegerOutOfRangeError"})}}class Vp extends cr{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed ${r} bytes. Given size: ${e} bytes.`,{name:"SizeOverflowError"})}}function ei(t,{size:e}){if(wa(t)>e)throw new Vp({givenSize:wa(t),maxSize:e})}function Un(t,e={}){const{signed:r}=e;e.size&&ei(t,{size:e.size});const i=BigInt(t);if(!r)return i;const s=(t.length-2)/2,n=(1n<<BigInt(s)*8n-1n)-1n;return i<=n?i:i-BigInt(`0x${"f".padStart(s*2,"f")}`)-1n}function Kp(t,e={}){return Number(Un(t,e))}const Wp=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Ln(t,e={}){return typeof t=="number"||typeof t=="bigint"?dh(t,e):typeof t=="string"?Jp(t,e):typeof t=="boolean"?Gp(t,e):uh(t,e)}function Gp(t,e={}){const r=`0x${Number(t)}`;return typeof e.size=="number"?(ei(r,{size:e.size}),Xr(r,{size:e.size})):r}function uh(t,e={}){let r="";for(let s=0;s<t.length;s++)r+=Wp[t[s]];const i=`0x${r}`;return typeof e.size=="number"?(ei(i,{size:e.size}),Xr(i,{dir:"right",size:e.size})):i}function dh(t,e={}){const{signed:r,size:i}=e,s=BigInt(t);let n;i?r?n=(1n<<BigInt(i)*8n-1n)-1n:n=2n**(BigInt(i)*8n)-1n:typeof t=="number"&&(n=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof n=="bigint"&&r?-n-1n:0;if(n&&s>n||s<o){const c=typeof t=="bigint"?"n":"";throw new Hp({max:n?`${n}${c}`:void 0,min:`${o}${c}`,signed:r,size:i,value:`${t}${c}`})}const a=`0x${(r&&s<0?(1n<<BigInt(i*8))+BigInt(s):s).toString(16)}`;return i?Xr(a,{size:i}):a}const Yp=new TextEncoder;function Jp(t,e={}){const r=Yp.encode(t);return uh(r,e)}const Zp=new TextEncoder;function Qp(t,e={}){return typeof t=="number"||typeof t=="bigint"?ef(t,e):typeof t=="boolean"?Xp(t,e):Oi(t)?ph(t,e):fh(t,e)}function Xp(t,e={}){const r=new Uint8Array(1);return r[0]=Number(t),typeof e.size=="number"?(ei(r,{size:e.size}),Xr(r,{size:e.size})):r}const St={zero:48,nine:57,A:65,F:70,a:97,f:102};function ba(t){if(t>=St.zero&&t<=St.nine)return t-St.zero;if(t>=St.A&&t<=St.F)return t-(St.A-10);if(t>=St.a&&t<=St.f)return t-(St.a-10)}function ph(t,e={}){let r=t;e.size&&(ei(r,{size:e.size}),r=Xr(r,{dir:"right",size:e.size}));let i=r.slice(2);i.length%2&&(i=`0${i}`);const s=i.length/2,n=new Uint8Array(s);for(let o=0,a=0;o<s;o++){const c=ba(i.charCodeAt(a++)),l=ba(i.charCodeAt(a++));if(c===void 0||l===void 0)throw new cr(`Invalid byte sequence ("${i[a-2]}${i[a-1]}" in "${i}").`);n[o]=c*16+l}return n}function ef(t,e){const r=dh(t,e);return ph(r)}function fh(t,e={}){const r=Zp.encode(t);return typeof e.size=="number"?(ei(r,{size:e.size}),Xr(r,{dir:"right",size:e.size})):r}function ys(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function tf(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function As(t,...e){if(!tf(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function qP(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");ys(t.outputLen),ys(t.blockLen)}function va(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function rf(t,e){As(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Ki=BigInt(2**32-1),Ea=BigInt(32);function sf(t,e=!1){return e?{h:Number(t&Ki),l:Number(t>>Ea&Ki)}:{h:Number(t>>Ea&Ki)|0,l:Number(t&Ki)|0}}function nf(t,e=!1){let r=new Uint32Array(t.length),i=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:n,l:o}=sf(t[s],e);[r[s],i[s]]=[n,o]}return[r,i]}const of=(t,e,r)=>t<<r|e>>>32-r,af=(t,e,r)=>e<<r|t>>>32-r,cf=(t,e,r)=>e<<r-32|t>>>64-r,lf=(t,e,r)=>t<<r-32|e>>>64-r,vr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function hf(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function MP(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function FP(t,e){return t<<32-e|t>>>e}const _a=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function uf(t){return t<<24&**********|t<<8&16711680|t>>>8&65280|t>>>24&255}function Ia(t){for(let e=0;e<t.length;e++)t[e]=uf(t[e])}function df(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function gh(t){return typeof t=="string"&&(t=df(t)),As(t),t}function zP(...t){let e=0;for(let i=0;i<t.length;i++){const s=t[i];As(s),e+=s.length}const r=new Uint8Array(e);for(let i=0,s=0;i<t.length;i++){const n=t[i];r.set(n,s),s+=n.length}return r}class pf{clone(){return this._cloneInto()}}function ff(t){const e=i=>t().update(gh(i)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function HP(t=32){if(vr&&typeof vr.getRandomValues=="function")return vr.getRandomValues(new Uint8Array(t));if(vr&&typeof vr.randomBytes=="function")return vr.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const yh=[],mh=[],wh=[],gf=BigInt(0),li=BigInt(1),yf=BigInt(2),mf=BigInt(7),wf=BigInt(256),bf=BigInt(113);for(let t=0,e=li,r=1,i=0;t<24;t++){[r,i]=[i,(2*r+3*i)%5],yh.push(2*(5*i+r)),mh.push((t+1)*(t+2)/2%64);let s=gf;for(let n=0;n<7;n++)e=(e<<li^(e>>mf)*bf)%wf,e&yf&&(s^=li<<(li<<BigInt(n))-li);wh.push(s)}const[vf,Ef]=nf(wh,!0),$a=(t,e,r)=>r>32?cf(t,e,r):of(t,e,r),Sa=(t,e,r)=>r>32?lf(t,e,r):af(t,e,r);function _f(t,e=24){const r=new Uint32Array(10);for(let i=24-e;i<24;i++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,l=r[c],h=r[c+1],u=$a(l,h,1)^r[a],d=Sa(l,h,1)^r[a+1];for(let f=0;f<50;f+=10)t[o+f]^=u,t[o+f+1]^=d}let s=t[2],n=t[3];for(let o=0;o<24;o++){const a=mh[o],c=$a(s,n,a),l=Sa(s,n,a),h=yh[o];s=t[h],n=t[h+1],t[h]=c,t[h+1]=l}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=vf[i],t[1]^=Ef[i]}r.fill(0)}class Ao extends pf{constructor(e,r,i,s=!1,n=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=i,this.enableXOF=s,this.rounds=n,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,ys(i),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=hf(this.state)}keccak(){_a||Ia(this.state32),_f(this.state32,this.rounds),_a||Ia(this.state32),this.posOut=0,this.pos=0}update(e){va(this);const{blockLen:r,state:i}=this;e=gh(e);const s=e.length;for(let n=0;n<s;){const o=Math.min(r-this.pos,s-n);for(let a=0;a<o;a++)i[this.pos++]^=e[n++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:i,blockLen:s}=this;e[i]^=r,r&128&&i===s-1&&this.keccak(),e[s-1]^=128,this.keccak()}writeInto(e){va(this,!1),As(e),this.finish();const r=this.state,{blockLen:i}=this;for(let s=0,n=e.length;s<n;){this.posOut>=i&&this.keccak();const o=Math.min(i-this.posOut,n-s);e.set(r.subarray(this.posOut,this.posOut+o),s),this.posOut+=o,s+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return ys(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(rf(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:i,outputLen:s,rounds:n,enableXOF:o}=this;return e||(e=new Ao(r,i,s,o,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=i,e.outputLen=s,e.enableXOF=o,e.destroyed=this.destroyed,e}}const If=(t,e,r)=>ff(()=>new Ao(e,t,r)),$f=If(1,136,256/8);function bh(t,e){const r=e||"hex",i=$f(Oi(t,{strict:!1})?Qp(t):t);return r==="bytes"?i:Ln(i)}class Sf extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const r=super.get(e);return super.has(e)&&r!==void 0&&(this.delete(e),super.set(e,r)),r}set(e,r){if(super.set(e,r),this.maxSize&&this.size>this.maxSize){const i=this.keys().next().value;i&&this.delete(i)}return this}}const Xs=new Sf(8192);function Of(t,e){if(Xs.has(`${t}.${e}`))return Xs.get(`${t}.${e}`);const r=t.substring(2).toLowerCase(),i=bh(fh(r),"bytes"),s=r.split("");for(let o=0;o<40;o+=2)i[o>>1]>>4>=8&&s[o]&&(s[o]=s[o].toUpperCase()),(i[o>>1]&15)>=8&&s[o+1]&&(s[o+1]=s[o+1].toUpperCase());const n=`0x${s.join("")}`;return Xs.set(`${t}.${e}`,n),n}function Pf(t){const e=bh(`0x${t.substring(4)}`).substring(26);return Of(`0x${e}`)}async function xf({hash:t,signature:e}){const r=Oi(t)?t:Ln(t),{secp256k1:i}=await rh(async()=>{const{secp256k1:o}=await import("./secp256k1-vwi1ufCg.js");return{secp256k1:o}},__vite__mapDeps([0,1,2,3,4]));return`0x${(()=>{if(typeof e=="object"&&"r"in e&&"s"in e){const{r:l,s:h,v:u,yParity:d}=e,f=Number(d??u),p=Oa(f);return new i.Signature(Un(l),Un(h)).addRecoveryBit(p)}const o=Oi(e)?e:Ln(e),a=Kp(`0x${o.slice(130)}`),c=Oa(a);return i.Signature.fromCompact(o.substring(2,130)).addRecoveryBit(c)})().recoverPublicKey(r.substring(2)).toHex(!1)}`}function Oa(t){if(t===0||t===1)return t;if(t===27)return 0;if(t===28)return 1;throw new Error("Invalid yParityOrV value")}async function Af({hash:t,signature:e}){return Pf(await xf({hash:t,signature:e}))}function Tf(t){if(t.length>=255)throw new TypeError("Alphabet too long");const e=new Uint8Array(256);for(let l=0;l<e.length;l++)e[l]=255;for(let l=0;l<t.length;l++){const h=t.charAt(l),u=h.charCodeAt(0);if(e[u]!==255)throw new TypeError(h+" is ambiguous");e[u]=l}const r=t.length,i=t.charAt(0),s=Math.log(r)/Math.log(256),n=Math.log(256)/Math.log(r);function o(l){if(l instanceof Uint8Array||(ArrayBuffer.isView(l)?l=new Uint8Array(l.buffer,l.byteOffset,l.byteLength):Array.isArray(l)&&(l=Uint8Array.from(l))),!(l instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(l.length===0)return"";let h=0,u=0,d=0;const f=l.length;for(;d!==f&&l[d]===0;)d++,h++;const p=(f-d)*n+1>>>0,y=new Uint8Array(p);for(;d!==f;){let b=l[d],E=0;for(let I=p-1;(b!==0||E<u)&&I!==-1;I--,E++)b+=256*y[I]>>>0,y[I]=b%r>>>0,b=b/r>>>0;if(b!==0)throw new Error("Non-zero carry");u=E,d++}let w=p-u;for(;w!==p&&y[w]===0;)w++;let v=i.repeat(h);for(;w<p;++w)v+=t.charAt(y[w]);return v}function a(l){if(typeof l!="string")throw new TypeError("Expected String");if(l.length===0)return new Uint8Array;let h=0,u=0,d=0;for(;l[h]===i;)u++,h++;const f=(l.length-h)*s+1>>>0,p=new Uint8Array(f);for(;h<l.length;){const b=l.charCodeAt(h);if(b>255)return;let E=e[b];if(E===255)return;let I=0;for(let x=f-1;(E!==0||I<d)&&x!==-1;x--,I++)E+=r*p[x]>>>0,p[x]=E%256>>>0,E=E/256>>>0;if(E!==0)throw new Error("Non-zero carry");d=I,h++}let y=f-d;for(;y!==f&&p[y]===0;)y++;const w=new Uint8Array(u+(f-y));let v=u;for(;y!==f;)w[v++]=p[y++];return w}function c(l){const h=a(l);if(h)return h;throw new Error("Non-base"+r+" character")}return{encode:o,decodeUnsafe:a,decode:c}}var Cf="**********************************************************";const Rf=Tf(Cf),Nf=t=>JSON.stringify(t,(e,r)=>typeof r=="bigint"?r.toString()+"n":r),jf=t=>{const e=/([\[:])?(\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\}\]])/g,r=t.replace(e,'$1"$2n"$3');return JSON.parse(r,(i,s)=>typeof s=="string"&&s.match(/^\d+n$/)?BigInt(s.substring(0,s.length-1)):s)};function lr(t){if(typeof t!="string")throw new Error(`Cannot safe json parse value of type ${typeof t}`);try{return jf(t)}catch{return t}}function Dt(t){return typeof t=="string"?t:Nf(t)||""}function Bf(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function vh(t,...e){if(!Bf(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function Pa(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function Df(t,e){vh(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Er=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const en=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);function Uf(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function Eh(t){return typeof t=="string"&&(t=Uf(t)),vh(t),t}let Lf=class{clone(){return this._cloneInto()}};function kf(t){const e=i=>t().update(Eh(i)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function _h(t=32){if(Er&&typeof Er.getRandomValues=="function")return Er.getRandomValues(new Uint8Array(t));if(Er&&typeof Er.randomBytes=="function")return Er.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}function qf(t,e,r,i){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,i);const s=BigInt(32),n=BigInt(**********),o=Number(r>>s&n),a=Number(r&n),c=i?4:0,l=i?0:4;t.setUint32(e+c,o,i),t.setUint32(e+l,a,i)}let Mf=class extends Lf{constructor(e,r,i,s){super(),this.blockLen=e,this.outputLen=r,this.padOffset=i,this.isLE=s,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=en(this.buffer)}update(e){Pa(this);const{view:r,buffer:i,blockLen:s}=this;e=Eh(e);const n=e.length;for(let o=0;o<n;){const a=Math.min(s-this.pos,n-o);if(a===s){const c=en(e);for(;s<=n-o;o+=s)this.process(c,o);continue}i.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===s&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Pa(this),Df(e,this),this.finished=!0;const{buffer:r,view:i,blockLen:s,isLE:n}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>s-o&&(this.process(i,0),o=0);for(let u=o;u<s;u++)r[u]=0;qf(i,s-8,BigInt(this.length*8),n),this.process(i,0);const a=en(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const l=c/4,h=this.get();if(l>h.length)throw new Error("_sha2: outputLen bigger than state");for(let u=0;u<l;u++)a.setUint32(4*u,h[u],n)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const i=e.slice(0,r);return this.destroy(),i}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:i,length:s,finished:n,destroyed:o,pos:a}=this;return e.length=s,e.pos=a,e.finished=n,e.destroyed=o,s%r&&e.buffer.set(i),e}};const Wi=BigInt(2**32-1),kn=BigInt(32);function Ih(t,e=!1){return e?{h:Number(t&Wi),l:Number(t>>kn&Wi)}:{h:Number(t>>kn&Wi)|0,l:Number(t&Wi)|0}}function Ff(t,e=!1){let r=new Uint32Array(t.length),i=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:n,l:o}=Ih(t[s],e);[r[s],i[s]]=[n,o]}return[r,i]}const zf=(t,e)=>BigInt(t>>>0)<<kn|BigInt(e>>>0),Hf=(t,e,r)=>t>>>r,Vf=(t,e,r)=>t<<32-r|e>>>r,Kf=(t,e,r)=>t>>>r|e<<32-r,Wf=(t,e,r)=>t<<32-r|e>>>r,Gf=(t,e,r)=>t<<64-r|e>>>r-32,Yf=(t,e,r)=>t>>>r-32|e<<64-r,Jf=(t,e)=>e,Zf=(t,e)=>t,Qf=(t,e,r)=>t<<r|e>>>32-r,Xf=(t,e,r)=>e<<r|t>>>32-r,eg=(t,e,r)=>e<<r-32|t>>>64-r,tg=(t,e,r)=>t<<r-32|e>>>64-r;function rg(t,e,r,i){const s=(e>>>0)+(i>>>0);return{h:t+r+(s/2**32|0)|0,l:s|0}}const ig=(t,e,r)=>(t>>>0)+(e>>>0)+(r>>>0),sg=(t,e,r,i)=>e+r+i+(t/2**32|0)|0,ng=(t,e,r,i)=>(t>>>0)+(e>>>0)+(r>>>0)+(i>>>0),og=(t,e,r,i,s)=>e+r+i+s+(t/2**32|0)|0,ag=(t,e,r,i,s)=>(t>>>0)+(e>>>0)+(r>>>0)+(i>>>0)+(s>>>0),cg=(t,e,r,i,s,n)=>e+r+i+s+n+(t/2**32|0)|0,V={fromBig:Ih,split:Ff,toBig:zf,shrSH:Hf,shrSL:Vf,rotrSH:Kf,rotrSL:Wf,rotrBH:Gf,rotrBL:Yf,rotr32H:Jf,rotr32L:Zf,rotlSH:Qf,rotlSL:Xf,rotlBH:eg,rotlBL:tg,add:rg,add3L:ig,add3H:sg,add4L:ng,add4H:og,add5H:cg,add5L:ag},[lg,hg]=V.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))),Ut=new Uint32Array(80),Lt=new Uint32Array(80);let ug=class extends Mf{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:r,Bh:i,Bl:s,Ch:n,Cl:o,Dh:a,Dl:c,Eh:l,El:h,Fh:u,Fl:d,Gh:f,Gl:p,Hh:y,Hl:w}=this;return[e,r,i,s,n,o,a,c,l,h,u,d,f,p,y,w]}set(e,r,i,s,n,o,a,c,l,h,u,d,f,p,y,w){this.Ah=e|0,this.Al=r|0,this.Bh=i|0,this.Bl=s|0,this.Ch=n|0,this.Cl=o|0,this.Dh=a|0,this.Dl=c|0,this.Eh=l|0,this.El=h|0,this.Fh=u|0,this.Fl=d|0,this.Gh=f|0,this.Gl=p|0,this.Hh=y|0,this.Hl=w|0}process(e,r){for(let E=0;E<16;E++,r+=4)Ut[E]=e.getUint32(r),Lt[E]=e.getUint32(r+=4);for(let E=16;E<80;E++){const I=Ut[E-15]|0,x=Lt[E-15]|0,A=V.rotrSH(I,x,1)^V.rotrSH(I,x,8)^V.shrSH(I,x,7),P=V.rotrSL(I,x,1)^V.rotrSL(I,x,8)^V.shrSL(I,x,7),C=Ut[E-2]|0,S=Lt[E-2]|0,M=V.rotrSH(C,S,19)^V.rotrBH(C,S,61)^V.shrSH(C,S,6),U=V.rotrSL(C,S,19)^V.rotrBL(C,S,61)^V.shrSL(C,S,6),D=V.add4L(P,U,Lt[E-7],Lt[E-16]),F=V.add4H(D,A,M,Ut[E-7],Ut[E-16]);Ut[E]=F|0,Lt[E]=D|0}let{Ah:i,Al:s,Bh:n,Bl:o,Ch:a,Cl:c,Dh:l,Dl:h,Eh:u,El:d,Fh:f,Fl:p,Gh:y,Gl:w,Hh:v,Hl:b}=this;for(let E=0;E<80;E++){const I=V.rotrSH(u,d,14)^V.rotrSH(u,d,18)^V.rotrBH(u,d,41),x=V.rotrSL(u,d,14)^V.rotrSL(u,d,18)^V.rotrBL(u,d,41),A=u&f^~u&y,P=d&p^~d&w,C=V.add5L(b,x,P,hg[E],Lt[E]),S=V.add5H(C,v,I,A,lg[E],Ut[E]),M=C|0,U=V.rotrSH(i,s,28)^V.rotrBH(i,s,34)^V.rotrBH(i,s,39),D=V.rotrSL(i,s,28)^V.rotrBL(i,s,34)^V.rotrBL(i,s,39),F=i&n^i&a^n&a,R=s&o^s&c^o&c;v=y|0,b=w|0,y=f|0,w=p|0,f=u|0,p=d|0,{h:u,l:d}=V.add(l|0,h|0,S|0,M|0),l=a|0,h=c|0,a=n|0,c=o|0,n=i|0,o=s|0;const g=V.add3L(M,D,R);i=V.add3H(g,S,U,F),s=g|0}({h:i,l:s}=V.add(this.Ah|0,this.Al|0,i|0,s|0)),{h:n,l:o}=V.add(this.Bh|0,this.Bl|0,n|0,o|0),{h:a,l:c}=V.add(this.Ch|0,this.Cl|0,a|0,c|0),{h:l,l:h}=V.add(this.Dh|0,this.Dl|0,l|0,h|0),{h:u,l:d}=V.add(this.Eh|0,this.El|0,u|0,d|0),{h:f,l:p}=V.add(this.Fh|0,this.Fl|0,f|0,p|0),{h:y,l:w}=V.add(this.Gh|0,this.Gl|0,y|0,w|0),{h:v,l:b}=V.add(this.Hh|0,this.Hl|0,v|0,b|0),this.set(i,s,n,o,a,c,l,h,u,d,f,p,y,w,v,b)}roundClean(){Ut.fill(0),Lt.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}};const dg=kf(()=>new ug);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const To=BigInt(0),$h=BigInt(1),pg=BigInt(2);function Co(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Ro(t){if(!Co(t))throw new Error("Uint8Array expected")}function tn(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const fg=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function No(t){Ro(t);let e="";for(let r=0;r<t.length;r++)e+=fg[t[r]];return e}function Sh(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?To:BigInt("0x"+t)}const Ot={_0:48,_9:57,A:65,F:70,a:97,f:102};function xa(t){if(t>=Ot._0&&t<=Ot._9)return t-Ot._0;if(t>=Ot.A&&t<=Ot.F)return t-(Ot.A-10);if(t>=Ot.a&&t<=Ot.f)return t-(Ot.a-10)}function Oh(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const i=new Uint8Array(r);for(let s=0,n=0;s<r;s++,n+=2){const o=xa(t.charCodeAt(n)),a=xa(t.charCodeAt(n+1));if(o===void 0||a===void 0){const c=t[n]+t[n+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+n)}i[s]=o*16+a}return i}function gg(t){return Sh(No(t))}function ls(t){return Ro(t),Sh(No(Uint8Array.from(t).reverse()))}function Ph(t,e){return Oh(t.toString(16).padStart(e*2,"0"))}function qn(t,e){return Ph(t,e).reverse()}function Pt(t,e,r){let i;if(typeof e=="string")try{i=Oh(e)}catch(n){throw new Error(t+" must be hex string or Uint8Array, cause: "+n)}else if(Co(e))i=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const s=i.length;if(typeof r=="number"&&s!==r)throw new Error(t+" of length "+r+" expected, got "+s);return i}function Aa(...t){let e=0;for(let i=0;i<t.length;i++){const s=t[i];Ro(s),e+=s.length}const r=new Uint8Array(e);for(let i=0,s=0;i<t.length;i++){const n=t[i];r.set(n,s),s+=n.length}return r}const rn=t=>typeof t=="bigint"&&To<=t;function yg(t,e,r){return rn(t)&&rn(e)&&rn(r)&&e<=t&&t<r}function hi(t,e,r,i){if(!yg(e,r,i))throw new Error("expected valid "+t+": "+r+" <= n < "+i+", got "+e)}function mg(t){let e;for(e=0;t>To;t>>=$h,e+=1);return e}const wg=t=>(pg<<BigInt(t-1))-$h,bg={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Co(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function jo(t,e,r={}){const i=(s,n,o)=>{const a=bg[n];if(typeof a!="function")throw new Error("invalid validator function");const c=t[s];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(s)+" is invalid. Expected "+n+", got "+c)};for(const[s,n]of Object.entries(e))i(s,n,!1);for(const[s,n]of Object.entries(r))i(s,n,!0);return t}function Ta(t){const e=new WeakMap;return(r,...i)=>{const s=e.get(r);if(s!==void 0)return s;const n=t(r,...i);return e.set(r,n),n}}const be=BigInt(0),ce=BigInt(1),er=BigInt(2),vg=BigInt(3),Mn=BigInt(4),Ca=BigInt(5),Ra=BigInt(8);function pe(t,e){const r=t%e;return r>=be?r:e+r}function Eg(t,e,r){if(e<be)throw new Error("invalid exponent, negatives unsupported");if(r<=be)throw new Error("invalid modulus");if(r===ce)return be;let i=ce;for(;e>be;)e&ce&&(i=i*t%r),t=t*t%r,e>>=ce;return i}function pt(t,e,r){let i=t;for(;e-- >be;)i*=i,i%=r;return i}function Na(t,e){if(t===be)throw new Error("invert: expected non-zero number");if(e<=be)throw new Error("invert: expected positive modulus, got "+e);let r=pe(t,e),i=e,s=be,n=ce;for(;r!==be;){const o=i/r,a=i%r,c=s-n*o;i=r,r=a,s=n,n=c}if(i!==ce)throw new Error("invert: does not exist");return pe(s,e)}function _g(t){const e=(t-ce)/er;let r,i,s;for(r=t-ce,i=0;r%er===be;r/=er,i++);for(s=er;s<t&&Eg(s,e,t)!==t-ce;s++)if(s>1e3)throw new Error("Cannot find square root: likely non-prime P");if(i===1){const o=(t+ce)/Mn;return function(a,c){const l=a.pow(c,o);if(!a.eql(a.sqr(l),c))throw new Error("Cannot find square root");return l}}const n=(r+ce)/er;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=i,l=o.pow(o.mul(o.ONE,s),r),h=o.pow(a,n),u=o.pow(a,r);for(;!o.eql(u,o.ONE);){if(o.eql(u,o.ZERO))return o.ZERO;let d=1;for(let p=o.sqr(u);d<c&&!o.eql(p,o.ONE);d++)p=o.sqr(p);const f=o.pow(l,ce<<BigInt(c-d-1));l=o.sqr(f),h=o.mul(h,f),u=o.mul(u,l),c=d}return h}}function Ig(t){if(t%Mn===vg){const e=(t+ce)/Mn;return function(r,i){const s=r.pow(i,e);if(!r.eql(r.sqr(s),i))throw new Error("Cannot find square root");return s}}if(t%Ra===Ca){const e=(t-Ca)/Ra;return function(r,i){const s=r.mul(i,er),n=r.pow(s,e),o=r.mul(i,n),a=r.mul(r.mul(o,er),n),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),i))throw new Error("Cannot find square root");return c}}return _g(t)}const $g=(t,e)=>(pe(t,e)&ce)===ce,Sg=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Og(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=Sg.reduce((i,s)=>(i[s]="function",i),e);return jo(t,r)}function Pg(t,e,r){if(r<be)throw new Error("invalid exponent, negatives unsupported");if(r===be)return t.ONE;if(r===ce)return e;let i=t.ONE,s=e;for(;r>be;)r&ce&&(i=t.mul(i,s)),s=t.sqr(s),r>>=ce;return i}function xg(t,e){const r=new Array(e.length),i=e.reduce((n,o,a)=>t.is0(o)?n:(r[a]=n,t.mul(n,o)),t.ONE),s=t.inv(i);return e.reduceRight((n,o,a)=>t.is0(o)?n:(r[a]=t.mul(n,r[a]),t.mul(n,o)),s),r}function xh(t,e){const r=e!==void 0?e:t.toString(2).length,i=Math.ceil(r/8);return{nBitLength:r,nByteLength:i}}function Ah(t,e,r=!1,i={}){if(t<=be)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:s,nByteLength:n}=xh(t,e);if(n>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:s,BYTES:n,MASK:wg(s),ZERO:be,ONE:ce,create:c=>pe(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return be<=c&&c<t},is0:c=>c===be,isOdd:c=>(c&ce)===ce,neg:c=>pe(-c,t),eql:(c,l)=>c===l,sqr:c=>pe(c*c,t),add:(c,l)=>pe(c+l,t),sub:(c,l)=>pe(c-l,t),mul:(c,l)=>pe(c*l,t),pow:(c,l)=>Pg(a,c,l),div:(c,l)=>pe(c*Na(l,t),t),sqrN:c=>c*c,addN:(c,l)=>c+l,subN:(c,l)=>c-l,mulN:(c,l)=>c*l,inv:c=>Na(c,t),sqrt:i.sqrt||(c=>(o||(o=Ig(t)),o(a,c))),invertBatch:c=>xg(a,c),cmov:(c,l,h)=>h?l:c,toBytes:c=>r?qn(c,n):Ph(c,n),fromBytes:c=>{if(c.length!==n)throw new Error("Field.fromBytes: expected "+n+" bytes, got "+c.length);return r?ls(c):gg(c)}});return Object.freeze(a)}const ja=BigInt(0),Gi=BigInt(1);function sn(t,e){const r=e.negate();return t?r:e}function Th(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function nn(t,e){Th(t,e);const r=Math.ceil(e/t)+1,i=2**(t-1);return{windows:r,windowSize:i}}function Ag(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,i)=>{if(!(r instanceof e))throw new Error("invalid point at index "+i)})}function Tg(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,i)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+i)})}const on=new WeakMap,Ch=new WeakMap;function an(t){return Ch.get(t)||1}function Cg(t,e){return{constTimeNegate:sn,hasPrecomputes(r){return an(r)!==1},unsafeLadder(r,i,s=t.ZERO){let n=r;for(;i>ja;)i&Gi&&(s=s.add(n)),n=n.double(),i>>=Gi;return s},precomputeWindow(r,i){const{windows:s,windowSize:n}=nn(i,e),o=[];let a=r,c=a;for(let l=0;l<s;l++){c=a,o.push(c);for(let h=1;h<n;h++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,i,s){const{windows:n,windowSize:o}=nn(r,e);let a=t.ZERO,c=t.BASE;const l=BigInt(2**r-1),h=2**r,u=BigInt(r);for(let d=0;d<n;d++){const f=d*o;let p=Number(s&l);s>>=u,p>o&&(p-=h,s+=Gi);const y=f,w=f+Math.abs(p)-1,v=d%2!==0,b=p<0;p===0?c=c.add(sn(v,i[y])):a=a.add(sn(b,i[w]))}return{p:a,f:c}},wNAFUnsafe(r,i,s,n=t.ZERO){const{windows:o,windowSize:a}=nn(r,e),c=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let u=0;u<o;u++){const d=u*a;if(s===ja)break;let f=Number(s&c);if(s>>=h,f>a&&(f-=l,s+=Gi),f===0)continue;let p=i[d+Math.abs(f)-1];f<0&&(p=p.negate()),n=n.add(p)}return n},getPrecomputes(r,i,s){let n=on.get(i);return n||(n=this.precomputeWindow(i,r),r!==1&&on.set(i,s(n))),n},wNAFCached(r,i,s){const n=an(r);return this.wNAF(n,this.getPrecomputes(n,r,s),i)},wNAFCachedUnsafe(r,i,s,n){const o=an(r);return o===1?this.unsafeLadder(r,i,n):this.wNAFUnsafe(o,this.getPrecomputes(o,r,s),i,n)},setWindowSize(r,i){Th(i,e),Ch.set(r,i),on.delete(r)}}}function Rg(t,e,r,i){if(Ag(r,t),Tg(i,e),r.length!==i.length)throw new Error("arrays of points and scalars must have equal length");const s=t.ZERO,n=mg(BigInt(r.length)),o=n>12?n-3:n>4?n-2:n?2:1,a=(1<<o)-1,c=new Array(a+1).fill(s),l=Math.floor((e.BITS-1)/o)*o;let h=s;for(let u=l;u>=0;u-=o){c.fill(s);for(let f=0;f<i.length;f++){const p=i[f],y=Number(p>>BigInt(u)&BigInt(a));c[y]=c[y].add(r[f])}let d=s;for(let f=c.length-1,p=s;f>0;f--)p=p.add(c[f]),d=d.add(p);if(h=h.add(d),u!==0)for(let f=0;f<o;f++)h=h.double()}return h}function Ng(t){return Og(t.Fp),jo(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...xh(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const nt=BigInt(0),Be=BigInt(1),Yi=BigInt(2),jg=BigInt(8),Bg={zip215:!0};function Dg(t){const e=Ng(t);return jo(t,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function Ug(t){const e=Dg(t),{Fp:r,n:i,prehash:s,hash:n,randomBytes:o,nByteLength:a,h:c}=e,l=Yi<<BigInt(a*8)-Be,h=r.create,u=Ah(e.n,e.nBitLength),d=e.uvRatio||((g,m)=>{try{return{isValid:!0,value:r.sqrt(g*r.inv(m))}}catch{return{isValid:!1,value:nt}}}),f=e.adjustScalarBytes||(g=>g),p=e.domain||((g,m,_)=>{if(tn("phflag",_),m.length||_)throw new Error("Contexts/pre-hash are not supported");return g});function y(g,m){hi("coordinate "+g,m,nt,l)}function w(g){if(!(g instanceof E))throw new Error("ExtendedPoint expected")}const v=Ta((g,m)=>{const{ex:_,ey:O,ez:$}=g,T=g.is0();m==null&&(m=T?jg:r.inv($));const L=h(_*m),q=h(O*m),z=h($*m);if(T)return{x:nt,y:Be};if(z!==Be)throw new Error("invZ was invalid");return{x:L,y:q}}),b=Ta(g=>{const{a:m,d:_}=e;if(g.is0())throw new Error("bad point: ZERO");const{ex:O,ey:$,ez:T,et:L}=g,q=h(O*O),z=h($*$),k=h(T*T),H=h(k*k),G=h(q*m),se=h(k*h(G+z)),ee=h(H+h(_*h(q*z)));if(se!==ee)throw new Error("bad point: equation left != right (1)");const Y=h(O*$),Ae=h(T*L);if(Y!==Ae)throw new Error("bad point: equation left != right (2)");return!0});class E{constructor(m,_,O,$){this.ex=m,this.ey=_,this.ez=O,this.et=$,y("x",m),y("y",_),y("z",O),y("t",$),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(m){if(m instanceof E)throw new Error("extended point not allowed");const{x:_,y:O}=m||{};return y("x",_),y("y",O),new E(_,O,Be,h(_*O))}static normalizeZ(m){const _=r.invertBatch(m.map(O=>O.ez));return m.map((O,$)=>O.toAffine(_[$])).map(E.fromAffine)}static msm(m,_){return Rg(E,u,m,_)}_setWindowSize(m){A.setWindowSize(this,m)}assertValidity(){b(this)}equals(m){w(m);const{ex:_,ey:O,ez:$}=this,{ex:T,ey:L,ez:q}=m,z=h(_*q),k=h(T*$),H=h(O*q),G=h(L*$);return z===k&&H===G}is0(){return this.equals(E.ZERO)}negate(){return new E(h(-this.ex),this.ey,this.ez,h(-this.et))}double(){const{a:m}=e,{ex:_,ey:O,ez:$}=this,T=h(_*_),L=h(O*O),q=h(Yi*h($*$)),z=h(m*T),k=_+O,H=h(h(k*k)-T-L),G=z+L,se=G-q,ee=z-L,Y=h(H*se),Ae=h(G*ee),Ee=h(H*ee),Ce=h(se*G);return new E(Y,Ae,Ce,Ee)}add(m){w(m);const{a:_,d:O}=e,{ex:$,ey:T,ez:L,et:q}=this,{ex:z,ey:k,ez:H,et:G}=m;if(_===BigInt(-1)){const Zo=h((T-$)*(k+z)),Qo=h((T+$)*(k-z)),Gs=h(Qo-Zo);if(Gs===nt)return this.double();const Xo=h(L*Yi*G),ea=h(q*Yi*H),ta=ea+Xo,ra=Qo+Zo,ia=ea-Xo,jd=h(ta*Gs),Bd=h(ra*ia),Dd=h(ta*ia),Ud=h(Gs*ra);return new E(jd,Bd,Ud,Dd)}const se=h($*z),ee=h(T*k),Y=h(q*O*G),Ae=h(L*H),Ee=h(($+T)*(z+k)-se-ee),Ce=Ae-Y,Ye=Ae+Y,Je=h(ee-_*se),wr=h(Ee*Ce),Cd=h(Ye*Je),Rd=h(Ee*Je),Nd=h(Ce*Ye);return new E(wr,Cd,Nd,Rd)}subtract(m){return this.add(m.negate())}wNAF(m){return A.wNAFCached(this,m,E.normalizeZ)}multiply(m){const _=m;hi("scalar",_,Be,i);const{p:O,f:$}=this.wNAF(_);return E.normalizeZ([O,$])[0]}multiplyUnsafe(m,_=E.ZERO){const O=m;return hi("scalar",O,nt,i),O===nt?x:this.is0()||O===Be?this:A.wNAFCachedUnsafe(this,O,E.normalizeZ,_)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return A.unsafeLadder(this,i).is0()}toAffine(m){return v(this,m)}clearCofactor(){const{h:m}=e;return m===Be?this:this.multiplyUnsafe(m)}static fromHex(m,_=!1){const{d:O,a:$}=e,T=r.BYTES;m=Pt("pointHex",m,T),tn("zip215",_);const L=m.slice(),q=m[T-1];L[T-1]=q&-129;const z=ls(L),k=_?l:r.ORDER;hi("pointHex.y",z,nt,k);const H=h(z*z),G=h(H-Be),se=h(O*H-$);let{isValid:ee,value:Y}=d(G,se);if(!ee)throw new Error("Point.fromHex: invalid y coordinate");const Ae=(Y&Be)===Be,Ee=(q&128)!==0;if(!_&&Y===nt&&Ee)throw new Error("Point.fromHex: x=0 and x_0=1");return Ee!==Ae&&(Y=h(-Y)),E.fromAffine({x:Y,y:z})}static fromPrivateKey(m){return S(m).point}toRawBytes(){const{x:m,y:_}=this.toAffine(),O=qn(_,r.BYTES);return O[O.length-1]|=m&Be?128:0,O}toHex(){return No(this.toRawBytes())}}E.BASE=new E(e.Gx,e.Gy,Be,h(e.Gx*e.Gy)),E.ZERO=new E(nt,Be,Be,nt);const{BASE:I,ZERO:x}=E,A=Cg(E,a*8);function P(g){return pe(g,i)}function C(g){return P(ls(g))}function S(g){const m=r.BYTES;g=Pt("private key",g,m);const _=Pt("hashed private key",n(g),2*m),O=f(_.slice(0,m)),$=_.slice(m,2*m),T=C(O),L=I.multiply(T),q=L.toRawBytes();return{head:O,prefix:$,scalar:T,point:L,pointBytes:q}}function M(g){return S(g).pointBytes}function U(g=new Uint8Array,...m){const _=Aa(...m);return C(n(p(_,Pt("context",g),!!s)))}function D(g,m,_={}){g=Pt("message",g),s&&(g=s(g));const{prefix:O,scalar:$,pointBytes:T}=S(m),L=U(_.context,O,g),q=I.multiply(L).toRawBytes(),z=U(_.context,q,T,g),k=P(L+z*$);hi("signature.s",k,nt,i);const H=Aa(q,qn(k,r.BYTES));return Pt("result",H,r.BYTES*2)}const F=Bg;function R(g,m,_,O=F){const{context:$,zip215:T}=O,L=r.BYTES;g=Pt("signature",g,2*L),m=Pt("message",m),_=Pt("publicKey",_,L),T!==void 0&&tn("zip215",T),s&&(m=s(m));const q=ls(g.slice(L,2*L));let z,k,H;try{z=E.fromHex(_,T),k=E.fromHex(g.slice(0,L),T),H=I.multiplyUnsafe(q)}catch{return!1}if(!T&&z.isSmallOrder())return!1;const G=U($,k.toRawBytes(),z.toRawBytes(),m);return k.add(z.multiplyUnsafe(G)).subtract(H).clearCofactor().equals(E.ZERO)}return I._setWindowSize(8),{CURVE:e,getPublicKey:M,sign:D,verify:R,ExtendedPoint:E,utils:{getExtendedPublicKey:S,randomPrivateKey:()=>o(r.BYTES),precompute(g=8,m=E.BASE){return m._setWindowSize(g),m.multiply(BigInt(3)),m}}}}BigInt(0),BigInt(1);const Bo=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),Ba=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");BigInt(0);const Lg=BigInt(1),Da=BigInt(2);BigInt(3);const kg=BigInt(5),qg=BigInt(8);function Mg(t){const e=BigInt(10),r=BigInt(20),i=BigInt(40),s=BigInt(80),n=Bo,o=t*t%n*t%n,a=pt(o,Da,n)*o%n,c=pt(a,Lg,n)*t%n,l=pt(c,kg,n)*c%n,h=pt(l,e,n)*l%n,u=pt(h,r,n)*h%n,d=pt(u,i,n)*u%n,f=pt(d,s,n)*d%n,p=pt(f,s,n)*d%n,y=pt(p,e,n)*l%n;return{pow_p_5_8:pt(y,Da,n)*t%n,b2:o}}function Fg(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}function zg(t,e){const r=Bo,i=pe(e*e*e,r),s=pe(i*i*e,r),n=Mg(t*s).pow_p_5_8;let o=pe(t*i*n,r);const a=pe(e*o*o,r),c=o,l=pe(o*Ba,r),h=a===t,u=a===pe(-t,r),d=a===pe(-t*Ba,r);return h&&(o=c),(u||d)&&(o=l),$g(o,r)&&(o=pe(-o,r)),{isValid:h||u,value:o}}const Hg=Ah(Bo,void 0,!0),Vg={a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:Hg,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:qg,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:dg,randomBytes:_h,adjustScalarBytes:Fg,uvRatio:zg},Rh=Ug(Vg),Kg="EdDSA",Wg="JWT",ms=".",Ts="base64url",Nh="utf8",jh="utf8",Gg=":",Yg="did",Jg="key",Ua="base58btc",Zg="z",Qg="K36",Xg=32;function Do(t){return globalThis.Buffer!=null?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t}function Bh(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?Do(globalThis.Buffer.allocUnsafe(t)):new Uint8Array(t)}function Dh(t,e){e||(e=t.reduce((s,n)=>s+n.length,0));const r=Bh(e);let i=0;for(const s of t)r.set(s,i),i+=s.length;return Do(r)}function ey(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var s=0;s<t.length;s++){var n=t.charAt(s),o=n.charCodeAt(0);if(r[o]!==255)throw new TypeError(n+" is ambiguous");r[o]=s}var a=t.length,c=t.charAt(0),l=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function u(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,w=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*h+1>>>0,I=new Uint8Array(E);v!==b;){for(var x=p[v],A=0,P=E-1;(x!==0||A<w)&&P!==-1;P--,A++)x+=256*I[P]>>>0,I[P]=x%a>>>0,x=x/a>>>0;if(x!==0)throw new Error("Non-zero carry");w=A,v++}for(var C=E-w;C!==E&&I[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(I[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var w=0,v=0;p[y]===c;)w++,y++;for(var b=(p.length-y)*l+1>>>0,E=new Uint8Array(b);p[y];){var I=r[p.charCodeAt(y)];if(I===255)return;for(var x=0,A=b-1;(I!==0||x<v)&&A!==-1;A--,x++)I+=a*E[A]>>>0,E[A]=I%256>>>0,I=I/256>>>0;if(I!==0)throw new Error("Non-zero carry");v=x,y++}if(p[y]!==" "){for(var P=b-v;P!==b&&E[P]===0;)P++;for(var C=new Uint8Array(w+(b-P)),S=w;P!==b;)C[S++]=E[P++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:u,decodeUnsafe:d,decode:f}}var ty=ey,ry=ty;const Uh=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},iy=t=>new TextEncoder().encode(t),sy=t=>new TextDecoder().decode(t);let ny=class{constructor(e,r,i){this.name=e,this.prefix=r,this.baseEncode=i}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}},oy=class{constructor(e,r,i){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=i}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return Lh(this,e)}},ay=class{constructor(e){this.decoders=e}or(e){return Lh(this,e)}decode(e){const r=e[0],i=this.decoders[r];if(i)return i.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}};const Lh=(t,e)=>new ay({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});let cy=class{constructor(e,r,i,s){this.name=e,this.prefix=r,this.baseEncode=i,this.baseDecode=s,this.encoder=new ny(e,r,i),this.decoder=new oy(e,r,s)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}};const Cs=({name:t,prefix:e,encode:r,decode:i})=>new cy(t,e,r,i),Bi=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:s}=ry(r,e);return Cs({prefix:t,name:e,encode:i,decode:n=>Uh(s(n))})},ly=(t,e,r,i)=>{const s={};for(let h=0;h<e.length;++h)s[e[h]]=h;let n=t.length;for(;t[n-1]==="=";)--n;const o=new Uint8Array(n*r/8|0);let a=0,c=0,l=0;for(let h=0;h<n;++h){const u=s[t[h]];if(u===void 0)throw new SyntaxError(`Non-${i} character`);c=c<<r|u,a+=r,a>=8&&(a-=8,o[l++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},hy=(t,e,r)=>{const i=e[e.length-1]==="=",s=(1<<r)-1;let n="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,n+=e[s&a>>o];if(o&&(n+=e[s&a<<r-o]),i)for(;n.length*r&7;)n+="=";return n},Oe=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>Cs({prefix:e,name:t,encode(s){return hy(s,i,r)},decode(s){return ly(s,i,r,t)}}),uy=Cs({prefix:"\0",name:"identity",encode:t=>sy(t),decode:t=>iy(t)});var dy=Object.freeze({__proto__:null,identity:uy});const py=Oe({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var fy=Object.freeze({__proto__:null,base2:py});const gy=Oe({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var yy=Object.freeze({__proto__:null,base8:gy});const my=Bi({prefix:"9",name:"base10",alphabet:"0123456789"});var wy=Object.freeze({__proto__:null,base10:my});const by=Oe({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),vy=Oe({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Ey=Object.freeze({__proto__:null,base16:by,base16upper:vy});const _y=Oe({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Iy=Oe({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),$y=Oe({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Sy=Oe({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Oy=Oe({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Py=Oe({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),xy=Oe({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),Ay=Oe({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),Ty=Oe({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var Cy=Object.freeze({__proto__:null,base32:_y,base32upper:Iy,base32pad:$y,base32padupper:Sy,base32hex:Oy,base32hexupper:Py,base32hexpad:xy,base32hexpadupper:Ay,base32z:Ty});const Ry=Bi({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),Ny=Bi({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var jy=Object.freeze({__proto__:null,base36:Ry,base36upper:Ny});const By=Bi({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Dy=Bi({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var Uy=Object.freeze({__proto__:null,base58btc:By,base58flickr:Dy});const Ly=Oe({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),ky=Oe({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),qy=Oe({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),My=Oe({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Fy=Object.freeze({__proto__:null,base64:Ly,base64pad:ky,base64url:qy,base64urlpad:My});const kh=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),zy=kh.reduce((t,e,r)=>(t[r]=e,t),[]),Hy=kh.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function Vy(t){return t.reduce((e,r)=>(e+=zy[r],e),"")}function Ky(t){const e=[];for(const r of t){const i=Hy[r.codePointAt(0)];if(i===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(i)}return new Uint8Array(e)}const Wy=Cs({prefix:"🚀",name:"base256emoji",encode:Vy,decode:Ky});var Gy=Object.freeze({__proto__:null,base256emoji:Wy}),Yy=qh,La=128,Jy=-128,Zy=Math.pow(2,31);function qh(t,e,r){e=e||[],r=r||0;for(var i=r;t>=Zy;)e[r++]=t&255|La,t/=128;for(;t&Jy;)e[r++]=t&255|La,t>>>=7;return e[r]=t|0,qh.bytes=r-i+1,e}var Qy=Fn,Xy=128,ka=127;function Fn(t,i){var r=0,i=i||0,s=0,n=i,o,a=t.length;do{if(n>=a)throw Fn.bytes=0,new RangeError("Could not decode varint");o=t[n++],r+=s<28?(o&ka)<<s:(o&ka)*Math.pow(2,s),s+=7}while(o>=Xy);return Fn.bytes=n-i,r}var em=Math.pow(2,7),tm=Math.pow(2,14),rm=Math.pow(2,21),im=Math.pow(2,28),sm=Math.pow(2,35),nm=Math.pow(2,42),om=Math.pow(2,49),am=Math.pow(2,56),cm=Math.pow(2,63),lm=function(t){return t<em?1:t<tm?2:t<rm?3:t<im?4:t<sm?5:t<nm?6:t<om?7:t<am?8:t<cm?9:10},hm={encode:Yy,decode:Qy,encodingLength:lm},Mh=hm;const qa=(t,e,r=0)=>(Mh.encode(t,e,r),e),Ma=t=>Mh.encodingLength(t),zn=(t,e)=>{const r=e.byteLength,i=Ma(t),s=i+Ma(r),n=new Uint8Array(s+r);return qa(t,n,0),qa(r,n,i),n.set(e,s),new um(t,r,e,n)};let um=class{constructor(e,r,i,s){this.code=e,this.size=r,this.digest=i,this.bytes=s}};const Fh=({name:t,code:e,encode:r})=>new dm(t,e,r);let dm=class{constructor(e,r,i){this.name=e,this.code=r,this.encode=i}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?zn(this.code,r):r.then(i=>zn(this.code,i))}else throw Error("Unknown type, must be binary type")}};const zh=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),pm=Fh({name:"sha2-256",code:18,encode:zh("SHA-256")}),fm=Fh({name:"sha2-512",code:19,encode:zh("SHA-512")});var gm=Object.freeze({__proto__:null,sha256:pm,sha512:fm});const Hh=0,ym="identity",Vh=Uh,mm=t=>zn(Hh,Vh(t)),wm={code:Hh,name:ym,encode:Vh,digest:mm};var bm=Object.freeze({__proto__:null,identity:wm});new TextEncoder,new TextDecoder;const Fa={...dy,...fy,...yy,...wy,...Ey,...Cy,...jy,...Uy,...Fy,...Gy};({...gm,...bm});function Kh(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const za=Kh("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),cn=Kh("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=Bh(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),Wh={utf8:za,"utf-8":za,hex:Fa.base16,latin1:cn,ascii:cn,binary:cn,...Fa};function Rs(t,e="utf8"){const r=Wh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}function ti(t,e="utf8"){const r=Wh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?Do(globalThis.Buffer.from(t,"utf-8")):r.decoder.decode(`${r.prefix}${t}`)}function Ha(t){return lr(Rs(ti(t,Ts),Nh))}function ws(t){return Rs(ti(Dt(t),Nh),Ts)}function Gh(t){const e=ti(Qg,Ua),r=Zg+Rs(Dh([e,t]),Ua);return[Yg,Jg,r].join(Gg)}function vm(t){return Rs(t,Ts)}function Em(t){return ti(t,Ts)}function _m(t){return ti([ws(t.header),ws(t.payload)].join(ms),jh)}function Im(t){return[ws(t.header),ws(t.payload),vm(t.signature)].join(ms)}function Hn(t){const e=t.split(ms),r=Ha(e[0]),i=Ha(e[1]),s=Em(e[2]),n=ti(e.slice(0,2).join(ms),jh);return{header:r,payload:i,signature:s,data:n}}function Va(t=_h(Xg)){const e=Rh.getPublicKey(t);return{secretKey:Dh([t,e]),publicKey:e}}async function $m(t,e,r,i,s=j.fromMiliseconds(Date.now())){const n={alg:Kg,typ:Wg},o=Gh(i.publicKey),a=s+r,c={iss:o,sub:t,aud:e,iat:s,exp:a},l=_m({header:n,payload:c}),h=Rh.sign(l,i.secretKey.slice(0,32));return Im({header:n,payload:c,signature:h})}function Yh(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function vi(t,e){e||(e=t.reduce((s,n)=>s+n.length,0));const r=Yh(e);let i=0;for(const s of t)r.set(s,i),i+=s.length;return r}function Sm(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var s=0;s<t.length;s++){var n=t.charAt(s),o=n.charCodeAt(0);if(r[o]!==255)throw new TypeError(n+" is ambiguous");r[o]=s}var a=t.length,c=t.charAt(0),l=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function u(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,w=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*h+1>>>0,I=new Uint8Array(E);v!==b;){for(var x=p[v],A=0,P=E-1;(x!==0||A<w)&&P!==-1;P--,A++)x+=256*I[P]>>>0,I[P]=x%a>>>0,x=x/a>>>0;if(x!==0)throw new Error("Non-zero carry");w=A,v++}for(var C=E-w;C!==E&&I[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(I[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var w=0,v=0;p[y]===c;)w++,y++;for(var b=(p.length-y)*l+1>>>0,E=new Uint8Array(b);p[y];){var I=r[p.charCodeAt(y)];if(I===255)return;for(var x=0,A=b-1;(I!==0||x<v)&&A!==-1;A--,x++)I+=a*E[A]>>>0,E[A]=I%256>>>0,I=I/256>>>0;if(I!==0)throw new Error("Non-zero carry");v=x,y++}if(p[y]!==" "){for(var P=b-v;P!==b&&E[P]===0;)P++;for(var C=new Uint8Array(w+(b-P)),S=w;P!==b;)C[S++]=E[P++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:u,decodeUnsafe:d,decode:f}}var Om=Sm,Pm=Om;const xm=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Am=t=>new TextEncoder().encode(t),Tm=t=>new TextDecoder().decode(t);class Cm{constructor(e,r,i){this.name=e,this.prefix=r,this.baseEncode=i}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Rm{constructor(e,r,i){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=i}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return Jh(this,e)}}class Nm{constructor(e){this.decoders=e}or(e){return Jh(this,e)}decode(e){const r=e[0],i=this.decoders[r];if(i)return i.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Jh=(t,e)=>new Nm({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class jm{constructor(e,r,i,s){this.name=e,this.prefix=r,this.baseEncode=i,this.baseDecode=s,this.encoder=new Cm(e,r,i),this.decoder=new Rm(e,r,s)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Ns=({name:t,prefix:e,encode:r,decode:i})=>new jm(t,e,r,i),Di=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:s}=Pm(r,e);return Ns({prefix:t,name:e,encode:i,decode:n=>xm(s(n))})},Bm=(t,e,r,i)=>{const s={};for(let h=0;h<e.length;++h)s[e[h]]=h;let n=t.length;for(;t[n-1]==="=";)--n;const o=new Uint8Array(n*r/8|0);let a=0,c=0,l=0;for(let h=0;h<n;++h){const u=s[t[h]];if(u===void 0)throw new SyntaxError(`Non-${i} character`);c=c<<r|u,a+=r,a>=8&&(a-=8,o[l++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},Dm=(t,e,r)=>{const i=e[e.length-1]==="=",s=(1<<r)-1;let n="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,n+=e[s&a>>o];if(o&&(n+=e[s&a<<r-o]),i)for(;n.length*r&7;)n+="=";return n},Pe=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>Ns({prefix:e,name:t,encode(s){return Dm(s,i,r)},decode(s){return Bm(s,i,r,t)}}),Um=Ns({prefix:"\0",name:"identity",encode:t=>Tm(t),decode:t=>Am(t)}),Lm=Object.freeze(Object.defineProperty({__proto__:null,identity:Um},Symbol.toStringTag,{value:"Module"})),km=Pe({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1}),qm=Object.freeze(Object.defineProperty({__proto__:null,base2:km},Symbol.toStringTag,{value:"Module"})),Mm=Pe({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3}),Fm=Object.freeze(Object.defineProperty({__proto__:null,base8:Mm},Symbol.toStringTag,{value:"Module"})),zm=Di({prefix:"9",name:"base10",alphabet:"0123456789"}),Hm=Object.freeze(Object.defineProperty({__proto__:null,base10:zm},Symbol.toStringTag,{value:"Module"})),Vm=Pe({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Km=Pe({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4}),Wm=Object.freeze(Object.defineProperty({__proto__:null,base16:Vm,base16upper:Km},Symbol.toStringTag,{value:"Module"})),Gm=Pe({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Ym=Pe({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Jm=Pe({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Zm=Pe({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Qm=Pe({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Xm=Pe({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),ew=Pe({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),tw=Pe({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),rw=Pe({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5}),iw=Object.freeze(Object.defineProperty({__proto__:null,base32:Gm,base32hex:Qm,base32hexpad:ew,base32hexpadupper:tw,base32hexupper:Xm,base32pad:Jm,base32padupper:Zm,base32upper:Ym,base32z:rw},Symbol.toStringTag,{value:"Module"})),sw=Di({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),nw=Di({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"}),ow=Object.freeze(Object.defineProperty({__proto__:null,base36:sw,base36upper:nw},Symbol.toStringTag,{value:"Module"})),aw=Di({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),cw=Di({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"}),lw=Object.freeze(Object.defineProperty({__proto__:null,base58btc:aw,base58flickr:cw},Symbol.toStringTag,{value:"Module"})),hw=Pe({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),uw=Pe({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),dw=Pe({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),pw=Pe({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6}),fw=Object.freeze(Object.defineProperty({__proto__:null,base64:hw,base64pad:uw,base64url:dw,base64urlpad:pw},Symbol.toStringTag,{value:"Module"})),Zh=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),gw=Zh.reduce((t,e,r)=>(t[r]=e,t),[]),yw=Zh.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function mw(t){return t.reduce((e,r)=>(e+=gw[r],e),"")}function ww(t){const e=[];for(const r of t){const i=yw[r.codePointAt(0)];if(i===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(i)}return new Uint8Array(e)}const bw=Ns({prefix:"🚀",name:"base256emoji",encode:mw,decode:ww}),vw=Object.freeze(Object.defineProperty({__proto__:null,base256emoji:bw},Symbol.toStringTag,{value:"Module"}));new TextEncoder;new TextDecoder;const Ka={...Lm,...qm,...Fm,...Hm,...Wm,...iw,...ow,...lw,...fw,...vw};function Qh(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const Wa=Qh("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),ln=Qh("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=Yh(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),Xh={utf8:Wa,"utf-8":Wa,hex:Ka.base16,latin1:ln,ascii:ln,binary:ln,...Ka};function tt(t,e="utf8"){const r=Xh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}function qe(t,e="utf8"){const r=Xh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}const Ew={waku:{publish:"waku_publish",batchPublish:"waku_batchPublish",subscribe:"waku_subscribe",batchSubscribe:"waku_batchSubscribe",subscription:"waku_subscription",unsubscribe:"waku_unsubscribe",batchUnsubscribe:"waku_batchUnsubscribe",batchFetchMessages:"waku_batchFetchMessages"},irn:{publish:"irn_publish",batchPublish:"irn_batchPublish",subscribe:"irn_subscribe",batchSubscribe:"irn_batchSubscribe",subscription:"irn_subscription",unsubscribe:"irn_unsubscribe",batchUnsubscribe:"irn_batchUnsubscribe",batchFetchMessages:"irn_batchFetchMessages"},iridium:{publish:"iridium_publish",batchPublish:"iridium_batchPublish",subscribe:"iridium_subscribe",batchSubscribe:"iridium_batchSubscribe",subscription:"iridium_subscription",unsubscribe:"iridium_unsubscribe",batchUnsubscribe:"iridium_batchUnsubscribe",batchFetchMessages:"iridium_batchFetchMessages"}};var _w={};const Iw=":";function Hr(t){const[e,r]=t.split(Iw);return{namespace:e,reference:r}}function Ga(t,e=[]){const r=[];return Object.keys(t).forEach(i=>{if(e.length&&!e.includes(i))return;const s=t[i];r.push(...s.accounts)}),r}function eu(t,e){return t.includes(":")?[t]:e.chains||[]}var $w=Object.defineProperty,Sw=Object.defineProperties,Ow=Object.getOwnPropertyDescriptors,Ya=Object.getOwnPropertySymbols,Pw=Object.prototype.hasOwnProperty,xw=Object.prototype.propertyIsEnumerable,Ja=(t,e,r)=>e in t?$w(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Za=(t,e)=>{for(var r in e||(e={}))Pw.call(e,r)&&Ja(t,r,e[r]);if(Ya)for(var r of Ya(e))xw.call(e,r)&&Ja(t,r,e[r]);return t},Aw=(t,e)=>Sw(t,Ow(e));const Tw="ReactNative",We={reactNative:"react-native",node:"node",browser:"browser",unknown:"unknown"},Cw="js";function bs(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}function Wt(){return!ar()&&!!Po()&&navigator.product===Tw}function Rw(){return Wt()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="android"}function Nw(){return Wt()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="ios"}function ri(){return!bs()&&!!Po()&&!!ar()}function Ui(){return Wt()?We.reactNative:bs()?We.node:ri()?We.browser:We.unknown}function Qa(){var t;try{return Wt()&&typeof global<"u"&&typeof(global==null?void 0:global.Application)<"u"?(t=global.Application)==null?void 0:t.applicationId:void 0}catch{return}}function jw(t,e){const r=new URLSearchParams(t);for(const i of Object.keys(e).sort())if(e.hasOwnProperty(i)){const s=e[i];s!==void 0&&r.set(i,s)}return r.toString()}function Bw(t){var e,r;const i=tu();try{return t!=null&&t.url&&i.url&&new URL(t.url).host!==new URL(i.url).host&&(console.warn(`The configured WalletConnect 'metadata.url':${t.url} differs from the actual page url:${i.url}. This is probably unintended and can lead to issues.`),t.url=i.url),(e=t==null?void 0:t.icons)!=null&&e.length&&t.icons.length>0&&(t.icons=t.icons.filter(s=>s!=="")),Aw(Za(Za({},i),t),{url:(t==null?void 0:t.url)||i.url,name:(t==null?void 0:t.name)||i.name,description:(t==null?void 0:t.description)||i.description,icons:(r=t==null?void 0:t.icons)!=null&&r.length&&t.icons.length>0?t.icons:i.icons})}catch(s){return console.warn("Error populating app metadata",s),t||i}}function tu(){return ah()||{name:"",description:"",url:"",icons:[""]}}function Dw(){if(Ui()===We.reactNative&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"){const{OS:r,Version:i}=global.Platform;return[r,i].join("-")}const t=Zd();if(t===null)return"unknown";const e=t.os?t.os.replace(" ","").toLowerCase():"unknown";return t.type==="browser"?[e,t.name,t.version].join("-"):[e,t.version].join("-")}function Uw(){var t;const e=Ui();return e===We.browser?[e,((t=oh())==null?void 0:t.host)||"unknown"].join(":"):e}function ru(t,e,r){const i=Dw(),s=Uw();return[[t,e].join("-"),[Cw,r].join("-"),i,s].join("/")}function Lw({protocol:t,version:e,relayUrl:r,sdkVersion:i,auth:s,projectId:n,useOnCloseEvent:o,bundleId:a,packageName:c}){const l=r.split("?"),h=ru(t,e,i),u={auth:s,ua:h,projectId:n,useOnCloseEvent:o,packageName:c||void 0,bundleId:a||void 0},d=jw(l[1]||"",u);return l[0]+"?"+d}function sr(t,e){return t.filter(r=>e.includes(r)).length===t.length}function Vn(t){return Object.fromEntries(t.entries())}function Kn(t){return new Map(Object.entries(t))}function Qt(t=j.FIVE_MINUTES,e){const r=j.toMiliseconds(t||j.FIVE_MINUTES);let i,s,n,o;return{resolve:a=>{n&&i&&(clearTimeout(n),i(a),o=Promise.resolve(a))},reject:a=>{n&&s&&(clearTimeout(n),s(a))},done:()=>new Promise((a,c)=>{if(o)return a(o);n=setTimeout(()=>{const l=new Error(e);o=Promise.reject(l),c(l)},r),i=a,s=c})}}function zt(t,e,r){return new Promise(async(i,s)=>{const n=setTimeout(()=>s(new Error(r)),e);try{const o=await t;i(o)}catch(o){s(o)}clearTimeout(n)})}function iu(t,e){if(typeof e=="string"&&e.startsWith(`${t}:`))return e;if(t.toLowerCase()==="topic"){if(typeof e!="string")throw new Error('Value must be "string" for expirer target type: topic');return`topic:${e}`}else if(t.toLowerCase()==="id"){if(typeof e!="number")throw new Error('Value must be "number" for expirer target type: id');return`id:${e}`}throw new Error(`Unknown expirer target type: ${t}`)}function kw(t){return iu("topic",t)}function qw(t){return iu("id",t)}function su(t){const[e,r]=t.split(":"),i={id:void 0,topic:void 0};if(e==="topic"&&typeof r=="string")i.topic=r;else if(e==="id"&&Number.isInteger(Number(r)))i.id=Number(r);else throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${r}`);return i}function de(t,e){return j.fromMiliseconds(Date.now()+j.toMiliseconds(t))}function Mt(t){return Date.now()>=j.toMiliseconds(t)}function X(t,e){return`${t}${e?`:${e}`:""}`}function _t(t=[],e=[]){return[...new Set([...t,...e])]}async function Mw({id:t,topic:e,wcDeepLink:r}){var i;try{if(!r)return;const s=typeof r=="string"?JSON.parse(r):r,n=s==null?void 0:s.href;if(typeof n!="string")return;const o=Fw(n,t,e),a=Ui();if(a===We.browser){if(!((i=ar())!=null&&i.hasFocus())){console.warn("Document does not have focus, skipping deeplink.");return}zw(o)}else a===We.reactNative&&typeof(global==null?void 0:global.Linking)<"u"&&await global.Linking.openURL(o)}catch(s){console.error(s)}}function Fw(t,e,r){const i=`requestId=${e}&sessionTopic=${r}`;t.endsWith("/")&&(t=t.slice(0,-1));let s=`${t}`;if(t.startsWith("https://t.me")){const n=t.includes("?")?"&startapp=":"?startapp=";s=`${s}${n}${Ww(i,!0)}`}else s=`${s}/wc?${i}`;return s}function zw(t){let e="_self";Kw()?e="_top":(Vw()||t.startsWith("https://")||t.startsWith("http://"))&&(e="_blank"),window.open(t,e,"noreferrer noopener")}async function Hw(t,e){let r="";try{if(ri()&&(r=localStorage.getItem(e),r))return r;r=await t.getItem(e)}catch(i){console.error(i)}return r}function Xa(t,e){if(!t.includes(e))return null;const r=t.split(/([&,?,=])/),i=r.indexOf(e);return r[i+2]}function ec(){return typeof crypto<"u"&&crypto!=null&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)})}function Uo(){return typeof process<"u"&&_w.IS_VITEST==="true"}function Vw(){return typeof window<"u"&&(!!window.TelegramWebviewProxy||!!window.Telegram||!!window.TelegramWebviewProxyProto)}function Kw(){try{return window.self!==window.top}catch{return!1}}function Ww(t,e=!1){const r=Buffer.from(t).toString("base64");return e?r.replace(/[=]/g,""):r}function nu(t){return Buffer.from(t,"base64").toString("utf-8")}function Gw(t){return new Promise(e=>setTimeout(e,t))}function Pi(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function Yw(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Li(t,...e){if(!Yw(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function Lo(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Pi(t.outputLen),Pi(t.blockLen)}function Vr(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function ou(t,e){Li(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Ji=BigInt(2**32-1),tc=BigInt(32);function Jw(t,e=!1){return e?{h:Number(t&Ji),l:Number(t>>tc&Ji)}:{h:Number(t>>tc&Ji)|0,l:Number(t&Ji)|0}}function Zw(t,e=!1){let r=new Uint32Array(t.length),i=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:n,l:o}=Jw(t[s],e);[r[s],i[s]]=[n,o]}return[r,i]}const Qw=(t,e,r)=>t<<r|e>>>32-r,Xw=(t,e,r)=>e<<r|t>>>32-r,eb=(t,e,r)=>e<<r-32|t>>>64-r,tb=(t,e,r)=>t<<r-32|e>>>64-r,_r=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function rb(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function hn(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function ft(t,e){return t<<32-e|t>>>e}const rc=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function ib(t){return t<<24&**********|t<<8&16711680|t>>>8&65280|t>>>24&255}function ic(t){for(let e=0;e<t.length;e++)t[e]=ib(t[e])}function sb(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function Kr(t){return typeof t=="string"&&(t=sb(t)),Li(t),t}function nb(...t){let e=0;for(let i=0;i<t.length;i++){const s=t[i];Li(s),e+=s.length}const r=new Uint8Array(e);for(let i=0,s=0;i<t.length;i++){const n=t[i];r.set(n,s),s+=n.length}return r}let ko=class{clone(){return this._cloneInto()}};function au(t){const e=i=>t().update(Kr(i)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function ii(t=32){if(_r&&typeof _r.getRandomValues=="function")return _r.getRandomValues(new Uint8Array(t));if(_r&&typeof _r.randomBytes=="function")return _r.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const cu=[],lu=[],hu=[],ob=BigInt(0),ui=BigInt(1),ab=BigInt(2),cb=BigInt(7),lb=BigInt(256),hb=BigInt(113);for(let t=0,e=ui,r=1,i=0;t<24;t++){[r,i]=[i,(2*r+3*i)%5],cu.push(2*(5*i+r)),lu.push((t+1)*(t+2)/2%64);let s=ob;for(let n=0;n<7;n++)e=(e<<ui^(e>>cb)*hb)%lb,e&ab&&(s^=ui<<(ui<<BigInt(n))-ui);hu.push(s)}const[ub,db]=Zw(hu,!0),sc=(t,e,r)=>r>32?eb(t,e,r):Qw(t,e,r),nc=(t,e,r)=>r>32?tb(t,e,r):Xw(t,e,r);function pb(t,e=24){const r=new Uint32Array(10);for(let i=24-e;i<24;i++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,l=r[c],h=r[c+1],u=sc(l,h,1)^r[a],d=nc(l,h,1)^r[a+1];for(let f=0;f<50;f+=10)t[o+f]^=u,t[o+f+1]^=d}let s=t[2],n=t[3];for(let o=0;o<24;o++){const a=lu[o],c=sc(s,n,a),l=nc(s,n,a),h=cu[o];s=t[h],n=t[h+1],t[h]=c,t[h+1]=l}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=ub[i],t[1]^=db[i]}r.fill(0)}let fb=class uu extends ko{constructor(e,r,i,s=!1,n=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=i,this.enableXOF=s,this.rounds=n,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Pi(i),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=rb(this.state)}keccak(){rc||ic(this.state32),pb(this.state32,this.rounds),rc||ic(this.state32),this.posOut=0,this.pos=0}update(e){Vr(this);const{blockLen:r,state:i}=this;e=Kr(e);const s=e.length;for(let n=0;n<s;){const o=Math.min(r-this.pos,s-n);for(let a=0;a<o;a++)i[this.pos++]^=e[n++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:i,blockLen:s}=this;e[i]^=r,r&128&&i===s-1&&this.keccak(),e[s-1]^=128,this.keccak()}writeInto(e){Vr(this,!1),Li(e),this.finish();const r=this.state,{blockLen:i}=this;for(let s=0,n=e.length;s<n;){this.posOut>=i&&this.keccak();const o=Math.min(i-this.posOut,n-s);e.set(r.subarray(this.posOut,this.posOut+o),s),this.posOut+=o,s+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return Pi(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(ou(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:i,outputLen:s,rounds:n,enableXOF:o}=this;return e||(e=new uu(r,i,s,o,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=i,e.outputLen=s,e.enableXOF=o,e.destroyed=this.destroyed,e}};const gb=(t,e,r)=>au(()=>new fb(e,t,r)),yb=gb(1,136,256/8),mb="https://rpc.walletconnect.org/v1";function du(t){const e=`Ethereum Signed Message:
${t.length}`,r=new TextEncoder().encode(e+t);return"0x"+Buffer.from(yb(r)).toString("hex")}async function wb(t,e,r,i,s,n){switch(r.t){case"eip191":return await bb(t,e,r.s);case"eip1271":return await vb(t,e,r.s,i,s,n);default:throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${r.t}`)}}async function bb(t,e,r){return(await Af({hash:du(e),signature:r})).toLowerCase()===t.toLowerCase()}async function vb(t,e,r,i,s,n){const o=Hr(i);if(!o.namespace||!o.reference)throw new Error(`isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${i}`);try{const a="0x1626ba7e",c="0000000000000000000000000000000000000000000000000000000000000040",l="0000000000000000000000000000000000000000000000000000000000000041",h=r.substring(2),u=du(e).substring(2),d=a+u+c+l+h,f=await fetch(`${n||mb}/?chainId=${i}&projectId=${s}`,{method:"POST",body:JSON.stringify({id:Eb(),jsonrpc:"2.0",method:"eth_call",params:[{to:t,data:d},"latest"]})}),{result:p}=await f.json();return p?p.slice(0,a.length).toLowerCase()===a.toLowerCase():!1}catch(a){return console.error("isValidEip1271Signature: ",a),!1}}function Eb(){return Date.now()+Math.floor(Math.random()*1e3)}function _b(t){const e=atob(t),r=new Uint8Array(e.length);for(let o=0;o<e.length;o++)r[o]=e.charCodeAt(o);const i=r[0];if(i===0)throw new Error("No signatures found");const s=1+i*64;if(r.length<s)throw new Error("Transaction data too short for claimed signature count");if(r.length<100)throw new Error("Transaction too short");const n=Buffer.from(t,"base64").slice(1,65);return Rf.encode(n)}var Ib=Object.defineProperty,$b=Object.defineProperties,Sb=Object.getOwnPropertyDescriptors,oc=Object.getOwnPropertySymbols,Ob=Object.prototype.hasOwnProperty,Pb=Object.prototype.propertyIsEnumerable,ac=(t,e,r)=>e in t?Ib(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,xb=(t,e)=>{for(var r in e||(e={}))Ob.call(e,r)&&ac(t,r,e[r]);if(oc)for(var r of oc(e))Pb.call(e,r)&&ac(t,r,e[r]);return t},Ab=(t,e)=>$b(t,Sb(e));const Tb="did:pkh:",qo=t=>t==null?void 0:t.split(":"),Cb=t=>{const e=t&&qo(t);if(e)return t.includes(Tb)?e[3]:e[1]},Wn=t=>{const e=t&&qo(t);if(e)return e[2]+":"+e[3]},vs=t=>{const e=t&&qo(t);if(e)return e.pop()};async function cc(t){const{cacao:e,projectId:r}=t,{s:i,p:s}=e,n=pu(s,s.iss),o=vs(s.iss);return await wb(o,n,i,Wn(s.iss),r)}const pu=(t,e)=>{const r=`${t.domain} wants you to sign in with your Ethereum account:`,i=vs(e);if(!t.aud&&!t.uri)throw new Error("Either `aud` or `uri` is required to construct the message");let s=t.statement||void 0;const n=`URI: ${t.aud||t.uri}`,o=`Version: ${t.version}`,a=`Chain ID: ${Cb(e)}`,c=`Nonce: ${t.nonce}`,l=`Issued At: ${t.iat}`,h=t.exp?`Expiration Time: ${t.exp}`:void 0,u=t.nbf?`Not Before: ${t.nbf}`:void 0,d=t.requestId?`Request ID: ${t.requestId}`:void 0,f=t.resources?`Resources:${t.resources.map(y=>`
- ${y}`).join("")}`:void 0,p=hs(t.resources);if(p){const y=xi(p);s=qb(s,y)}return[r,i,"",s,"",n,o,a,c,l,h,u,d,f].filter(y=>y!=null).join(`
`)};function Rb(t){return Buffer.from(JSON.stringify(t)).toString("base64")}function Nb(t){return JSON.parse(Buffer.from(t,"base64").toString("utf-8"))}function hr(t){if(!t)throw new Error("No recap provided, value is undefined");if(!t.att)throw new Error("No `att` property found");const e=Object.keys(t.att);if(!(e!=null&&e.length))throw new Error("No resources found in `att` property");e.forEach(r=>{const i=t.att[r];if(Array.isArray(i))throw new Error(`Resource must be an object: ${r}`);if(typeof i!="object")throw new Error(`Resource must be an object: ${r}`);if(!Object.keys(i).length)throw new Error(`Resource object is empty: ${r}`);Object.keys(i).forEach(s=>{const n=i[s];if(!Array.isArray(n))throw new Error(`Ability limits ${s} must be an array of objects, found: ${n}`);if(!n.length)throw new Error(`Value of ${s} is empty array, must be an array with objects`);n.forEach(o=>{if(typeof o!="object")throw new Error(`Ability limits (${s}) must be an array of objects, found: ${o}`)})})})}function jb(t,e,r,i={}){return r==null||r.sort((s,n)=>s.localeCompare(n)),{att:{[t]:Bb(e,r,i)}}}function Bb(t,e,r={}){e=e==null?void 0:e.sort((s,n)=>s.localeCompare(n));const i=e.map(s=>({[`${t}/${s}`]:[r]}));return Object.assign({},...i)}function fu(t){return hr(t),`urn:recap:${Rb(t).replace(/=/g,"")}`}function xi(t){const e=Nb(t.replace("urn:recap:",""));return hr(e),e}function Db(t,e,r){const i=jb(t,e,r);return fu(i)}function Ub(t){return t&&t.includes("urn:recap:")}function Lb(t,e){const r=xi(t),i=xi(e),s=kb(r,i);return fu(s)}function kb(t,e){hr(t),hr(e);const r=Object.keys(t.att).concat(Object.keys(e.att)).sort((s,n)=>s.localeCompare(n)),i={att:{}};return r.forEach(s=>{var n,o;Object.keys(((n=t.att)==null?void 0:n[s])||{}).concat(Object.keys(((o=e.att)==null?void 0:o[s])||{})).sort((a,c)=>a.localeCompare(c)).forEach(a=>{var c,l;i.att[s]=Ab(xb({},i.att[s]),{[a]:((c=t.att[s])==null?void 0:c[a])||((l=e.att[s])==null?void 0:l[a])})})}),i}function qb(t="",e){hr(e);const r="I further authorize the stated URI to perform the following actions on my behalf: ";if(t.includes(r))return t;const i=[];let s=0;Object.keys(e.att).forEach(a=>{const c=Object.keys(e.att[a]).map(u=>({ability:u.split("/")[0],action:u.split("/")[1]}));c.sort((u,d)=>u.action.localeCompare(d.action));const l={};c.forEach(u=>{l[u.ability]||(l[u.ability]=[]),l[u.ability].push(u.action)});const h=Object.keys(l).map(u=>(s++,`(${s}) '${u}': '${l[u].join("', '")}' for '${a}'.`));i.push(h.join(", ").replace(".,","."))});const n=i.join(" "),o=`${r}${n}`;return`${t?t+" ":""}${o}`}function lc(t){var e;const r=xi(t);hr(r);const i=(e=r.att)==null?void 0:e.eip155;return i?Object.keys(i).map(s=>s.split("/")[1]):[]}function hc(t){const e=xi(t);hr(e);const r=[];return Object.values(e.att).forEach(i=>{Object.values(i).forEach(s=>{var n;(n=s==null?void 0:s[0])!=null&&n.chains&&r.push(s[0].chains)})}),[...new Set(r.flat())]}function hs(t){if(!t)return;const e=t==null?void 0:t[t.length-1];return Ub(e)?e:void 0}function un(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function gu(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Ke(t,...e){if(!gu(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function uc(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function Mb(t,e){Ke(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function dc(t){if(typeof t!="boolean")throw new Error(`boolean expected, not ${t}`)}const Vt=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4)),Fb=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),zb=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!zb)throw new Error("Non little-endian hardware is not supported");function Hb(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}function Gn(t){if(typeof t=="string")t=Hb(t);else if(gu(t))t=Yn(t);else throw new Error("Uint8Array expected, got "+typeof t);return t}function Vb(t,e){if(e==null||typeof e!="object")throw new Error("options must be defined");return Object.assign(t,e)}function Kb(t,e){if(t.length!==e.length)return!1;let r=0;for(let i=0;i<t.length;i++)r|=t[i]^e[i];return r===0}const Wb=(t,e)=>{function r(i,...s){if(Ke(i),t.nonceLength!==void 0){const l=s[0];if(!l)throw new Error("nonce / iv required");t.varSizeNonce?Ke(l):Ke(l,t.nonceLength)}const n=t.tagLength;n&&s[1]!==void 0&&Ke(s[1]);const o=e(i,...s),a=(l,h)=>{if(h!==void 0){if(l!==2)throw new Error("cipher output not supported");Ke(h)}};let c=!1;return{encrypt(l,h){if(c)throw new Error("cannot encrypt() twice with same key + nonce");return c=!0,Ke(l),a(o.encrypt.length,h),o.encrypt(l,h)},decrypt(l,h){if(Ke(l),n&&l.length<n)throw new Error("invalid ciphertext length: smaller than tagLength="+n);return a(o.decrypt.length,h),o.decrypt(l,h)}}}return Object.assign(r,t),r};function pc(t,e,r=!0){if(e===void 0)return new Uint8Array(t);if(e.length!==t)throw new Error("invalid output length, expected "+t+", got: "+e.length);if(r&&!Gb(e))throw new Error("invalid output, must be aligned");return e}function fc(t,e,r,i){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,i);const s=BigInt(32),n=BigInt(**********),o=Number(r>>s&n),a=Number(r&n);t.setUint32(e+4,o,i),t.setUint32(e+0,a,i)}function Gb(t){return t.byteOffset%4===0}function Yn(t){return Uint8Array.from(t)}function Wr(...t){for(let e=0;e<t.length;e++)t[e].fill(0)}const yu=t=>Uint8Array.from(t.split("").map(e=>e.charCodeAt(0))),Yb=yu("expand 16-byte k"),Jb=yu("expand 32-byte k"),Zb=Vt(Yb),Qb=Vt(Jb);function J(t,e){return t<<e|t>>>32-e}function Jn(t){return t.byteOffset%4===0}const Zi=64,Xb=16,mu=2**32-1,gc=new Uint32Array;function ev(t,e,r,i,s,n,o,a){const c=s.length,l=new Uint8Array(Zi),h=Vt(l),u=Jn(s)&&Jn(n),d=u?Vt(s):gc,f=u?Vt(n):gc;for(let p=0;p<c;o++){if(t(e,r,i,h,o,a),o>=mu)throw new Error("arx: counter overflow");const y=Math.min(Zi,c-p);if(u&&y===Zi){const w=p/4;if(p%4!==0)throw new Error("arx: invalid block position");for(let v=0,b;v<Xb;v++)b=w+v,f[b]=d[b]^h[v];p+=Zi;continue}for(let w=0,v;w<y;w++)v=p+w,n[v]=s[v]^l[w];p+=y}}function tv(t,e){const{allowShortKeys:r,extendNonceFn:i,counterLength:s,counterRight:n,rounds:o}=Vb({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},e);if(typeof t!="function")throw new Error("core must be a function");return un(s),un(o),dc(n),dc(r),(a,c,l,h,u=0)=>{Ke(a),Ke(c),Ke(l);const d=l.length;if(h===void 0&&(h=new Uint8Array(d)),Ke(h),un(u),u<0||u>=mu)throw new Error("arx: counter overflow");if(h.length<d)throw new Error(`arx: output (${h.length}) is shorter than data (${d})`);const f=[];let p=a.length,y,w;if(p===32)f.push(y=Yn(a)),w=Qb;else if(p===16&&r)y=new Uint8Array(32),y.set(a),y.set(a,16),w=Zb,f.push(y);else throw new Error(`arx: invalid 32-byte key, got length=${p}`);Jn(c)||f.push(c=Yn(c));const v=Vt(y);if(i){if(c.length!==24)throw new Error("arx: extended nonce must be 24 bytes");i(w,v,Vt(c.subarray(0,16)),v),c=c.subarray(16)}const b=16-s;if(b!==c.length)throw new Error(`arx: nonce must be ${b} or 16 bytes`);if(b!==12){const I=new Uint8Array(12);I.set(c,n?0:12-c.length),c=I,f.push(c)}const E=Vt(c);return ev(t,w,v,E,l,h,u,o),Wr(...f),h}}const _e=(t,e)=>t[e++]&255|(t[e++]&255)<<8;class rv{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,e=Gn(e),Ke(e,32);const r=_e(e,0),i=_e(e,2),s=_e(e,4),n=_e(e,6),o=_e(e,8),a=_e(e,10),c=_e(e,12),l=_e(e,14);this.r[0]=r&8191,this.r[1]=(r>>>13|i<<3)&8191,this.r[2]=(i>>>10|s<<6)&7939,this.r[3]=(s>>>7|n<<9)&8191,this.r[4]=(n>>>4|o<<12)&255,this.r[5]=o>>>1&8190,this.r[6]=(o>>>14|a<<2)&8191,this.r[7]=(a>>>11|c<<5)&8065,this.r[8]=(c>>>8|l<<8)&8191,this.r[9]=l>>>5&127;for(let h=0;h<8;h++)this.pad[h]=_e(e,16+2*h)}process(e,r,i=!1){const s=i?0:2048,{h:n,r:o}=this,a=o[0],c=o[1],l=o[2],h=o[3],u=o[4],d=o[5],f=o[6],p=o[7],y=o[8],w=o[9],v=_e(e,r+0),b=_e(e,r+2),E=_e(e,r+4),I=_e(e,r+6),x=_e(e,r+8),A=_e(e,r+10),P=_e(e,r+12),C=_e(e,r+14);let S=n[0]+(v&8191),M=n[1]+((v>>>13|b<<3)&8191),U=n[2]+((b>>>10|E<<6)&8191),D=n[3]+((E>>>7|I<<9)&8191),F=n[4]+((I>>>4|x<<12)&8191),R=n[5]+(x>>>1&8191),g=n[6]+((x>>>14|A<<2)&8191),m=n[7]+((A>>>11|P<<5)&8191),_=n[8]+((P>>>8|C<<8)&8191),O=n[9]+(C>>>5|s),$=0,T=$+S*a+M*(5*w)+U*(5*y)+D*(5*p)+F*(5*f);$=T>>>13,T&=8191,T+=R*(5*d)+g*(5*u)+m*(5*h)+_*(5*l)+O*(5*c),$+=T>>>13,T&=8191;let L=$+S*c+M*a+U*(5*w)+D*(5*y)+F*(5*p);$=L>>>13,L&=8191,L+=R*(5*f)+g*(5*d)+m*(5*u)+_*(5*h)+O*(5*l),$+=L>>>13,L&=8191;let q=$+S*l+M*c+U*a+D*(5*w)+F*(5*y);$=q>>>13,q&=8191,q+=R*(5*p)+g*(5*f)+m*(5*d)+_*(5*u)+O*(5*h),$+=q>>>13,q&=8191;let z=$+S*h+M*l+U*c+D*a+F*(5*w);$=z>>>13,z&=8191,z+=R*(5*y)+g*(5*p)+m*(5*f)+_*(5*d)+O*(5*u),$+=z>>>13,z&=8191;let k=$+S*u+M*h+U*l+D*c+F*a;$=k>>>13,k&=8191,k+=R*(5*w)+g*(5*y)+m*(5*p)+_*(5*f)+O*(5*d),$+=k>>>13,k&=8191;let H=$+S*d+M*u+U*h+D*l+F*c;$=H>>>13,H&=8191,H+=R*a+g*(5*w)+m*(5*y)+_*(5*p)+O*(5*f),$+=H>>>13,H&=8191;let G=$+S*f+M*d+U*u+D*h+F*l;$=G>>>13,G&=8191,G+=R*c+g*a+m*(5*w)+_*(5*y)+O*(5*p),$+=G>>>13,G&=8191;let se=$+S*p+M*f+U*d+D*u+F*h;$=se>>>13,se&=8191,se+=R*l+g*c+m*a+_*(5*w)+O*(5*y),$+=se>>>13,se&=8191;let ee=$+S*y+M*p+U*f+D*d+F*u;$=ee>>>13,ee&=8191,ee+=R*h+g*l+m*c+_*a+O*(5*w),$+=ee>>>13,ee&=8191;let Y=$+S*w+M*y+U*p+D*f+F*d;$=Y>>>13,Y&=8191,Y+=R*u+g*h+m*l+_*c+O*a,$+=Y>>>13,Y&=8191,$=($<<2)+$|0,$=$+T|0,T=$&8191,$=$>>>13,L+=$,n[0]=T,n[1]=L,n[2]=q,n[3]=z,n[4]=k,n[5]=H,n[6]=G,n[7]=se,n[8]=ee,n[9]=Y}finalize(){const{h:e,pad:r}=this,i=new Uint16Array(10);let s=e[1]>>>13;e[1]&=8191;for(let a=2;a<10;a++)e[a]+=s,s=e[a]>>>13,e[a]&=8191;e[0]+=s*5,s=e[0]>>>13,e[0]&=8191,e[1]+=s,s=e[1]>>>13,e[1]&=8191,e[2]+=s,i[0]=e[0]+5,s=i[0]>>>13,i[0]&=8191;for(let a=1;a<10;a++)i[a]=e[a]+s,s=i[a]>>>13,i[a]&=8191;i[9]-=8192;let n=(s^1)-1;for(let a=0;a<10;a++)i[a]&=n;n=~n;for(let a=0;a<10;a++)e[a]=e[a]&n|i[a];e[0]=(e[0]|e[1]<<13)&65535,e[1]=(e[1]>>>3|e[2]<<10)&65535,e[2]=(e[2]>>>6|e[3]<<7)&65535,e[3]=(e[3]>>>9|e[4]<<4)&65535,e[4]=(e[4]>>>12|e[5]<<1|e[6]<<14)&65535,e[5]=(e[6]>>>2|e[7]<<11)&65535,e[6]=(e[7]>>>5|e[8]<<8)&65535,e[7]=(e[8]>>>8|e[9]<<5)&65535;let o=e[0]+r[0];e[0]=o&65535;for(let a=1;a<8;a++)o=(e[a]+r[a]|0)+(o>>>16)|0,e[a]=o&65535;Wr(i)}update(e){uc(this);const{buffer:r,blockLen:i}=this;e=Gn(e);const s=e.length;for(let n=0;n<s;){const o=Math.min(i-this.pos,s-n);if(o===i){for(;i<=s-n;n+=i)this.process(e,n);continue}r.set(e.subarray(n,n+o),this.pos),this.pos+=o,n+=o,this.pos===i&&(this.process(r,0,!1),this.pos=0)}return this}destroy(){Wr(this.h,this.r,this.buffer,this.pad)}digestInto(e){uc(this),Mb(e,this),this.finished=!0;const{buffer:r,h:i}=this;let{pos:s}=this;if(s){for(r[s++]=1;s<16;s++)r[s]=0;this.process(r,0,!0)}this.finalize();let n=0;for(let o=0;o<8;o++)e[n++]=i[o]>>>0,e[n++]=i[o]>>>8;return e}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const i=e.slice(0,r);return this.destroy(),i}}function iv(t){const e=(i,s)=>t(s).update(Gn(i)).digest(),r=t(new Uint8Array(32));return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=i=>t(i),e}const sv=iv(t=>new rv(t));function nv(t,e,r,i,s,n=20){let o=t[0],a=t[1],c=t[2],l=t[3],h=e[0],u=e[1],d=e[2],f=e[3],p=e[4],y=e[5],w=e[6],v=e[7],b=s,E=r[0],I=r[1],x=r[2],A=o,P=a,C=c,S=l,M=h,U=u,D=d,F=f,R=p,g=y,m=w,_=v,O=b,$=E,T=I,L=x;for(let z=0;z<n;z+=2)A=A+M|0,O=J(O^A,16),R=R+O|0,M=J(M^R,12),A=A+M|0,O=J(O^A,8),R=R+O|0,M=J(M^R,7),P=P+U|0,$=J($^P,16),g=g+$|0,U=J(U^g,12),P=P+U|0,$=J($^P,8),g=g+$|0,U=J(U^g,7),C=C+D|0,T=J(T^C,16),m=m+T|0,D=J(D^m,12),C=C+D|0,T=J(T^C,8),m=m+T|0,D=J(D^m,7),S=S+F|0,L=J(L^S,16),_=_+L|0,F=J(F^_,12),S=S+F|0,L=J(L^S,8),_=_+L|0,F=J(F^_,7),A=A+U|0,L=J(L^A,16),m=m+L|0,U=J(U^m,12),A=A+U|0,L=J(L^A,8),m=m+L|0,U=J(U^m,7),P=P+D|0,O=J(O^P,16),_=_+O|0,D=J(D^_,12),P=P+D|0,O=J(O^P,8),_=_+O|0,D=J(D^_,7),C=C+F|0,$=J($^C,16),R=R+$|0,F=J(F^R,12),C=C+F|0,$=J($^C,8),R=R+$|0,F=J(F^R,7),S=S+M|0,T=J(T^S,16),g=g+T|0,M=J(M^g,12),S=S+M|0,T=J(T^S,8),g=g+T|0,M=J(M^g,7);let q=0;i[q++]=o+A|0,i[q++]=a+P|0,i[q++]=c+C|0,i[q++]=l+S|0,i[q++]=h+M|0,i[q++]=u+U|0,i[q++]=d+D|0,i[q++]=f+F|0,i[q++]=p+R|0,i[q++]=y+g|0,i[q++]=w+m|0,i[q++]=v+_|0,i[q++]=b+O|0,i[q++]=E+$|0,i[q++]=I+T|0,i[q++]=x+L|0}const ov=tv(nv,{counterRight:!1,counterLength:4,allowShortKeys:!1}),av=new Uint8Array(16),yc=(t,e)=>{t.update(e);const r=e.length%16;r&&t.update(av.subarray(r))},cv=new Uint8Array(32);function mc(t,e,r,i,s){const n=t(e,r,cv),o=sv.create(n);s&&yc(o,s),yc(o,i);const a=new Uint8Array(16),c=Fb(a);fc(c,0,BigInt(s?s.length:0),!0),fc(c,8,BigInt(i.length),!0),o.update(a);const l=o.digest();return Wr(n,a),l}const lv=t=>(e,r,i)=>({encrypt(s,n){const o=s.length;n=pc(o+16,n,!1),n.set(s);const a=n.subarray(0,-16);t(e,r,a,a,1);const c=mc(t,e,r,a,i);return n.set(c,o),Wr(c),n},decrypt(s,n){n=pc(s.length-16,n,!1);const o=s.subarray(0,-16),a=s.subarray(-16),c=mc(t,e,r,o,i);if(!Kb(a,c))throw new Error("invalid tag");return n.set(s.subarray(0,-16)),t(e,r,n,n,1),Wr(c),n}}),wu=Wb({blockSize:64,nonceLength:12,tagLength:16},lv(ov));let bu=class extends ko{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,Lo(e);const i=Kr(r);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,n=new Uint8Array(s);n.set(i.length>s?e.create().update(i).digest():i);for(let o=0;o<n.length;o++)n[o]^=54;this.iHash.update(n),this.oHash=e.create();for(let o=0;o<n.length;o++)n[o]^=106;this.oHash.update(n),n.fill(0)}update(e){return Vr(this),this.iHash.update(e),this}digestInto(e){Vr(this),Li(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:r,iHash:i,finished:s,destroyed:n,blockLen:o,outputLen:a}=this;return e=e,e.finished=s,e.destroyed=n,e.blockLen=o,e.outputLen=a,e.oHash=r._cloneInto(e.oHash),e.iHash=i._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}};const js=(t,e,r)=>new bu(t,e).update(r).digest();js.create=(t,e)=>new bu(t,e);function hv(t,e,r){return Lo(t),r===void 0&&(r=new Uint8Array(t.outputLen)),js(t,Kr(r),Kr(e))}const dn=new Uint8Array([0]),wc=new Uint8Array;function uv(t,e,r,i=32){if(Lo(t),Pi(i),i>255*t.outputLen)throw new Error("Length should be <= 255*HashLen");const s=Math.ceil(i/t.outputLen);r===void 0&&(r=wc);const n=new Uint8Array(s*t.outputLen),o=js.create(t,e),a=o._cloneInto(),c=new Uint8Array(o.outputLen);for(let l=0;l<s;l++)dn[0]=l+1,a.update(l===0?wc:c).update(r).update(dn).digestInto(c),n.set(c,t.outputLen*l),o._cloneInto(a);return o.destroy(),a.destroy(),c.fill(0),dn.fill(0),n.slice(0,i)}const dv=(t,e,r,i,s)=>uv(t,hv(t,e,r),i,s);function pv(t,e,r,i){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,i);const s=BigInt(32),n=BigInt(**********),o=Number(r>>s&n),a=Number(r&n),c=i?4:0,l=i?0:4;t.setUint32(e+c,o,i),t.setUint32(e+l,a,i)}function fv(t,e,r){return t&e^~t&r}function gv(t,e,r){return t&e^t&r^e&r}let yv=class extends ko{constructor(e,r,i,s){super(),this.blockLen=e,this.outputLen=r,this.padOffset=i,this.isLE=s,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=hn(this.buffer)}update(e){Vr(this);const{view:r,buffer:i,blockLen:s}=this;e=Kr(e);const n=e.length;for(let o=0;o<n;){const a=Math.min(s-this.pos,n-o);if(a===s){const c=hn(e);for(;s<=n-o;o+=s)this.process(c,o);continue}i.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===s&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Vr(this),ou(e,this),this.finished=!0;const{buffer:r,view:i,blockLen:s,isLE:n}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>s-o&&(this.process(i,0),o=0);for(let u=o;u<s;u++)r[u]=0;pv(i,s-8,BigInt(this.length*8),n),this.process(i,0);const a=hn(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const l=c/4,h=this.get();if(l>h.length)throw new Error("_sha2: outputLen bigger than state");for(let u=0;u<l;u++)a.setUint32(4*u,h[u],n)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const i=e.slice(0,r);return this.destroy(),i}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:i,length:s,finished:n,destroyed:o,pos:a}=this;return e.length=s,e.pos=a,e.finished=n,e.destroyed=o,s%r&&e.buffer.set(i),e}};const mv=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),kt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),qt=new Uint32Array(64);class wv extends yv{constructor(){super(64,32,8,!1),this.A=kt[0]|0,this.B=kt[1]|0,this.C=kt[2]|0,this.D=kt[3]|0,this.E=kt[4]|0,this.F=kt[5]|0,this.G=kt[6]|0,this.H=kt[7]|0}get(){const{A:e,B:r,C:i,D:s,E:n,F:o,G:a,H:c}=this;return[e,r,i,s,n,o,a,c]}set(e,r,i,s,n,o,a,c){this.A=e|0,this.B=r|0,this.C=i|0,this.D=s|0,this.E=n|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,r){for(let u=0;u<16;u++,r+=4)qt[u]=e.getUint32(r,!1);for(let u=16;u<64;u++){const d=qt[u-15],f=qt[u-2],p=ft(d,7)^ft(d,18)^d>>>3,y=ft(f,17)^ft(f,19)^f>>>10;qt[u]=y+qt[u-7]+p+qt[u-16]|0}let{A:i,B:s,C:n,D:o,E:a,F:c,G:l,H:h}=this;for(let u=0;u<64;u++){const d=ft(a,6)^ft(a,11)^ft(a,25),f=h+d+fv(a,c,l)+mv[u]+qt[u]|0,p=(ft(i,2)^ft(i,13)^ft(i,22))+gv(i,s,n)|0;h=l,l=c,c=a,a=o+f|0,o=n,n=s,s=i,i=f+p|0}i=i+this.A|0,s=s+this.B|0,n=n+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,l=l+this.G|0,h=h+this.H|0,this.set(i,s,n,o,a,c,l,h)}roundClean(){qt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const ki=au(()=>new wv);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Bs=BigInt(0),Ds=BigInt(1),bv=BigInt(2);function ur(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function qi(t){if(!ur(t))throw new Error("Uint8Array expected")}function Gr(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const vv=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Yr(t){qi(t);let e="";for(let r=0;r<t.length;r++)e+=vv[t[r]];return e}function Mr(t){const e=t.toString(16);return e.length&1?"0"+e:e}function Mo(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?Bs:BigInt("0x"+t)}const xt={_0:48,_9:57,A:65,F:70,a:97,f:102};function bc(t){if(t>=xt._0&&t<=xt._9)return t-xt._0;if(t>=xt.A&&t<=xt.F)return t-(xt.A-10);if(t>=xt.a&&t<=xt.f)return t-(xt.a-10)}function Jr(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const i=new Uint8Array(r);for(let s=0,n=0;s<r;s++,n+=2){const o=bc(t.charCodeAt(n)),a=bc(t.charCodeAt(n+1));if(o===void 0||a===void 0){const c=t[n]+t[n+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+n)}i[s]=o*16+a}return i}function or(t){return Mo(Yr(t))}function Ai(t){return qi(t),Mo(Yr(Uint8Array.from(t).reverse()))}function Zr(t,e){return Jr(t.toString(16).padStart(e*2,"0"))}function Us(t,e){return Zr(t,e).reverse()}function Ev(t){return Jr(Mr(t))}function Ve(t,e,r){let i;if(typeof e=="string")try{i=Jr(e)}catch(n){throw new Error(t+" must be hex string or Uint8Array, cause: "+n)}else if(ur(e))i=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const s=i.length;if(typeof r=="number"&&s!==r)throw new Error(t+" of length "+r+" expected, got "+s);return i}function Ti(...t){let e=0;for(let i=0;i<t.length;i++){const s=t[i];qi(s),e+=s.length}const r=new Uint8Array(e);for(let i=0,s=0;i<t.length;i++){const n=t[i];r.set(n,s),s+=n.length}return r}function _v(t,e){if(t.length!==e.length)return!1;let r=0;for(let i=0;i<t.length;i++)r|=t[i]^e[i];return r===0}function Iv(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const pn=t=>typeof t=="bigint"&&Bs<=t;function Ls(t,e,r){return pn(t)&&pn(e)&&pn(r)&&e<=t&&t<r}function jt(t,e,r,i){if(!Ls(e,r,i))throw new Error("expected valid "+t+": "+r+" <= n < "+i+", got "+e)}function vu(t){let e;for(e=0;t>Bs;t>>=Ds,e+=1);return e}function $v(t,e){return t>>BigInt(e)&Ds}function Sv(t,e,r){return t|(r?Ds:Bs)<<BigInt(e)}const Fo=t=>(bv<<BigInt(t-1))-Ds,fn=t=>new Uint8Array(t),vc=t=>Uint8Array.from(t);function Eu(t,e,r){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let i=fn(t),s=fn(t),n=0;const o=()=>{i.fill(1),s.fill(0),n=0},a=(...h)=>r(s,i,...h),c=(h=fn())=>{s=a(vc([0]),h),i=a(),h.length!==0&&(s=a(vc([1]),h),i=a())},l=()=>{if(n++>=1e3)throw new Error("drbg: tried 1000 values");let h=0;const u=[];for(;h<e;){i=a();const d=i.slice();u.push(d),h+=i.length}return Ti(...u)};return(h,u)=>{o(),c(h);let d;for(;!(d=u(l()));)c();return o(),d}}const Ov={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||ur(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function si(t,e,r={}){const i=(s,n,o)=>{const a=Ov[n];if(typeof a!="function")throw new Error("invalid validator function");const c=t[s];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(s)+" is invalid. Expected "+n+", got "+c)};for(const[s,n]of Object.entries(e))i(s,n,!1);for(const[s,n]of Object.entries(r))i(s,n,!0);return t}const Pv=()=>{throw new Error("not implemented")};function Zn(t){const e=new WeakMap;return(r,...i)=>{const s=e.get(r);if(s!==void 0)return s;const n=t(r,...i);return e.set(r,n),n}}var xv=Object.freeze({__proto__:null,isBytes:ur,abytes:qi,abool:Gr,bytesToHex:Yr,numberToHexUnpadded:Mr,hexToNumber:Mo,hexToBytes:Jr,bytesToNumberBE:or,bytesToNumberLE:Ai,numberToBytesBE:Zr,numberToBytesLE:Us,numberToVarBytesBE:Ev,ensureBytes:Ve,concatBytes:Ti,equalBytes:_v,utf8ToBytes:Iv,inRange:Ls,aInRange:jt,bitLen:vu,bitGet:$v,bitSet:Sv,bitMask:Fo,createHmacDrbg:Eu,validateObject:si,notImplemented:Pv,memoized:Zn});const ve=BigInt(0),le=BigInt(1),tr=BigInt(2),Av=BigInt(3),Qn=BigInt(4),Ec=BigInt(5),_c=BigInt(8);function ke(t,e){const r=t%e;return r>=ve?r:e+r}function _u(t,e,r){if(e<ve)throw new Error("invalid exponent, negatives unsupported");if(r<=ve)throw new Error("invalid modulus");if(r===le)return ve;let i=le;for(;e>ve;)e&le&&(i=i*t%r),t=t*t%r,e>>=le;return i}function lt(t,e,r){let i=t;for(;e-- >ve;)i*=i,i%=r;return i}function Xn(t,e){if(t===ve)throw new Error("invert: expected non-zero number");if(e<=ve)throw new Error("invert: expected positive modulus, got "+e);let r=ke(t,e),i=e,s=ve,n=le;for(;r!==ve;){const o=i/r,a=i%r,c=s-n*o;i=r,r=a,s=n,n=c}if(i!==le)throw new Error("invert: does not exist");return ke(s,e)}function Tv(t){const e=(t-le)/tr;let r,i,s;for(r=t-le,i=0;r%tr===ve;r/=tr,i++);for(s=tr;s<t&&_u(s,e,t)!==t-le;s++)if(s>1e3)throw new Error("Cannot find square root: likely non-prime P");if(i===1){const o=(t+le)/Qn;return function(a,c){const l=a.pow(c,o);if(!a.eql(a.sqr(l),c))throw new Error("Cannot find square root");return l}}const n=(r+le)/tr;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=i,l=o.pow(o.mul(o.ONE,s),r),h=o.pow(a,n),u=o.pow(a,r);for(;!o.eql(u,o.ONE);){if(o.eql(u,o.ZERO))return o.ZERO;let d=1;for(let p=o.sqr(u);d<c&&!o.eql(p,o.ONE);d++)p=o.sqr(p);const f=o.pow(l,le<<BigInt(c-d-1));l=o.sqr(f),h=o.mul(h,f),u=o.mul(u,l),c=d}return h}}function Cv(t){if(t%Qn===Av){const e=(t+le)/Qn;return function(r,i){const s=r.pow(i,e);if(!r.eql(r.sqr(s),i))throw new Error("Cannot find square root");return s}}if(t%_c===Ec){const e=(t-Ec)/_c;return function(r,i){const s=r.mul(i,tr),n=r.pow(s,e),o=r.mul(i,n),a=r.mul(r.mul(o,tr),n),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),i))throw new Error("Cannot find square root");return c}}return Tv(t)}const Rv=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Nv(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=Rv.reduce((i,s)=>(i[s]="function",i),e);return si(t,r)}function jv(t,e,r){if(r<ve)throw new Error("invalid exponent, negatives unsupported");if(r===ve)return t.ONE;if(r===le)return e;let i=t.ONE,s=e;for(;r>ve;)r&le&&(i=t.mul(i,s)),s=t.sqr(s),r>>=le;return i}function Bv(t,e){const r=new Array(e.length),i=e.reduce((n,o,a)=>t.is0(o)?n:(r[a]=n,t.mul(n,o)),t.ONE),s=t.inv(i);return e.reduceRight((n,o,a)=>t.is0(o)?n:(r[a]=t.mul(n,r[a]),t.mul(n,o)),s),r}function Iu(t,e){const r=e!==void 0?e:t.toString(2).length,i=Math.ceil(r/8);return{nBitLength:r,nByteLength:i}}function $u(t,e,r=!1,i={}){if(t<=ve)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:s,nByteLength:n}=Iu(t,e);if(n>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:s,BYTES:n,MASK:Fo(s),ZERO:ve,ONE:le,create:c=>ke(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return ve<=c&&c<t},is0:c=>c===ve,isOdd:c=>(c&le)===le,neg:c=>ke(-c,t),eql:(c,l)=>c===l,sqr:c=>ke(c*c,t),add:(c,l)=>ke(c+l,t),sub:(c,l)=>ke(c-l,t),mul:(c,l)=>ke(c*l,t),pow:(c,l)=>jv(a,c,l),div:(c,l)=>ke(c*Xn(l,t),t),sqrN:c=>c*c,addN:(c,l)=>c+l,subN:(c,l)=>c-l,mulN:(c,l)=>c*l,inv:c=>Xn(c,t),sqrt:i.sqrt||(c=>(o||(o=Cv(t)),o(a,c))),invertBatch:c=>Bv(a,c),cmov:(c,l,h)=>h?l:c,toBytes:c=>r?Us(c,n):Zr(c,n),fromBytes:c=>{if(c.length!==n)throw new Error("Field.fromBytes: expected "+n+" bytes, got "+c.length);return r?Ai(c):or(c)}});return Object.freeze(a)}function Su(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function Ou(t){const e=Su(t);return e+Math.ceil(e/2)}function Dv(t,e,r=!1){const i=t.length,s=Su(e),n=Ou(e);if(i<16||i<n||i>1024)throw new Error("expected "+n+"-1024 bytes of input, got "+i);const o=r?Ai(t):or(t),a=ke(o,e-le)+le;return r?Us(a,s):Zr(a,s)}const Ic=BigInt(0),Qi=BigInt(1);function gn(t,e){const r=e.negate();return t?r:e}function Pu(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function yn(t,e){Pu(t,e);const r=Math.ceil(e/t)+1,i=2**(t-1);return{windows:r,windowSize:i}}function Uv(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,i)=>{if(!(r instanceof e))throw new Error("invalid point at index "+i)})}function Lv(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,i)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+i)})}const mn=new WeakMap,xu=new WeakMap;function wn(t){return xu.get(t)||1}function kv(t,e){return{constTimeNegate:gn,hasPrecomputes(r){return wn(r)!==1},unsafeLadder(r,i,s=t.ZERO){let n=r;for(;i>Ic;)i&Qi&&(s=s.add(n)),n=n.double(),i>>=Qi;return s},precomputeWindow(r,i){const{windows:s,windowSize:n}=yn(i,e),o=[];let a=r,c=a;for(let l=0;l<s;l++){c=a,o.push(c);for(let h=1;h<n;h++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,i,s){const{windows:n,windowSize:o}=yn(r,e);let a=t.ZERO,c=t.BASE;const l=BigInt(2**r-1),h=2**r,u=BigInt(r);for(let d=0;d<n;d++){const f=d*o;let p=Number(s&l);s>>=u,p>o&&(p-=h,s+=Qi);const y=f,w=f+Math.abs(p)-1,v=d%2!==0,b=p<0;p===0?c=c.add(gn(v,i[y])):a=a.add(gn(b,i[w]))}return{p:a,f:c}},wNAFUnsafe(r,i,s,n=t.ZERO){const{windows:o,windowSize:a}=yn(r,e),c=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let u=0;u<o;u++){const d=u*a;if(s===Ic)break;let f=Number(s&c);if(s>>=h,f>a&&(f-=l,s+=Qi),f===0)continue;let p=i[d+Math.abs(f)-1];f<0&&(p=p.negate()),n=n.add(p)}return n},getPrecomputes(r,i,s){let n=mn.get(i);return n||(n=this.precomputeWindow(i,r),r!==1&&mn.set(i,s(n))),n},wNAFCached(r,i,s){const n=wn(r);return this.wNAF(n,this.getPrecomputes(n,r,s),i)},wNAFCachedUnsafe(r,i,s,n){const o=wn(r);return o===1?this.unsafeLadder(r,i,n):this.wNAFUnsafe(o,this.getPrecomputes(o,r,s),i,n)},setWindowSize(r,i){Pu(i,e),xu.set(r,i),mn.delete(r)}}}function qv(t,e,r,i){if(Uv(r,t),Lv(i,e),r.length!==i.length)throw new Error("arrays of points and scalars must have equal length");const s=t.ZERO,n=vu(BigInt(r.length)),o=n>12?n-3:n>4?n-2:n?2:1,a=(1<<o)-1,c=new Array(a+1).fill(s),l=Math.floor((e.BITS-1)/o)*o;let h=s;for(let u=l;u>=0;u-=o){c.fill(s);for(let f=0;f<i.length;f++){const p=i[f],y=Number(p>>BigInt(u)&BigInt(a));c[y]=c[y].add(r[f])}let d=s;for(let f=c.length-1,p=s;f>0;f--)p=p.add(c[f]),d=d.add(p);if(h=h.add(d),u!==0)for(let f=0;f<o;f++)h=h.double()}return h}function Au(t){return Nv(t.Fp),si(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Iu(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}BigInt(0),BigInt(1),BigInt(2),BigInt(8);const Ir=BigInt(0),bn=BigInt(1);function Mv(t){return si(t,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze({...t})}function Fv(t){const e=Mv(t),{P:r}=e,i=b=>ke(b,r),s=e.montgomeryBits,n=Math.ceil(s/8),o=e.nByteLength,a=e.adjustScalarBytes||(b=>b),c=e.powPminus2||(b=>_u(b,r-BigInt(2),r));function l(b,E,I){const x=i(b*(E-I));return E=i(E-x),I=i(I+x),[E,I]}const h=(e.a-BigInt(2))/BigInt(4);function u(b,E){jt("u",b,Ir,r),jt("scalar",E,Ir,r);const I=E,x=b;let A=bn,P=Ir,C=b,S=bn,M=Ir,U;for(let F=BigInt(s-1);F>=Ir;F--){const R=I>>F&bn;M^=R,U=l(M,A,C),A=U[0],C=U[1],U=l(M,P,S),P=U[0],S=U[1],M=R;const g=A+P,m=i(g*g),_=A-P,O=i(_*_),$=m-O,T=C+S,L=C-S,q=i(L*g),z=i(T*_),k=q+z,H=q-z;C=i(k*k),S=i(x*i(H*H)),A=i(m*O),P=i($*(m+i(h*$)))}U=l(M,A,C),A=U[0],C=U[1],U=l(M,P,S),P=U[0],S=U[1];const D=c(P);return i(A*D)}function d(b){return Us(i(b),n)}function f(b){const E=Ve("u coordinate",b,n);return o===32&&(E[31]&=127),Ai(E)}function p(b){const E=Ve("scalar",b),I=E.length;if(I!==n&&I!==o){let x=""+n+" or "+o;throw new Error("invalid scalar, expected "+x+" bytes, got "+I)}return Ai(a(E))}function y(b,E){const I=f(E),x=p(b),A=u(I,x);if(A===Ir)throw new Error("invalid private or public key received");return d(A)}const w=d(e.Gu);function v(b){return y(b,w)}return{scalarMult:y,scalarMultBase:v,getSharedSecret:(b,E)=>y(b,E),getPublicKey:b=>v(b),utils:{randomPrivateKey:()=>e.randomBytes(e.nByteLength)},GuBytes:w}}const eo=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");BigInt(0);const zv=BigInt(1),$c=BigInt(2),Hv=BigInt(3),Vv=BigInt(5);BigInt(8);function Kv(t){const e=BigInt(10),r=BigInt(20),i=BigInt(40),s=BigInt(80),n=eo,o=t*t%n*t%n,a=lt(o,$c,n)*o%n,c=lt(a,zv,n)*t%n,l=lt(c,Vv,n)*c%n,h=lt(l,e,n)*l%n,u=lt(h,r,n)*h%n,d=lt(u,i,n)*u%n,f=lt(d,s,n)*d%n,p=lt(f,s,n)*d%n,y=lt(p,e,n)*l%n;return{pow_p_5_8:lt(y,$c,n)*t%n,b2:o}}function Wv(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}const to=Fv({P:eo,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:t=>{const e=eo,{pow_p_5_8:r,b2:i}=Kv(t);return ke(lt(r,Hv,e)*i,e)},adjustScalarBytes:Wv,randomBytes:ii});function Sc(t){t.lowS!==void 0&&Gr("lowS",t.lowS),t.prehash!==void 0&&Gr("prehash",t.prehash)}function Gv(t){const e=Au(t);si(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:i,a:s}=e;if(r){if(!i.eql(s,i.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof r!="object"||typeof r.beta!="bigint"||typeof r.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...e})}const{bytesToNumberBE:Yv,hexToBytes:Jv}=xv;class Zv extends Error{constructor(e=""){super(e)}}const Rt={Err:Zv,_tlv:{encode:(t,e)=>{const{Err:r}=Rt;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length&1)throw new r("tlv.encode: unpadded data");const i=e.length/2,s=Mr(i);if(s.length/2&128)throw new r("tlv.encode: long form length too big");const n=i>127?Mr(s.length/2|128):"";return Mr(t)+n+s+e},decode(t,e){const{Err:r}=Rt;let i=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[i++]!==t)throw new r("tlv.decode: wrong tlv");const s=e[i++],n=!!(s&128);let o=0;if(!n)o=s;else{const c=s&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");const l=e.subarray(i,i+c);if(l.length!==c)throw new r("tlv.decode: length bytes not complete");if(l[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(const h of l)o=o<<8|h;if(i+=c,o<128)throw new r("tlv.decode(long): not minimal encoding")}const a=e.subarray(i,i+o);if(a.length!==o)throw new r("tlv.decode: wrong value length");return{v:a,l:e.subarray(i+o)}}},_int:{encode(t){const{Err:e}=Rt;if(t<Nt)throw new e("integer: negative integers are not allowed");let r=Mr(t);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){const{Err:e}=Rt;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return Yv(t)}},toSig(t){const{Err:e,_int:r,_tlv:i}=Rt,s=typeof t=="string"?Jv(t):t;qi(s);const{v:n,l:o}=i.decode(48,s);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=i.decode(2,n),{v:l,l:h}=i.decode(2,c);if(h.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(l)}},hexFromSig(t){const{_tlv:e,_int:r}=Rt,i=e.encode(2,r.encode(t.r)),s=e.encode(2,r.encode(t.s)),n=i+s;return e.encode(48,n)}},Nt=BigInt(0),me=BigInt(1);BigInt(2);const Oc=BigInt(3);BigInt(4);function Qv(t){const e=Gv(t),{Fp:r}=e,i=$u(e.n,e.nBitLength),s=e.toBytes||((y,w,v)=>{const b=w.toAffine();return Ti(Uint8Array.from([4]),r.toBytes(b.x),r.toBytes(b.y))}),n=e.fromBytes||(y=>{const w=y.subarray(1),v=r.fromBytes(w.subarray(0,r.BYTES)),b=r.fromBytes(w.subarray(r.BYTES,2*r.BYTES));return{x:v,y:b}});function o(y){const{a:w,b:v}=e,b=r.sqr(y),E=r.mul(b,y);return r.add(r.add(E,r.mul(y,w)),v)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(y){return Ls(y,me,e.n)}function c(y){const{allowedPrivateKeyLengths:w,nByteLength:v,wrapPrivateKey:b,n:E}=e;if(w&&typeof y!="bigint"){if(ur(y)&&(y=Yr(y)),typeof y!="string"||!w.includes(y.length))throw new Error("invalid private key");y=y.padStart(v*2,"0")}let I;try{I=typeof y=="bigint"?y:or(Ve("private key",y,v))}catch{throw new Error("invalid private key, expected hex or "+v+" bytes, got "+typeof y)}return b&&(I=ke(I,E)),jt("private key",I,me,E),I}function l(y){if(!(y instanceof d))throw new Error("ProjectivePoint expected")}const h=Zn((y,w)=>{const{px:v,py:b,pz:E}=y;if(r.eql(E,r.ONE))return{x:v,y:b};const I=y.is0();w==null&&(w=I?r.ONE:r.inv(E));const x=r.mul(v,w),A=r.mul(b,w),P=r.mul(E,w);if(I)return{x:r.ZERO,y:r.ZERO};if(!r.eql(P,r.ONE))throw new Error("invZ was invalid");return{x,y:A}}),u=Zn(y=>{if(y.is0()){if(e.allowInfinityPoint&&!r.is0(y.py))return;throw new Error("bad point: ZERO")}const{x:w,y:v}=y.toAffine();if(!r.isValid(w)||!r.isValid(v))throw new Error("bad point: x or y not FE");const b=r.sqr(v),E=o(w);if(!r.eql(b,E))throw new Error("bad point: equation left != right");if(!y.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(w,v,b){if(this.px=w,this.py=v,this.pz=b,w==null||!r.isValid(w))throw new Error("x required");if(v==null||!r.isValid(v))throw new Error("y required");if(b==null||!r.isValid(b))throw new Error("z required");Object.freeze(this)}static fromAffine(w){const{x:v,y:b}=w||{};if(!w||!r.isValid(v)||!r.isValid(b))throw new Error("invalid affine point");if(w instanceof d)throw new Error("projective point not allowed");const E=I=>r.eql(I,r.ZERO);return E(v)&&E(b)?d.ZERO:new d(v,b,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(w){const v=r.invertBatch(w.map(b=>b.pz));return w.map((b,E)=>b.toAffine(v[E])).map(d.fromAffine)}static fromHex(w){const v=d.fromAffine(n(Ve("pointHex",w)));return v.assertValidity(),v}static fromPrivateKey(w){return d.BASE.multiply(c(w))}static msm(w,v){return qv(d,i,w,v)}_setWindowSize(w){p.setWindowSize(this,w)}assertValidity(){u(this)}hasEvenY(){const{y:w}=this.toAffine();if(r.isOdd)return!r.isOdd(w);throw new Error("Field doesn't support isOdd")}equals(w){l(w);const{px:v,py:b,pz:E}=this,{px:I,py:x,pz:A}=w,P=r.eql(r.mul(v,A),r.mul(I,E)),C=r.eql(r.mul(b,A),r.mul(x,E));return P&&C}negate(){return new d(this.px,r.neg(this.py),this.pz)}double(){const{a:w,b:v}=e,b=r.mul(v,Oc),{px:E,py:I,pz:x}=this;let A=r.ZERO,P=r.ZERO,C=r.ZERO,S=r.mul(E,E),M=r.mul(I,I),U=r.mul(x,x),D=r.mul(E,I);return D=r.add(D,D),C=r.mul(E,x),C=r.add(C,C),A=r.mul(w,C),P=r.mul(b,U),P=r.add(A,P),A=r.sub(M,P),P=r.add(M,P),P=r.mul(A,P),A=r.mul(D,A),C=r.mul(b,C),U=r.mul(w,U),D=r.sub(S,U),D=r.mul(w,D),D=r.add(D,C),C=r.add(S,S),S=r.add(C,S),S=r.add(S,U),S=r.mul(S,D),P=r.add(P,S),U=r.mul(I,x),U=r.add(U,U),S=r.mul(U,D),A=r.sub(A,S),C=r.mul(U,M),C=r.add(C,C),C=r.add(C,C),new d(A,P,C)}add(w){l(w);const{px:v,py:b,pz:E}=this,{px:I,py:x,pz:A}=w;let P=r.ZERO,C=r.ZERO,S=r.ZERO;const M=e.a,U=r.mul(e.b,Oc);let D=r.mul(v,I),F=r.mul(b,x),R=r.mul(E,A),g=r.add(v,b),m=r.add(I,x);g=r.mul(g,m),m=r.add(D,F),g=r.sub(g,m),m=r.add(v,E);let _=r.add(I,A);return m=r.mul(m,_),_=r.add(D,R),m=r.sub(m,_),_=r.add(b,E),P=r.add(x,A),_=r.mul(_,P),P=r.add(F,R),_=r.sub(_,P),S=r.mul(M,m),P=r.mul(U,R),S=r.add(P,S),P=r.sub(F,S),S=r.add(F,S),C=r.mul(P,S),F=r.add(D,D),F=r.add(F,D),R=r.mul(M,R),m=r.mul(U,m),F=r.add(F,R),R=r.sub(D,R),R=r.mul(M,R),m=r.add(m,R),D=r.mul(F,m),C=r.add(C,D),D=r.mul(_,m),P=r.mul(g,P),P=r.sub(P,D),D=r.mul(g,F),S=r.mul(_,S),S=r.add(S,D),new d(P,C,S)}subtract(w){return this.add(w.negate())}is0(){return this.equals(d.ZERO)}wNAF(w){return p.wNAFCached(this,w,d.normalizeZ)}multiplyUnsafe(w){const{endo:v,n:b}=e;jt("scalar",w,Nt,b);const E=d.ZERO;if(w===Nt)return E;if(this.is0()||w===me)return this;if(!v||p.hasPrecomputes(this))return p.wNAFCachedUnsafe(this,w,d.normalizeZ);let{k1neg:I,k1:x,k2neg:A,k2:P}=v.splitScalar(w),C=E,S=E,M=this;for(;x>Nt||P>Nt;)x&me&&(C=C.add(M)),P&me&&(S=S.add(M)),M=M.double(),x>>=me,P>>=me;return I&&(C=C.negate()),A&&(S=S.negate()),S=new d(r.mul(S.px,v.beta),S.py,S.pz),C.add(S)}multiply(w){const{endo:v,n:b}=e;jt("scalar",w,me,b);let E,I;if(v){const{k1neg:x,k1:A,k2neg:P,k2:C}=v.splitScalar(w);let{p:S,f:M}=this.wNAF(A),{p:U,f:D}=this.wNAF(C);S=p.constTimeNegate(x,S),U=p.constTimeNegate(P,U),U=new d(r.mul(U.px,v.beta),U.py,U.pz),E=S.add(U),I=M.add(D)}else{const{p:x,f:A}=this.wNAF(w);E=x,I=A}return d.normalizeZ([E,I])[0]}multiplyAndAddUnsafe(w,v,b){const E=d.BASE,I=(A,P)=>P===Nt||P===me||!A.equals(E)?A.multiplyUnsafe(P):A.multiply(P),x=I(this,v).add(I(w,b));return x.is0()?void 0:x}toAffine(w){return h(this,w)}isTorsionFree(){const{h:w,isTorsionFree:v}=e;if(w===me)return!0;if(v)return v(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:w,clearCofactor:v}=e;return w===me?this:v?v(d,this):this.multiplyUnsafe(e.h)}toRawBytes(w=!0){return Gr("isCompressed",w),this.assertValidity(),s(d,this,w)}toHex(w=!0){return Gr("isCompressed",w),Yr(this.toRawBytes(w))}}d.BASE=new d(e.Gx,e.Gy,r.ONE),d.ZERO=new d(r.ZERO,r.ONE,r.ZERO);const f=e.nBitLength,p=kv(d,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function Xv(t){const e=Au(t);return si(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function e0(t){const e=Xv(t),{Fp:r,n:i}=e,s=r.BYTES+1,n=2*r.BYTES+1;function o(R){return ke(R,i)}function a(R){return Xn(R,i)}const{ProjectivePoint:c,normPrivateKeyToScalar:l,weierstrassEquation:h,isWithinCurveOrder:u}=Qv({...e,toBytes(R,g,m){const _=g.toAffine(),O=r.toBytes(_.x),$=Ti;return Gr("isCompressed",m),m?$(Uint8Array.from([g.hasEvenY()?2:3]),O):$(Uint8Array.from([4]),O,r.toBytes(_.y))},fromBytes(R){const g=R.length,m=R[0],_=R.subarray(1);if(g===s&&(m===2||m===3)){const O=or(_);if(!Ls(O,me,r.ORDER))throw new Error("Point is not on curve");const $=h(O);let T;try{T=r.sqrt($)}catch(q){const z=q instanceof Error?": "+q.message:"";throw new Error("Point is not on curve"+z)}const L=(T&me)===me;return(m&1)===1!==L&&(T=r.neg(T)),{x:O,y:T}}else if(g===n&&m===4){const O=r.fromBytes(_.subarray(0,r.BYTES)),$=r.fromBytes(_.subarray(r.BYTES,2*r.BYTES));return{x:O,y:$}}else{const O=s,$=n;throw new Error("invalid Point, expected length of "+O+", or uncompressed "+$+", got "+g)}}}),d=R=>Yr(Zr(R,e.nByteLength));function f(R){const g=i>>me;return R>g}function p(R){return f(R)?o(-R):R}const y=(R,g,m)=>or(R.slice(g,m));class w{constructor(g,m,_){this.r=g,this.s=m,this.recovery=_,this.assertValidity()}static fromCompact(g){const m=e.nByteLength;return g=Ve("compactSignature",g,m*2),new w(y(g,0,m),y(g,m,2*m))}static fromDER(g){const{r:m,s:_}=Rt.toSig(Ve("DER",g));return new w(m,_)}assertValidity(){jt("r",this.r,me,i),jt("s",this.s,me,i)}addRecoveryBit(g){return new w(this.r,this.s,g)}recoverPublicKey(g){const{r:m,s:_,recovery:O}=this,$=A(Ve("msgHash",g));if(O==null||![0,1,2,3].includes(O))throw new Error("recovery id invalid");const T=O===2||O===3?m+e.n:m;if(T>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const L=O&1?"03":"02",q=c.fromHex(L+d(T)),z=a(T),k=o(-$*z),H=o(_*z),G=c.BASE.multiplyAndAddUnsafe(q,k,H);if(!G)throw new Error("point at infinify");return G.assertValidity(),G}hasHighS(){return f(this.s)}normalizeS(){return this.hasHighS()?new w(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return Jr(this.toDERHex())}toDERHex(){return Rt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Jr(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const v={isValidPrivateKey(R){try{return l(R),!0}catch{return!1}},normPrivateKeyToScalar:l,randomPrivateKey:()=>{const R=Ou(e.n);return Dv(e.randomBytes(R),e.n)},precompute(R=8,g=c.BASE){return g._setWindowSize(R),g.multiply(BigInt(3)),g}};function b(R,g=!0){return c.fromPrivateKey(R).toRawBytes(g)}function E(R){const g=ur(R),m=typeof R=="string",_=(g||m)&&R.length;return g?_===s||_===n:m?_===2*s||_===2*n:R instanceof c}function I(R,g,m=!0){if(E(R))throw new Error("first arg must be private key");if(!E(g))throw new Error("second arg must be public key");return c.fromHex(g).multiply(l(R)).toRawBytes(m)}const x=e.bits2int||function(R){if(R.length>8192)throw new Error("input is too large");const g=or(R),m=R.length*8-e.nBitLength;return m>0?g>>BigInt(m):g},A=e.bits2int_modN||function(R){return o(x(R))},P=Fo(e.nBitLength);function C(R){return jt("num < 2^"+e.nBitLength,R,Nt,P),Zr(R,e.nByteLength)}function S(R,g,m=M){if(["recovered","canonical"].some(ee=>ee in m))throw new Error("sign() legacy options not supported");const{hash:_,randomBytes:O}=e;let{lowS:$,prehash:T,extraEntropy:L}=m;$==null&&($=!0),R=Ve("msgHash",R),Sc(m),T&&(R=Ve("prehashed msgHash",_(R)));const q=A(R),z=l(g),k=[C(z),C(q)];if(L!=null&&L!==!1){const ee=L===!0?O(r.BYTES):L;k.push(Ve("extraEntropy",ee))}const H=Ti(...k),G=q;function se(ee){const Y=x(ee);if(!u(Y))return;const Ae=a(Y),Ee=c.BASE.multiply(Y).toAffine(),Ce=o(Ee.x);if(Ce===Nt)return;const Ye=o(Ae*o(G+Ce*z));if(Ye===Nt)return;let Je=(Ee.x===Ce?0:2)|Number(Ee.y&me),wr=Ye;return $&&f(Ye)&&(wr=p(Ye),Je^=1),new w(Ce,wr,Je)}return{seed:H,k2sig:se}}const M={lowS:e.lowS,prehash:!1},U={lowS:e.lowS,prehash:!1};function D(R,g,m=M){const{seed:_,k2sig:O}=S(R,g,m),$=e;return Eu($.hash.outputLen,$.nByteLength,$.hmac)(_,O)}c.BASE._setWindowSize(8);function F(R,g,m,_=U){var Ye;const O=R;g=Ve("msgHash",g),m=Ve("publicKey",m);const{lowS:$,prehash:T,format:L}=_;if(Sc(_),"strict"in _)throw new Error("options.strict was renamed to lowS");if(L!==void 0&&L!=="compact"&&L!=="der")throw new Error("format must be compact or der");const q=typeof O=="string"||ur(O),z=!q&&!L&&typeof O=="object"&&O!==null&&typeof O.r=="bigint"&&typeof O.s=="bigint";if(!q&&!z)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let k,H;try{if(z&&(k=new w(O.r,O.s)),q){try{L!=="compact"&&(k=w.fromDER(O))}catch(Je){if(!(Je instanceof Rt.Err))throw Je}!k&&L!=="der"&&(k=w.fromCompact(O))}H=c.fromHex(m)}catch{return!1}if(!k||$&&k.hasHighS())return!1;T&&(g=e.hash(g));const{r:G,s:se}=k,ee=A(g),Y=a(se),Ae=o(ee*Y),Ee=o(G*Y),Ce=(Ye=c.BASE.multiplyAndAddUnsafe(H,Ae,Ee))==null?void 0:Ye.toAffine();return Ce?o(Ce.x)===G:!1}return{CURVE:e,getPublicKey:b,getSharedSecret:I,sign:D,verify:F,ProjectivePoint:c,Signature:w,utils:v}}function t0(t){return{hash:t,hmac:(e,...r)=>js(t,e,nb(...r)),randomBytes:ii}}function r0(t,e){const r=i=>e0({...t,...t0(i)});return{...r(e),create:r}}const Tu=$u(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff")),i0=Tu.create(BigInt("-3")),s0=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"),n0=r0({a:i0,b:s0,Fp:Tu,n:BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"),Gx:BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"),Gy:BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"),h:BigInt(1),lowS:!1},ki),Cu="base10",Ne="base16",ht="base64pad",Ft="base64url",Mi="utf8",Ru=0,Bt=1,Fi=2,o0=0,Pc=1,Ei=12,zo=32;function a0(){const t=to.utils.randomPrivateKey(),e=to.getPublicKey(t);return{privateKey:qe(t,Ne),publicKey:qe(e,Ne)}}function ro(){const t=ii(zo);return qe(t,Ne)}function c0(t,e){const r=to.getSharedSecret(tt(t,Ne),tt(e,Ne)),i=dv(ki,r,void 0,void 0,zo);return qe(i,Ne)}function us(t){const e=ki(tt(t,Ne));return qe(e,Ne)}function Et(t){const e=ki(tt(t,Mi));return qe(e,Ne)}function Nu(t){return tt(`${t}`,Cu)}function dr(t){return Number(qe(t,Cu))}function ju(t){return t.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function Bu(t){const e=t.replace(/-/g,"+").replace(/_/g,"/"),r=(4-e.length%4)%4;return e+"=".repeat(r)}function l0(t){const e=Nu(typeof t.type<"u"?t.type:Ru);if(dr(e)===Bt&&typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");const r=typeof t.senderPublicKey<"u"?tt(t.senderPublicKey,Ne):void 0,i=typeof t.iv<"u"?tt(t.iv,Ne):ii(Ei),s=tt(t.symKey,Ne),n=wu(s,i).encrypt(tt(t.message,Mi)),o=Du({type:e,sealed:n,iv:i,senderPublicKey:r});return t.encoding===Ft?ju(o):o}function h0(t){const e=tt(t.symKey,Ne),{sealed:r,iv:i}=Ci({encoded:t.encoded,encoding:t.encoding}),s=wu(e,i).decrypt(r);if(s===null)throw new Error("Failed to decrypt");return qe(s,Mi)}function u0(t,e){const r=Nu(Fi),i=ii(Ei),s=tt(t,Mi),n=Du({type:r,sealed:s,iv:i});return e===Ft?ju(n):n}function d0(t,e){const{sealed:r}=Ci({encoded:t,encoding:e});return qe(r,Mi)}function Du(t){if(dr(t.type)===Fi)return qe(vi([t.type,t.sealed]),ht);if(dr(t.type)===Bt){if(typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");return qe(vi([t.type,t.senderPublicKey,t.iv,t.sealed]),ht)}return qe(vi([t.type,t.iv,t.sealed]),ht)}function Ci(t){const e=(t.encoding||ht)===Ft?Bu(t.encoded):t.encoded,r=tt(e,ht),i=r.slice(o0,Pc),s=Pc;if(dr(i)===Bt){const c=s+zo,l=c+Ei,h=r.slice(s,c),u=r.slice(c,l),d=r.slice(l);return{type:i,sealed:d,iv:u,senderPublicKey:h}}if(dr(i)===Fi){const c=r.slice(s),l=ii(Ei);return{type:i,sealed:c,iv:l}}const n=s+Ei,o=r.slice(s,n),a=r.slice(n);return{type:i,sealed:a,iv:o}}function p0(t,e){const r=Ci({encoded:t,encoding:e==null?void 0:e.encoding});return Uu({type:dr(r.type),senderPublicKey:typeof r.senderPublicKey<"u"?qe(r.senderPublicKey,Ne):void 0,receiverPublicKey:e==null?void 0:e.receiverPublicKey})}function Uu(t){const e=(t==null?void 0:t.type)||Ru;if(e===Bt){if(typeof(t==null?void 0:t.senderPublicKey)>"u")throw new Error("missing sender public key");if(typeof(t==null?void 0:t.receiverPublicKey)>"u")throw new Error("missing receiver public key")}return{type:e,senderPublicKey:t==null?void 0:t.senderPublicKey,receiverPublicKey:t==null?void 0:t.receiverPublicKey}}function xc(t){return t.type===Bt&&typeof t.senderPublicKey=="string"&&typeof t.receiverPublicKey=="string"}function Ac(t){return t.type===Fi}function f0(t){const e=Buffer.from(t.x,"base64"),r=Buffer.from(t.y,"base64");return vi([new Uint8Array([4]),e,r])}function g0(t,e){const[r,i,s]=t.split("."),n=Buffer.from(Bu(s),"base64");if(n.length!==64)throw new Error("Invalid signature length");const o=n.slice(0,32),a=n.slice(32,64),c=`${r}.${i}`,l=ki(c),h=f0(e);if(!n0.verify(vi([o,a]),l,h))throw new Error("Invalid signature");return Hn(t).payload}const y0="irn";function Es(t){return(t==null?void 0:t.relay)||{protocol:y0}}function wi(t){const e=Ew[t];if(typeof e>"u")throw new Error(`Relay Protocol not supported: ${t}`);return e}function m0(t,e="-"){const r={},i="relay"+e;return Object.keys(t).forEach(s=>{if(s.startsWith(i)){const n=s.replace(i,""),o=t[s];r[n]=o}}),r}function Tc(t){if(!t.includes("wc:")){const l=nu(t);l!=null&&l.includes("wc:")&&(t=l)}t=t.includes("wc://")?t.replace("wc://",""):t,t=t.includes("wc:")?t.replace("wc:",""):t;const e=t.indexOf(":"),r=t.indexOf("?")!==-1?t.indexOf("?"):void 0,i=t.substring(0,e),s=t.substring(e+1,r).split("@"),n=typeof r<"u"?t.substring(r):"",o=new URLSearchParams(n),a={};o.forEach((l,h)=>{a[h]=l});const c=typeof a.methods=="string"?a.methods.split(","):void 0;return{protocol:i,topic:w0(s[0]),version:parseInt(s[1],10),symKey:a.symKey,relay:m0(a),methods:c,expiryTimestamp:a.expiryTimestamp?parseInt(a.expiryTimestamp,10):void 0}}function w0(t){return t.startsWith("//")?t.substring(2):t}function b0(t,e="-"){const r="relay",i={};return Object.keys(t).forEach(s=>{const n=s,o=r+e+n;t[n]&&(i[o]=t[n])}),i}function Cc(t){const e=new URLSearchParams,r=b0(t.relay);Object.keys(r).sort().forEach(s=>{e.set(s,r[s])}),e.set("symKey",t.symKey),t.expiryTimestamp&&e.set("expiryTimestamp",t.expiryTimestamp.toString()),t.methods&&e.set("methods",t.methods.join(","));const i=e.toString();return`${t.protocol}:${t.topic}@${t.version}?${i}`}function Xi(t,e,r){return`${t}?wc_ev=${r}&topic=${e}`}var v0=Object.defineProperty,E0=Object.defineProperties,_0=Object.getOwnPropertyDescriptors,Rc=Object.getOwnPropertySymbols,I0=Object.prototype.hasOwnProperty,$0=Object.prototype.propertyIsEnumerable,Nc=(t,e,r)=>e in t?v0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,S0=(t,e)=>{for(var r in e||(e={}))I0.call(e,r)&&Nc(t,r,e[r]);if(Rc)for(var r of Rc(e))$0.call(e,r)&&Nc(t,r,e[r]);return t},O0=(t,e)=>E0(t,_0(e));function ni(t){const e=[];return t.forEach(r=>{const[i,s]=r.split(":");e.push(`${i}:${s}`)}),e}function P0(t){const e=[];return Object.values(t).forEach(r=>{e.push(...ni(r.accounts))}),e}function x0(t,e){const r=[];return Object.values(t).forEach(i=>{ni(i.accounts).includes(e)&&r.push(...i.methods)}),r}function A0(t,e){const r=[];return Object.values(t).forEach(i=>{ni(i.accounts).includes(e)&&r.push(...i.events)}),r}function ks(t){return t.includes(":")}function Fr(t){return ks(t)?t.split(":")[0]:t}function jc(t){var e,r,i;const s={};if(!Kt(t))return s;for(const[n,o]of Object.entries(t)){const a=ks(n)?[n]:o.chains,c=o.methods||[],l=o.events||[],h=Fr(n);s[h]=O0(S0({},s[h]),{chains:_t(a,(e=s[h])==null?void 0:e.chains),methods:_t(c,(r=s[h])==null?void 0:r.methods),events:_t(l,(i=s[h])==null?void 0:i.events)})}return s}function T0(t){const e={};return t==null||t.forEach(r=>{var i;const[s,n]=r.split(":");e[s]||(e[s]={accounts:[],chains:[],events:[],methods:[]}),e[s].accounts.push(r),(i=e[s].chains)==null||i.push(`${s}:${n}`)}),e}function Bc(t,e){e=e.map(i=>i.replace("did:pkh:",""));const r=T0(e);for(const[i,s]of Object.entries(r))s.methods?s.methods=_t(s.methods,t):s.methods=t,s.events=["chainChanged","accountsChanged"];return r}function C0(t,e){var r,i,s,n,o,a;const c=jc(t),l=jc(e),h={},u=Object.keys(c).concat(Object.keys(l));for(const d of u)h[d]={chains:_t((r=c[d])==null?void 0:r.chains,(i=l[d])==null?void 0:i.chains),methods:_t((s=c[d])==null?void 0:s.methods,(n=l[d])==null?void 0:n.methods),events:_t((o=c[d])==null?void 0:o.events,(a=l[d])==null?void 0:a.events)};return h}const R0={INVALID_METHOD:{message:"Invalid method.",code:1001},INVALID_EVENT:{message:"Invalid event.",code:1002},INVALID_UPDATE_REQUEST:{message:"Invalid update request.",code:1003},INVALID_EXTEND_REQUEST:{message:"Invalid extend request.",code:1004},INVALID_SESSION_SETTLE_REQUEST:{message:"Invalid session settle request.",code:1005},UNAUTHORIZED_METHOD:{message:"Unauthorized method.",code:3001},UNAUTHORIZED_EVENT:{message:"Unauthorized event.",code:3002},UNAUTHORIZED_UPDATE_REQUEST:{message:"Unauthorized update request.",code:3003},UNAUTHORIZED_EXTEND_REQUEST:{message:"Unauthorized extend request.",code:3004},USER_REJECTED:{message:"User rejected.",code:5e3},USER_REJECTED_CHAINS:{message:"User rejected chains.",code:5001},USER_REJECTED_METHODS:{message:"User rejected methods.",code:5002},USER_REJECTED_EVENTS:{message:"User rejected events.",code:5003},UNSUPPORTED_CHAINS:{message:"Unsupported chains.",code:5100},UNSUPPORTED_METHODS:{message:"Unsupported methods.",code:5101},UNSUPPORTED_EVENTS:{message:"Unsupported events.",code:5102},UNSUPPORTED_ACCOUNTS:{message:"Unsupported accounts.",code:5103},UNSUPPORTED_NAMESPACE_KEY:{message:"Unsupported namespace key.",code:5104},USER_DISCONNECTED:{message:"User disconnected.",code:6e3},SESSION_SETTLEMENT_FAILED:{message:"Session settlement failed.",code:7e3},WC_METHOD_UNSUPPORTED:{message:"Unsupported wc_ method.",code:10001}},N0={NOT_INITIALIZED:{message:"Not initialized.",code:1},NO_MATCHING_KEY:{message:"No matching key.",code:2},RESTORE_WILL_OVERRIDE:{message:"Restore will override.",code:3},RESUBSCRIBED:{message:"Resubscribed.",code:4},MISSING_OR_INVALID:{message:"Missing or invalid.",code:5},EXPIRED:{message:"Expired.",code:6},UNKNOWN_TYPE:{message:"Unknown type.",code:7},MISMATCHED_TOPIC:{message:"Mismatched topic.",code:8},NON_CONFORMING_NAMESPACES:{message:"Non conforming namespaces.",code:9}};function B(t,e){const{message:r,code:i}=N0[t];return{message:e?`${r} ${e}`:r,code:i}}function te(t,e){const{message:r,code:i}=R0[t];return{message:e?`${r} ${e}`:r,code:i}}function ut(t,e){return!!Array.isArray(t)}function Kt(t){return Object.getPrototypeOf(t)===Object.prototype&&Object.keys(t).length}function Se(t){return typeof t>"u"}function ue(t,e){return e&&Se(t)?!0:typeof t=="string"&&!!t.trim().length}function Ho(t,e){return e&&Se(t)?!0:typeof t=="number"&&!isNaN(t)}function j0(t,e){const{requiredNamespaces:r}=e,i=Object.keys(t.namespaces),s=Object.keys(r);let n=!0;return sr(s,i)?(i.forEach(o=>{const{accounts:a,methods:c,events:l}=t.namespaces[o],h=ni(a),u=r[o];(!sr(eu(o,u),h)||!sr(u.methods,c)||!sr(u.events,l))&&(n=!1)}),n):!1}function _s(t){return ue(t,!1)&&t.includes(":")?t.split(":").length===2:!1}function B0(t){if(ue(t,!1)&&t.includes(":")){const e=t.split(":");if(e.length===3){const r=e[0]+":"+e[1];return!!e[2]&&_s(r)}}return!1}function D0(t){function e(r){try{return typeof new URL(r)<"u"}catch{return!1}}try{if(ue(t,!1)){if(e(t))return!0;const r=nu(t);return e(r)}}catch{}return!1}function U0(t){var e;return(e=t==null?void 0:t.proposer)==null?void 0:e.publicKey}function L0(t){return t==null?void 0:t.topic}function k0(t,e){let r=null;return ue(t==null?void 0:t.publicKey,!1)||(r=B("MISSING_OR_INVALID",`${e} controller public key should be a string`)),r}function Dc(t){let e=!0;return ut(t)?t.length&&(e=t.every(r=>ue(r,!1))):e=!1,e}function q0(t,e,r){let i=null;return ut(e)&&e.length?e.forEach(s=>{i||_s(s)||(i=te("UNSUPPORTED_CHAINS",`${r}, chain ${s} should be a string and conform to "namespace:chainId" format`))}):_s(t)||(i=te("UNSUPPORTED_CHAINS",`${r}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)),i}function M0(t,e,r){let i=null;return Object.entries(t).forEach(([s,n])=>{if(i)return;const o=q0(s,eu(s,n),`${e} ${r}`);o&&(i=o)}),i}function F0(t,e){let r=null;return ut(t)?t.forEach(i=>{r||B0(i)||(r=te("UNSUPPORTED_ACCOUNTS",`${e}, account ${i} should be a string and conform to "namespace:chainId:address" format`))}):r=te("UNSUPPORTED_ACCOUNTS",`${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`),r}function z0(t,e){let r=null;return Object.values(t).forEach(i=>{if(r)return;const s=F0(i==null?void 0:i.accounts,`${e} namespace`);s&&(r=s)}),r}function H0(t,e){let r=null;return Dc(t==null?void 0:t.methods)?Dc(t==null?void 0:t.events)||(r=te("UNSUPPORTED_EVENTS",`${e}, events should be an array of strings or empty array for no events`)):r=te("UNSUPPORTED_METHODS",`${e}, methods should be an array of strings or empty array for no methods`),r}function Lu(t,e){let r=null;return Object.values(t).forEach(i=>{if(r)return;const s=H0(i,`${e}, namespace`);s&&(r=s)}),r}function V0(t,e,r){let i=null;if(t&&Kt(t)){const s=Lu(t,e);s&&(i=s);const n=M0(t,e,r);n&&(i=n)}else i=B("MISSING_OR_INVALID",`${e}, ${r} should be an object with data`);return i}function vn(t,e){let r=null;if(t&&Kt(t)){const i=Lu(t,e);i&&(r=i);const s=z0(t,e);s&&(r=s)}else r=B("MISSING_OR_INVALID",`${e}, namespaces should be an object with data`);return r}function ku(t){return ue(t.protocol,!0)}function K0(t,e){let r=!1;return t?t&&ut(t)&&t.length&&t.forEach(i=>{r=ku(i)}):r=!0,r}function W0(t){return typeof t=="number"}function Le(t){return typeof t<"u"&&typeof t!==null}function G0(t){return!(!t||typeof t!="object"||!t.code||!Ho(t.code,!1)||!t.message||!ue(t.message,!1))}function Y0(t){return!(Se(t)||!ue(t.method,!1))}function J0(t){return!(Se(t)||Se(t.result)&&Se(t.error)||!Ho(t.id,!1)||!ue(t.jsonrpc,!1))}function Z0(t){return!(Se(t)||!ue(t.name,!1))}function Uc(t,e){return!(!_s(e)||!P0(t).includes(e))}function Q0(t,e,r){return ue(r,!1)?x0(t,e).includes(r):!1}function X0(t,e,r){return ue(r,!1)?A0(t,e).includes(r):!1}function Lc(t,e,r){let i=null;const s=e1(t),n=t1(e),o=Object.keys(s),a=Object.keys(n),c=kc(Object.keys(t)),l=kc(Object.keys(e)),h=c.filter(u=>!l.includes(u));return h.length&&(i=B("NON_CONFORMING_NAMESPACES",`${r} namespaces keys don't satisfy requiredNamespaces.
      Required: ${h.toString()}
      Received: ${Object.keys(e).toString()}`)),sr(o,a)||(i=B("NON_CONFORMING_NAMESPACES",`${r} namespaces chains don't satisfy required namespaces.
      Required: ${o.toString()}
      Approved: ${a.toString()}`)),Object.keys(e).forEach(u=>{if(!u.includes(":")||i)return;const d=ni(e[u].accounts);d.includes(u)||(i=B("NON_CONFORMING_NAMESPACES",`${r} namespaces accounts don't satisfy namespace accounts for ${u}
        Required: ${u}
        Approved: ${d.toString()}`))}),o.forEach(u=>{i||(sr(s[u].methods,n[u].methods)?sr(s[u].events,n[u].events)||(i=B("NON_CONFORMING_NAMESPACES",`${r} namespaces events don't satisfy namespace events for ${u}`)):i=B("NON_CONFORMING_NAMESPACES",`${r} namespaces methods don't satisfy namespace methods for ${u}`))}),i}function e1(t){const e={};return Object.keys(t).forEach(r=>{var i;r.includes(":")?e[r]=t[r]:(i=t[r].chains)==null||i.forEach(s=>{e[s]={methods:t[r].methods,events:t[r].events}})}),e}function kc(t){return[...new Set(t.map(e=>e.includes(":")?e.split(":")[0]:e))]}function t1(t){const e={};return Object.keys(t).forEach(r=>{if(r.includes(":"))e[r]=t[r];else{const i=ni(t[r].accounts);i==null||i.forEach(s=>{e[s]={accounts:t[r].accounts.filter(n=>n.includes(`${s}:`)),methods:t[r].methods,events:t[r].events}})}}),e}function r1(t,e){return Ho(t,!1)&&t<=e.max&&t>=e.min}function qc(){const t=Ui();return new Promise(e=>{switch(t){case We.browser:e(i1());break;case We.reactNative:e(s1());break;case We.node:e(n1());break;default:e(!0)}})}function i1(){return ri()&&(navigator==null?void 0:navigator.onLine)}async function s1(){if(Wt()&&typeof global<"u"&&global!=null&&global.NetInfo){const t=await(global==null?void 0:global.NetInfo.fetch());return t==null?void 0:t.isConnected}return!0}function n1(){return!0}function o1(t){switch(Ui()){case We.browser:a1(t);break;case We.reactNative:c1(t);break}}function a1(t){!Wt()&&ri()&&(window.addEventListener("online",()=>t(!0)),window.addEventListener("offline",()=>t(!1)))}function c1(t){Wt()&&typeof global<"u"&&global!=null&&global.NetInfo&&(global==null||global.NetInfo.addEventListener(e=>t(e==null?void 0:e.isConnected)))}function l1(){var t;return ri()&&ar()?((t=ar())==null?void 0:t.visibilityState)==="visible":!0}const En={};class di{static get(e){return En[e]}static set(e,r){En[e]=r}static delete(e){delete En[e]}}class fr{}let h1=class extends fr{constructor(e){super()}};const Mc=j.FIVE_SECONDS,gr={pulse:"heartbeat_pulse"};let u1=class qu extends h1{constructor(e){super(e),this.events=new rt.EventEmitter,this.interval=Mc,this.interval=(e==null?void 0:e.interval)||Mc}static async init(e){const r=new qu(e);return await r.init(),r}async init(){await this.initialize()}stop(){clearInterval(this.intervalRef)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async initialize(){this.intervalRef=setInterval(()=>this.pulse(),j.toMiliseconds(this.interval))}pulse(){this.events.emit(gr.pulse)}};const d1=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,p1=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,f1=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function g1(t,e){if(t==="__proto__"||t==="constructor"&&e&&typeof e=="object"&&"prototype"in e){y1(t);return}return e}function y1(t){console.warn(`[destr] Dropping "${t}" key to prevent prototype pollution.`)}function es(t,e={}){if(typeof t!="string")return t;if(t[0]==='"'&&t[t.length-1]==='"'&&t.indexOf("\\")===-1)return t.slice(1,-1);const r=t.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!f1.test(t)){if(e.strict)throw new SyntaxError("[destr] Invalid JSON");return t}try{if(d1.test(t)||p1.test(t)){if(e.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(t,g1)}return JSON.parse(t)}catch(i){if(e.strict)throw i;return t}}function m1(t){return!t||typeof t.then!="function"?Promise.resolve(t):t}function ye(t,...e){try{return m1(t(...e))}catch(r){return Promise.reject(r)}}function w1(t){const e=typeof t;return t===null||e!=="object"&&e!=="function"}function b1(t){const e=Object.getPrototypeOf(t);return!e||e.isPrototypeOf(Object)}function ds(t){if(w1(t))return String(t);if(b1(t)||Array.isArray(t))return JSON.stringify(t);if(typeof t.toJSON=="function")return ds(t.toJSON());throw new Error("[unstorage] Cannot stringify value!")}const io="base64:";function v1(t){return typeof t=="string"?t:io+I1(t)}function E1(t){return typeof t!="string"||!t.startsWith(io)?t:_1(t.slice(io.length))}function _1(t){return globalThis.Buffer?Buffer.from(t,"base64"):Uint8Array.from(globalThis.atob(t),e=>e.codePointAt(0))}function I1(t){return globalThis.Buffer?Buffer.from(t).toString("base64"):globalThis.btoa(String.fromCodePoint(...t))}function Ue(t){var e;return t&&((e=t.split("?")[0])==null?void 0:e.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,""))||""}function $1(...t){return Ue(t.join(":"))}function ts(t){return t=Ue(t),t?t+":":""}function S1(t,e){if(e===void 0)return!0;let r=0,i=t.indexOf(":");for(;i>-1;)r++,i=t.indexOf(":",i+1);return r<=e}function O1(t,e){return e?t.startsWith(e)&&t[t.length-1]!=="$":t[t.length-1]!=="$"}const P1="memory",x1=()=>{const t=new Map;return{name:P1,getInstance:()=>t,hasItem(e){return t.has(e)},getItem(e){return t.get(e)??null},getItemRaw(e){return t.get(e)??null},setItem(e,r){t.set(e,r)},setItemRaw(e,r){t.set(e,r)},removeItem(e){t.delete(e)},getKeys(){return[...t.keys()]},clear(){t.clear()},dispose(){t.clear()}}};function A1(t={}){const e={mounts:{"":t.driver||x1()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},r=l=>{for(const h of e.mountpoints)if(l.startsWith(h))return{base:h,relativeKey:l.slice(h.length),driver:e.mounts[h]};return{base:"",relativeKey:l,driver:e.mounts[""]}},i=(l,h)=>e.mountpoints.filter(u=>u.startsWith(l)||h&&l.startsWith(u)).map(u=>({relativeBase:l.length>u.length?l.slice(u.length):void 0,mountpoint:u,driver:e.mounts[u]})),s=(l,h)=>{if(e.watching){h=Ue(h);for(const u of e.watchListeners)u(l,h)}},n=async()=>{if(!e.watching){e.watching=!0;for(const l in e.mounts)e.unwatch[l]=await Fc(e.mounts[l],s,l)}},o=async()=>{if(e.watching){for(const l in e.unwatch)await e.unwatch[l]();e.unwatch={},e.watching=!1}},a=(l,h,u)=>{const d=new Map,f=p=>{let y=d.get(p.base);return y||(y={driver:p.driver,base:p.base,items:[]},d.set(p.base,y)),y};for(const p of l){const y=typeof p=="string",w=Ue(y?p:p.key),v=y?void 0:p.value,b=y||!p.options?h:{...h,...p.options},E=r(w);f(E).items.push({key:w,value:v,relativeKey:E.relativeKey,options:b})}return Promise.all([...d.values()].map(p=>u(p))).then(p=>p.flat())},c={hasItem(l,h={}){l=Ue(l);const{relativeKey:u,driver:d}=r(l);return ye(d.hasItem,u,h)},getItem(l,h={}){l=Ue(l);const{relativeKey:u,driver:d}=r(l);return ye(d.getItem,u,h).then(f=>es(f))},getItems(l,h={}){return a(l,h,u=>u.driver.getItems?ye(u.driver.getItems,u.items.map(d=>({key:d.relativeKey,options:d.options})),h).then(d=>d.map(f=>({key:$1(u.base,f.key),value:es(f.value)}))):Promise.all(u.items.map(d=>ye(u.driver.getItem,d.relativeKey,d.options).then(f=>({key:d.key,value:es(f)})))))},getItemRaw(l,h={}){l=Ue(l);const{relativeKey:u,driver:d}=r(l);return d.getItemRaw?ye(d.getItemRaw,u,h):ye(d.getItem,u,h).then(f=>E1(f))},async setItem(l,h,u={}){if(h===void 0)return c.removeItem(l);l=Ue(l);const{relativeKey:d,driver:f}=r(l);f.setItem&&(await ye(f.setItem,d,ds(h),u),f.watch||s("update",l))},async setItems(l,h){await a(l,h,async u=>{if(u.driver.setItems)return ye(u.driver.setItems,u.items.map(d=>({key:d.relativeKey,value:ds(d.value),options:d.options})),h);u.driver.setItem&&await Promise.all(u.items.map(d=>ye(u.driver.setItem,d.relativeKey,ds(d.value),d.options)))})},async setItemRaw(l,h,u={}){if(h===void 0)return c.removeItem(l,u);l=Ue(l);const{relativeKey:d,driver:f}=r(l);if(f.setItemRaw)await ye(f.setItemRaw,d,h,u);else if(f.setItem)await ye(f.setItem,d,v1(h),u);else return;f.watch||s("update",l)},async removeItem(l,h={}){typeof h=="boolean"&&(h={removeMeta:h}),l=Ue(l);const{relativeKey:u,driver:d}=r(l);d.removeItem&&(await ye(d.removeItem,u,h),(h.removeMeta||h.removeMata)&&await ye(d.removeItem,u+"$",h),d.watch||s("remove",l))},async getMeta(l,h={}){typeof h=="boolean"&&(h={nativeOnly:h}),l=Ue(l);const{relativeKey:u,driver:d}=r(l),f=Object.create(null);if(d.getMeta&&Object.assign(f,await ye(d.getMeta,u,h)),!h.nativeOnly){const p=await ye(d.getItem,u+"$",h).then(y=>es(y));p&&typeof p=="object"&&(typeof p.atime=="string"&&(p.atime=new Date(p.atime)),typeof p.mtime=="string"&&(p.mtime=new Date(p.mtime)),Object.assign(f,p))}return f},setMeta(l,h,u={}){return this.setItem(l+"$",h,u)},removeMeta(l,h={}){return this.removeItem(l+"$",h)},async getKeys(l,h={}){var w;l=ts(l);const u=i(l,!0);let d=[];const f=[];let p=!0;for(const v of u){(w=v.driver.flags)!=null&&w.maxDepth||(p=!1);const b=await ye(v.driver.getKeys,v.relativeBase,h);for(const E of b){const I=v.mountpoint+Ue(E);d.some(x=>I.startsWith(x))||f.push(I)}d=[v.mountpoint,...d.filter(E=>!E.startsWith(v.mountpoint))]}const y=h.maxDepth!==void 0&&!p;return f.filter(v=>(!y||S1(v,h.maxDepth))&&O1(v,l))},async clear(l,h={}){l=ts(l),await Promise.all(i(l,!1).map(async u=>{if(u.driver.clear)return ye(u.driver.clear,u.relativeBase,h);if(u.driver.removeItem){const d=await u.driver.getKeys(u.relativeBase||"",h);return Promise.all(d.map(f=>u.driver.removeItem(f,h)))}}))},async dispose(){await Promise.all(Object.values(e.mounts).map(l=>zc(l)))},async watch(l){return await n(),e.watchListeners.push(l),async()=>{e.watchListeners=e.watchListeners.filter(h=>h!==l),e.watchListeners.length===0&&await o()}},async unwatch(){e.watchListeners=[],await o()},mount(l,h){if(l=ts(l),l&&e.mounts[l])throw new Error(`already mounted at ${l}`);return l&&(e.mountpoints.push(l),e.mountpoints.sort((u,d)=>d.length-u.length)),e.mounts[l]=h,e.watching&&Promise.resolve(Fc(h,s,l)).then(u=>{e.unwatch[l]=u}).catch(console.error),c},async unmount(l,h=!0){var u,d;l=ts(l),!(!l||!e.mounts[l])&&(e.watching&&l in e.unwatch&&((d=(u=e.unwatch)[l])==null||d.call(u),delete e.unwatch[l]),h&&await zc(e.mounts[l]),e.mountpoints=e.mountpoints.filter(f=>f!==l),delete e.mounts[l])},getMount(l=""){l=Ue(l)+":";const h=r(l);return{driver:h.driver,base:h.base}},getMounts(l="",h={}){return l=Ue(l),i(l,h.parents).map(d=>({driver:d.driver,base:d.mountpoint}))},keys:(l,h={})=>c.getKeys(l,h),get:(l,h={})=>c.getItem(l,h),set:(l,h,u={})=>c.setItem(l,h,u),has:(l,h={})=>c.hasItem(l,h),del:(l,h={})=>c.removeItem(l,h),remove:(l,h={})=>c.removeItem(l,h)};return c}function Fc(t,e,r){return t.watch?t.watch((i,s)=>e(i,r+s)):()=>{}}async function zc(t){typeof t.dispose=="function"&&await ye(t.dispose)}const T1="idb-keyval";var C1=(t={})=>{const e=t.base&&t.base.length>0?`${t.base}:`:"",r=s=>e+s;let i;return t.dbName&&t.storeName&&(i=Ld(t.dbName,t.storeName)),{name:T1,options:t,async hasItem(s){return!(typeof await sa(r(s),i)>"u")},async getItem(s){return await sa(r(s),i)??null},setItem(s,n){return Fd(r(s),n,i)},removeItem(s){return Md(r(s),i)},getKeys(){return qd(i)},clear(){return kd(i)}}};const R1="WALLET_CONNECT_V2_INDEXED_DB",N1="keyvaluestorage";let j1=class{constructor(){this.indexedDb=A1({driver:C1({dbName:R1,storeName:N1})})}async getKeys(){return this.indexedDb.getKeys()}async getEntries(){return(await this.indexedDb.getItems(await this.indexedDb.getKeys())).map(e=>[e.key,e.value])}async getItem(e){const r=await this.indexedDb.getItem(e);if(r!==null)return r}async setItem(e,r){await this.indexedDb.setItem(e,Dt(r))}async removeItem(e){await this.indexedDb.removeItem(e)}};var _n=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ps={exports:{}};(function(){let t;function e(){}t=e,t.prototype.getItem=function(r){return this.hasOwnProperty(r)?String(this[r]):null},t.prototype.setItem=function(r,i){this[r]=String(i)},t.prototype.removeItem=function(r){delete this[r]},t.prototype.clear=function(){const r=this;Object.keys(r).forEach(function(i){r[i]=void 0,delete r[i]})},t.prototype.key=function(r){return r=r||0,Object.keys(this)[r]},t.prototype.__defineGetter__("length",function(){return Object.keys(this).length}),typeof _n<"u"&&_n.localStorage?ps.exports=_n.localStorage:typeof window<"u"&&window.localStorage?ps.exports=window.localStorage:ps.exports=new e})();function B1(t){var e;return[t[0],lr((e=t[1])!=null?e:"")]}let D1=class{constructor(){this.localStorage=ps.exports}async getKeys(){return Object.keys(this.localStorage)}async getEntries(){return Object.entries(this.localStorage).map(B1)}async getItem(e){const r=this.localStorage.getItem(e);if(r!==null)return lr(r)}async setItem(e,r){this.localStorage.setItem(e,Dt(r))}async removeItem(e){this.localStorage.removeItem(e)}};const U1="wc_storage_version",Hc=1,L1=async(t,e,r)=>{const i=U1,s=await e.getItem(i);if(s&&s>=Hc){r(e);return}const n=await t.getKeys();if(!n.length){r(e);return}const o=[];for(;n.length;){const a=n.shift();if(!a)continue;const c=a.toLowerCase();if(c.includes("wc@")||c.includes("walletconnect")||c.includes("wc_")||c.includes("wallet_connect")){const l=await t.getItem(a);await e.setItem(a,l),o.push(a)}}await e.setItem(i,Hc),r(e),k1(t,o)},k1=async(t,e)=>{e.length&&e.forEach(async r=>{await t.removeItem(r)})};let q1=class{constructor(){this.initialized=!1,this.setInitialized=r=>{this.storage=r,this.initialized=!0};const e=new D1;this.storage=e;try{const r=new j1;L1(e,r,this.setInitialized)}catch{this.initialized=!0}}async getKeys(){return await this.initialize(),this.storage.getKeys()}async getEntries(){return await this.initialize(),this.storage.getEntries()}async getItem(e){return await this.initialize(),this.storage.getItem(e)}async setItem(e,r){return await this.initialize(),this.storage.setItem(e,r)}async removeItem(e){return await this.initialize(),this.storage.removeItem(e)}async initialize(){this.initialized||await new Promise(e=>{const r=setInterval(()=>{this.initialized&&(clearInterval(r),e())},20)})}};function M1(t){try{return JSON.stringify(t)}catch{return'"[Circular]"'}}var F1=z1;function z1(t,e,r){var i=r&&r.stringify||M1,s=1;if(typeof t=="object"&&t!==null){var n=e.length+s;if(n===1)return t;var o=new Array(n);o[0]=i(t);for(var a=1;a<n;a++)o[a]=i(e[a]);return o.join(" ")}if(typeof t!="string")return t;var c=e.length;if(c===0)return t;for(var l="",h=1-s,u=-1,d=t&&t.length||0,f=0;f<d;){if(t.charCodeAt(f)===37&&f+1<d){switch(u=u>-1?u:0,t.charCodeAt(f+1)){case 100:case 102:if(h>=c||e[h]==null)break;u<f&&(l+=t.slice(u,f)),l+=Number(e[h]),u=f+2,f++;break;case 105:if(h>=c||e[h]==null)break;u<f&&(l+=t.slice(u,f)),l+=Math.floor(Number(e[h])),u=f+2,f++;break;case 79:case 111:case 106:if(h>=c||e[h]===void 0)break;u<f&&(l+=t.slice(u,f));var p=typeof e[h];if(p==="string"){l+="'"+e[h]+"'",u=f+2,f++;break}if(p==="function"){l+=e[h].name||"<anonymous>",u=f+2,f++;break}l+=i(e[h]),u=f+2,f++;break;case 115:if(h>=c)break;u<f&&(l+=t.slice(u,f)),l+=String(e[h]),u=f+2,f++;break;case 37:u<f&&(l+=t.slice(u,f)),l+="%",u=f+2,f++,h--;break}++h}++f}return u===-1?t:(u<d&&(l+=t.slice(u)),l)}const Vc=F1;var kr=It;const Ri=X1().console||{},H1={mapHttpRequest:rs,mapHttpResponse:rs,wrapRequestSerializer:In,wrapResponseSerializer:In,wrapErrorSerializer:In,req:rs,res:rs,err:Y1};function V1(t,e){return Array.isArray(t)?t.filter(function(i){return i!=="!stdSerializers.err"}):t===!0?Object.keys(e):!1}function It(t){t=t||{},t.browser=t.browser||{};const e=t.browser.transmit;if(e&&typeof e.send!="function")throw Error("pino: transmit option must have a send function");const r=t.browser.write||Ri;t.browser.write&&(t.browser.asObject=!0);const i=t.serializers||{},s=V1(t.browser.serialize,i);let n=t.browser.serialize;Array.isArray(t.browser.serialize)&&t.browser.serialize.indexOf("!stdSerializers.err")>-1&&(n=!1);const o=["error","fatal","warn","info","debug","trace"];typeof r=="function"&&(r.error=r.fatal=r.warn=r.info=r.debug=r.trace=r),t.enabled===!1&&(t.level="silent");const a=t.level||"info",c=Object.create(r);c.log||(c.log=Ni),Object.defineProperty(c,"levelVal",{get:h}),Object.defineProperty(c,"level",{get:u,set:d});const l={transmit:e,serialize:s,asObject:t.browser.asObject,levels:o,timestamp:J1(t)};c.levels=It.levels,c.level=a,c.setMaxListeners=c.getMaxListeners=c.emit=c.addListener=c.on=c.prependListener=c.once=c.prependOnceListener=c.removeListener=c.removeAllListeners=c.listeners=c.listenerCount=c.eventNames=c.write=c.flush=Ni,c.serializers=i,c._serialize=s,c._stdErrSerialize=n,c.child=f,e&&(c._logEvent=so());function h(){return this.level==="silent"?1/0:this.levels.values[this.level]}function u(){return this._level}function d(p){if(p!=="silent"&&!this.levels.values[p])throw Error("unknown level "+p);this._level=p,$r(l,c,"error","log"),$r(l,c,"fatal","error"),$r(l,c,"warn","error"),$r(l,c,"info","log"),$r(l,c,"debug","log"),$r(l,c,"trace","log")}function f(p,y){if(!p)throw new Error("missing bindings for child Pino");y=y||{},s&&p.serializers&&(y.serializers=p.serializers);const w=y.serializers;if(s&&w){var v=Object.assign({},i,w),b=t.browser.serialize===!0?Object.keys(v):s;delete p.serializers,qs([p],b,v,this._stdErrSerialize)}function E(I){this._childLevel=(I._childLevel|0)+1,this.error=Sr(I,p,"error"),this.fatal=Sr(I,p,"fatal"),this.warn=Sr(I,p,"warn"),this.info=Sr(I,p,"info"),this.debug=Sr(I,p,"debug"),this.trace=Sr(I,p,"trace"),v&&(this.serializers=v,this._serialize=b),e&&(this._logEvent=so([].concat(I._logEvent.bindings,p)))}return E.prototype=this,new E(this)}return c}It.levels={values:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},labels:{10:"trace",20:"debug",30:"info",40:"warn",50:"error",60:"fatal"}};It.stdSerializers=H1;It.stdTimeFunctions=Object.assign({},{nullTime:Mu,epochTime:Fu,unixTime:Z1,isoTime:Q1});function $r(t,e,r,i){const s=Object.getPrototypeOf(e);e[r]=e.levelVal>e.levels.values[r]?Ni:s[r]?s[r]:Ri[r]||Ri[i]||Ni,K1(t,e,r)}function K1(t,e,r){!t.transmit&&e[r]===Ni||(e[r]=function(i){return function(){const n=t.timestamp(),o=new Array(arguments.length),a=Object.getPrototypeOf&&Object.getPrototypeOf(this)===Ri?Ri:this;for(var c=0;c<o.length;c++)o[c]=arguments[c];if(t.serialize&&!t.asObject&&qs(o,this._serialize,this.serializers,this._stdErrSerialize),t.asObject?i.call(a,W1(this,r,o,n)):i.apply(a,o),t.transmit){const l=t.transmit.level||e.level,h=It.levels.values[l],u=It.levels.values[r];if(u<h)return;G1(this,{ts:n,methodLevel:r,methodValue:u,transmitValue:It.levels.values[t.transmit.level||e.level],send:t.transmit.send,val:e.levelVal},o)}}}(e[r]))}function W1(t,e,r,i){t._serialize&&qs(r,t._serialize,t.serializers,t._stdErrSerialize);const s=r.slice();let n=s[0];const o={};i&&(o.time=i),o.level=It.levels.values[e];let a=(t._childLevel|0)+1;if(a<1&&(a=1),n!==null&&typeof n=="object"){for(;a--&&typeof s[0]=="object";)Object.assign(o,s.shift());n=s.length?Vc(s.shift(),s):void 0}else typeof n=="string"&&(n=Vc(s.shift(),s));return n!==void 0&&(o.msg=n),o}function qs(t,e,r,i){for(const s in t)if(i&&t[s]instanceof Error)t[s]=It.stdSerializers.err(t[s]);else if(typeof t[s]=="object"&&!Array.isArray(t[s]))for(const n in t[s])e&&e.indexOf(n)>-1&&n in r&&(t[s][n]=r[n](t[s][n]))}function Sr(t,e,r){return function(){const i=new Array(1+arguments.length);i[0]=e;for(var s=1;s<i.length;s++)i[s]=arguments[s-1];return t[r].apply(this,i)}}function G1(t,e,r){const i=e.send,s=e.ts,n=e.methodLevel,o=e.methodValue,a=e.val,c=t._logEvent.bindings;qs(r,t._serialize||Object.keys(t.serializers),t.serializers,t._stdErrSerialize===void 0?!0:t._stdErrSerialize),t._logEvent.ts=s,t._logEvent.messages=r.filter(function(l){return c.indexOf(l)===-1}),t._logEvent.level.label=n,t._logEvent.level.value=o,i(n,t._logEvent,a),t._logEvent=so(c)}function so(t){return{ts:0,messages:[],bindings:t||[],level:{label:"",value:0}}}function Y1(t){const e={type:t.constructor.name,msg:t.message,stack:t.stack};for(const r in t)e[r]===void 0&&(e[r]=t[r]);return e}function J1(t){return typeof t.timestamp=="function"?t.timestamp:t.timestamp===!1?Mu:Fu}function rs(){return{}}function In(t){return t}function Ni(){}function Mu(){return!1}function Fu(){return Date.now()}function Z1(){return Math.round(Date.now()/1e3)}function Q1(){return new Date(Date.now()).toISOString()}function X1(){function t(e){return typeof e<"u"&&e}try{return typeof globalThis<"u"||Object.defineProperty(Object.prototype,"globalThis",{get:function(){return delete Object.prototype.globalThis,this.globalThis=this},configurable:!0}),globalThis}catch{return t(self)||t(window)||t(this)||{}}}const zi=ih(kr),eE={level:"info"},Hi="custom_context",Vo=1e3*1024;let tE=class{constructor(e){this.nodeValue=e,this.sizeInBytes=new TextEncoder().encode(this.nodeValue).length,this.next=null}get value(){return this.nodeValue}get size(){return this.sizeInBytes}},Kc=class{constructor(e){this.head=null,this.tail=null,this.lengthInNodes=0,this.maxSizeInBytes=e,this.sizeInBytes=0}append(e){const r=new tE(e);if(r.size>this.maxSizeInBytes)throw new Error(`[LinkedList] Value too big to insert into list: ${e} with size ${r.size}`);for(;this.size+r.size>this.maxSizeInBytes;)this.shift();this.head?(this.tail&&(this.tail.next=r),this.tail=r):(this.head=r,this.tail=r),this.lengthInNodes++,this.sizeInBytes+=r.size}shift(){if(!this.head)return;const e=this.head;this.head=this.head.next,this.head||(this.tail=null),this.lengthInNodes--,this.sizeInBytes-=e.size}toArray(){const e=[];let r=this.head;for(;r!==null;)e.push(r.value),r=r.next;return e}get length(){return this.lengthInNodes}get size(){return this.sizeInBytes}toOrderedArray(){return Array.from(this)}[Symbol.iterator](){let e=this.head;return{next:()=>{if(!e)return{done:!0,value:null};const r=e.value;return e=e.next,{done:!1,value:r}}}}},zu=class{constructor(e,r=Vo){this.level=e??"error",this.levelValue=kr.levels.values[this.level],this.MAX_LOG_SIZE_IN_BYTES=r,this.logs=new Kc(this.MAX_LOG_SIZE_IN_BYTES)}forwardToConsole(e,r){r===kr.levels.values.error?console.error(e):r===kr.levels.values.warn?console.warn(e):r===kr.levels.values.debug?console.debug(e):r===kr.levels.values.trace?console.trace(e):console.log(e)}appendToLogs(e){this.logs.append(Dt({timestamp:new Date().toISOString(),log:e}));const r=typeof e=="string"?JSON.parse(e).level:e.level;r>=this.levelValue&&this.forwardToConsole(e,r)}getLogs(){return this.logs}clearLogs(){this.logs=new Kc(this.MAX_LOG_SIZE_IN_BYTES)}getLogArray(){return Array.from(this.logs)}logsToBlob(e){const r=this.getLogArray();return r.push(Dt({extraMetadata:e})),new Blob(r,{type:"application/json"})}},rE=class{constructor(e,r=Vo){this.baseChunkLogger=new zu(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}downloadLogsBlobInBrowser(e){const r=URL.createObjectURL(this.logsToBlob(e)),i=document.createElement("a");i.href=r,i.download=`walletconnect-logs-${new Date().toISOString()}.txt`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(r)}},iE=class{constructor(e,r=Vo){this.baseChunkLogger=new zu(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}};var sE=Object.defineProperty,nE=Object.defineProperties,oE=Object.getOwnPropertyDescriptors,Wc=Object.getOwnPropertySymbols,aE=Object.prototype.hasOwnProperty,cE=Object.prototype.propertyIsEnumerable,Gc=(t,e,r)=>e in t?sE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Is=(t,e)=>{for(var r in e||(e={}))aE.call(e,r)&&Gc(t,r,e[r]);if(Wc)for(var r of Wc(e))cE.call(e,r)&&Gc(t,r,e[r]);return t},$s=(t,e)=>nE(t,oE(e));function Ms(t){return $s(Is({},t),{level:(t==null?void 0:t.level)||eE.level})}function lE(t,e=Hi){return t[e]||""}function hE(t,e,r=Hi){return t[r]=e,t}function Me(t,e=Hi){let r="";return typeof t.bindings>"u"?r=lE(t,e):r=t.bindings().context||"",r}function uE(t,e,r=Hi){const i=Me(t,r);return i.trim()?`${i}/${e}`:e}function je(t,e,r=Hi){const i=uE(t,e,r),s=t.child({context:i});return hE(s,i,r)}function dE(t){var e,r;const i=new rE((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:zi($s(Is({},t.opts),{level:"trace",browser:$s(Is({},(r=t.opts)==null?void 0:r.browser),{write:s=>i.write(s)})})),chunkLoggerController:i}}function pE(t){var e;const r=new iE((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:zi($s(Is({},t.opts),{level:"trace"}),r),chunkLoggerController:r}}function fE(t){return typeof t.loggerOverride<"u"&&typeof t.loggerOverride!="string"?{logger:t.loggerOverride,chunkLoggerController:null}:typeof window<"u"?dE(t):pE(t)}var gE=Object.defineProperty,yE=(t,e,r)=>e in t?gE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Yc=(t,e,r)=>yE(t,typeof e!="symbol"?e+"":e,r);let mE=class extends fr{constructor(e){super(),this.opts=e,Yc(this,"protocol","wc"),Yc(this,"version",2)}};var wE=Object.defineProperty,bE=(t,e,r)=>e in t?wE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,vE=(t,e,r)=>bE(t,e+"",r);let EE=class extends fr{constructor(e,r){super(),this.core=e,this.logger=r,vE(this,"records",new Map)}},_E=class{constructor(e,r){this.logger=e,this.core=r}};class IE extends fr{constructor(e,r){super(),this.relayer=e,this.logger=r}}let $E=class extends fr{constructor(e){super()}},SE=class{constructor(e,r,i,s){this.core=e,this.logger=r,this.name=i}},OE=class extends fr{constructor(e,r){super(),this.relayer=e,this.logger=r}},PE=class extends fr{constructor(e,r){super(),this.core=e,this.logger=r}},xE=class{constructor(e,r,i){this.core=e,this.logger=r,this.store=i}},AE=class{constructor(e,r){this.projectId=e,this.logger=r}},TE=class{constructor(e,r,i){this.core=e,this.logger=r,this.telemetryEnabled=i}};var CE=Object.defineProperty,RE=(t,e,r)=>e in t?CE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Jc=(t,e,r)=>RE(t,typeof e!="symbol"?e+"":e,r);let NE=class{constructor(e){this.opts=e,Jc(this,"protocol","wc"),Jc(this,"version",2)}},jE=class{constructor(e){this.client=e}};const BE="PARSE_ERROR",DE="INVALID_REQUEST",UE="METHOD_NOT_FOUND",LE="INVALID_PARAMS",Hu="INTERNAL_ERROR",Ko="SERVER_ERROR",kE=[-32700,-32600,-32601,-32602,-32603],_i={[BE]:{code:-32700,message:"Parse error"},[DE]:{code:-32600,message:"Invalid Request"},[UE]:{code:-32601,message:"Method not found"},[LE]:{code:-32602,message:"Invalid params"},[Hu]:{code:-32603,message:"Internal error"},[Ko]:{code:-32e3,message:"Server error"}},Vu=Ko;function qE(t){return kE.includes(t)}function Zc(t){return Object.keys(_i).includes(t)?_i[t]:_i[Vu]}function ME(t){const e=Object.values(_i).find(r=>r.code===t);return e||_i[Vu]}function Ku(t,e,r){return t.message.includes("getaddrinfo ENOTFOUND")||t.message.includes("connect ECONNREFUSED")?new Error(`Unavailable ${r} RPC url at ${e}`):t}var Wu={};/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var no=function(t,e){return no=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var s in i)i.hasOwnProperty(s)&&(r[s]=i[s])},no(t,e)};function FE(t,e){no(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var oo=function(){return oo=Object.assign||function(e){for(var r,i=1,s=arguments.length;i<s;i++){r=arguments[i];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},oo.apply(this,arguments)};function zE(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,i=Object.getOwnPropertySymbols(t);s<i.length;s++)e.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(t,i[s])&&(r[i[s]]=t[i[s]]);return r}function HE(t,e,r,i){var s=arguments.length,n=s<3?e:i===null?i=Object.getOwnPropertyDescriptor(e,r):i,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(t,e,r,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(n=(s<3?o(n):s>3?o(e,r,n):o(e,r))||n);return s>3&&n&&Object.defineProperty(e,r,n),n}function VE(t,e){return function(r,i){e(r,i,t)}}function KE(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function WE(t,e,r,i){function s(n){return n instanceof r?n:new r(function(o){o(n)})}return new(r||(r=Promise))(function(n,o){function a(h){try{l(i.next(h))}catch(u){o(u)}}function c(h){try{l(i.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):s(h.value).then(a,c)}l((i=i.apply(t,e||[])).next())})}function GE(t,e){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,s,n,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(l){return function(h){return c([l,h])}}function c(l){if(i)throw new TypeError("Generator is already executing.");for(;r;)try{if(i=1,s&&(n=l[0]&2?s.return:l[0]?s.throw||((n=s.return)&&n.call(s),0):s.next)&&!(n=n.call(s,l[1])).done)return n;switch(s=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,s=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){r.label=l[1];break}if(l[0]===6&&r.label<n[1]){r.label=n[1],n=l;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(l);break}n[2]&&r.ops.pop(),r.trys.pop();continue}l=e.call(t,r)}catch(h){l=[6,h],s=0}finally{i=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function YE(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}function JE(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function ao(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Gu(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var i=r.call(t),s,n=[],o;try{for(;(e===void 0||e-- >0)&&!(s=i.next()).done;)n.push(s.value)}catch(a){o={error:a}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return n}function ZE(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(Gu(arguments[e]));return t}function QE(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var i=Array(t),s=0,e=0;e<r;e++)for(var n=arguments[e],o=0,a=n.length;o<a;o++,s++)i[s]=n[o];return i}function ji(t){return this instanceof ji?(this.v=t,this):new ji(t)}function XE(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=r.apply(t,e||[]),s,n=[];return s={},o("next"),o("throw"),o("return"),s[Symbol.asyncIterator]=function(){return this},s;function o(d){i[d]&&(s[d]=function(f){return new Promise(function(p,y){n.push([d,f,p,y])>1||a(d,f)})})}function a(d,f){try{c(i[d](f))}catch(p){u(n[0][3],p)}}function c(d){d.value instanceof ji?Promise.resolve(d.value.v).then(l,h):u(n[0][2],d)}function l(d){a("next",d)}function h(d){a("throw",d)}function u(d,f){d(f),n.shift(),n.length&&a(n[0][0],n[0][1])}}function e_(t){var e,r;return e={},i("next"),i("throw",function(s){throw s}),i("return"),e[Symbol.iterator]=function(){return this},e;function i(s,n){e[s]=t[s]?function(o){return(r=!r)?{value:ji(t[s](o)),done:s==="return"}:n?n(o):o}:n}}function t_(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof ao=="function"?ao(t):t[Symbol.iterator](),r={},i("next"),i("throw"),i("return"),r[Symbol.asyncIterator]=function(){return this},r);function i(n){r[n]=t[n]&&function(o){return new Promise(function(a,c){o=t[n](o),s(a,c,o.done,o.value)})}}function s(n,o,a,c){Promise.resolve(c).then(function(l){n({value:l,done:a})},o)}}function r_(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function i_(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function s_(t){return t&&t.__esModule?t:{default:t}}function n_(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function o_(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}const a_=Object.freeze(Object.defineProperty({__proto__:null,get __assign(){return oo},__asyncDelegator:e_,__asyncGenerator:XE,__asyncValues:t_,__await:ji,__awaiter:WE,__classPrivateFieldGet:n_,__classPrivateFieldSet:o_,__createBinding:YE,__decorate:HE,__exportStar:JE,__extends:FE,__generator:GE,__importDefault:s_,__importStar:i_,__makeTemplateObject:r_,__metadata:KE,__param:VE,__read:Gu,__rest:zE,__spread:ZE,__spreadArrays:QE,__values:ao},Symbol.toStringTag,{value:"Module"})),c_=th(a_);var At={},Qc;function l_(){if(Qc)return At;Qc=1,Object.defineProperty(At,"__esModule",{value:!0}),At.isBrowserCryptoAvailable=At.getSubtleCrypto=At.getBrowerCrypto=void 0;function t(){return(vt===null||vt===void 0?void 0:vt.crypto)||(vt===null||vt===void 0?void 0:vt.msCrypto)||{}}At.getBrowerCrypto=t;function e(){const i=t();return i.subtle||i.webkitSubtle}At.getSubtleCrypto=e;function r(){return!!t()&&!!e()}return At.isBrowserCryptoAvailable=r,At}var Tt={},Xc;function h_(){if(Xc)return Tt;Xc=1,Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.isBrowser=Tt.isNode=Tt.isReactNative=void 0;function t(){return typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"}Tt.isReactNative=t;function e(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}Tt.isNode=e;function r(){return!t()&&!e()}return Tt.isBrowser=r,Tt}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=c_;e.__exportStar(l_(),t),e.__exportStar(h_(),t)})(Wu);function wt(t=3){const e=Date.now()*Math.pow(10,t),r=Math.floor(Math.random()*Math.pow(10,t));return e+r}function nr(t=6){return BigInt(wt(t))}function Ht(t,e,r){return{id:r||wt(),jsonrpc:"2.0",method:t,params:e}}function Fs(t,e){return{id:t,jsonrpc:"2.0",result:e}}function zs(t,e,r){return{id:t,jsonrpc:"2.0",error:u_(e)}}function u_(t,e){return typeof t>"u"?Zc(Hu):(typeof t=="string"&&(t=Object.assign(Object.assign({},Zc(Ko)),{message:t})),qE(t.code)&&(t=ME(t.code)),t)}class d_{}class p_ extends d_{constructor(){super()}}class f_ extends p_{constructor(e){super()}}const g_="^https?:",y_="^wss?:";function m_(t){const e=t.match(new RegExp(/^\w+:/,"gi"));if(!(!e||!e.length))return e[0]}function Yu(t,e){const r=m_(t);return typeof r>"u"?!1:new RegExp(e).test(r)}function el(t){return Yu(t,g_)}function tl(t){return Yu(t,y_)}function w_(t){return new RegExp("wss?://localhost(:d{2,5})?").test(t)}function Ju(t){return typeof t=="object"&&"id"in t&&"jsonrpc"in t&&t.jsonrpc==="2.0"}function Wo(t){return Ju(t)&&"method"in t}function Hs(t){return Ju(t)&&(bt(t)||et(t))}function bt(t){return"result"in t}function et(t){return"error"in t}let it=class extends f_{constructor(e){super(e),this.events=new rt.EventEmitter,this.hasRegisteredEventListeners=!1,this.connection=this.setConnection(e),this.connection.connected&&this.registerEventListeners()}async connect(e=this.connection){await this.open(e)}async disconnect(){await this.close()}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async request(e,r){return this.requestStrict(Ht(e.method,e.params||[],e.id||nr().toString()),r)}async requestStrict(e,r){return new Promise(async(i,s)=>{if(!this.connection.connected)try{await this.open()}catch(n){s(n)}this.events.on(`${e.id}`,n=>{et(n)?s(n.error):i(n.result)});try{await this.connection.send(e,r)}catch(n){s(n)}})}setConnection(e=this.connection){return e}onPayload(e){this.events.emit("payload",e),Hs(e)?this.events.emit(`${e.id}`,e):this.events.emit("message",{type:e.method,data:e.params})}onClose(e){e&&e.code===3e3&&this.events.emit("error",new Error(`WebSocket connection closed abnormally with code: ${e.code} ${e.reason?`(${e.reason})`:""}`)),this.events.emit("disconnect")}async open(e=this.connection){this.connection===e&&this.connection.connected||(this.connection.connected&&this.close(),typeof e=="string"&&(await this.connection.open(e),e=this.connection),this.connection=this.setConnection(e),await this.connection.open(),this.registerEventListeners(),this.events.emit("connect"))}async close(){await this.connection.close()}registerEventListeners(){this.hasRegisteredEventListeners||(this.connection.on("payload",e=>this.onPayload(e)),this.connection.on("close",e=>this.onClose(e)),this.connection.on("error",e=>this.events.emit("error",e)),this.connection.on("register_error",e=>this.onClose()),this.hasRegisteredEventListeners=!0)}};const b_=()=>typeof WebSocket<"u"?WebSocket:typeof global<"u"&&typeof global.WebSocket<"u"?global.WebSocket:typeof window<"u"&&typeof window.WebSocket<"u"?window.WebSocket:typeof self<"u"&&typeof self.WebSocket<"u"?self.WebSocket:require("ws"),v_=()=>typeof WebSocket<"u"||typeof global<"u"&&typeof global.WebSocket<"u"||typeof window<"u"&&typeof window.WebSocket<"u"||typeof self<"u"&&typeof self.WebSocket<"u",rl=t=>t.split("?")[0],il=10,E_=b_();let __=class{constructor(e){if(this.url=e,this.events=new rt.EventEmitter,this.registering=!1,!tl(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);this.url=e}get connected(){return typeof this.socket<"u"}get connecting(){return this.registering}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async open(e=this.url){await this.register(e)}async close(){return new Promise((e,r)=>{if(typeof this.socket>"u"){r(new Error("Connection already closed"));return}this.socket.onclose=i=>{this.onClose(i),e()},this.socket.close()})}async send(e){typeof this.socket>"u"&&(this.socket=await this.register());try{this.socket.send(Dt(e))}catch(r){this.onError(e.id,r)}}register(e=this.url){if(!tl(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);if(this.registering){const r=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=r||this.events.listenerCount("open")>=r)&&this.events.setMaxListeners(r+1),new Promise((i,s)=>{this.events.once("register_error",n=>{this.resetMaxListeners(),s(n)}),this.events.once("open",()=>{if(this.resetMaxListeners(),typeof this.socket>"u")return s(new Error("WebSocket connection is missing or invalid"));i(this.socket)})})}return this.url=e,this.registering=!0,new Promise((r,i)=>{const s=Wu.isReactNative()?void 0:{rejectUnauthorized:!w_(e)},n=new E_(e,[],s);v_()?n.onerror=o=>{const a=o;i(this.emitError(a.error))}:n.on("error",o=>{i(this.emitError(o))}),n.onopen=()=>{this.onOpen(n),r(n)}})}onOpen(e){e.onmessage=r=>this.onPayload(r),e.onclose=r=>this.onClose(r),this.socket=e,this.registering=!1,this.events.emit("open")}onClose(e){this.socket=void 0,this.registering=!1,this.events.emit("close",e)}onPayload(e){if(typeof e.data>"u")return;const r=typeof e.data=="string"?lr(e.data):e.data;this.events.emit("payload",r)}onError(e,r){const i=this.parseError(r),s=i.message||i.toString(),n=zs(e,s);this.events.emit("payload",n)}parseError(e,r=this.url){return Ku(e,rl(r),"WS")}resetMaxListeners(){this.events.getMaxListeners()>il&&this.events.setMaxListeners(il)}emitError(e){const r=this.parseError(new Error((e==null?void 0:e.message)||`WebSocket connection failed for host: ${rl(this.url)}`));return this.events.emit("register_error",r),r}};var I_={};const Zu="wc",Qu=2,co="core",$t=`${Zu}@2:${co}:`,$_={logger:"error"},S_={database:":memory:"},O_="crypto",sl="client_ed25519_seed",P_=j.ONE_DAY,x_="keychain",A_="0.3",T_="messages",C_="0.3",nl=j.SIX_HOURS,R_="publisher",Xu="irn",N_="error",ed="wss://relay.walletconnect.org",j_="relayer",we={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},B_="_subscription",Ze={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},D_=.1,lo="2.21.1",ae={link_mode:"link_mode",relay:"relay"},fs={inbound:"inbound",outbound:"outbound"},U_="0.3",L_="WALLETCONNECT_CLIENT_ID",ol="WALLETCONNECT_LINK_MODE_APPS",He={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},k_="subscription",q_="0.3",M_="pairing",F_="0.3",pi={wc_pairingDelete:{req:{ttl:j.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:j.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:j.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:j.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:j.ONE_DAY,prompt:!1,tag:0},res:{ttl:j.ONE_DAY,prompt:!1,tag:0}}},rr={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},ot={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},z_="history",H_="0.3",V_="expirer",Xe={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},K_="0.3",W_="verify-api",G_="https://verify.walletconnect.com",td="https://verify.walletconnect.org",Ii=td,Y_=`${Ii}/v3`,J_=[G_,td],Z_="echo",Q_="https://echo.walletconnect.com",mt={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},Ct={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},at={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},Jt={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},Zt={authenticated_session_approve_started:"authenticated_session_approve_started",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve"},fi={no_internet_connection:"no_internet_connection",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},X_=.1,eI="event-client",tI=86400,rI="https://pulse.walletconnect.org/batch";function iI(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var s=0;s<t.length;s++){var n=t.charAt(s),o=n.charCodeAt(0);if(r[o]!==255)throw new TypeError(n+" is ambiguous");r[o]=s}var a=t.length,c=t.charAt(0),l=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function u(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,w=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*h+1>>>0,I=new Uint8Array(E);v!==b;){for(var x=p[v],A=0,P=E-1;(x!==0||A<w)&&P!==-1;P--,A++)x+=256*I[P]>>>0,I[P]=x%a>>>0,x=x/a>>>0;if(x!==0)throw new Error("Non-zero carry");w=A,v++}for(var C=E-w;C!==E&&I[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(I[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var w=0,v=0;p[y]===c;)w++,y++;for(var b=(p.length-y)*l+1>>>0,E=new Uint8Array(b);p[y];){var I=r[p.charCodeAt(y)];if(I===255)return;for(var x=0,A=b-1;(I!==0||x<v)&&A!==-1;A--,x++)I+=a*E[A]>>>0,E[A]=I%256>>>0,I=I/256>>>0;if(I!==0)throw new Error("Non-zero carry");v=x,y++}if(p[y]!==" "){for(var P=b-v;P!==b&&E[P]===0;)P++;for(var C=new Uint8Array(w+(b-P)),S=w;P!==b;)C[S++]=E[P++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:u,decodeUnsafe:d,decode:f}}var sI=iI,nI=sI;const rd=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},oI=t=>new TextEncoder().encode(t),aI=t=>new TextDecoder().decode(t);class cI{constructor(e,r,i){this.name=e,this.prefix=r,this.baseEncode=i}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class lI{constructor(e,r,i){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=i}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return id(this,e)}}class hI{constructor(e){this.decoders=e}or(e){return id(this,e)}decode(e){const r=e[0],i=this.decoders[r];if(i)return i.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const id=(t,e)=>new hI({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class uI{constructor(e,r,i,s){this.name=e,this.prefix=r,this.baseEncode=i,this.baseDecode=s,this.encoder=new cI(e,r,i),this.decoder=new lI(e,r,s)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Vs=({name:t,prefix:e,encode:r,decode:i})=>new uI(t,e,r,i),Vi=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:s}=nI(r,e);return Vs({prefix:t,name:e,encode:i,decode:n=>rd(s(n))})},dI=(t,e,r,i)=>{const s={};for(let h=0;h<e.length;++h)s[e[h]]=h;let n=t.length;for(;t[n-1]==="=";)--n;const o=new Uint8Array(n*r/8|0);let a=0,c=0,l=0;for(let h=0;h<n;++h){const u=s[t[h]];if(u===void 0)throw new SyntaxError(`Non-${i} character`);c=c<<r|u,a+=r,a>=8&&(a-=8,o[l++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},pI=(t,e,r)=>{const i=e[e.length-1]==="=",s=(1<<r)-1;let n="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,n+=e[s&a>>o];if(o&&(n+=e[s&a<<r-o]),i)for(;n.length*r&7;)n+="=";return n},xe=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>Vs({prefix:e,name:t,encode(s){return pI(s,i,r)},decode(s){return dI(s,i,r,t)}}),fI=Vs({prefix:"\0",name:"identity",encode:t=>aI(t),decode:t=>oI(t)});var gI=Object.freeze({__proto__:null,identity:fI});const yI=xe({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var mI=Object.freeze({__proto__:null,base2:yI});const wI=xe({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var bI=Object.freeze({__proto__:null,base8:wI});const vI=Vi({prefix:"9",name:"base10",alphabet:"0123456789"});var EI=Object.freeze({__proto__:null,base10:vI});const _I=xe({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),II=xe({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var $I=Object.freeze({__proto__:null,base16:_I,base16upper:II});const SI=xe({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),OI=xe({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),PI=xe({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),xI=xe({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),AI=xe({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),TI=xe({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),CI=xe({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),RI=xe({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),NI=xe({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var jI=Object.freeze({__proto__:null,base32:SI,base32upper:OI,base32pad:PI,base32padupper:xI,base32hex:AI,base32hexupper:TI,base32hexpad:CI,base32hexpadupper:RI,base32z:NI});const BI=Vi({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),DI=Vi({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var UI=Object.freeze({__proto__:null,base36:BI,base36upper:DI});const LI=Vi({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),kI=Vi({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var qI=Object.freeze({__proto__:null,base58btc:LI,base58flickr:kI});const MI=xe({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),FI=xe({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),zI=xe({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),HI=xe({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var VI=Object.freeze({__proto__:null,base64:MI,base64pad:FI,base64url:zI,base64urlpad:HI});const sd=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),KI=sd.reduce((t,e,r)=>(t[r]=e,t),[]),WI=sd.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function GI(t){return t.reduce((e,r)=>(e+=KI[r],e),"")}function YI(t){const e=[];for(const r of t){const i=WI[r.codePointAt(0)];if(i===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(i)}return new Uint8Array(e)}const JI=Vs({prefix:"🚀",name:"base256emoji",encode:GI,decode:YI});var ZI=Object.freeze({__proto__:null,base256emoji:JI}),QI=nd,al=128,XI=-128,e$=Math.pow(2,31);function nd(t,e,r){e=e||[],r=r||0;for(var i=r;t>=e$;)e[r++]=t&255|al,t/=128;for(;t&XI;)e[r++]=t&255|al,t>>>=7;return e[r]=t|0,nd.bytes=r-i+1,e}var t$=ho,r$=128,cl=127;function ho(t,i){var r=0,i=i||0,s=0,n=i,o,a=t.length;do{if(n>=a)throw ho.bytes=0,new RangeError("Could not decode varint");o=t[n++],r+=s<28?(o&cl)<<s:(o&cl)*Math.pow(2,s),s+=7}while(o>=r$);return ho.bytes=n-i,r}var i$=Math.pow(2,7),s$=Math.pow(2,14),n$=Math.pow(2,21),o$=Math.pow(2,28),a$=Math.pow(2,35),c$=Math.pow(2,42),l$=Math.pow(2,49),h$=Math.pow(2,56),u$=Math.pow(2,63),d$=function(t){return t<i$?1:t<s$?2:t<n$?3:t<o$?4:t<a$?5:t<c$?6:t<l$?7:t<h$?8:t<u$?9:10},p$={encode:QI,decode:t$,encodingLength:d$},od=p$;const ll=(t,e,r=0)=>(od.encode(t,e,r),e),hl=t=>od.encodingLength(t),uo=(t,e)=>{const r=e.byteLength,i=hl(t),s=i+hl(r),n=new Uint8Array(s+r);return ll(t,n,0),ll(r,n,i),n.set(e,s),new f$(t,r,e,n)};class f${constructor(e,r,i,s){this.code=e,this.size=r,this.digest=i,this.bytes=s}}const ad=({name:t,code:e,encode:r})=>new g$(t,e,r);class g${constructor(e,r,i){this.name=e,this.code=r,this.encode=i}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?uo(this.code,r):r.then(i=>uo(this.code,i))}else throw Error("Unknown type, must be binary type")}}const cd=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),y$=ad({name:"sha2-256",code:18,encode:cd("SHA-256")}),m$=ad({name:"sha2-512",code:19,encode:cd("SHA-512")});var w$=Object.freeze({__proto__:null,sha256:y$,sha512:m$});const ld=0,b$="identity",hd=rd,v$=t=>uo(ld,hd(t)),E$={code:ld,name:b$,encode:hd,digest:v$};var _$=Object.freeze({__proto__:null,identity:E$});new TextEncoder,new TextDecoder;const ul={...gI,...mI,...bI,...EI,...$I,...jI,...UI,...qI,...VI,...ZI};({...w$,..._$});function I$(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function ud(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const dl=ud("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),$n=ud("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=I$(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),$$={utf8:dl,"utf-8":dl,hex:ul.base16,latin1:$n,ascii:$n,binary:$n,...ul};function S$(t,e="utf8"){const r=$$[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}var O$=Object.defineProperty,P$=(t,e,r)=>e in t?O$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,gt=(t,e,r)=>P$(t,typeof e!="symbol"?e+"":e,r);class x${constructor(e,r){this.core=e,this.logger=r,gt(this,"keychain",new Map),gt(this,"name",x_),gt(this,"version",A_),gt(this,"initialized",!1),gt(this,"storagePrefix",$t),gt(this,"init",async()=>{if(!this.initialized){const i=await this.getKeyChain();typeof i<"u"&&(this.keychain=i),this.initialized=!0}}),gt(this,"has",i=>(this.isInitialized(),this.keychain.has(i))),gt(this,"set",async(i,s)=>{this.isInitialized(),this.keychain.set(i,s),await this.persist()}),gt(this,"get",i=>{this.isInitialized();const s=this.keychain.get(i);if(typeof s>"u"){const{message:n}=B("NO_MATCHING_KEY",`${this.name}: ${i}`);throw new Error(n)}return s}),gt(this,"del",async i=>{this.isInitialized(),this.keychain.delete(i),await this.persist()}),this.core=e,this.logger=je(r,this.name)}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,Vn(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Kn(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var A$=Object.defineProperty,T$=(t,e,r)=>e in t?A$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ie=(t,e,r)=>T$(t,typeof e!="symbol"?e+"":e,r);class C${constructor(e,r,i){this.core=e,this.logger=r,Ie(this,"name",O_),Ie(this,"keychain"),Ie(this,"randomSessionIdentifier",ro()),Ie(this,"initialized",!1),Ie(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),Ie(this,"hasKeys",s=>(this.isInitialized(),this.keychain.has(s))),Ie(this,"getClientId",async()=>{this.isInitialized();const s=await this.getClientSeed(),n=Va(s);return Gh(n.publicKey)}),Ie(this,"generateKeyPair",()=>{this.isInitialized();const s=a0();return this.setPrivateKey(s.publicKey,s.privateKey)}),Ie(this,"signJWT",async s=>{this.isInitialized();const n=await this.getClientSeed(),o=Va(n),a=this.randomSessionIdentifier;return await $m(a,s,P_,o)}),Ie(this,"generateSharedKey",(s,n,o)=>{this.isInitialized();const a=this.getPrivateKey(s),c=c0(a,n);return this.setSymKey(c,o)}),Ie(this,"setSymKey",async(s,n)=>{this.isInitialized();const o=n||us(s);return await this.keychain.set(o,s),o}),Ie(this,"deleteKeyPair",async s=>{this.isInitialized(),await this.keychain.del(s)}),Ie(this,"deleteSymKey",async s=>{this.isInitialized(),await this.keychain.del(s)}),Ie(this,"encode",async(s,n,o)=>{this.isInitialized();const a=Uu(o),c=Dt(n);if(Ac(a))return u0(c,o==null?void 0:o.encoding);if(xc(a)){const d=a.senderPublicKey,f=a.receiverPublicKey;s=await this.generateSharedKey(d,f)}const l=this.getSymKey(s),{type:h,senderPublicKey:u}=a;return l0({type:h,symKey:l,message:c,senderPublicKey:u,encoding:o==null?void 0:o.encoding})}),Ie(this,"decode",async(s,n,o)=>{this.isInitialized();const a=p0(n,o);if(Ac(a)){const c=d0(n,o==null?void 0:o.encoding);return lr(c)}if(xc(a)){const c=a.receiverPublicKey,l=a.senderPublicKey;s=await this.generateSharedKey(c,l)}try{const c=this.getSymKey(s),l=h0({symKey:c,encoded:n,encoding:o==null?void 0:o.encoding});return lr(l)}catch(c){this.logger.error(`Failed to decode message from topic: '${s}', clientId: '${await this.getClientId()}'`),this.logger.error(c)}}),Ie(this,"getPayloadType",(s,n=ht)=>{const o=Ci({encoded:s,encoding:n});return dr(o.type)}),Ie(this,"getPayloadSenderPublicKey",(s,n=ht)=>{const o=Ci({encoded:s,encoding:n});return o.senderPublicKey?qe(o.senderPublicKey,Ne):void 0}),this.core=e,this.logger=je(r,this.name),this.keychain=i||new x$(this.core,this.logger)}get context(){return Me(this.logger)}async setPrivateKey(e,r){return await this.keychain.set(e,r),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(sl)}catch{e=ro(),await this.keychain.set(sl,e)}return S$(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var R$=Object.defineProperty,N$=Object.defineProperties,j$=Object.getOwnPropertyDescriptors,pl=Object.getOwnPropertySymbols,B$=Object.prototype.hasOwnProperty,D$=Object.prototype.propertyIsEnumerable,po=(t,e,r)=>e in t?R$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,U$=(t,e)=>{for(var r in e||(e={}))B$.call(e,r)&&po(t,r,e[r]);if(pl)for(var r of pl(e))D$.call(e,r)&&po(t,r,e[r]);return t},L$=(t,e)=>N$(t,j$(e)),Fe=(t,e,r)=>po(t,typeof e!="symbol"?e+"":e,r);class k$ extends _E{constructor(e,r){super(e,r),this.logger=e,this.core=r,Fe(this,"messages",new Map),Fe(this,"messagesWithoutClientAck",new Map),Fe(this,"name",T_),Fe(this,"version",C_),Fe(this,"initialized",!1),Fe(this,"storagePrefix",$t),Fe(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const i=await this.getRelayerMessages();typeof i<"u"&&(this.messages=i);const s=await this.getRelayerMessagesWithoutClientAck();typeof s<"u"&&(this.messagesWithoutClientAck=s),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(i){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(i)}finally{this.initialized=!0}}}),Fe(this,"set",async(i,s,n)=>{this.isInitialized();const o=Et(s);let a=this.messages.get(i);if(typeof a>"u"&&(a={}),typeof a[o]<"u")return o;if(a[o]=s,this.messages.set(i,a),n===fs.inbound){const c=this.messagesWithoutClientAck.get(i)||{};this.messagesWithoutClientAck.set(i,L$(U$({},c),{[o]:s}))}return await this.persist(),o}),Fe(this,"get",i=>{this.isInitialized();let s=this.messages.get(i);return typeof s>"u"&&(s={}),s}),Fe(this,"getWithoutAck",i=>{this.isInitialized();const s={};for(const n of i){const o=this.messagesWithoutClientAck.get(n)||{};s[n]=Object.values(o)}return s}),Fe(this,"has",(i,s)=>{this.isInitialized();const n=this.get(i),o=Et(s);return typeof n[o]<"u"}),Fe(this,"ack",async(i,s)=>{this.isInitialized();const n=this.messagesWithoutClientAck.get(i);if(typeof n>"u")return;const o=Et(s);delete n[o],Object.keys(n).length===0?this.messagesWithoutClientAck.delete(i):this.messagesWithoutClientAck.set(i,n),await this.persist()}),Fe(this,"del",async i=>{this.isInitialized(),this.messages.delete(i),this.messagesWithoutClientAck.delete(i),await this.persist()}),this.logger=je(e,this.name),this.core=r}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get storageKeyWithoutClientAck(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name+"_withoutClientAck"}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,Vn(e))}async setRelayerMessagesWithoutClientAck(e){await this.core.storage.setItem(this.storageKeyWithoutClientAck,Vn(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Kn(e):void 0}async getRelayerMessagesWithoutClientAck(){const e=await this.core.storage.getItem(this.storageKeyWithoutClientAck);return typeof e<"u"?Kn(e):void 0}async persist(){await this.setRelayerMessages(this.messages),await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck)}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var q$=Object.defineProperty,M$=Object.defineProperties,F$=Object.getOwnPropertyDescriptors,fl=Object.getOwnPropertySymbols,z$=Object.prototype.hasOwnProperty,H$=Object.prototype.propertyIsEnumerable,fo=(t,e,r)=>e in t?q$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,is=(t,e)=>{for(var r in e||(e={}))z$.call(e,r)&&fo(t,r,e[r]);if(fl)for(var r of fl(e))H$.call(e,r)&&fo(t,r,e[r]);return t},Sn=(t,e)=>M$(t,F$(e)),ct=(t,e,r)=>fo(t,typeof e!="symbol"?e+"":e,r);class V$ extends IE{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,ct(this,"events",new rt.EventEmitter),ct(this,"name",R_),ct(this,"queue",new Map),ct(this,"publishTimeout",j.toMiliseconds(j.ONE_MINUTE)),ct(this,"initialPublishTimeout",j.toMiliseconds(j.ONE_SECOND*15)),ct(this,"needsTransportRestart",!1),ct(this,"publish",async(i,s,n)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:i,message:s,opts:n}});const a=(n==null?void 0:n.ttl)||nl,c=Es(n),l=(n==null?void 0:n.prompt)||!1,h=(n==null?void 0:n.tag)||0,u=(n==null?void 0:n.id)||nr().toString(),d={topic:i,message:s,opts:{ttl:a,relay:c,prompt:l,tag:h,id:u,attestation:n==null?void 0:n.attestation,tvf:n==null?void 0:n.tvf}},f=`Failed to publish payload, please try again. id:${u} tag:${h}`;try{const p=new Promise(async y=>{const w=({id:b})=>{d.opts.id===b&&(this.removeRequestFromQueue(b),this.relayer.events.removeListener(we.publish,w),y(d))};this.relayer.events.on(we.publish,w);const v=zt(new Promise((b,E)=>{this.rpcPublish({topic:i,message:s,ttl:a,prompt:l,tag:h,id:u,attestation:n==null?void 0:n.attestation,tvf:n==null?void 0:n.tvf}).then(b).catch(I=>{this.logger.warn(I,I==null?void 0:I.message),E(I)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${u} tag:${h}`);try{await v,this.events.removeListener(we.publish,w)}catch(b){this.queue.set(u,Sn(is({},d),{attempt:1})),this.logger.warn(b,b==null?void 0:b.message)}});this.logger.trace({type:"method",method:"publish",params:{id:u,topic:i,message:s,opts:n}}),await zt(p,this.publishTimeout,f)}catch(p){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(p),(o=n==null?void 0:n.internal)!=null&&o.throwOnFailedPublish)throw p}finally{this.queue.delete(u)}}),ct(this,"on",(i,s)=>{this.events.on(i,s)}),ct(this,"once",(i,s)=>{this.events.once(i,s)}),ct(this,"off",(i,s)=>{this.events.off(i,s)}),ct(this,"removeListener",(i,s)=>{this.events.removeListener(i,s)}),this.relayer=e,this.logger=je(r,this.name),this.registerEventListeners()}get context(){return Me(this.logger)}async rpcPublish(e){var r,i,s,n;const{topic:o,message:a,ttl:c=nl,prompt:l,tag:h,id:u,attestation:d,tvf:f}=e,p={method:wi(Es().protocol).publish,params:is({topic:o,message:a,ttl:c,prompt:l,tag:h,attestation:d},f),id:u};Se((r=p.params)==null?void 0:r.prompt)&&((i=p.params)==null||delete i.prompt),Se((s=p.params)==null?void 0:s.tag)&&((n=p.params)==null||delete n.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:p});const y=await this.relayer.request(p);return this.relayer.events.emit(we.publish,e),this.logger.debug("Successfully Published Payload"),y}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,r)=>{const i=e.attempt+1;this.queue.set(r,Sn(is({},e),{attempt:i}));const{topic:s,message:n,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${i}`),await this.rpcPublish(Sn(is({},e),{topic:s,message:n,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(gr.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(we.connection_stalled);return}this.checkQueue()}),this.relayer.on(we.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var K$=Object.defineProperty,W$=(t,e,r)=>e in t?K$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Or=(t,e,r)=>W$(t,typeof e!="symbol"?e+"":e,r);class G${constructor(){Or(this,"map",new Map),Or(this,"set",(e,r)=>{const i=this.get(e);this.exists(e,r)||this.map.set(e,[...i,r])}),Or(this,"get",e=>this.map.get(e)||[]),Or(this,"exists",(e,r)=>this.get(e).includes(r)),Or(this,"delete",(e,r)=>{if(typeof r>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const i=this.get(e);if(!this.exists(e,r))return;const s=i.filter(n=>n!==r);if(!s.length){this.map.delete(e);return}this.map.set(e,s)}),Or(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var Y$=Object.defineProperty,J$=Object.defineProperties,Z$=Object.getOwnPropertyDescriptors,gl=Object.getOwnPropertySymbols,Q$=Object.prototype.hasOwnProperty,X$=Object.prototype.propertyIsEnumerable,go=(t,e,r)=>e in t?Y$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,gi=(t,e)=>{for(var r in e||(e={}))Q$.call(e,r)&&go(t,r,e[r]);if(gl)for(var r of gl(e))X$.call(e,r)&&go(t,r,e[r]);return t},On=(t,e)=>J$(t,Z$(e)),re=(t,e,r)=>go(t,typeof e!="symbol"?e+"":e,r);class e2 extends OE{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,re(this,"subscriptions",new Map),re(this,"topicMap",new G$),re(this,"events",new rt.EventEmitter),re(this,"name",k_),re(this,"version",q_),re(this,"pending",new Map),re(this,"cached",[]),re(this,"initialized",!1),re(this,"storagePrefix",$t),re(this,"subscribeTimeout",j.toMiliseconds(j.ONE_MINUTE)),re(this,"initialSubscribeTimeout",j.toMiliseconds(j.ONE_SECOND*15)),re(this,"clientId"),re(this,"batchSubscribeTopicsLimit",500),re(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),re(this,"subscribe",async(i,s)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:i,opts:s}});try{const n=Es(s),o={topic:i,relay:n,transportType:s==null?void 0:s.transportType};this.pending.set(i,o);const a=await this.rpcSubscribe(i,n,s);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:i,opts:s}})),a}catch(n){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(n),n}}),re(this,"unsubscribe",async(i,s)=>{this.isInitialized(),typeof(s==null?void 0:s.id)<"u"?await this.unsubscribeById(i,s.id,s):await this.unsubscribeByTopic(i,s)}),re(this,"isSubscribed",i=>new Promise(s=>{s(this.topicMap.topics.includes(i))})),re(this,"isKnownTopic",i=>new Promise(s=>{s(this.topicMap.topics.includes(i)||this.pending.has(i)||this.cached.some(n=>n.topic===i))})),re(this,"on",(i,s)=>{this.events.on(i,s)}),re(this,"once",(i,s)=>{this.events.once(i,s)}),re(this,"off",(i,s)=>{this.events.off(i,s)}),re(this,"removeListener",(i,s)=>{this.events.removeListener(i,s)}),re(this,"start",async()=>{await this.onConnect()}),re(this,"stop",async()=>{await this.onDisconnect()}),re(this,"restart",async()=>{await this.restore(),await this.onRestart()}),re(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const i=[];this.pending.forEach(s=>{i.push(s)}),await this.batchSubscribe(i)}),re(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(gr.pulse,async()=>{await this.checkPending()}),this.events.on(He.created,async i=>{const s=He.created;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,data:i}),await this.persist()}),this.events.on(He.deleted,async i=>{const s=He.deleted;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,data:i}),await this.persist()})}),this.relayer=e,this.logger=je(r,this.name),this.clientId=""}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,r){let i=!1;try{i=this.getSubscription(e).topic===r}catch{}return i}reset(){this.cached=[],this.initialized=!0}onDisable(){this.values.length>0&&(this.cached=this.values),this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,r){const i=this.topicMap.get(e);await Promise.all(i.map(async s=>await this.unsubscribeById(e,s,r)))}async unsubscribeById(e,r,i){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:i}});try{const s=Es(i);await this.restartToComplete({topic:e,id:r,relay:s}),await this.rpcUnsubscribe(e,r,s);const n=te("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,r,n),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:i}})}catch(s){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(s),s}}async rpcSubscribe(e,r,i){var s;(!i||(i==null?void 0:i.transportType)===ae.relay)&&await this.restartToComplete({topic:e,id:e,relay:r});const n={method:wi(r.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});const o=(s=i==null?void 0:i.internal)==null?void 0:s.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if((i==null?void 0:i.transportType)===ae.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(n).catch(h=>this.logger.warn(h))},j.toMiliseconds(j.ONE_SECOND)),a;const c=new Promise(async h=>{const u=d=>{d.topic===e&&(this.events.removeListener(He.created,u),h(d.id))};this.events.on(He.created,u);try{const d=await zt(new Promise((f,p)=>{this.relayer.request(n).catch(y=>{this.logger.warn(y,y==null?void 0:y.message),p(y)}).then(f)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener(He.created,u),h(d)}catch{}}),l=await zt(c,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!l&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return l?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(we.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const r=e[0].relay,i={method:wi(r.protocol).batchSubscribe,params:{topics:e.map(s=>s.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i});try{await await zt(new Promise(s=>{this.relayer.request(i).catch(n=>this.logger.warn(n)).then(s)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(we.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const r=e[0].relay,i={method:wi(r.protocol).batchFetchMessages,params:{topics:e.map(n=>n.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i});let s;try{s=await await zt(new Promise((n,o)=>{this.relayer.request(i).catch(a=>{this.logger.warn(a),o(a)}).then(n)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(we.connection_stalled)}return s}rpcUnsubscribe(e,r,i){const s={method:wi(i.protocol).unsubscribe,params:{topic:e,id:r}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s}),this.relayer.request(s)}onSubscribe(e,r){this.setSubscription(e,On(gi({},r),{id:e})),this.pending.delete(r.topic)}onBatchSubscribe(e){e.length&&e.forEach(r=>{this.setSubscription(r.id,gi({},r)),this.pending.delete(r.topic)})}async onUnsubscribe(e,r,i){this.events.removeAllListeners(r),this.hasSubscription(r,e)&&this.deleteSubscription(r,i),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,r){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:r}),this.addSubscription(e,r)}addSubscription(e,r){this.subscriptions.set(e,gi({},r)),this.topicMap.set(r.topic,e),this.events.emit(He.created,r)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const r=this.subscriptions.get(e);if(!r){const{message:i}=B("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(i)}return r}deleteSubscription(e,r){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:r});const i=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(i.topic,e),this.events.emit(He.deleted,On(gi({},i),{reason:r}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit(He.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],r=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let i=0;i<r;i++){const s=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(s)}}this.events.emit(He.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:r}=B("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async r=>On(gi({},r),{id:await this.getSubscriptionId(r.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const r=await this.rpcBatchFetchMessages(e);r&&r.messages&&(await Gw(j.toMiliseconds(j.ONE_SECOND)),await this.relayer.handleBatchMessageEvents(r.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return Et(e+await this.getClientId())}}var t2=Object.defineProperty,yl=Object.getOwnPropertySymbols,r2=Object.prototype.hasOwnProperty,i2=Object.prototype.propertyIsEnumerable,yo=(t,e,r)=>e in t?t2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ml=(t,e)=>{for(var r in e||(e={}))r2.call(e,r)&&yo(t,r,e[r]);if(yl)for(var r of yl(e))i2.call(e,r)&&yo(t,r,e[r]);return t},Z=(t,e,r)=>yo(t,typeof e!="symbol"?e+"":e,r);class s2 extends $E{constructor(e){super(e),Z(this,"protocol","wc"),Z(this,"version",2),Z(this,"core"),Z(this,"logger"),Z(this,"events",new rt.EventEmitter),Z(this,"provider"),Z(this,"messages"),Z(this,"subscriber"),Z(this,"publisher"),Z(this,"name",j_),Z(this,"transportExplicitlyClosed",!1),Z(this,"initialized",!1),Z(this,"connectionAttemptInProgress",!1),Z(this,"relayUrl"),Z(this,"projectId"),Z(this,"packageName"),Z(this,"bundleId"),Z(this,"hasExperiencedNetworkDisruption",!1),Z(this,"pingTimeout"),Z(this,"heartBeatTimeout",j.toMiliseconds(j.THIRTY_SECONDS+j.FIVE_SECONDS)),Z(this,"reconnectTimeout"),Z(this,"connectPromise"),Z(this,"reconnectInProgress",!1),Z(this,"requestsInFlight",[]),Z(this,"connectTimeout",j.toMiliseconds(j.ONE_SECOND*15)),Z(this,"request",async r=>{var i,s;this.logger.debug("Publishing Request Payload");const n=r.id||nr().toString();await this.toEstablishConnection();try{this.logger.trace({id:n,method:r.method,topic:(i=r.params)==null?void 0:i.topic},"relayer.request - publishing...");const o=`${n}:${((s=r.params)==null?void 0:s.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(r);return this.requestsInFlight=this.requestsInFlight.filter(c=>c!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${n}`),o}}),Z(this,"resetPingTimeout",()=>{bs()&&(clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var r,i,s,n;try{this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(n=(s=(i=(r=this.provider)==null?void 0:r.connection)==null?void 0:i.socket)==null?void 0:s.terminate)==null||n.call(s)}catch(o){this.logger.warn(o,o==null?void 0:o.message)}},this.heartBeatTimeout))}),Z(this,"onPayloadHandler",r=>{this.onProviderPayload(r),this.resetPingTimeout()}),Z(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected 🛜"),this.startPingTimeout(),this.events.emit(we.connect)}),Z(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected 🛑"),this.requestsInFlight=[],this.onProviderDisconnect()}),Z(this,"onProviderErrorHandler",r=>{this.logger.fatal(`Fatal socket error: ${r.message}`),this.events.emit(we.error,r),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),Z(this,"registerProviderListeners",()=>{this.provider.on(Ze.payload,this.onPayloadHandler),this.provider.on(Ze.connect,this.onConnectHandler),this.provider.on(Ze.disconnect,this.onDisconnectHandler),this.provider.on(Ze.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?je(e.logger,this.name):zi(Ms({level:e.logger||N_})),this.messages=new k$(this.logger,e.core),this.subscriber=new e2(this,this.logger),this.publisher=new V$(this,this.logger),this.relayUrl=(e==null?void 0:e.relayUrl)||ed,this.projectId=e.projectId,Rw()?this.packageName=Qa():Nw()&&(this.bundleId=Qa()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e==null?void 0:e.message)}}get context(){return Me(this.logger)}get connected(){var e,r,i;return((i=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:i.readyState)===1||!1}get connecting(){var e,r,i;return((i=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:i.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,r,i){this.isInitialized(),await this.publisher.publish(e,r,i),await this.recordMessageEvent({topic:e,message:r,publishedAt:Date.now(),transportType:ae.relay},fs.outbound)}async subscribe(e,r){var i,s,n;this.isInitialized(),(!(r!=null&&r.transportType)||(r==null?void 0:r.transportType)==="relay")&&await this.toEstablishConnection();const o=typeof((i=r==null?void 0:r.internal)==null?void 0:i.throwOnFailedPublish)>"u"?!0:(s=r==null?void 0:r.internal)==null?void 0:s.throwOnFailedPublish;let a=((n=this.subscriber.topicMap.get(e))==null?void 0:n[0])||"",c;const l=h=>{h.topic===e&&(this.subscriber.off(He.created,l),c())};return await Promise.all([new Promise(h=>{c=h,this.subscriber.on(He.created,l)}),new Promise(async(h,u)=>{a=await this.subscriber.subscribe(e,ml({internal:{throwOnFailedPublish:o}},r)).catch(d=>{o&&u(d)})||a,h()})]),a}async unsubscribe(e,r){this.isInitialized(),await this.subscriber.unsubscribe(e,r)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await zt(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(r,i)=>{await this.connect(e).then(r).catch(i).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await qc())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if((e==null?void 0:e.length)===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const r=e.sort((i,s)=>i.publishedAt-s.publishedAt);this.logger.debug(`Batch of ${r.length} message events sorted`);for(const i of r)try{await this.onMessageEvent(i)}catch(s){this.logger.warn(s,"Error while processing batch message event: "+(s==null?void 0:s.message))}this.logger.trace(`Batch of ${r.length} message events processed`)}async onLinkMessageEvent(e,r){const{topic:i}=e;if(!r.sessionExists){const s=de(j.FIVE_MINUTES),n={topic:i,expiry:s,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(i,n)}this.events.emit(we.message,e),await this.recordMessageEvent(e,fs.inbound)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let r=1;for(;r<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${r}...`),await this.createProvider(),await new Promise(async(i,s)=>{const n=()=>{s(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(Ze.disconnect,n),await zt(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{s(o)}).finally(()=>{this.provider.off(Ze.disconnect,n),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const c=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(Ze.disconnect,c),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(Ze.disconnect,c)})}),this.hasExperiencedNetworkDisruption=!1,i()})}catch(i){await this.subscriber.stop();const s=i;this.logger.warn({},s.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${r}`);break}await new Promise(i=>setTimeout(i,j.toMiliseconds(r*1))),r++}}startPingTimeout(){var e,r,i,s,n;if(bs())try{(r=(e=this.provider)==null?void 0:e.connection)!=null&&r.socket&&((n=(s=(i=this.provider)==null?void 0:i.connection)==null?void 0:s.socket)==null||n.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o==null?void 0:o.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new it(new __(Lw({sdkVersion:lo,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e,r){const{topic:i,message:s}=e;await this.messages.set(i,s,r)}async shouldIgnoreMessageEvent(e){const{topic:r,message:i}=e;if(!i||i.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${i}`),!0;if(!await this.subscriber.isKnownTopic(r))return this.logger.warn(`Ignoring message for unknown topic ${r}`),!0;const s=this.messages.has(r,i);return s&&this.logger.warn(`Ignoring duplicate message: ${i}`),s}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),Wo(e)){if(!e.method.endsWith(B_))return;const r=e.params,{topic:i,message:s,publishedAt:n,attestation:o}=r.data,a={topic:i,message:s,publishedAt:n,transportType:ae.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(ml({type:"event",event:r.id},a)),this.events.emit(r.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else Hs(e)&&this.events.emit(we.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(await this.recordMessageEvent(e,fs.inbound),this.events.emit(we.message,e))}async acknowledgePayload(e){const r=Fs(e.id,!0);await this.provider.connection.send(r)}unregisterProviderListeners(){this.provider.off(Ze.payload,this.onPayloadHandler),this.provider.off(Ze.connect,this.onConnectHandler),this.provider.off(Ze.disconnect,this.onDisconnectHandler),this.provider.off(Ze.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await qc();o1(async r=>{e!==r&&(e=r,r?await this.transportOpen().catch(i=>this.logger.error(i,i==null?void 0:i.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))}),this.core.heartbeat.on(gr.pulse,async()=>{if(!this.transportExplicitlyClosed&&!this.connected&&l1())try{await this.confirmOnlineStateOrThrow(),await this.transportOpen()}catch(r){this.logger.warn(r,r==null?void 0:r.message)}})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(we.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e==null?void 0:e.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},j.toMiliseconds(D_)))))}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){if(await this.confirmOnlineStateOrThrow(),!this.connected){if(this.connectPromise){await this.connectPromise;return}await this.connect()}}}function n2(){}function wl(t){if(!t||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e===null||e===Object.prototype||Object.getPrototypeOf(e)===null?Object.prototype.toString.call(t)==="[object Object]":!1}function bl(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function vl(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const o2="[object RegExp]",a2="[object String]",c2="[object Number]",l2="[object Boolean]",El="[object Arguments]",h2="[object Symbol]",u2="[object Date]",d2="[object Map]",p2="[object Set]",f2="[object Array]",g2="[object Function]",y2="[object ArrayBuffer]",Pn="[object Object]",m2="[object Error]",w2="[object DataView]",b2="[object Uint8Array]",v2="[object Uint8ClampedArray]",E2="[object Uint16Array]",_2="[object Uint32Array]",I2="[object BigUint64Array]",$2="[object Int8Array]",S2="[object Int16Array]",O2="[object Int32Array]",P2="[object BigInt64Array]",x2="[object Float32Array]",A2="[object Float64Array]";function T2(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}function C2(t,e,r){return bi(t,e,void 0,void 0,void 0,void 0,r)}function bi(t,e,r,i,s,n,o){const a=o(t,e,r,i,s,n);if(a!==void 0)return a;if(typeof t==typeof e)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return t===e;case"number":return t===e||Object.is(t,e);case"function":return t===e;case"object":return $i(t,e,n,o)}return $i(t,e,n,o)}function $i(t,e,r,i){if(Object.is(t,e))return!0;let s=vl(t),n=vl(e);if(s===El&&(s=Pn),n===El&&(n=Pn),s!==n)return!1;switch(s){case a2:return t.toString()===e.toString();case c2:{const c=t.valueOf(),l=e.valueOf();return T2(c,l)}case l2:case u2:case h2:return Object.is(t.valueOf(),e.valueOf());case o2:return t.source===e.source&&t.flags===e.flags;case g2:return t===e}r=r??new Map;const o=r.get(t),a=r.get(e);if(o!=null&&a!=null)return o===e;r.set(t,e),r.set(e,t);try{switch(s){case d2:{if(t.size!==e.size)return!1;for(const[c,l]of t.entries())if(!e.has(c)||!bi(l,e.get(c),c,t,e,r,i))return!1;return!0}case p2:{if(t.size!==e.size)return!1;const c=Array.from(t.values()),l=Array.from(e.values());for(let h=0;h<c.length;h++){const u=c[h],d=l.findIndex(f=>bi(u,f,void 0,t,e,r,i));if(d===-1)return!1;l.splice(d,1)}return!0}case f2:case b2:case v2:case E2:case _2:case I2:case $2:case S2:case O2:case P2:case x2:case A2:{if(typeof Buffer<"u"&&Buffer.isBuffer(t)!==Buffer.isBuffer(e)||t.length!==e.length)return!1;for(let c=0;c<t.length;c++)if(!bi(t[c],e[c],c,t,e,r,i))return!1;return!0}case y2:return t.byteLength!==e.byteLength?!1:$i(new Uint8Array(t),new Uint8Array(e),r,i);case w2:return t.byteLength!==e.byteLength||t.byteOffset!==e.byteOffset?!1:$i(new Uint8Array(t),new Uint8Array(e),r,i);case m2:return t.name===e.name&&t.message===e.message;case Pn:{if(!($i(t.constructor,e.constructor,r,i)||wl(t)&&wl(e)))return!1;const c=[...Object.keys(t),...bl(t)],l=[...Object.keys(e),...bl(e)];if(c.length!==l.length)return!1;for(let h=0;h<c.length;h++){const u=c[h],d=t[u];if(!Object.hasOwn(e,u))return!1;const f=e[u];if(!bi(d,f,u,t,e,r,i))return!1}return!0}default:return!1}}finally{r.delete(t),r.delete(e)}}function R2(t,e){return C2(t,e,n2)}var N2=Object.defineProperty,_l=Object.getOwnPropertySymbols,j2=Object.prototype.hasOwnProperty,B2=Object.prototype.propertyIsEnumerable,mo=(t,e,r)=>e in t?N2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Il=(t,e)=>{for(var r in e||(e={}))j2.call(e,r)&&mo(t,r,e[r]);if(_l)for(var r of _l(e))B2.call(e,r)&&mo(t,r,e[r]);return t},De=(t,e,r)=>mo(t,typeof e!="symbol"?e+"":e,r);class yr extends SE{constructor(e,r,i,s=$t,n=void 0){super(e,r,i,s),this.core=e,this.logger=r,this.name=i,De(this,"map",new Map),De(this,"version",U_),De(this,"cached",[]),De(this,"initialized",!1),De(this,"getKey"),De(this,"storagePrefix",$t),De(this,"recentlyDeleted",[]),De(this,"recentlyDeletedLimit",200),De(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!Se(o)?this.map.set(this.getKey(o),o):U0(o)?this.map.set(o.id,o):L0(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),De(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),De(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),De(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(c=>R2(a[c],o[c]))):this.values)),De(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const c=Il(Il({},this.getData(o)),a);this.map.set(o,c),await this.persist()}),De(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=je(r,this.name),this.storagePrefix=s,this.getKey=n}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const r=this.map.get(e);if(!r){if(this.recentlyDeleted.includes(e)){const{message:s}=B("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(s),new Error(s)}const{message:i}=B("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}return r}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:r}=B("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var D2=Object.defineProperty,U2=(t,e,r)=>e in t?D2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,K=(t,e,r)=>U2(t,typeof e!="symbol"?e+"":e,r);class L2{constructor(e,r){this.core=e,this.logger=r,K(this,"name",M_),K(this,"version",F_),K(this,"events",new Oo),K(this,"pairings"),K(this,"initialized",!1),K(this,"storagePrefix",$t),K(this,"ignoredPayloadTypes",[Bt]),K(this,"registeredMethods",[]),K(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),K(this,"register",({methods:i})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...i])]}),K(this,"create",async i=>{this.isInitialized();const s=ro(),n=await this.core.crypto.setSymKey(s),o=de(j.FIVE_MINUTES),a={protocol:Xu},c={topic:n,expiry:o,relay:a,active:!1,methods:i==null?void 0:i.methods},l=Cc({protocol:this.core.protocol,version:this.core.version,topic:n,symKey:s,relay:a,expiryTimestamp:o,methods:i==null?void 0:i.methods});return this.events.emit(rr.create,c),this.core.expirer.set(n,o),await this.pairings.set(n,c),await this.core.relayer.subscribe(n,{transportType:i==null?void 0:i.transportType}),{topic:n,uri:l}}),K(this,"pair",async i=>{this.isInitialized();const s=this.core.eventClient.createEvent({properties:{topic:i==null?void 0:i.uri,trace:[mt.pairing_started]}});this.isValidPair(i,s);const{topic:n,symKey:o,relay:a,expiryTimestamp:c,methods:l}=Tc(i.uri);s.props.properties.topic=n,s.addTrace(mt.pairing_uri_validation_success),s.addTrace(mt.pairing_uri_not_expired);let h;if(this.pairings.keys.includes(n)){if(h=this.pairings.get(n),s.addTrace(mt.existing_pairing),h.active)throw s.setError(Ct.active_pairing_already_exists),new Error(`Pairing already exists: ${n}. Please try again with a new connection URI.`);s.addTrace(mt.pairing_not_expired)}const u=c||de(j.FIVE_MINUTES),d={topic:n,relay:a,expiry:u,active:!1,methods:l};this.core.expirer.set(n,u),await this.pairings.set(n,d),s.addTrace(mt.store_new_pairing),i.activatePairing&&await this.activate({topic:n}),this.events.emit(rr.create,d),s.addTrace(mt.emit_inactive_pairing),this.core.crypto.keychain.has(n)||await this.core.crypto.setSymKey(o,n),s.addTrace(mt.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{s.setError(Ct.no_internet_connection)}try{await this.core.relayer.subscribe(n,{relay:a})}catch(f){throw s.setError(Ct.subscribe_pairing_topic_failure),f}return s.addTrace(mt.subscribe_pairing_topic_success),d}),K(this,"activate",async({topic:i})=>{this.isInitialized();const s=de(j.FIVE_MINUTES);this.core.expirer.set(i,s),await this.pairings.update(i,{active:!0,expiry:s})}),K(this,"ping",async i=>{this.isInitialized(),await this.isValidPing(i),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:s}=i;if(this.pairings.keys.includes(s)){const n=await this.sendRequest(s,"wc_pairingPing",{}),{done:o,resolve:a,reject:c}=Qt();this.events.once(X("pairing_ping",n),({error:l})=>{l?c(l):a()}),await o()}}),K(this,"updateExpiry",async({topic:i,expiry:s})=>{this.isInitialized(),await this.pairings.update(i,{expiry:s})}),K(this,"updateMetadata",async({topic:i,metadata:s})=>{this.isInitialized(),await this.pairings.update(i,{peerMetadata:s})}),K(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),K(this,"disconnect",async i=>{this.isInitialized(),await this.isValidDisconnect(i);const{topic:s}=i;this.pairings.keys.includes(s)&&(await this.sendRequest(s,"wc_pairingDelete",te("USER_DISCONNECTED")),await this.deletePairing(s))}),K(this,"formatUriFromPairing",i=>{this.isInitialized();const{topic:s,relay:n,expiry:o,methods:a}=i,c=this.core.crypto.keychain.get(s);return Cc({protocol:this.core.protocol,version:this.core.version,topic:s,symKey:c,relay:n,expiryTimestamp:o,methods:a})}),K(this,"sendRequest",async(i,s,n)=>{const o=Ht(s,n),a=await this.core.crypto.encode(i,o),c=pi[s].req;return this.core.history.set(i,o),this.core.relayer.publish(i,a,c),o.id}),K(this,"sendResult",async(i,s,n)=>{const o=Fs(i,n),a=await this.core.crypto.encode(s,o),c=(await this.core.history.get(s,i)).request.method,l=pi[c].res;await this.core.relayer.publish(s,a,l),await this.core.history.resolve(o)}),K(this,"sendError",async(i,s,n)=>{const o=zs(i,n),a=await this.core.crypto.encode(s,o),c=(await this.core.history.get(s,i)).request.method,l=pi[c]?pi[c].res:pi.unregistered_method.res;await this.core.relayer.publish(s,a,l),await this.core.history.resolve(o)}),K(this,"deletePairing",async(i,s)=>{await this.core.relayer.unsubscribe(i),await Promise.all([this.pairings.delete(i,te("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(i),s?Promise.resolve():this.core.expirer.del(i)])}),K(this,"cleanup",async()=>{const i=this.pairings.getAll().filter(s=>Mt(s.expiry));await Promise.all(i.map(s=>this.deletePairing(s.topic)))}),K(this,"onRelayEventRequest",async i=>{const{topic:s,payload:n}=i;switch(n.method){case"wc_pairingPing":return await this.onPairingPingRequest(s,n);case"wc_pairingDelete":return await this.onPairingDeleteRequest(s,n);default:return await this.onUnknownRpcMethodRequest(s,n)}}),K(this,"onRelayEventResponse",async i=>{const{topic:s,payload:n}=i,o=(await this.core.history.get(s,n.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(s,n);default:return this.onUnknownRpcMethodResponse(o)}}),K(this,"onPairingPingRequest",async(i,s)=>{const{id:n}=s;try{this.isValidPing({topic:i}),await this.sendResult(n,i,!0),this.events.emit(rr.ping,{id:n,topic:i})}catch(o){await this.sendError(n,i,o),this.logger.error(o)}}),K(this,"onPairingPingResponse",(i,s)=>{const{id:n}=s;setTimeout(()=>{bt(s)?this.events.emit(X("pairing_ping",n),{}):et(s)&&this.events.emit(X("pairing_ping",n),{error:s.error})},500)}),K(this,"onPairingDeleteRequest",async(i,s)=>{const{id:n}=s;try{this.isValidDisconnect({topic:i}),await this.deletePairing(i),this.events.emit(rr.delete,{id:n,topic:i})}catch(o){await this.sendError(n,i,o),this.logger.error(o)}}),K(this,"onUnknownRpcMethodRequest",async(i,s)=>{const{id:n,method:o}=s;try{if(this.registeredMethods.includes(o))return;const a=te("WC_METHOD_UNSUPPORTED",o);await this.sendError(n,i,a),this.logger.error(a)}catch(a){await this.sendError(n,i,a),this.logger.error(a)}}),K(this,"onUnknownRpcMethodResponse",i=>{this.registeredMethods.includes(i)||this.logger.error(te("WC_METHOD_UNSUPPORTED",i))}),K(this,"isValidPair",(i,s)=>{var n;if(!Le(i)){const{message:a}=B("MISSING_OR_INVALID",`pair() params: ${i}`);throw s.setError(Ct.malformed_pairing_uri),new Error(a)}if(!D0(i.uri)){const{message:a}=B("MISSING_OR_INVALID",`pair() uri: ${i.uri}`);throw s.setError(Ct.malformed_pairing_uri),new Error(a)}const o=Tc(i==null?void 0:i.uri);if(!((n=o==null?void 0:o.relay)!=null&&n.protocol)){const{message:a}=B("MISSING_OR_INVALID","pair() uri#relay-protocol");throw s.setError(Ct.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=B("MISSING_OR_INVALID","pair() uri#symKey");throw s.setError(Ct.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&j.toMiliseconds(o==null?void 0:o.expiryTimestamp)<Date.now()){s.setError(Ct.pairing_expired);const{message:a}=B("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),K(this,"isValidPing",async i=>{if(!Le(i)){const{message:n}=B("MISSING_OR_INVALID",`ping() params: ${i}`);throw new Error(n)}const{topic:s}=i;await this.isValidPairingTopic(s)}),K(this,"isValidDisconnect",async i=>{if(!Le(i)){const{message:n}=B("MISSING_OR_INVALID",`disconnect() params: ${i}`);throw new Error(n)}const{topic:s}=i;await this.isValidPairingTopic(s)}),K(this,"isValidPairingTopic",async i=>{if(!ue(i,!1)){const{message:s}=B("MISSING_OR_INVALID",`pairing topic should be a string: ${i}`);throw new Error(s)}if(!this.pairings.keys.includes(i)){const{message:s}=B("NO_MATCHING_KEY",`pairing topic doesn't exist: ${i}`);throw new Error(s)}if(Mt(this.pairings.get(i).expiry)){await this.deletePairing(i);const{message:s}=B("EXPIRED",`pairing topic: ${i}`);throw new Error(s)}}),this.core=e,this.logger=je(r,this.name),this.pairings=new yr(this.core,this.logger,this.name,this.storagePrefix)}get context(){return Me(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(we.message,async e=>{const{topic:r,message:i,transportType:s}=e;if(this.pairings.keys.includes(r)&&s!==ae.link_mode&&!this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(i)))try{const n=await this.core.crypto.decode(r,i);Wo(n)?(this.core.history.set(r,n),await this.onRelayEventRequest({topic:r,payload:n})):Hs(n)&&(await this.core.history.resolve(n),await this.onRelayEventResponse({topic:r,payload:n}),this.core.history.delete(r,n.id)),await this.core.relayer.messages.ack(r,i)}catch(n){this.logger.error(n)}})}registerExpirerEvents(){this.core.expirer.on(Xe.expired,async e=>{const{topic:r}=su(e.target);r&&this.pairings.keys.includes(r)&&(await this.deletePairing(r,!0),this.events.emit(rr.expire,{topic:r}))})}}var k2=Object.defineProperty,q2=(t,e,r)=>e in t?k2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,$e=(t,e,r)=>q2(t,typeof e!="symbol"?e+"":e,r);class M2 extends EE{constructor(e,r){super(e,r),this.core=e,this.logger=r,$e(this,"records",new Map),$e(this,"events",new rt.EventEmitter),$e(this,"name",z_),$e(this,"version",H_),$e(this,"cached",[]),$e(this,"initialized",!1),$e(this,"storagePrefix",$t),$e(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(i=>this.records.set(i.id,i)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),$e(this,"set",(i,s,n)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:i,request:s,chainId:n}),this.records.has(s.id))return;const o={id:s.id,topic:i,request:{method:s.method,params:s.params||null},chainId:n,expiry:de(j.THIRTY_DAYS)};this.records.set(o.id,o),this.persist(),this.events.emit(ot.created,o)}),$e(this,"resolve",async i=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:i}),!this.records.has(i.id))return;const s=await this.getRecord(i.id);typeof s.response>"u"&&(s.response=et(i)?{error:i.error}:{result:i.result},this.records.set(s.id,s),this.persist(),this.events.emit(ot.updated,s))}),$e(this,"get",async(i,s)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:i,id:s}),await this.getRecord(s))),$e(this,"delete",(i,s)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:s}),this.values.forEach(n=>{if(n.topic===i){if(typeof s<"u"&&n.id!==s)return;this.records.delete(n.id),this.events.emit(ot.deleted,n)}}),this.persist()}),$e(this,"exists",async(i,s)=>(this.isInitialized(),this.records.has(s)?(await this.getRecord(s)).topic===i:!1)),$e(this,"on",(i,s)=>{this.events.on(i,s)}),$e(this,"once",(i,s)=>{this.events.once(i,s)}),$e(this,"off",(i,s)=>{this.events.off(i,s)}),$e(this,"removeListener",(i,s)=>{this.events.removeListener(i,s)}),this.logger=je(r,this.name)}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(r=>{if(typeof r.response<"u")return;const i={topic:r.topic,request:Ht(r.request.method,r.request.params,r.id),chainId:r.chainId};return e.push(i)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const r=this.records.get(e);if(!r){const{message:i}=B("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(i)}return r}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(ot.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:r}=B("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(ot.created,e=>{const r=ot.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(ot.updated,e=>{const r=ot.updated;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(ot.deleted,e=>{const r=ot.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.core.heartbeat.on(gr.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(r=>{j.toMiliseconds(r.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${r.id}`),this.records.delete(r.id),this.events.emit(ot.deleted,r,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var F2=Object.defineProperty,z2=(t,e,r)=>e in t?F2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Te=(t,e,r)=>z2(t,typeof e!="symbol"?e+"":e,r);class H2 extends PE{constructor(e,r){super(e,r),this.core=e,this.logger=r,Te(this,"expirations",new Map),Te(this,"events",new rt.EventEmitter),Te(this,"name",V_),Te(this,"version",K_),Te(this,"cached",[]),Te(this,"initialized",!1),Te(this,"storagePrefix",$t),Te(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(i=>this.expirations.set(i.target,i)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),Te(this,"has",i=>{try{const s=this.formatTarget(i);return typeof this.getExpiration(s)<"u"}catch{return!1}}),Te(this,"set",(i,s)=>{this.isInitialized();const n=this.formatTarget(i),o={target:n,expiry:s};this.expirations.set(n,o),this.checkExpiry(n,o),this.events.emit(Xe.created,{target:n,expiration:o})}),Te(this,"get",i=>{this.isInitialized();const s=this.formatTarget(i);return this.getExpiration(s)}),Te(this,"del",i=>{if(this.isInitialized(),this.has(i)){const s=this.formatTarget(i),n=this.getExpiration(s);this.expirations.delete(s),this.events.emit(Xe.deleted,{target:s,expiration:n})}}),Te(this,"on",(i,s)=>{this.events.on(i,s)}),Te(this,"once",(i,s)=>{this.events.once(i,s)}),Te(this,"off",(i,s)=>{this.events.off(i,s)}),Te(this,"removeListener",(i,s)=>{this.events.removeListener(i,s)}),this.logger=je(r,this.name)}get context(){return Me(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return kw(e);if(typeof e=="number")return qw(e);const{message:r}=B("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(r)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(Xe.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:r}=B("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const r=this.expirations.get(e);if(!r){const{message:i}=B("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(i),new Error(i)}return r}checkExpiry(e,r){const{expiry:i}=r;j.toMiliseconds(i)-Date.now()<=0&&this.expire(e,r)}expire(e,r){this.expirations.delete(e),this.events.emit(Xe.expired,{target:e,expiration:r})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,r)=>this.checkExpiry(r,e))}registerEventListeners(){this.core.heartbeat.on(gr.pulse,()=>this.checkExpirations()),this.events.on(Xe.created,e=>{const r=Xe.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(Xe.expired,e=>{const r=Xe.expired;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(Xe.deleted,e=>{const r=Xe.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}}var V2=Object.defineProperty,K2=(t,e,r)=>e in t?V2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,he=(t,e,r)=>K2(t,typeof e!="symbol"?e+"":e,r);class W2 extends xE{constructor(e,r,i){super(e,r,i),this.core=e,this.logger=r,this.store=i,he(this,"name",W_),he(this,"abortController"),he(this,"isDevEnv"),he(this,"verifyUrlV3",Y_),he(this,"storagePrefix",$t),he(this,"version",Qu),he(this,"publicKey"),he(this,"fetchPromise"),he(this,"init",async()=>{var s;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&j.toMiliseconds((s=this.publicKey)==null?void 0:s.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),he(this,"register",async s=>{if(!ri()||this.isDevEnv)return;const n=window.location.origin,{id:o,decryptedId:a}=s,c=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${n}&id=${o}&decryptedId=${a}`;try{const l=ar(),h=this.startAbortTimer(j.ONE_SECOND*5),u=await new Promise((d,f)=>{const p=()=>{window.removeEventListener("message",w),l.body.removeChild(y),f("attestation aborted")};this.abortController.signal.addEventListener("abort",p);const y=l.createElement("iframe");y.src=c,y.style.display="none",y.addEventListener("error",p,{signal:this.abortController.signal});const w=v=>{if(v.data&&typeof v.data=="string")try{const b=JSON.parse(v.data);if(b.type==="verify_attestation"){if(Hn(b.attestation).payload.id!==o)return;clearInterval(h),l.body.removeChild(y),this.abortController.signal.removeEventListener("abort",p),window.removeEventListener("message",w),d(b.attestation===null?"":b.attestation)}}catch(b){this.logger.warn(b)}};l.body.appendChild(y),window.addEventListener("message",w,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",u),u}catch(l){this.logger.warn(l)}return""}),he(this,"resolve",async s=>{if(this.isDevEnv)return"";const{attestationId:n,hash:o,encryptedId:a}=s;if(n===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(n){if(Hn(n).payload.id!==a)return;const l=await this.isValidJwtAttestation(n);if(l){if(!l.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return l}}if(!o)return;const c=this.getVerifyUrl(s==null?void 0:s.verifyUrl);return this.fetchAttestation(o,c)}),he(this,"fetchAttestation",async(s,n)=>{this.logger.debug(`resolving attestation: ${s} from url: ${n}`);const o=this.startAbortTimer(j.ONE_SECOND*5),a=await fetch(`${n}/attestation/${s}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),he(this,"getVerifyUrl",s=>{let n=s||Ii;return J_.includes(n)||(this.logger.info(`verify url: ${n}, not included in trusted list, assigning default: ${Ii}`),n=Ii),n}),he(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const s=this.startAbortTimer(j.FIVE_SECONDS),n=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(s),await n.json()}catch(s){this.logger.warn(s)}}),he(this,"persistPublicKey",async s=>{this.logger.debug("persisting public key to local storage",s),await this.store.setItem(this.storeKey,s),this.publicKey=s}),he(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),he(this,"isValidJwtAttestation",async s=>{const n=await this.getPublicKey();try{if(n)return this.validateAttestation(s,n)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(s,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),he(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),he(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async n=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),n(o))});const s=await this.fetchPromise;return this.fetchPromise=void 0,s}),he(this,"validateAttestation",(s,n)=>{const o=g0(s,n.publicKey),a={hasExpired:j.toMiliseconds(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=je(r,this.name),this.abortController=new AbortController,this.isDevEnv=Uo(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return Me(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),j.toMiliseconds(e))}}var G2=Object.defineProperty,Y2=(t,e,r)=>e in t?G2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,$l=(t,e,r)=>Y2(t,typeof e!="symbol"?e+"":e,r);class J2 extends AE{constructor(e,r){super(e,r),this.projectId=e,this.logger=r,$l(this,"context",Z_),$l(this,"registerDeviceToken",async i=>{const{clientId:s,token:n,notificationType:o,enableEncrypted:a=!1}=i,c=`${Q_}/${this.projectId}/clients`;await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:s,type:o,token:n,always_raw:a})})}),this.logger=je(r,this.context)}}var Z2=Object.defineProperty,Sl=Object.getOwnPropertySymbols,Q2=Object.prototype.hasOwnProperty,X2=Object.prototype.propertyIsEnumerable,wo=(t,e,r)=>e in t?Z2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,yi=(t,e)=>{for(var r in e||(e={}))Q2.call(e,r)&&wo(t,r,e[r]);if(Sl)for(var r of Sl(e))X2.call(e,r)&&wo(t,r,e[r]);return t},fe=(t,e,r)=>wo(t,typeof e!="symbol"?e+"":e,r);class eS extends TE{constructor(e,r,i=!0){super(e,r,i),this.core=e,this.logger=r,fe(this,"context",eI),fe(this,"storagePrefix",$t),fe(this,"storageVersion",X_),fe(this,"events",new Map),fe(this,"shouldPersist",!1),fe(this,"init",async()=>{if(!Uo())try{const s={eventId:ec(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:ru(this.core.relayer.protocol,this.core.relayer.version,lo)}}};await this.sendEvent([s])}catch(s){this.logger.warn(s)}}),fe(this,"createEvent",s=>{const{event:n="ERROR",type:o="",properties:{topic:a,trace:c}}=s,l=ec(),h=this.core.projectId||"",u=Date.now(),d=yi({eventId:l,timestamp:u,props:{event:n,type:o,properties:{topic:a,trace:c}},bundleId:h,domain:this.getAppDomain()},this.setMethods(l));return this.telemetryEnabled&&(this.events.set(l,d),this.shouldPersist=!0),d}),fe(this,"getEvent",s=>{const{eventId:n,topic:o}=s;if(n)return this.events.get(n);const a=Array.from(this.events.values()).find(c=>c.props.properties.topic===o);if(a)return yi(yi({},a),this.setMethods(a.eventId))}),fe(this,"deleteEvent",s=>{const{eventId:n}=s;this.events.delete(n),this.shouldPersist=!0}),fe(this,"setEventListeners",()=>{this.core.heartbeat.on(gr.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(s=>{j.fromMiliseconds(Date.now())-j.fromMiliseconds(s.timestamp)>tI&&(this.events.delete(s.eventId),this.shouldPersist=!0)})})}),fe(this,"setMethods",s=>({addTrace:n=>this.addTrace(s,n),setError:n=>this.setError(s,n)})),fe(this,"addTrace",(s,n)=>{const o=this.events.get(s);o&&(o.props.properties.trace.push(n),this.events.set(s,o),this.shouldPersist=!0)}),fe(this,"setError",(s,n)=>{const o=this.events.get(s);o&&(o.props.type=n,o.timestamp=Date.now(),this.events.set(s,o),this.shouldPersist=!0)}),fe(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),fe(this,"restore",async()=>{try{const s=await this.core.storage.getItem(this.storageKey)||[];if(!s.length)return;s.forEach(n=>{this.events.set(n.eventId,yi(yi({},n),this.setMethods(n.eventId)))})}catch(s){this.logger.warn(s)}}),fe(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const s=[];for(const[n,o]of this.events)o.props.type&&s.push(o);if(s.length!==0)try{if((await this.sendEvent(s)).ok)for(const n of s)this.events.delete(n.eventId),this.shouldPersist=!0}catch(n){this.logger.warn(n)}}),fe(this,"sendEvent",async s=>{const n=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${rI}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${lo}${n}`,{method:"POST",body:JSON.stringify(s)})}),fe(this,"getAppDomain",()=>tu().url),this.logger=je(r,this.context),this.telemetryEnabled=i,i?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var tS=Object.defineProperty,Ol=Object.getOwnPropertySymbols,rS=Object.prototype.hasOwnProperty,iS=Object.prototype.propertyIsEnumerable,bo=(t,e,r)=>e in t?tS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Pl=(t,e)=>{for(var r in e||(e={}))rS.call(e,r)&&bo(t,r,e[r]);if(Ol)for(var r of Ol(e))iS.call(e,r)&&bo(t,r,e[r]);return t},ne=(t,e,r)=>bo(t,typeof e!="symbol"?e+"":e,r);let sS=class dd extends mE{constructor(e){var r;super(e),ne(this,"protocol",Zu),ne(this,"version",Qu),ne(this,"name",co),ne(this,"relayUrl"),ne(this,"projectId"),ne(this,"customStoragePrefix"),ne(this,"events",new rt.EventEmitter),ne(this,"logger"),ne(this,"heartbeat"),ne(this,"relayer"),ne(this,"crypto"),ne(this,"storage"),ne(this,"history"),ne(this,"expirer"),ne(this,"pairing"),ne(this,"verify"),ne(this,"echoClient"),ne(this,"linkModeSupportedApps"),ne(this,"eventClient"),ne(this,"initialized",!1),ne(this,"logChunkController"),ne(this,"on",(a,c)=>this.events.on(a,c)),ne(this,"once",(a,c)=>this.events.once(a,c)),ne(this,"off",(a,c)=>this.events.off(a,c)),ne(this,"removeListener",(a,c)=>this.events.removeListener(a,c)),ne(this,"dispatchEnvelope",({topic:a,message:c,sessionExists:l})=>{if(!a||!c)return;const h={topic:a,message:c,publishedAt:Date.now(),transportType:ae.link_mode};this.relayer.onLinkMessageEvent(h,{sessionExists:l})});const i=this.getGlobalCore(e==null?void 0:e.customStoragePrefix);if(i)try{return this.customStoragePrefix=i.customStoragePrefix,this.logger=i.logger,this.heartbeat=i.heartbeat,this.crypto=i.crypto,this.history=i.history,this.expirer=i.expirer,this.storage=i.storage,this.relayer=i.relayer,this.pairing=i.pairing,this.verify=i.verify,this.echoClient=i.echoClient,this.linkModeSupportedApps=i.linkModeSupportedApps,this.eventClient=i.eventClient,this.initialized=i.initialized,this.logChunkController=i.logChunkController,i}catch(a){console.warn("Failed to copy global core",a)}this.projectId=e==null?void 0:e.projectId,this.relayUrl=(e==null?void 0:e.relayUrl)||ed,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const s=Ms({level:typeof(e==null?void 0:e.logger)=="string"&&e.logger?e.logger:$_.logger,name:co}),{logger:n,chunkLoggerController:o}=fE({opts:s,maxSizeInBytes:e==null?void 0:e.maxLogBlobSizeInBytes,loggerOverride:e==null?void 0:e.logger});this.logChunkController=o,(r=this.logChunkController)!=null&&r.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var a,c;(a=this.logChunkController)!=null&&a.downloadLogsBlobInBrowser&&((c=this.logChunkController)==null||c.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=je(n,this.name),this.heartbeat=new u1,this.crypto=new C$(this,this.logger,e==null?void 0:e.keychain),this.history=new M2(this,this.logger),this.expirer=new H2(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new q1(Pl(Pl({},S_),e==null?void 0:e.storageOptions)),this.relayer=new s2({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new L2(this,this.logger),this.verify=new W2(this,this.logger,this.storage),this.echoClient=new J2(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new eS(this,this.logger,e==null?void 0:e.telemetryEnabled),this.setGlobalCore(this)}static async init(e){const r=new dd(e);await r.initialize();const i=await r.crypto.getClientId();return await r.storage.setItem(L_,i),r}get context(){return Me(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(ol,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(ol)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}getGlobalCore(e=""){try{if(this.isGlobalCoreDisabled())return;const r=`_walletConnectCore_${e}`,i=`${r}_count`;return globalThis[i]=(globalThis[i]||0)+1,globalThis[i]>1&&console.warn(`WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[i]} times.`),globalThis[r]}catch(r){console.warn("Failed to get global WalletConnect core",r);return}}setGlobalCore(e){var r;try{if(this.isGlobalCoreDisabled())return;const i=`_walletConnectCore_${((r=e.opts)==null?void 0:r.customStoragePrefix)||""}`;globalThis[i]=e}catch(i){console.warn("Failed to set global WalletConnect core",i)}}isGlobalCoreDisabled(){try{return typeof process<"u"&&I_.DISABLE_GLOBAL_CORE==="true"}catch{return!0}}};const nS=sS,pd="wc",fd=2,gd="client",Go=`${pd}@${fd}:${gd}:`,xn={name:gd,logger:"error"},xl="WALLETCONNECT_DEEPLINK_CHOICE",oS="proposal",Al="Proposal expired",aS="session",Pr=j.SEVEN_DAYS,cS="engine",ge={wc_sessionPropose:{req:{ttl:j.FIVE_MINUTES,prompt:!0,tag:1100},res:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1101},reject:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1120},autoReject:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1121}},wc_sessionSettle:{req:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1102},res:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1103}},wc_sessionUpdate:{req:{ttl:j.ONE_DAY,prompt:!1,tag:1104},res:{ttl:j.ONE_DAY,prompt:!1,tag:1105}},wc_sessionExtend:{req:{ttl:j.ONE_DAY,prompt:!1,tag:1106},res:{ttl:j.ONE_DAY,prompt:!1,tag:1107}},wc_sessionRequest:{req:{ttl:j.FIVE_MINUTES,prompt:!0,tag:1108},res:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1109}},wc_sessionEvent:{req:{ttl:j.FIVE_MINUTES,prompt:!0,tag:1110},res:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1111}},wc_sessionDelete:{req:{ttl:j.ONE_DAY,prompt:!1,tag:1112},res:{ttl:j.ONE_DAY,prompt:!1,tag:1113}},wc_sessionPing:{req:{ttl:j.ONE_DAY,prompt:!1,tag:1114},res:{ttl:j.ONE_DAY,prompt:!1,tag:1115}},wc_sessionAuthenticate:{req:{ttl:j.ONE_HOUR,prompt:!0,tag:1116},res:{ttl:j.ONE_HOUR,prompt:!1,tag:1117},reject:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1118},autoReject:{ttl:j.FIVE_MINUTES,prompt:!1,tag:1119}}},An={min:j.FIVE_MINUTES,max:j.SEVEN_DAYS},yt={idle:"IDLE",active:"ACTIVE"},Tl={eth_sendTransaction:{key:""},eth_sendRawTransaction:{key:""},wallet_sendCalls:{key:""},solana_signTransaction:{key:"signature"},solana_signAllTransactions:{key:"transactions"},solana_signAndSendTransaction:{key:"signature"}},lS="request",hS=["wc_sessionPropose","wc_sessionRequest","wc_authRequest","wc_sessionAuthenticate"],uS="wc",dS="auth",pS="authKeys",fS="pairingTopics",gS="requests",Ks=`${uS}@${1.5}:${dS}:`,gs=`${Ks}:PUB_KEY`;var yS=Object.defineProperty,mS=Object.defineProperties,wS=Object.getOwnPropertyDescriptors,Cl=Object.getOwnPropertySymbols,bS=Object.prototype.hasOwnProperty,vS=Object.prototype.propertyIsEnumerable,vo=(t,e,r)=>e in t?yS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ie=(t,e)=>{for(var r in e||(e={}))bS.call(e,r)&&vo(t,r,e[r]);if(Cl)for(var r of Cl(e))vS.call(e,r)&&vo(t,r,e[r]);return t},Re=(t,e)=>mS(t,wS(e)),N=(t,e,r)=>vo(t,typeof e!="symbol"?e+"":e,r);class ES extends jE{constructor(e){super(e),N(this,"name",cS),N(this,"events",new Oo),N(this,"initialized",!1),N(this,"requestQueue",{state:yt.idle,queue:[]}),N(this,"sessionRequestQueue",{state:yt.idle,queue:[]}),N(this,"requestQueueDelay",j.ONE_SECOND),N(this,"expectedPairingMethodMap",new Map),N(this,"recentlyDeletedMap",new Map),N(this,"recentlyDeletedLimit",200),N(this,"relayMessageCache",[]),N(this,"pendingSessions",new Map),N(this,"init",async()=>{this.initialized||(await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.registerPairingEvents(),await this.registerLinkModeListeners(),this.client.core.pairing.register({methods:Object.keys(ge)}),this.initialized=!0,setTimeout(async()=>{await this.processPendingMessageEvents(),this.sessionRequestQueue.queue=this.getPendingSessionRequests(),this.processSessionRequestQueue()},j.toMiliseconds(this.requestQueueDelay)))}),N(this,"connect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();const i=Re(ie({},r),{requiredNamespaces:r.requiredNamespaces||{},optionalNamespaces:r.optionalNamespaces||{}});await this.isValidConnect(i),i.optionalNamespaces=C0(i.requiredNamespaces,i.optionalNamespaces),i.requiredNamespaces={};const{pairingTopic:s,requiredNamespaces:n,optionalNamespaces:o,sessionProperties:a,scopedProperties:c,relays:l}=i;let h=s,u,d=!1;try{if(h){const A=this.client.core.pairing.pairings.get(h);this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."),d=A.active}}catch(A){throw this.client.logger.error(`connect() -> pairing.get(${h}) failed`),A}if(!h||!d){const{topic:A,uri:P}=await this.client.core.pairing.create();h=A,u=P}if(!h){const{message:A}=B("NO_MATCHING_KEY",`connect() pairing topic: ${h}`);throw new Error(A)}const f=await this.client.core.crypto.generateKeyPair(),p=ge.wc_sessionPropose.req.ttl||j.FIVE_MINUTES,y=de(p),w=Re(ie(ie({requiredNamespaces:n,optionalNamespaces:o,relays:l??[{protocol:Xu}],proposer:{publicKey:f,metadata:this.client.metadata},expiryTimestamp:y,pairingTopic:h},a&&{sessionProperties:a}),c&&{scopedProperties:c}),{id:wt()}),v=X("session_connect",w.id),{reject:b,resolve:E,done:I}=Qt(p,Al),x=({id:A})=>{A===w.id&&(this.client.events.off("proposal_expire",x),this.pendingSessions.delete(w.id),this.events.emit(v,{error:{message:Al,code:0}}))};return this.client.events.on("proposal_expire",x),this.events.once(v,({error:A,session:P})=>{this.client.events.off("proposal_expire",x),A?b(A):P&&E(P)}),await this.sendRequest({topic:h,method:"wc_sessionPropose",params:w,throwOnFailedPublish:!0,clientRpcId:w.id}),await this.setProposal(w.id,w),{uri:u,approval:I}}),N(this,"pair",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{return await this.client.core.pairing.pair(r)}catch(i){throw this.client.logger.error("pair() failed"),i}}),N(this,"approve",async r=>{var i,s,n;const o=this.client.core.eventClient.createEvent({properties:{topic:(i=r==null?void 0:r.id)==null?void 0:i.toString(),trace:[at.session_approve_started]}});try{this.isInitialized(),await this.confirmOnlineStateOrThrow()}catch(S){throw o.setError(Jt.no_internet_connection),S}try{await this.isValidProposalId(r==null?void 0:r.id)}catch(S){throw this.client.logger.error(`approve() -> proposal.get(${r==null?void 0:r.id}) failed`),o.setError(Jt.proposal_not_found),S}try{await this.isValidApprove(r)}catch(S){throw this.client.logger.error("approve() -> isValidApprove() failed"),o.setError(Jt.session_approve_namespace_validation_failure),S}const{id:a,relayProtocol:c,namespaces:l,sessionProperties:h,scopedProperties:u,sessionConfig:d}=r,f=this.client.proposal.get(a);this.client.core.eventClient.deleteEvent({eventId:o.eventId});const{pairingTopic:p,proposer:y,requiredNamespaces:w,optionalNamespaces:v}=f;let b=(s=this.client.core.eventClient)==null?void 0:s.getEvent({topic:p});b||(b=(n=this.client.core.eventClient)==null?void 0:n.createEvent({type:at.session_approve_started,properties:{topic:p,trace:[at.session_approve_started,at.session_namespaces_validation_success]}}));const E=await this.client.core.crypto.generateKeyPair(),I=y.publicKey,x=await this.client.core.crypto.generateSharedKey(E,I),A=ie(ie(ie({relay:{protocol:c??"irn"},namespaces:l,controller:{publicKey:E,metadata:this.client.metadata},expiry:de(Pr)},h&&{sessionProperties:h}),u&&{scopedProperties:u}),d&&{sessionConfig:d}),P=ae.relay;b.addTrace(at.subscribing_session_topic);try{await this.client.core.relayer.subscribe(x,{transportType:P})}catch(S){throw b.setError(Jt.subscribe_session_topic_failure),S}b.addTrace(at.subscribe_session_topic_success);const C=Re(ie({},A),{topic:x,requiredNamespaces:w,optionalNamespaces:v,pairingTopic:p,acknowledged:!1,self:A.controller,peer:{publicKey:y.publicKey,metadata:y.metadata},controller:E,transportType:ae.relay});await this.client.session.set(x,C),b.addTrace(at.store_session);try{b.addTrace(at.publishing_session_settle),await this.sendRequest({topic:x,method:"wc_sessionSettle",params:A,throwOnFailedPublish:!0}).catch(S=>{throw b==null||b.setError(Jt.session_settle_publish_failure),S}),b.addTrace(at.session_settle_publish_success),b.addTrace(at.publishing_session_approve),await this.sendResult({id:a,topic:p,result:{relay:{protocol:c??"irn"},responderPublicKey:E},throwOnFailedPublish:!0}).catch(S=>{throw b==null||b.setError(Jt.session_approve_publish_failure),S}),b.addTrace(at.session_approve_publish_success)}catch(S){throw this.client.logger.error(S),this.client.session.delete(x,te("USER_DISCONNECTED")),await this.client.core.relayer.unsubscribe(x),S}return this.client.core.eventClient.deleteEvent({eventId:b.eventId}),await this.client.core.pairing.updateMetadata({topic:p,metadata:y.metadata}),await this.client.proposal.delete(a,te("USER_DISCONNECTED")),await this.client.core.pairing.activate({topic:p}),await this.setExpiry(x,de(Pr)),{topic:x,acknowledged:()=>Promise.resolve(this.client.session.get(x))}}),N(this,"reject",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidReject(r)}catch(o){throw this.client.logger.error("reject() -> isValidReject() failed"),o}const{id:i,reason:s}=r;let n;try{n=this.client.proposal.get(i).pairingTopic}catch(o){throw this.client.logger.error(`reject() -> proposal.get(${i}) failed`),o}n&&(await this.sendError({id:i,topic:n,error:s,rpcOpts:ge.wc_sessionPropose.reject}),await this.client.proposal.delete(i,te("USER_DISCONNECTED")))}),N(this,"update",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidUpdate(r)}catch(u){throw this.client.logger.error("update() -> isValidUpdate() failed"),u}const{topic:i,namespaces:s}=r,{done:n,resolve:o,reject:a}=Qt(),c=wt(),l=nr().toString(),h=this.client.session.get(i).namespaces;return this.events.once(X("session_update",c),({error:u})=>{u?a(u):o()}),await this.client.session.update(i,{namespaces:s}),await this.sendRequest({topic:i,method:"wc_sessionUpdate",params:{namespaces:s},throwOnFailedPublish:!0,clientRpcId:c,relayRpcId:l}).catch(u=>{this.client.logger.error(u),this.client.session.update(i,{namespaces:h}),a(u)}),{acknowledged:n}}),N(this,"extend",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidExtend(r)}catch(c){throw this.client.logger.error("extend() -> isValidExtend() failed"),c}const{topic:i}=r,s=wt(),{done:n,resolve:o,reject:a}=Qt();return this.events.once(X("session_extend",s),({error:c})=>{c?a(c):o()}),await this.setExpiry(i,de(Pr)),this.sendRequest({topic:i,method:"wc_sessionExtend",params:{},clientRpcId:s,throwOnFailedPublish:!0}).catch(c=>{a(c)}),{acknowledged:n}}),N(this,"request",async r=>{this.isInitialized();try{await this.isValidRequest(r)}catch(v){throw this.client.logger.error("request() -> isValidRequest() failed"),v}const{chainId:i,request:s,topic:n,expiry:o=ge.wc_sessionRequest.req.ttl}=r,a=this.client.session.get(n);(a==null?void 0:a.transportType)===ae.relay&&await this.confirmOnlineStateOrThrow();const c=wt(),l=nr().toString(),{done:h,resolve:u,reject:d}=Qt(o,"Request expired. Please try again.");this.events.once(X("session_request",c),({error:v,result:b})=>{v?d(v):u(b)});const f="wc_sessionRequest",p=this.getAppLinkIfEnabled(a.peer.metadata,a.transportType);if(p)return await this.sendRequest({clientRpcId:c,relayRpcId:l,topic:n,method:f,params:{request:Re(ie({},s),{expiryTimestamp:de(o)}),chainId:i},expiry:o,throwOnFailedPublish:!0,appLink:p}).catch(v=>d(v)),this.client.events.emit("session_request_sent",{topic:n,request:s,chainId:i,id:c}),await h();const y={request:Re(ie({},s),{expiryTimestamp:de(o)}),chainId:i},w=this.shouldSetTVF(f,y);return await Promise.all([new Promise(async v=>{await this.sendRequest(ie({clientRpcId:c,relayRpcId:l,topic:n,method:f,params:y,expiry:o,throwOnFailedPublish:!0},w&&{tvf:this.getTVFParams(c,y)})).catch(b=>d(b)),this.client.events.emit("session_request_sent",{topic:n,request:s,chainId:i,id:c}),v()}),new Promise(async v=>{var b;if(!((b=a.sessionConfig)!=null&&b.disableDeepLink)){const E=await Hw(this.client.core.storage,xl);await Mw({id:c,topic:n,wcDeepLink:E})}v()}),h()]).then(v=>v[2])}),N(this,"respond",async r=>{this.isInitialized(),await this.isValidRespond(r);const{topic:i,response:s}=r,{id:n}=s,o=this.client.session.get(i);o.transportType===ae.relay&&await this.confirmOnlineStateOrThrow();const a=this.getAppLinkIfEnabled(o.peer.metadata,o.transportType);bt(s)?await this.sendResult({id:n,topic:i,result:s.result,throwOnFailedPublish:!0,appLink:a}):et(s)&&await this.sendError({id:n,topic:i,error:s.error,appLink:a}),this.cleanupAfterResponse(r)}),N(this,"ping",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidPing(r)}catch(s){throw this.client.logger.error("ping() -> isValidPing() failed"),s}const{topic:i}=r;if(this.client.session.keys.includes(i)){const s=wt(),n=nr().toString(),{done:o,resolve:a,reject:c}=Qt();this.events.once(X("session_ping",s),({error:l})=>{l?c(l):a()}),await Promise.all([this.sendRequest({topic:i,method:"wc_sessionPing",params:{},throwOnFailedPublish:!0,clientRpcId:s,relayRpcId:n}),o()])}else this.client.core.pairing.pairings.keys.includes(i)&&(this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."),await this.client.core.pairing.ping({topic:i}))}),N(this,"emit",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidEmit(r);const{topic:i,event:s,chainId:n}=r,o=nr().toString(),a=wt();await this.sendRequest({topic:i,method:"wc_sessionEvent",params:{event:s,chainId:n},throwOnFailedPublish:!0,relayRpcId:o,clientRpcId:a})}),N(this,"disconnect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidDisconnect(r);const{topic:i}=r;if(this.client.session.keys.includes(i))await this.sendRequest({topic:i,method:"wc_sessionDelete",params:te("USER_DISCONNECTED"),throwOnFailedPublish:!0}),await this.deleteSession({topic:i,emitEvent:!1});else if(this.client.core.pairing.pairings.keys.includes(i))await this.client.core.pairing.disconnect({topic:i});else{const{message:s}=B("MISMATCHED_TOPIC",`Session or pairing topic not found: ${i}`);throw new Error(s)}}),N(this,"find",r=>(this.isInitialized(),this.client.session.getAll().filter(i=>j0(i,r)))),N(this,"getPendingSessionRequests",()=>this.client.pendingRequest.getAll()),N(this,"authenticate",async(r,i)=>{var s;this.isInitialized(),this.isValidAuthenticate(r);const n=i&&this.client.core.linkModeSupportedApps.includes(i)&&((s=this.client.metadata.redirect)==null?void 0:s.linkMode),o=n?ae.link_mode:ae.relay;o===ae.relay&&await this.confirmOnlineStateOrThrow();const{chains:a,statement:c="",uri:l,domain:h,nonce:u,type:d,exp:f,nbf:p,methods:y=[],expiry:w}=r,v=[...r.resources||[]],{topic:b,uri:E}=await this.client.core.pairing.create({methods:["wc_sessionAuthenticate"],transportType:o});this.client.logger.info({message:"Generated new pairing",pairing:{topic:b,uri:E}});const I=await this.client.core.crypto.generateKeyPair(),x=us(I);if(await Promise.all([this.client.auth.authKeys.set(gs,{responseTopic:x,publicKey:I}),this.client.auth.pairingTopics.set(x,{topic:x,pairingTopic:b})]),await this.client.core.relayer.subscribe(x,{transportType:o}),this.client.logger.info(`sending request to new pairing topic: ${b}`),y.length>0){const{namespace:$}=Hr(a[0]);let T=Db($,"request",y);hs(v)&&(T=Lb(T,v.pop())),v.push(T)}const A=w&&w>ge.wc_sessionAuthenticate.req.ttl?w:ge.wc_sessionAuthenticate.req.ttl,P={authPayload:{type:d??"caip122",chains:a,statement:c,aud:l,domain:h,version:"1",nonce:u,iat:new Date().toISOString(),exp:f,nbf:p,resources:v},requester:{publicKey:I,metadata:this.client.metadata},expiryTimestamp:de(A)},C={eip155:{chains:a,methods:[...new Set(["personal_sign",...y])],events:["chainChanged","accountsChanged"]}},S={requiredNamespaces:{},optionalNamespaces:C,relays:[{protocol:"irn"}],pairingTopic:b,proposer:{publicKey:I,metadata:this.client.metadata},expiryTimestamp:de(ge.wc_sessionPropose.req.ttl),id:wt()},{done:M,resolve:U,reject:D}=Qt(A,"Request expired"),F=wt(),R=X("session_connect",S.id),g=X("session_request",F),m=async({error:$,session:T})=>{this.events.off(g,_),$?D($):T&&U({session:T})},_=async $=>{var T,L,q;if(await this.deletePendingAuthRequest(F,{message:"fulfilled",code:0}),$.error){const Y=te("WC_METHOD_UNSUPPORTED","wc_sessionAuthenticate");return $.error.code===Y.code?void 0:(this.events.off(R,m),D($.error.message))}await this.deleteProposal(S.id),this.events.off(R,m);const{cacaos:z,responder:k}=$.result,H=[],G=[];for(const Y of z){await cc({cacao:Y,projectId:this.client.core.projectId})||(this.client.logger.error(Y,"Signature verification failed"),D(te("SESSION_SETTLEMENT_FAILED","Signature verification failed")));const{p:Ae}=Y,Ee=hs(Ae.resources),Ce=[Wn(Ae.iss)],Ye=vs(Ae.iss);if(Ee){const Je=lc(Ee),wr=hc(Ee);H.push(...Je),Ce.push(...wr)}for(const Je of Ce)G.push(`${Je}:${Ye}`)}const se=await this.client.core.crypto.generateSharedKey(I,k.publicKey);let ee;H.length>0&&(ee={topic:se,acknowledged:!0,self:{publicKey:I,metadata:this.client.metadata},peer:k,controller:k.publicKey,expiry:de(Pr),requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:b,namespaces:Bc([...new Set(H)],[...new Set(G)]),transportType:o},await this.client.core.relayer.subscribe(se,{transportType:o}),await this.client.session.set(se,ee),b&&await this.client.core.pairing.updateMetadata({topic:b,metadata:k.metadata}),ee=this.client.session.get(se)),(T=this.client.metadata.redirect)!=null&&T.linkMode&&(L=k.metadata.redirect)!=null&&L.linkMode&&(q=k.metadata.redirect)!=null&&q.universal&&i&&(this.client.core.addLinkModeSupportedApp(k.metadata.redirect.universal),this.client.session.update(se,{transportType:ae.link_mode})),U({auths:z,session:ee})};this.events.once(R,m),this.events.once(g,_);let O;try{if(n){const $=Ht("wc_sessionAuthenticate",P,F);this.client.core.history.set(b,$);const T=await this.client.core.crypto.encode("",$,{type:Fi,encoding:Ft});O=Xi(i,b,T)}else await Promise.all([this.sendRequest({topic:b,method:"wc_sessionAuthenticate",params:P,expiry:r.expiry,throwOnFailedPublish:!0,clientRpcId:F}),this.sendRequest({topic:b,method:"wc_sessionPropose",params:S,expiry:ge.wc_sessionPropose.req.ttl,throwOnFailedPublish:!0,clientRpcId:S.id})])}catch($){throw this.events.off(R,m),this.events.off(g,_),$}return await this.setProposal(S.id,S),await this.setAuthRequest(F,{request:Re(ie({},P),{verifyContext:{}}),pairingTopic:b,transportType:o}),{uri:O??E,response:M}}),N(this,"approveSessionAuthenticate",async r=>{const{id:i,auths:s}=r,n=this.client.core.eventClient.createEvent({properties:{topic:i.toString(),trace:[Zt.authenticated_session_approve_started]}});try{this.isInitialized()}catch(w){throw n.setError(fi.no_internet_connection),w}const o=this.getPendingAuthRequest(i);if(!o)throw n.setError(fi.authenticated_session_pending_request_not_found),new Error(`Could not find pending auth request with id ${i}`);const a=o.transportType||ae.relay;a===ae.relay&&await this.confirmOnlineStateOrThrow();const c=o.requester.publicKey,l=await this.client.core.crypto.generateKeyPair(),h=us(c),u={type:Bt,receiverPublicKey:c,senderPublicKey:l},d=[],f=[];for(const w of s){if(!await cc({cacao:w,projectId:this.client.core.projectId})){n.setError(fi.invalid_cacao);const x=te("SESSION_SETTLEMENT_FAILED","Signature verification failed");throw await this.sendError({id:i,topic:h,error:x,encodeOpts:u}),new Error(x.message)}n.addTrace(Zt.cacaos_verified);const{p:v}=w,b=hs(v.resources),E=[Wn(v.iss)],I=vs(v.iss);if(b){const x=lc(b),A=hc(b);d.push(...x),E.push(...A)}for(const x of E)f.push(`${x}:${I}`)}const p=await this.client.core.crypto.generateSharedKey(l,c);n.addTrace(Zt.create_authenticated_session_topic);let y;if((d==null?void 0:d.length)>0){y={topic:p,acknowledged:!0,self:{publicKey:l,metadata:this.client.metadata},peer:{publicKey:c,metadata:o.requester.metadata},controller:c,expiry:de(Pr),authentication:s,requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:o.pairingTopic,namespaces:Bc([...new Set(d)],[...new Set(f)]),transportType:a},n.addTrace(Zt.subscribing_authenticated_session_topic);try{await this.client.core.relayer.subscribe(p,{transportType:a})}catch(w){throw n.setError(fi.subscribe_authenticated_session_topic_failure),w}n.addTrace(Zt.subscribe_authenticated_session_topic_success),await this.client.session.set(p,y),n.addTrace(Zt.store_authenticated_session),await this.client.core.pairing.updateMetadata({topic:o.pairingTopic,metadata:o.requester.metadata})}n.addTrace(Zt.publishing_authenticated_session_approve);try{await this.sendResult({topic:h,id:i,result:{cacaos:s,responder:{publicKey:l,metadata:this.client.metadata}},encodeOpts:u,throwOnFailedPublish:!0,appLink:this.getAppLinkIfEnabled(o.requester.metadata,a)})}catch(w){throw n.setError(fi.authenticated_session_approve_publish_failure),w}return await this.client.auth.requests.delete(i,{message:"fulfilled",code:0}),await this.client.core.pairing.activate({topic:o.pairingTopic}),this.client.core.eventClient.deleteEvent({eventId:n.eventId}),{session:y}}),N(this,"rejectSessionAuthenticate",async r=>{this.isInitialized();const{id:i,reason:s}=r,n=this.getPendingAuthRequest(i);if(!n)throw new Error(`Could not find pending auth request with id ${i}`);n.transportType===ae.relay&&await this.confirmOnlineStateOrThrow();const o=n.requester.publicKey,a=await this.client.core.crypto.generateKeyPair(),c=us(o),l={type:Bt,receiverPublicKey:o,senderPublicKey:a};await this.sendError({id:i,topic:c,error:s,encodeOpts:l,rpcOpts:ge.wc_sessionAuthenticate.reject,appLink:this.getAppLinkIfEnabled(n.requester.metadata,n.transportType)}),await this.client.auth.requests.delete(i,{message:"rejected",code:0}),await this.client.proposal.delete(i,te("USER_DISCONNECTED"))}),N(this,"formatAuthMessage",r=>{this.isInitialized();const{request:i,iss:s}=r;return pu(i,s)}),N(this,"processRelayMessageCache",()=>{setTimeout(async()=>{if(this.relayMessageCache.length!==0)for(;this.relayMessageCache.length>0;)try{const r=this.relayMessageCache.shift();r&&await this.onRelayMessage(r)}catch(r){this.client.logger.error(r)}},50)}),N(this,"cleanupDuplicatePairings",async r=>{if(r.pairingTopic)try{const i=this.client.core.pairing.pairings.get(r.pairingTopic),s=this.client.core.pairing.pairings.getAll().filter(n=>{var o,a;return((o=n.peerMetadata)==null?void 0:o.url)&&((a=n.peerMetadata)==null?void 0:a.url)===r.peer.metadata.url&&n.topic&&n.topic!==i.topic});if(s.length===0)return;this.client.logger.info(`Cleaning up ${s.length} duplicate pairing(s)`),await Promise.all(s.map(n=>this.client.core.pairing.disconnect({topic:n.topic}))),this.client.logger.info("Duplicate pairings clean up finished")}catch(i){this.client.logger.error(i)}}),N(this,"deleteSession",async r=>{var i;const{topic:s,expirerHasDeleted:n=!1,emitEvent:o=!0,id:a=0}=r,{self:c}=this.client.session.get(s);await this.client.core.relayer.unsubscribe(s),await this.client.session.delete(s,te("USER_DISCONNECTED")),this.addToRecentlyDeleted(s,"session"),this.client.core.crypto.keychain.has(c.publicKey)&&await this.client.core.crypto.deleteKeyPair(c.publicKey),this.client.core.crypto.keychain.has(s)&&await this.client.core.crypto.deleteSymKey(s),n||this.client.core.expirer.del(s),this.client.core.storage.removeItem(xl).catch(l=>this.client.logger.warn(l)),this.getPendingSessionRequests().forEach(l=>{l.topic===s&&this.deletePendingSessionRequest(l.id,te("USER_DISCONNECTED"))}),s===((i=this.sessionRequestQueue.queue[0])==null?void 0:i.topic)&&(this.sessionRequestQueue.state=yt.idle),o&&this.client.events.emit("session_delete",{id:a,topic:s})}),N(this,"deleteProposal",async(r,i)=>{if(i)try{const s=this.client.proposal.get(r),n=this.client.core.eventClient.getEvent({topic:s.pairingTopic});n==null||n.setError(Jt.proposal_expired)}catch{}await Promise.all([this.client.proposal.delete(r,te("USER_DISCONNECTED")),i?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"proposal")}),N(this,"deletePendingSessionRequest",async(r,i,s=!1)=>{await Promise.all([this.client.pendingRequest.delete(r,i),s?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"request"),this.sessionRequestQueue.queue=this.sessionRequestQueue.queue.filter(n=>n.id!==r),s&&(this.sessionRequestQueue.state=yt.idle,this.client.events.emit("session_request_expire",{id:r}))}),N(this,"deletePendingAuthRequest",async(r,i,s=!1)=>{await Promise.all([this.client.auth.requests.delete(r,i),s?Promise.resolve():this.client.core.expirer.del(r)])}),N(this,"setExpiry",async(r,i)=>{this.client.session.keys.includes(r)&&(this.client.core.expirer.set(r,i),await this.client.session.update(r,{expiry:i}))}),N(this,"setProposal",async(r,i)=>{this.client.core.expirer.set(r,de(ge.wc_sessionPropose.req.ttl)),await this.client.proposal.set(r,i)}),N(this,"setAuthRequest",async(r,i)=>{const{request:s,pairingTopic:n,transportType:o=ae.relay}=i;this.client.core.expirer.set(r,s.expiryTimestamp),await this.client.auth.requests.set(r,{authPayload:s.authPayload,requester:s.requester,expiryTimestamp:s.expiryTimestamp,id:r,pairingTopic:n,verifyContext:s.verifyContext,transportType:o})}),N(this,"setPendingSessionRequest",async r=>{const{id:i,topic:s,params:n,verifyContext:o}=r,a=n.request.expiryTimestamp||de(ge.wc_sessionRequest.req.ttl);this.client.core.expirer.set(i,a),await this.client.pendingRequest.set(i,{id:i,topic:s,params:n,verifyContext:o})}),N(this,"sendRequest",async r=>{const{topic:i,method:s,params:n,expiry:o,relayRpcId:a,clientRpcId:c,throwOnFailedPublish:l,appLink:h,tvf:u}=r,d=Ht(s,n,c);let f;const p=!!h;try{const v=p?Ft:ht;f=await this.client.core.crypto.encode(i,d,{encoding:v})}catch(v){throw await this.cleanup(),this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${i} failed`),v}let y;if(hS.includes(s)){const v=Et(JSON.stringify(d)),b=Et(f);y=await this.client.core.verify.register({id:b,decryptedId:v})}const w=ge[s].req;if(w.attestation=y,o&&(w.ttl=o),a&&(w.id=a),this.client.core.history.set(i,d),p){const v=Xi(h,i,f);await global.Linking.openURL(v,this.client.name)}else{const v=ge[s].req;o&&(v.ttl=o),a&&(v.id=a),v.tvf=Re(ie({},u),{correlationId:d.id}),l?(v.internal=Re(ie({},v.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(i,f,v)):this.client.core.relayer.publish(i,f,v).catch(b=>this.client.logger.error(b))}return d.id}),N(this,"sendResult",async r=>{const{id:i,topic:s,result:n,throwOnFailedPublish:o,encodeOpts:a,appLink:c}=r,l=Fs(i,n);let h;const u=c&&typeof(global==null?void 0:global.Linking)<"u";try{const p=u?Ft:ht;h=await this.client.core.crypto.encode(s,l,Re(ie({},a||{}),{encoding:p}))}catch(p){throw await this.cleanup(),this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${s} failed`),p}let d,f;try{d=await this.client.core.history.get(s,i);const p=d.request;try{this.shouldSetTVF(p.method,p.params)&&(f=this.getTVFParams(i,p.params,n))}catch(y){this.client.logger.warn("sendResult() -> getTVFParams() failed",y)}}catch(p){throw this.client.logger.error(`sendResult() -> history.get(${s}, ${i}) failed`),p}if(u){const p=Xi(c,s,h);await global.Linking.openURL(p,this.client.name)}else{const p=d.request.method,y=ge[p].res;y.tvf=Re(ie({},f),{correlationId:i}),o?(y.internal=Re(ie({},y.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(s,h,y)):this.client.core.relayer.publish(s,h,y).catch(w=>this.client.logger.error(w))}await this.client.core.history.resolve(l)}),N(this,"sendError",async r=>{const{id:i,topic:s,error:n,encodeOpts:o,rpcOpts:a,appLink:c}=r,l=zs(i,n);let h;const u=c&&typeof(global==null?void 0:global.Linking)<"u";try{const f=u?Ft:ht;h=await this.client.core.crypto.encode(s,l,Re(ie({},o||{}),{encoding:f}))}catch(f){throw await this.cleanup(),this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${s} failed`),f}let d;try{d=await this.client.core.history.get(s,i)}catch(f){throw this.client.logger.error(`sendError() -> history.get(${s}, ${i}) failed`),f}if(u){const f=Xi(c,s,h);await global.Linking.openURL(f,this.client.name)}else{const f=d.request.method,p=a||ge[f].res;this.client.core.relayer.publish(s,h,p)}await this.client.core.history.resolve(l)}),N(this,"cleanup",async()=>{const r=[],i=[];this.client.session.getAll().forEach(s=>{let n=!1;Mt(s.expiry)&&(n=!0),this.client.core.crypto.keychain.has(s.topic)||(n=!0),n&&r.push(s.topic)}),this.client.proposal.getAll().forEach(s=>{Mt(s.expiryTimestamp)&&i.push(s.id)}),await Promise.all([...r.map(s=>this.deleteSession({topic:s})),...i.map(s=>this.deleteProposal(s))])}),N(this,"onProviderMessageEvent",async r=>{!this.initialized||this.relayMessageCache.length>0?this.relayMessageCache.push(r):await this.onRelayMessage(r)}),N(this,"onRelayEventRequest",async r=>{this.requestQueue.queue.push(r),await this.processRequestsQueue()}),N(this,"processRequestsQueue",async()=>{if(this.requestQueue.state===yt.active){this.client.logger.info("Request queue already active, skipping...");return}for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`);this.requestQueue.queue.length>0;){this.requestQueue.state=yt.active;const r=this.requestQueue.queue.shift();if(r)try{await this.processRequest(r)}catch(i){this.client.logger.warn(i)}}this.requestQueue.state=yt.idle}),N(this,"processRequest",async r=>{const{topic:i,payload:s,attestation:n,transportType:o,encryptedId:a}=r,c=s.method;if(!this.shouldIgnorePairingRequest({topic:i,requestMethod:c}))switch(c){case"wc_sessionPropose":return await this.onSessionProposeRequest({topic:i,payload:s,attestation:n,encryptedId:a});case"wc_sessionSettle":return await this.onSessionSettleRequest(i,s);case"wc_sessionUpdate":return await this.onSessionUpdateRequest(i,s);case"wc_sessionExtend":return await this.onSessionExtendRequest(i,s);case"wc_sessionPing":return await this.onSessionPingRequest(i,s);case"wc_sessionDelete":return await this.onSessionDeleteRequest(i,s);case"wc_sessionRequest":return await this.onSessionRequest({topic:i,payload:s,attestation:n,encryptedId:a,transportType:o});case"wc_sessionEvent":return await this.onSessionEventRequest(i,s);case"wc_sessionAuthenticate":return await this.onSessionAuthenticateRequest({topic:i,payload:s,attestation:n,encryptedId:a,transportType:o});default:return this.client.logger.info(`Unsupported request method ${c}`)}}),N(this,"onRelayEventResponse",async r=>{const{topic:i,payload:s,transportType:n}=r,o=(await this.client.core.history.get(i,s.id)).request.method;switch(o){case"wc_sessionPropose":return this.onSessionProposeResponse(i,s,n);case"wc_sessionSettle":return this.onSessionSettleResponse(i,s);case"wc_sessionUpdate":return this.onSessionUpdateResponse(i,s);case"wc_sessionExtend":return this.onSessionExtendResponse(i,s);case"wc_sessionPing":return this.onSessionPingResponse(i,s);case"wc_sessionRequest":return this.onSessionRequestResponse(i,s);case"wc_sessionAuthenticate":return this.onSessionAuthenticateResponse(i,s);default:return this.client.logger.info(`Unsupported response method ${o}`)}}),N(this,"onRelayEventUnknownPayload",r=>{const{topic:i}=r,{message:s}=B("MISSING_OR_INVALID",`Decoded payload on topic ${i} is not identifiable as a JSON-RPC request or a response.`);throw new Error(s)}),N(this,"shouldIgnorePairingRequest",r=>{const{topic:i,requestMethod:s}=r,n=this.expectedPairingMethodMap.get(i);return!n||n.includes(s)?!1:!!(n.includes("wc_sessionAuthenticate")&&this.client.events.listenerCount("session_authenticate")>0)}),N(this,"onSessionProposeRequest",async r=>{const{topic:i,payload:s,attestation:n,encryptedId:o}=r,{params:a,id:c}=s;try{const l=this.client.core.eventClient.getEvent({topic:i});this.client.events.listenerCount("session_proposal")===0&&(console.warn("No listener for session_proposal event"),l==null||l.setError(Ct.proposal_listener_not_found)),this.isValidConnect(ie({},s.params));const h=a.expiryTimestamp||de(ge.wc_sessionPropose.req.ttl),u=ie({id:c,pairingTopic:i,expiryTimestamp:h},a);await this.setProposal(c,u);const d=await this.getVerifyContext({attestationId:n,hash:Et(JSON.stringify(s)),encryptedId:o,metadata:u.proposer.metadata});l==null||l.addTrace(mt.emit_session_proposal),this.client.events.emit("session_proposal",{id:c,params:u,verifyContext:d})}catch(l){await this.sendError({id:c,topic:i,error:l,rpcOpts:ge.wc_sessionPropose.autoReject}),this.client.logger.error(l)}}),N(this,"onSessionProposeResponse",async(r,i,s)=>{const{id:n}=i;if(bt(i)){const{result:o}=i;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",result:o});const a=this.client.proposal.get(n);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",proposal:a});const c=a.proposer.publicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",selfPublicKey:c});const l=o.responderPublicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",peerPublicKey:l});const h=await this.client.core.crypto.generateSharedKey(c,l);this.pendingSessions.set(n,{sessionTopic:h,pairingTopic:r,proposalId:n,publicKey:c});const u=await this.client.core.relayer.subscribe(h,{transportType:s});this.client.logger.trace({type:"method",method:"onSessionProposeResponse",subscriptionId:u}),await this.client.core.pairing.activate({topic:r})}else if(et(i)){await this.client.proposal.delete(n,te("USER_DISCONNECTED"));const o=X("session_connect",n);if(this.events.listenerCount(o)===0)throw new Error(`emitting ${o} without any listeners, 954`);this.events.emit(o,{error:i.error})}}),N(this,"onSessionSettleRequest",async(r,i)=>{const{id:s,params:n}=i;try{this.isValidSessionSettleRequest(n);const{relay:o,controller:a,expiry:c,namespaces:l,sessionProperties:h,scopedProperties:u,sessionConfig:d}=i.params,f=[...this.pendingSessions.values()].find(w=>w.sessionTopic===r);if(!f)return this.client.logger.error(`Pending session not found for topic ${r}`);const p=this.client.proposal.get(f.proposalId),y=Re(ie(ie(ie({topic:r,relay:o,expiry:c,namespaces:l,acknowledged:!0,pairingTopic:f.pairingTopic,requiredNamespaces:p.requiredNamespaces,optionalNamespaces:p.optionalNamespaces,controller:a.publicKey,self:{publicKey:f.publicKey,metadata:this.client.metadata},peer:{publicKey:a.publicKey,metadata:a.metadata}},h&&{sessionProperties:h}),u&&{scopedProperties:u}),d&&{sessionConfig:d}),{transportType:ae.relay});await this.client.session.set(y.topic,y),await this.setExpiry(y.topic,y.expiry),await this.client.core.pairing.updateMetadata({topic:f.pairingTopic,metadata:y.peer.metadata}),this.client.events.emit("session_connect",{session:y}),this.events.emit(X("session_connect",f.proposalId),{session:y}),this.pendingSessions.delete(f.proposalId),this.deleteProposal(f.proposalId,!1),this.cleanupDuplicatePairings(y),await this.sendResult({id:i.id,topic:r,result:!0,throwOnFailedPublish:!0})}catch(o){await this.sendError({id:s,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"onSessionSettleResponse",async(r,i)=>{const{id:s}=i;bt(i)?(await this.client.session.update(r,{acknowledged:!0}),this.events.emit(X("session_approve",s),{})):et(i)&&(await this.client.session.delete(r,te("USER_DISCONNECTED")),this.events.emit(X("session_approve",s),{error:i.error}))}),N(this,"onSessionUpdateRequest",async(r,i)=>{const{params:s,id:n}=i;try{const o=`${r}_session_update`,a=di.get(o);if(a&&this.isRequestOutOfSync(a,n)){this.client.logger.warn(`Discarding out of sync request - ${n}`),this.sendError({id:n,topic:r,error:te("INVALID_UPDATE_REQUEST")});return}this.isValidUpdate(ie({topic:r},s));try{di.set(o,n),await this.client.session.update(r,{namespaces:s.namespaces}),await this.sendResult({id:n,topic:r,result:!0,throwOnFailedPublish:!0})}catch(c){throw di.delete(o),c}this.client.events.emit("session_update",{id:n,topic:r,params:s})}catch(o){await this.sendError({id:n,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"isRequestOutOfSync",(r,i)=>i.toString().slice(0,-3)<r.toString().slice(0,-3)),N(this,"onSessionUpdateResponse",(r,i)=>{const{id:s}=i,n=X("session_update",s);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);bt(i)?this.events.emit(X("session_update",s),{}):et(i)&&this.events.emit(X("session_update",s),{error:i.error})}),N(this,"onSessionExtendRequest",async(r,i)=>{const{id:s}=i;try{this.isValidExtend({topic:r}),await this.setExpiry(r,de(Pr)),await this.sendResult({id:s,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_extend",{id:s,topic:r})}catch(n){await this.sendError({id:s,topic:r,error:n}),this.client.logger.error(n)}}),N(this,"onSessionExtendResponse",(r,i)=>{const{id:s}=i,n=X("session_extend",s);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);bt(i)?this.events.emit(X("session_extend",s),{}):et(i)&&this.events.emit(X("session_extend",s),{error:i.error})}),N(this,"onSessionPingRequest",async(r,i)=>{const{id:s}=i;try{this.isValidPing({topic:r}),await this.sendResult({id:s,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_ping",{id:s,topic:r})}catch(n){await this.sendError({id:s,topic:r,error:n}),this.client.logger.error(n)}}),N(this,"onSessionPingResponse",(r,i)=>{const{id:s}=i,n=X("session_ping",s);setTimeout(()=>{if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners 2176`);bt(i)?this.events.emit(X("session_ping",s),{}):et(i)&&this.events.emit(X("session_ping",s),{error:i.error})},500)}),N(this,"onSessionDeleteRequest",async(r,i)=>{const{id:s}=i;try{this.isValidDisconnect({topic:r,reason:i.params}),Promise.all([new Promise(n=>{this.client.core.relayer.once(we.publish,async()=>{n(await this.deleteSession({topic:r,id:s}))})}),this.sendResult({id:s,topic:r,result:!0,throwOnFailedPublish:!0}),this.cleanupPendingSentRequestsForTopic({topic:r,error:te("USER_DISCONNECTED")})]).catch(n=>this.client.logger.error(n))}catch(n){this.client.logger.error(n)}}),N(this,"onSessionRequest",async r=>{var i,s,n;const{topic:o,payload:a,attestation:c,encryptedId:l,transportType:h}=r,{id:u,params:d}=a;try{await this.isValidRequest(ie({topic:o},d));const f=this.client.session.get(o),p=await this.getVerifyContext({attestationId:c,hash:Et(JSON.stringify(Ht("wc_sessionRequest",d,u))),encryptedId:l,metadata:f.peer.metadata,transportType:h}),y={id:u,topic:o,params:d,verifyContext:p};await this.setPendingSessionRequest(y),h===ae.link_mode&&(i=f.peer.metadata.redirect)!=null&&i.universal&&this.client.core.addLinkModeSupportedApp((s=f.peer.metadata.redirect)==null?void 0:s.universal),(n=this.client.signConfig)!=null&&n.disableRequestQueue?this.emitSessionRequest(y):(this.addSessionRequestToSessionRequestQueue(y),this.processSessionRequestQueue())}catch(f){await this.sendError({id:u,topic:o,error:f}),this.client.logger.error(f)}}),N(this,"onSessionRequestResponse",(r,i)=>{const{id:s}=i,n=X("session_request",s);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);bt(i)?this.events.emit(X("session_request",s),{result:i.result}):et(i)&&this.events.emit(X("session_request",s),{error:i.error})}),N(this,"onSessionEventRequest",async(r,i)=>{const{id:s,params:n}=i;try{const o=`${r}_session_event_${n.event.name}`,a=di.get(o);if(a&&this.isRequestOutOfSync(a,s)){this.client.logger.info(`Discarding out of sync request - ${s}`);return}this.isValidEmit(ie({topic:r},n)),this.client.events.emit("session_event",{id:s,topic:r,params:n}),di.set(o,s)}catch(o){await this.sendError({id:s,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"onSessionAuthenticateResponse",(r,i)=>{const{id:s}=i;this.client.logger.trace({type:"method",method:"onSessionAuthenticateResponse",topic:r,payload:i}),bt(i)?this.events.emit(X("session_request",s),{result:i.result}):et(i)&&this.events.emit(X("session_request",s),{error:i.error})}),N(this,"onSessionAuthenticateRequest",async r=>{var i;const{topic:s,payload:n,attestation:o,encryptedId:a,transportType:c}=r;try{const{requester:l,authPayload:h,expiryTimestamp:u}=n.params,d=await this.getVerifyContext({attestationId:o,hash:Et(JSON.stringify(n)),encryptedId:a,metadata:l.metadata,transportType:c}),f={requester:l,pairingTopic:s,id:n.id,authPayload:h,verifyContext:d,expiryTimestamp:u};await this.setAuthRequest(n.id,{request:f,pairingTopic:s,transportType:c}),c===ae.link_mode&&(i=l.metadata.redirect)!=null&&i.universal&&this.client.core.addLinkModeSupportedApp(l.metadata.redirect.universal),this.client.events.emit("session_authenticate",{topic:s,params:n.params,id:n.id,verifyContext:d})}catch(l){this.client.logger.error(l);const h=n.params.requester.publicKey,u=await this.client.core.crypto.generateKeyPair(),d=this.getAppLinkIfEnabled(n.params.requester.metadata,c),f={type:Bt,receiverPublicKey:h,senderPublicKey:u};await this.sendError({id:n.id,topic:s,error:l,encodeOpts:f,rpcOpts:ge.wc_sessionAuthenticate.autoReject,appLink:d})}}),N(this,"addSessionRequestToSessionRequestQueue",r=>{this.sessionRequestQueue.queue.push(r)}),N(this,"cleanupAfterResponse",r=>{this.deletePendingSessionRequest(r.response.id,{message:"fulfilled",code:0}),setTimeout(()=>{this.sessionRequestQueue.state=yt.idle,this.processSessionRequestQueue()},j.toMiliseconds(this.requestQueueDelay))}),N(this,"cleanupPendingSentRequestsForTopic",({topic:r,error:i})=>{const s=this.client.core.history.pending;s.length>0&&s.filter(n=>n.topic===r&&n.request.method==="wc_sessionRequest").forEach(n=>{const o=n.request.id,a=X("session_request",o);if(this.events.listenerCount(a)===0)throw new Error(`emitting ${a} without any listeners`);this.events.emit(X("session_request",n.request.id),{error:i})})}),N(this,"processSessionRequestQueue",()=>{if(this.sessionRequestQueue.state===yt.active){this.client.logger.info("session request queue is already active.");return}const r=this.sessionRequestQueue.queue[0];if(!r){this.client.logger.info("session request queue is empty.");return}try{this.sessionRequestQueue.state=yt.active,this.emitSessionRequest(r)}catch(i){this.client.logger.error(i)}}),N(this,"emitSessionRequest",r=>{this.client.events.emit("session_request",r)}),N(this,"onPairingCreated",r=>{if(r.methods&&this.expectedPairingMethodMap.set(r.topic,r.methods),r.active)return;const i=this.client.proposal.getAll().find(s=>s.pairingTopic===r.topic);i&&this.onSessionProposeRequest({topic:r.topic,payload:Ht("wc_sessionPropose",Re(ie({},i),{requiredNamespaces:i.requiredNamespaces,optionalNamespaces:i.optionalNamespaces,relays:i.relays,proposer:i.proposer,sessionProperties:i.sessionProperties,scopedProperties:i.scopedProperties}),i.id)})}),N(this,"isValidConnect",async r=>{if(!Le(r)){const{message:l}=B("MISSING_OR_INVALID",`connect() params: ${JSON.stringify(r)}`);throw new Error(l)}const{pairingTopic:i,requiredNamespaces:s,optionalNamespaces:n,sessionProperties:o,scopedProperties:a,relays:c}=r;if(Se(i)||await this.isValidPairingTopic(i),!K0(c)){const{message:l}=B("MISSING_OR_INVALID",`connect() relays: ${c}`);throw new Error(l)}if(!Se(s)&&Kt(s)!==0){const l="requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";["fatal","error","silent"].includes(this.client.logger.level)?console.warn(l):this.client.logger.warn(l),this.validateNamespaces(s,"requiredNamespaces")}if(!Se(n)&&Kt(n)!==0&&this.validateNamespaces(n,"optionalNamespaces"),Se(o)||this.validateSessionProps(o,"sessionProperties"),!Se(a)){this.validateSessionProps(a,"scopedProperties");const l=Object.keys(s||{}).concat(Object.keys(n||{}));if(!Object.keys(a).every(h=>l.includes(h)))throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(a)}, required/optional namespaces: ${JSON.stringify(l)}`)}}),N(this,"validateNamespaces",(r,i)=>{const s=V0(r,"connect()",i);if(s)throw new Error(s.message)}),N(this,"isValidApprove",async r=>{if(!Le(r))throw new Error(B("MISSING_OR_INVALID",`approve() params: ${r}`).message);const{id:i,namespaces:s,relayProtocol:n,sessionProperties:o,scopedProperties:a}=r;this.checkRecentlyDeleted(i),await this.isValidProposalId(i);const c=this.client.proposal.get(i),l=vn(s,"approve()");if(l)throw new Error(l.message);const h=Lc(c.requiredNamespaces,s,"approve()");if(h)throw new Error(h.message);if(!ue(n,!0)){const{message:u}=B("MISSING_OR_INVALID",`approve() relayProtocol: ${n}`);throw new Error(u)}if(Se(o)||this.validateSessionProps(o,"sessionProperties"),!Se(a)){this.validateSessionProps(a,"scopedProperties");const u=new Set(Object.keys(s));if(!Object.keys(a).every(d=>u.has(d)))throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(a)}, approved namespaces: ${Array.from(u).join(", ")}`)}}),N(this,"isValidReject",async r=>{if(!Le(r)){const{message:n}=B("MISSING_OR_INVALID",`reject() params: ${r}`);throw new Error(n)}const{id:i,reason:s}=r;if(this.checkRecentlyDeleted(i),await this.isValidProposalId(i),!G0(s)){const{message:n}=B("MISSING_OR_INVALID",`reject() reason: ${JSON.stringify(s)}`);throw new Error(n)}}),N(this,"isValidSessionSettleRequest",r=>{if(!Le(r)){const{message:l}=B("MISSING_OR_INVALID",`onSessionSettleRequest() params: ${r}`);throw new Error(l)}const{relay:i,controller:s,namespaces:n,expiry:o}=r;if(!ku(i)){const{message:l}=B("MISSING_OR_INVALID","onSessionSettleRequest() relay protocol should be a string");throw new Error(l)}const a=k0(s,"onSessionSettleRequest()");if(a)throw new Error(a.message);const c=vn(n,"onSessionSettleRequest()");if(c)throw new Error(c.message);if(Mt(o)){const{message:l}=B("EXPIRED","onSessionSettleRequest()");throw new Error(l)}}),N(this,"isValidUpdate",async r=>{if(!Le(r)){const{message:c}=B("MISSING_OR_INVALID",`update() params: ${r}`);throw new Error(c)}const{topic:i,namespaces:s}=r;this.checkRecentlyDeleted(i),await this.isValidSessionTopic(i);const n=this.client.session.get(i),o=vn(s,"update()");if(o)throw new Error(o.message);const a=Lc(n.requiredNamespaces,s,"update()");if(a)throw new Error(a.message)}),N(this,"isValidExtend",async r=>{if(!Le(r)){const{message:s}=B("MISSING_OR_INVALID",`extend() params: ${r}`);throw new Error(s)}const{topic:i}=r;this.checkRecentlyDeleted(i),await this.isValidSessionTopic(i)}),N(this,"isValidRequest",async r=>{if(!Le(r)){const{message:c}=B("MISSING_OR_INVALID",`request() params: ${r}`);throw new Error(c)}const{topic:i,request:s,chainId:n,expiry:o}=r;this.checkRecentlyDeleted(i),await this.isValidSessionTopic(i);const{namespaces:a}=this.client.session.get(i);if(!Uc(a,n)){const{message:c}=B("MISSING_OR_INVALID",`request() chainId: ${n}`);throw new Error(c)}if(!Y0(s)){const{message:c}=B("MISSING_OR_INVALID",`request() ${JSON.stringify(s)}`);throw new Error(c)}if(!Q0(a,n,s.method)){const{message:c}=B("MISSING_OR_INVALID",`request() method: ${s.method}`);throw new Error(c)}if(o&&!r1(o,An)){const{message:c}=B("MISSING_OR_INVALID",`request() expiry: ${o}. Expiry must be a number (in seconds) between ${An.min} and ${An.max}`);throw new Error(c)}}),N(this,"isValidRespond",async r=>{var i;if(!Le(r)){const{message:o}=B("MISSING_OR_INVALID",`respond() params: ${r}`);throw new Error(o)}const{topic:s,response:n}=r;try{await this.isValidSessionTopic(s)}catch(o){throw(i=r==null?void 0:r.response)!=null&&i.id&&this.cleanupAfterResponse(r),o}if(!J0(n)){const{message:o}=B("MISSING_OR_INVALID",`respond() response: ${JSON.stringify(n)}`);throw new Error(o)}}),N(this,"isValidPing",async r=>{if(!Le(r)){const{message:s}=B("MISSING_OR_INVALID",`ping() params: ${r}`);throw new Error(s)}const{topic:i}=r;await this.isValidSessionOrPairingTopic(i)}),N(this,"isValidEmit",async r=>{if(!Le(r)){const{message:a}=B("MISSING_OR_INVALID",`emit() params: ${r}`);throw new Error(a)}const{topic:i,event:s,chainId:n}=r;await this.isValidSessionTopic(i);const{namespaces:o}=this.client.session.get(i);if(!Uc(o,n)){const{message:a}=B("MISSING_OR_INVALID",`emit() chainId: ${n}`);throw new Error(a)}if(!Z0(s)){const{message:a}=B("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(s)}`);throw new Error(a)}if(!X0(o,n,s.name)){const{message:a}=B("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(s)}`);throw new Error(a)}}),N(this,"isValidDisconnect",async r=>{if(!Le(r)){const{message:s}=B("MISSING_OR_INVALID",`disconnect() params: ${r}`);throw new Error(s)}const{topic:i}=r;await this.isValidSessionOrPairingTopic(i)}),N(this,"isValidAuthenticate",r=>{const{chains:i,uri:s,domain:n,nonce:o}=r;if(!Array.isArray(i)||i.length===0)throw new Error("chains is required and must be a non-empty array");if(!ue(s,!1))throw new Error("uri is required parameter");if(!ue(n,!1))throw new Error("domain is required parameter");if(!ue(o,!1))throw new Error("nonce is required parameter");if([...new Set(i.map(c=>Hr(c).namespace))].length>1)throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");const{namespace:a}=Hr(i[0]);if(a!=="eip155")throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.")}),N(this,"getVerifyContext",async r=>{const{attestationId:i,hash:s,encryptedId:n,metadata:o,transportType:a}=r,c={verified:{verifyUrl:o.verifyUrl||Ii,validation:"UNKNOWN",origin:o.url||""}};try{if(a===ae.link_mode){const h=this.getAppLinkIfEnabled(o,a);return c.verified.validation=h&&new URL(h).origin===new URL(o.url).origin?"VALID":"INVALID",c}const l=await this.client.core.verify.resolve({attestationId:i,hash:s,encryptedId:n,verifyUrl:o.verifyUrl});l&&(c.verified.origin=l.origin,c.verified.isScam=l.isScam,c.verified.validation=l.origin===new URL(o.url).origin?"VALID":"INVALID")}catch(l){this.client.logger.warn(l)}return this.client.logger.debug(`Verify context: ${JSON.stringify(c)}`),c}),N(this,"validateSessionProps",(r,i)=>{Object.values(r).forEach((s,n)=>{if(s==null){const{message:o}=B("MISSING_OR_INVALID",`${i} must contain an existing value for each key. Received: ${s} for key ${Object.keys(r)[n]}`);throw new Error(o)}})}),N(this,"getPendingAuthRequest",r=>{const i=this.client.auth.requests.get(r);return typeof i=="object"?i:void 0}),N(this,"addToRecentlyDeleted",(r,i)=>{if(this.recentlyDeletedMap.set(r,i),this.recentlyDeletedMap.size>=this.recentlyDeletedLimit){let s=0;const n=this.recentlyDeletedLimit/2;for(const o of this.recentlyDeletedMap.keys()){if(s++>=n)break;this.recentlyDeletedMap.delete(o)}}}),N(this,"checkRecentlyDeleted",r=>{const i=this.recentlyDeletedMap.get(r);if(i){const{message:s}=B("MISSING_OR_INVALID",`Record was recently deleted - ${i}: ${r}`);throw new Error(s)}}),N(this,"isLinkModeEnabled",(r,i)=>{var s,n,o,a,c,l,h,u,d;return!r||i!==ae.link_mode?!1:((n=(s=this.client.metadata)==null?void 0:s.redirect)==null?void 0:n.linkMode)===!0&&((a=(o=this.client.metadata)==null?void 0:o.redirect)==null?void 0:a.universal)!==void 0&&((l=(c=this.client.metadata)==null?void 0:c.redirect)==null?void 0:l.universal)!==""&&((h=r==null?void 0:r.redirect)==null?void 0:h.universal)!==void 0&&((u=r==null?void 0:r.redirect)==null?void 0:u.universal)!==""&&((d=r==null?void 0:r.redirect)==null?void 0:d.linkMode)===!0&&this.client.core.linkModeSupportedApps.includes(r.redirect.universal)&&typeof(global==null?void 0:global.Linking)<"u"}),N(this,"getAppLinkIfEnabled",(r,i)=>{var s;return this.isLinkModeEnabled(r,i)?(s=r==null?void 0:r.redirect)==null?void 0:s.universal:void 0}),N(this,"handleLinkModeMessage",({url:r})=>{if(!r||!r.includes("wc_ev")||!r.includes("topic"))return;const i=Xa(r,"topic")||"",s=decodeURIComponent(Xa(r,"wc_ev")||""),n=this.client.session.keys.includes(i);n&&this.client.session.update(i,{transportType:ae.link_mode}),this.client.core.dispatchEnvelope({topic:i,message:s,sessionExists:n})}),N(this,"registerLinkModeListeners",async()=>{var r;if(Uo()||Wt()&&(r=this.client.metadata.redirect)!=null&&r.linkMode){const i=global==null?void 0:global.Linking;if(typeof i<"u"){i.addEventListener("url",this.handleLinkModeMessage,this.client.name);const s=await i.getInitialURL();s&&setTimeout(()=>{this.handleLinkModeMessage({url:s})},50)}}}),N(this,"shouldSetTVF",(r,i)=>{if(!i||r!=="wc_sessionRequest")return!1;const{request:s}=i;return Object.keys(Tl).includes(s.method)}),N(this,"getTVFParams",(r,i,s)=>{var n,o;try{const a=i.request.method,c=this.extractTxHashesFromResult(a,s);return Re(ie({correlationId:r,rpcMethods:[a],chainId:i.chainId},this.isValidContractData(i.request.params)&&{contractAddresses:[(o=(n=i.request.params)==null?void 0:n[0])==null?void 0:o.to]}),{txHashes:c})}catch(a){this.client.logger.warn("Error getting TVF params",a)}return{}}),N(this,"isValidContractData",r=>{var i;if(!r)return!1;try{const s=(r==null?void 0:r.data)||((i=r==null?void 0:r[0])==null?void 0:i.data);if(!s.startsWith("0x"))return!1;const n=s.slice(2);return/^[0-9a-fA-F]*$/.test(n)?n.length%2===0:!1}catch{}return!1}),N(this,"extractTxHashesFromResult",(r,i)=>{try{const s=Tl[r];if(typeof i=="string")return[i];const n=i[s.key];if(ut(n))return r==="solana_signAllTransactions"?n.map(o=>_b(o)):n;if(typeof n=="string")return[n]}catch(s){this.client.logger.warn("Error extracting tx hashes from result",s)}return[]})}async processPendingMessageEvents(){try{const e=this.client.session.keys,r=this.client.core.relayer.messages.getWithoutAck(e);for(const[i,s]of Object.entries(r))for(const n of s)try{await this.onProviderMessageEvent({topic:i,message:n,publishedAt:Date.now()})}catch{this.client.logger.warn(`Error processing pending message event for topic: ${i}, message: ${n}`)}}catch(e){this.client.logger.warn("processPendingMessageEvents failed",e)}}isInitialized(){if(!this.initialized){const{message:e}=B("NOT_INITIALIZED",this.name);throw new Error(e)}}async confirmOnlineStateOrThrow(){await this.client.core.relayer.confirmOnlineStateOrThrow()}registerRelayerEvents(){this.client.core.relayer.on(we.message,e=>{this.onProviderMessageEvent(e)})}async onRelayMessage(e){const{topic:r,message:i,attestation:s,transportType:n}=e,{publicKey:o}=this.client.auth.authKeys.keys.includes(gs)?this.client.auth.authKeys.get(gs):{publicKey:void 0};try{const a=await this.client.core.crypto.decode(r,i,{receiverPublicKey:o,encoding:n===ae.link_mode?Ft:ht});Wo(a)?(this.client.core.history.set(r,a),await this.onRelayEventRequest({topic:r,payload:a,attestation:s,transportType:n,encryptedId:Et(i)})):Hs(a)?(await this.client.core.history.resolve(a),await this.onRelayEventResponse({topic:r,payload:a,transportType:n}),this.client.core.history.delete(r,a.id)):await this.onRelayEventUnknownPayload({topic:r,payload:a,transportType:n}),await this.client.core.relayer.messages.ack(r,i)}catch(a){this.client.logger.error(a)}}registerExpirerEvents(){this.client.core.expirer.on(Xe.expired,async e=>{const{topic:r,id:i}=su(e.target);if(i&&this.client.pendingRequest.keys.includes(i))return await this.deletePendingSessionRequest(i,B("EXPIRED"),!0);if(i&&this.client.auth.requests.keys.includes(i))return await this.deletePendingAuthRequest(i,B("EXPIRED"),!0);r?this.client.session.keys.includes(r)&&(await this.deleteSession({topic:r,expirerHasDeleted:!0}),this.client.events.emit("session_expire",{topic:r})):i&&(await this.deleteProposal(i,!0),this.client.events.emit("proposal_expire",{id:i}))})}registerPairingEvents(){this.client.core.pairing.events.on(rr.create,e=>this.onPairingCreated(e)),this.client.core.pairing.events.on(rr.delete,e=>{this.addToRecentlyDeleted(e.topic,"pairing")})}isValidPairingTopic(e){if(!ue(e,!1)){const{message:r}=B("MISSING_OR_INVALID",`pairing topic should be a string: ${e}`);throw new Error(r)}if(!this.client.core.pairing.pairings.keys.includes(e)){const{message:r}=B("NO_MATCHING_KEY",`pairing topic doesn't exist: ${e}`);throw new Error(r)}if(Mt(this.client.core.pairing.pairings.get(e).expiry)){const{message:r}=B("EXPIRED",`pairing topic: ${e}`);throw new Error(r)}}async isValidSessionTopic(e){if(!ue(e,!1)){const{message:r}=B("MISSING_OR_INVALID",`session topic should be a string: ${e}`);throw new Error(r)}if(this.checkRecentlyDeleted(e),!this.client.session.keys.includes(e)){const{message:r}=B("NO_MATCHING_KEY",`session topic doesn't exist: ${e}`);throw new Error(r)}if(Mt(this.client.session.get(e).expiry)){await this.deleteSession({topic:e});const{message:r}=B("EXPIRED",`session topic: ${e}`);throw new Error(r)}if(!this.client.core.crypto.keychain.has(e)){const{message:r}=B("MISSING_OR_INVALID",`session topic does not exist in keychain: ${e}`);throw await this.deleteSession({topic:e}),new Error(r)}}async isValidSessionOrPairingTopic(e){if(this.checkRecentlyDeleted(e),this.client.session.keys.includes(e))await this.isValidSessionTopic(e);else if(this.client.core.pairing.pairings.keys.includes(e))this.isValidPairingTopic(e);else if(ue(e,!1)){const{message:r}=B("NO_MATCHING_KEY",`session or pairing topic doesn't exist: ${e}`);throw new Error(r)}else{const{message:r}=B("MISSING_OR_INVALID",`session or pairing topic should be a string: ${e}`);throw new Error(r)}}async isValidProposalId(e){if(!W0(e)){const{message:r}=B("MISSING_OR_INVALID",`proposal id should be a number: ${e}`);throw new Error(r)}if(!this.client.proposal.keys.includes(e)){const{message:r}=B("NO_MATCHING_KEY",`proposal id doesn't exist: ${e}`);throw new Error(r)}if(Mt(this.client.proposal.get(e).expiryTimestamp)){await this.deleteProposal(e);const{message:r}=B("EXPIRED",`proposal id: ${e}`);throw new Error(r)}}}class _S extends yr{constructor(e,r){super(e,r,oS,Go),this.core=e,this.logger=r}}let IS=class extends yr{constructor(e,r){super(e,r,aS,Go),this.core=e,this.logger=r}};class $S extends yr{constructor(e,r){super(e,r,lS,Go,i=>i.id),this.core=e,this.logger=r}}class SS extends yr{constructor(e,r){super(e,r,pS,Ks,()=>gs),this.core=e,this.logger=r}}class OS extends yr{constructor(e,r){super(e,r,fS,Ks),this.core=e,this.logger=r}}class PS extends yr{constructor(e,r){super(e,r,gS,Ks,i=>i.id),this.core=e,this.logger=r}}var xS=Object.defineProperty,AS=(t,e,r)=>e in t?xS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Tn=(t,e,r)=>AS(t,typeof e!="symbol"?e+"":e,r);class TS{constructor(e,r){this.core=e,this.logger=r,Tn(this,"authKeys"),Tn(this,"pairingTopics"),Tn(this,"requests"),this.authKeys=new SS(this.core,this.logger),this.pairingTopics=new OS(this.core,this.logger),this.requests=new PS(this.core,this.logger)}async init(){await this.authKeys.init(),await this.pairingTopics.init(),await this.requests.init()}}var CS=Object.defineProperty,RS=(t,e,r)=>e in t?CS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,W=(t,e,r)=>RS(t,typeof e!="symbol"?e+"":e,r);let NS=class yd extends NE{constructor(e){super(e),W(this,"protocol",pd),W(this,"version",fd),W(this,"name",xn.name),W(this,"metadata"),W(this,"core"),W(this,"logger"),W(this,"events",new rt.EventEmitter),W(this,"engine"),W(this,"session"),W(this,"proposal"),W(this,"pendingRequest"),W(this,"auth"),W(this,"signConfig"),W(this,"on",(i,s)=>this.events.on(i,s)),W(this,"once",(i,s)=>this.events.once(i,s)),W(this,"off",(i,s)=>this.events.off(i,s)),W(this,"removeListener",(i,s)=>this.events.removeListener(i,s)),W(this,"removeAllListeners",i=>this.events.removeAllListeners(i)),W(this,"connect",async i=>{try{return await this.engine.connect(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"pair",async i=>{try{return await this.engine.pair(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"approve",async i=>{try{return await this.engine.approve(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"reject",async i=>{try{return await this.engine.reject(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"update",async i=>{try{return await this.engine.update(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"extend",async i=>{try{return await this.engine.extend(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"request",async i=>{try{return await this.engine.request(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"respond",async i=>{try{return await this.engine.respond(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"ping",async i=>{try{return await this.engine.ping(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"emit",async i=>{try{return await this.engine.emit(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"disconnect",async i=>{try{return await this.engine.disconnect(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"find",i=>{try{return this.engine.find(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"getPendingSessionRequests",()=>{try{return this.engine.getPendingSessionRequests()}catch(i){throw this.logger.error(i.message),i}}),W(this,"authenticate",async(i,s)=>{try{return await this.engine.authenticate(i,s)}catch(n){throw this.logger.error(n.message),n}}),W(this,"formatAuthMessage",i=>{try{return this.engine.formatAuthMessage(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"approveSessionAuthenticate",async i=>{try{return await this.engine.approveSessionAuthenticate(i)}catch(s){throw this.logger.error(s.message),s}}),W(this,"rejectSessionAuthenticate",async i=>{try{return await this.engine.rejectSessionAuthenticate(i)}catch(s){throw this.logger.error(s.message),s}}),this.name=(e==null?void 0:e.name)||xn.name,this.metadata=Bw(e==null?void 0:e.metadata),this.signConfig=e==null?void 0:e.signConfig;const r=typeof(e==null?void 0:e.logger)<"u"&&typeof(e==null?void 0:e.logger)!="string"?e.logger:zi(Ms({level:(e==null?void 0:e.logger)||xn.logger}));this.core=(e==null?void 0:e.core)||new nS(e),this.logger=je(r,this.name),this.session=new IS(this.core,this.logger),this.proposal=new _S(this.core,this.logger),this.pendingRequest=new $S(this.core,this.logger),this.engine=new ES(this),this.auth=new TS(this.core,this.logger)}static async init(e){const r=new yd(e);return await r.initialize(),r}get context(){return Me(this.logger)}get pairing(){return this.core.pairing.pairings}async initialize(){this.logger.trace("Initialized");try{await this.core.start(),await this.session.init(),await this.proposal.init(),await this.pendingRequest.init(),await this.auth.init(),await this.engine.init(),this.logger.info("SignClient Initialization Success"),setTimeout(()=>{this.engine.processRelayMessageCache()},j.toMiliseconds(j.ONE_SECOND))}catch(e){throw this.logger.info("SignClient Initialization Failure"),this.logger.error(e.message),e}}};var Eo={exports:{}};(function(t,e){var r=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof vt<"u"&&vt,i=function(){function n(){this.fetch=!1,this.DOMException=r.DOMException}return n.prototype=r,new n}();(function(n){(function(o){var a=typeof n<"u"&&n||typeof self<"u"&&self||typeof vt<"u"&&vt||{},c={searchParams:"URLSearchParams"in a,iterable:"Symbol"in a&&"iterator"in Symbol,blob:"FileReader"in a&&"Blob"in a&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in a,arrayBuffer:"ArrayBuffer"in a};function l(g){return g&&DataView.prototype.isPrototypeOf(g)}if(c.arrayBuffer)var h=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(g){return g&&h.indexOf(Object.prototype.toString.call(g))>-1};function d(g){if(typeof g!="string"&&(g=String(g)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(g)||g==="")throw new TypeError('Invalid character in header field name: "'+g+'"');return g.toLowerCase()}function f(g){return typeof g!="string"&&(g=String(g)),g}function p(g){var m={next:function(){var _=g.shift();return{done:_===void 0,value:_}}};return c.iterable&&(m[Symbol.iterator]=function(){return m}),m}function y(g){this.map={},g instanceof y?g.forEach(function(m,_){this.append(_,m)},this):Array.isArray(g)?g.forEach(function(m){if(m.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+m.length);this.append(m[0],m[1])},this):g&&Object.getOwnPropertyNames(g).forEach(function(m){this.append(m,g[m])},this)}y.prototype.append=function(g,m){g=d(g),m=f(m);var _=this.map[g];this.map[g]=_?_+", "+m:m},y.prototype.delete=function(g){delete this.map[d(g)]},y.prototype.get=function(g){return g=d(g),this.has(g)?this.map[g]:null},y.prototype.has=function(g){return this.map.hasOwnProperty(d(g))},y.prototype.set=function(g,m){this.map[d(g)]=f(m)},y.prototype.forEach=function(g,m){for(var _ in this.map)this.map.hasOwnProperty(_)&&g.call(m,this.map[_],_,this)},y.prototype.keys=function(){var g=[];return this.forEach(function(m,_){g.push(_)}),p(g)},y.prototype.values=function(){var g=[];return this.forEach(function(m){g.push(m)}),p(g)},y.prototype.entries=function(){var g=[];return this.forEach(function(m,_){g.push([_,m])}),p(g)},c.iterable&&(y.prototype[Symbol.iterator]=y.prototype.entries);function w(g){if(!g._noBody){if(g.bodyUsed)return Promise.reject(new TypeError("Already read"));g.bodyUsed=!0}}function v(g){return new Promise(function(m,_){g.onload=function(){m(g.result)},g.onerror=function(){_(g.error)}})}function b(g){var m=new FileReader,_=v(m);return m.readAsArrayBuffer(g),_}function E(g){var m=new FileReader,_=v(m),O=/charset=([A-Za-z0-9_-]+)/.exec(g.type),$=O?O[1]:"utf-8";return m.readAsText(g,$),_}function I(g){for(var m=new Uint8Array(g),_=new Array(m.length),O=0;O<m.length;O++)_[O]=String.fromCharCode(m[O]);return _.join("")}function x(g){if(g.slice)return g.slice(0);var m=new Uint8Array(g.byteLength);return m.set(new Uint8Array(g)),m.buffer}function A(){return this.bodyUsed=!1,this._initBody=function(g){this.bodyUsed=this.bodyUsed,this._bodyInit=g,g?typeof g=="string"?this._bodyText=g:c.blob&&Blob.prototype.isPrototypeOf(g)?this._bodyBlob=g:c.formData&&FormData.prototype.isPrototypeOf(g)?this._bodyFormData=g:c.searchParams&&URLSearchParams.prototype.isPrototypeOf(g)?this._bodyText=g.toString():c.arrayBuffer&&c.blob&&l(g)?(this._bodyArrayBuffer=x(g.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):c.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(g)||u(g))?this._bodyArrayBuffer=x(g):this._bodyText=g=Object.prototype.toString.call(g):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof g=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):c.searchParams&&URLSearchParams.prototype.isPrototypeOf(g)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},c.blob&&(this.blob=function(){var g=w(this);if(g)return g;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var g=w(this);return g||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(c.blob)return this.blob().then(b);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var g=w(this);if(g)return g;if(this._bodyBlob)return E(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(I(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},c.formData&&(this.formData=function(){return this.text().then(M)}),this.json=function(){return this.text().then(JSON.parse)},this}var P=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function C(g){var m=g.toUpperCase();return P.indexOf(m)>-1?m:g}function S(g,m){if(!(this instanceof S))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');m=m||{};var _=m.body;if(g instanceof S){if(g.bodyUsed)throw new TypeError("Already read");this.url=g.url,this.credentials=g.credentials,m.headers||(this.headers=new y(g.headers)),this.method=g.method,this.mode=g.mode,this.signal=g.signal,!_&&g._bodyInit!=null&&(_=g._bodyInit,g.bodyUsed=!0)}else this.url=String(g);if(this.credentials=m.credentials||this.credentials||"same-origin",(m.headers||!this.headers)&&(this.headers=new y(m.headers)),this.method=C(m.method||this.method||"GET"),this.mode=m.mode||this.mode||null,this.signal=m.signal||this.signal||function(){if("AbortController"in a){var T=new AbortController;return T.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&_)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(_),(this.method==="GET"||this.method==="HEAD")&&(m.cache==="no-store"||m.cache==="no-cache")){var O=/([?&])_=[^&]*/;if(O.test(this.url))this.url=this.url.replace(O,"$1_="+new Date().getTime());else{var $=/\?/;this.url+=($.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}S.prototype.clone=function(){return new S(this,{body:this._bodyInit})};function M(g){var m=new FormData;return g.trim().split("&").forEach(function(_){if(_){var O=_.split("="),$=O.shift().replace(/\+/g," "),T=O.join("=").replace(/\+/g," ");m.append(decodeURIComponent($),decodeURIComponent(T))}}),m}function U(g){var m=new y,_=g.replace(/\r?\n[\t ]+/g," ");return _.split("\r").map(function(O){return O.indexOf(`
`)===0?O.substr(1,O.length):O}).forEach(function(O){var $=O.split(":"),T=$.shift().trim();if(T){var L=$.join(":").trim();try{m.append(T,L)}catch(q){console.warn("Response "+q.message)}}}),m}A.call(S.prototype);function D(g,m){if(!(this instanceof D))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(m||(m={}),this.type="default",this.status=m.status===void 0?200:m.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=m.statusText===void 0?"":""+m.statusText,this.headers=new y(m.headers),this.url=m.url||"",this._initBody(g)}A.call(D.prototype),D.prototype.clone=function(){return new D(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new y(this.headers),url:this.url})},D.error=function(){var g=new D(null,{status:200,statusText:""});return g.ok=!1,g.status=0,g.type="error",g};var F=[301,302,303,307,308];D.redirect=function(g,m){if(F.indexOf(m)===-1)throw new RangeError("Invalid status code");return new D(null,{status:m,headers:{location:g}})},o.DOMException=a.DOMException;try{new o.DOMException}catch{o.DOMException=function(m,_){this.message=m,this.name=_;var O=Error(m);this.stack=O.stack},o.DOMException.prototype=Object.create(Error.prototype),o.DOMException.prototype.constructor=o.DOMException}function R(g,m){return new Promise(function(_,O){var $=new S(g,m);if($.signal&&$.signal.aborted)return O(new o.DOMException("Aborted","AbortError"));var T=new XMLHttpRequest;function L(){T.abort()}T.onload=function(){var k={statusText:T.statusText,headers:U(T.getAllResponseHeaders()||"")};$.url.indexOf("file://")===0&&(T.status<200||T.status>599)?k.status=200:k.status=T.status,k.url="responseURL"in T?T.responseURL:k.headers.get("X-Request-URL");var H="response"in T?T.response:T.responseText;setTimeout(function(){_(new D(H,k))},0)},T.onerror=function(){setTimeout(function(){O(new TypeError("Network request failed"))},0)},T.ontimeout=function(){setTimeout(function(){O(new TypeError("Network request timed out"))},0)},T.onabort=function(){setTimeout(function(){O(new o.DOMException("Aborted","AbortError"))},0)};function q(k){try{return k===""&&a.location.href?a.location.href:k}catch{return k}}if(T.open($.method,q($.url),!0),$.credentials==="include"?T.withCredentials=!0:$.credentials==="omit"&&(T.withCredentials=!1),"responseType"in T&&(c.blob?T.responseType="blob":c.arrayBuffer&&(T.responseType="arraybuffer")),m&&typeof m.headers=="object"&&!(m.headers instanceof y||a.Headers&&m.headers instanceof a.Headers)){var z=[];Object.getOwnPropertyNames(m.headers).forEach(function(k){z.push(d(k)),T.setRequestHeader(k,f(m.headers[k]))}),$.headers.forEach(function(k,H){z.indexOf(H)===-1&&T.setRequestHeader(H,k)})}else $.headers.forEach(function(k,H){T.setRequestHeader(H,k)});$.signal&&($.signal.addEventListener("abort",L),T.onreadystatechange=function(){T.readyState===4&&$.signal.removeEventListener("abort",L)}),T.send(typeof $._bodyInit>"u"?null:$._bodyInit)})}return R.polyfill=!0,a.fetch||(a.fetch=R,a.Headers=y,a.Request=S,a.Response=D),o.Headers=y,o.Request=S,o.Response=D,o.fetch=R,Object.defineProperty(o,"__esModule",{value:!0}),o})({})})(i),i.fetch.ponyfill=!0,delete i.fetch.polyfill;var s=r.fetch?r:i;e=s.fetch,e.default=s.fetch,e.fetch=s.fetch,e.Headers=s.Headers,e.Request=s.Request,e.Response=s.Response,t.exports=e})(Eo,Eo.exports);var jS=Eo.exports;const Rl=ih(jS);var BS=Object.defineProperty,DS=Object.defineProperties,US=Object.getOwnPropertyDescriptors,Nl=Object.getOwnPropertySymbols,LS=Object.prototype.hasOwnProperty,kS=Object.prototype.propertyIsEnumerable,jl=(t,e,r)=>e in t?BS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Bl=(t,e)=>{for(var r in e||(e={}))LS.call(e,r)&&jl(t,r,e[r]);if(Nl)for(var r of Nl(e))kS.call(e,r)&&jl(t,r,e[r]);return t},Dl=(t,e)=>DS(t,US(e));const qS={Accept:"application/json","Content-Type":"application/json"},MS="POST",Ul={headers:qS,method:MS},Ll=10;let dt=class{constructor(e,r=!1){if(this.url=e,this.disableProviderPing=r,this.events=new rt.EventEmitter,this.isAvailable=!1,this.registering=!1,!el(e))throw new Error(`Provided URL is not compatible with HTTP connection: ${e}`);this.url=e,this.disableProviderPing=r}get connected(){return this.isAvailable}get connecting(){return this.registering}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async open(e=this.url){await this.register(e)}async close(){if(!this.isAvailable)throw new Error("Connection already closed");this.onClose()}async send(e){this.isAvailable||await this.register();try{const r=Dt(e),i=await(await Rl(this.url,Dl(Bl({},Ul),{body:r}))).json();this.onPayload({data:i})}catch(r){this.onError(e.id,r)}}async register(e=this.url){if(!el(e))throw new Error(`Provided URL is not compatible with HTTP connection: ${e}`);if(this.registering){const r=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=r||this.events.listenerCount("open")>=r)&&this.events.setMaxListeners(r+1),new Promise((i,s)=>{this.events.once("register_error",n=>{this.resetMaxListeners(),s(n)}),this.events.once("open",()=>{if(this.resetMaxListeners(),typeof this.isAvailable>"u")return s(new Error("HTTP connection is missing or invalid"));i()})})}this.url=e,this.registering=!0;try{if(!this.disableProviderPing){const r=Dt({id:1,jsonrpc:"2.0",method:"test",params:[]});await Rl(e,Dl(Bl({},Ul),{body:r}))}this.onOpen()}catch(r){const i=this.parseError(r);throw this.events.emit("register_error",i),this.onClose(),i}}onOpen(){this.isAvailable=!0,this.registering=!1,this.events.emit("open")}onClose(){this.isAvailable=!1,this.registering=!1,this.events.emit("close")}onPayload(e){if(typeof e.data>"u")return;const r=typeof e.data=="string"?lr(e.data):e.data;this.events.emit("payload",r)}onError(e,r){const i=this.parseError(r),s=i.message||i.toString(),n=zs(e,s);this.events.emit("payload",n)}parseError(e,r=this.url){return Ku(e,r,"HTTP")}resetMaxListeners(){this.events.getMaxListeners()>Ll&&this.events.setMaxListeners(Ll)}};const kl="error",FS="wss://relay.walletconnect.org",zS="wc",HS="universal_provider",ss=`${zS}@2:${HS}:`,md="https://rpc.walletconnect.org/v1/",qr="generic",VS=`${md}bundler`,st={DEFAULT_CHAIN_CHANGED:"default_chain_changed"};function KS(){}function Yo(t){return t==null||typeof t!="object"&&typeof t!="function"}function Jo(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function WS(t){if(Yo(t))return t;if(Array.isArray(t)||Jo(t)||t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);const e=Object.getPrototypeOf(t),r=e.constructor;if(t instanceof Date||t instanceof Map||t instanceof Set)return new r(t);if(t instanceof RegExp){const i=new r(t);return i.lastIndex=t.lastIndex,i}if(t instanceof DataView)return new r(t.buffer.slice(0));if(t instanceof Error){const i=new r(t.message);return i.stack=t.stack,i.name=t.name,i.cause=t.cause,i}if(typeof File<"u"&&t instanceof File)return new r([t],t.name,{type:t.type,lastModified:t.lastModified});if(typeof t=="object"){const i=Object.create(e);return Object.assign(i,t)}return t}function ql(t){return typeof t=="object"&&t!==null}function wd(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function bd(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const GS="[object RegExp]",vd="[object String]",Ed="[object Number]",_d="[object Boolean]",Id="[object Arguments]",YS="[object Symbol]",JS="[object Date]",ZS="[object Map]",QS="[object Set]",XS="[object Array]",eO="[object ArrayBuffer]",tO="[object Object]",rO="[object DataView]",iO="[object Uint8Array]",sO="[object Uint8ClampedArray]",nO="[object Uint16Array]",oO="[object Uint32Array]",aO="[object Int8Array]",cO="[object Int16Array]",lO="[object Int32Array]",hO="[object Float32Array]",uO="[object Float64Array]";function dO(t,e){return zr(t,void 0,t,new Map,e)}function zr(t,e,r,i=new Map,s=void 0){const n=s==null?void 0:s(t,e,r,i);if(n!=null)return n;if(Yo(t))return t;if(i.has(t))return i.get(t);if(Array.isArray(t)){const o=new Array(t.length);i.set(t,o);for(let a=0;a<t.length;a++)o[a]=zr(t[a],a,r,i,s);return Object.hasOwn(t,"index")&&(o.index=t.index),Object.hasOwn(t,"input")&&(o.input=t.input),o}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){const o=new RegExp(t.source,t.flags);return o.lastIndex=t.lastIndex,o}if(t instanceof Map){const o=new Map;i.set(t,o);for(const[a,c]of t)o.set(a,zr(c,a,r,i,s));return o}if(t instanceof Set){const o=new Set;i.set(t,o);for(const a of t)o.add(zr(a,void 0,r,i,s));return o}if(typeof Buffer<"u"&&Buffer.isBuffer(t))return t.subarray();if(Jo(t)){const o=new(Object.getPrototypeOf(t)).constructor(t.length);i.set(t,o);for(let a=0;a<t.length;a++)o[a]=zr(t[a],a,r,i,s);return o}if(t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){const o=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return i.set(t,o),ir(o,t,r,i,s),o}if(typeof File<"u"&&t instanceof File){const o=new File([t],t.name,{type:t.type});return i.set(t,o),ir(o,t,r,i,s),o}if(t instanceof Blob){const o=new Blob([t],{type:t.type});return i.set(t,o),ir(o,t,r,i,s),o}if(t instanceof Error){const o=new t.constructor;return i.set(t,o),o.message=t.message,o.name=t.name,o.stack=t.stack,o.cause=t.cause,ir(o,t,r,i,s),o}if(typeof t=="object"&&pO(t)){const o=Object.create(Object.getPrototypeOf(t));return i.set(t,o),ir(o,t,r,i,s),o}return t}function ir(t,e,r=t,i,s){const n=[...Object.keys(e),...wd(e)];for(let o=0;o<n.length;o++){const a=n[o],c=Object.getOwnPropertyDescriptor(t,a);(c==null||c.writable)&&(t[a]=zr(e[a],a,r,i,s))}}function pO(t){switch(bd(t)){case Id:case XS:case eO:case rO:case _d:case JS:case hO:case uO:case aO:case cO:case lO:case ZS:case Ed:case tO:case GS:case QS:case vd:case YS:case iO:case sO:case nO:case oO:return!0;default:return!1}}function fO(t,e){return dO(t,(r,i,s,n)=>{if(typeof t=="object")switch(Object.prototype.toString.call(t)){case Ed:case vd:case _d:{const o=new t.constructor(t==null?void 0:t.valueOf());return ir(o,t),o}case Id:{const o={};return ir(o,t),o.length=t.length,o[Symbol.iterator]=t[Symbol.iterator],o}default:return}})}function Ml(t){return fO(t)}function Fl(t){return t!==null&&typeof t=="object"&&bd(t)==="[object Arguments]"}function gO(t){return Jo(t)}function yO(t){var r;if(typeof t!="object"||t==null)return!1;if(Object.getPrototypeOf(t)===null)return!0;if(Object.prototype.toString.call(t)!=="[object Object]"){const i=t[Symbol.toStringTag];return i==null||!((r=Object.getOwnPropertyDescriptor(t,Symbol.toStringTag))!=null&&r.writable)?!1:t.toString()===`[object ${i}]`}let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function mO(t,...e){const r=e.slice(0,-1),i=e[e.length-1];let s=t;for(let n=0;n<r.length;n++){const o=r[n];s=_o(s,o,i,new Map)}return s}function _o(t,e,r,i){if(Yo(t)&&(t=Object(t)),e==null||typeof e!="object")return t;if(i.has(e))return WS(i.get(e));if(i.set(e,t),Array.isArray(e)){e=e.slice();for(let n=0;n<e.length;n++)e[n]=e[n]??void 0}const s=[...Object.keys(e),...wd(e)];for(let n=0;n<s.length;n++){const o=s[n];let a=e[o],c=t[o];if(Fl(a)&&(a={...a}),Fl(c)&&(c={...c}),typeof Buffer<"u"&&Buffer.isBuffer(a)&&(a=Ml(a)),Array.isArray(a))if(typeof c=="object"&&c!=null){const h=[],u=Reflect.ownKeys(c);for(let d=0;d<u.length;d++){const f=u[d];h[f]=c[f]}c=h}else c=[];const l=r(c,a,o,t,e,i);l!=null?t[o]=l:Array.isArray(a)||ql(c)&&ql(a)?t[o]=_o(c,a,r,i):c==null&&yO(a)?t[o]=_o({},a,r,i):c==null&&gO(a)?t[o]=Ml(a):(c===void 0||a!==void 0)&&(t[o]=a)}return t}function wO(t,...e){return mO(t,...e,KS)}var bO=Object.defineProperty,vO=Object.defineProperties,EO=Object.getOwnPropertyDescriptors,zl=Object.getOwnPropertySymbols,_O=Object.prototype.hasOwnProperty,IO=Object.prototype.propertyIsEnumerable,Hl=(t,e,r)=>e in t?bO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ns=(t,e)=>{for(var r in e||(e={}))_O.call(e,r)&&Hl(t,r,e[r]);if(zl)for(var r of zl(e))IO.call(e,r)&&Hl(t,r,e[r]);return t},$O=(t,e)=>vO(t,EO(e));function Ge(t,e,r){var i;const s=Hr(t);return((i=e.rpcMap)==null?void 0:i[s.reference])||`${md}?chainId=${s.namespace}:${s.reference}&projectId=${r}`}function mr(t){return t.includes(":")?t.split(":")[1]:t}function $d(t){return t.map(e=>`${e.split(":")[0]}:${e.split(":")[1]}`)}function SO(t,e){const r=Object.keys(e.namespaces).filter(s=>s.includes(t));if(!r.length)return[];const i=[];return r.forEach(s=>{const n=e.namespaces[s].accounts;i.push(...n)}),i}function os(t={},e={}){const r=Vl(t),i=Vl(e);return wO(r,i)}function Vl(t){var e,r,i,s,n;const o={};if(!Kt(t))return o;for(const[a,c]of Object.entries(t)){const l=ks(a)?[a]:c.chains,h=c.methods||[],u=c.events||[],d=c.rpcMap||{},f=Fr(a);o[f]=$O(ns(ns({},o[f]),c),{chains:_t(l,(e=o[f])==null?void 0:e.chains),methods:_t(h,(r=o[f])==null?void 0:r.methods),events:_t(u,(i=o[f])==null?void 0:i.events)}),(Kt(d)||Kt(((s=o[f])==null?void 0:s.rpcMap)||{}))&&(o[f].rpcMap=ns(ns({},d),(n=o[f])==null?void 0:n.rpcMap))}return o}function Kl(t){return t.includes(":")?t.split(":")[2]:t}function Wl(t){const e={};for(const[r,i]of Object.entries(t)){const s=i.methods||[],n=i.events||[],o=i.accounts||[],a=ks(r)?[r]:i.chains?i.chains:$d(i.accounts);e[r]={chains:a,methods:s,events:n,accounts:o}}return e}function Cn(t){return typeof t=="number"?t:t.includes("0x")?parseInt(t,16):(t=t.includes(":")?t.split(":")[1]:t,isNaN(Number(t))?t:Number(t))}const Sd={},Q=t=>Sd[t],Rn=(t,e)=>{Sd[t]=e};var OO=Object.defineProperty,PO=(t,e,r)=>e in t?OO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,xr=(t,e,r)=>PO(t,typeof e!="symbol"?e+"":e,r);class xO{constructor(e){xr(this,"name","polkadot"),xr(this,"client"),xr(this,"httpProviders"),xr(this,"events"),xr(this,"namespace"),xr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=mr(r);e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var AO=Object.defineProperty,TO=Object.defineProperties,CO=Object.getOwnPropertyDescriptors,Gl=Object.getOwnPropertySymbols,RO=Object.prototype.hasOwnProperty,NO=Object.prototype.propertyIsEnumerable,Io=(t,e,r)=>e in t?AO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Yl=(t,e)=>{for(var r in e||(e={}))RO.call(e,r)&&Io(t,r,e[r]);if(Gl)for(var r of Gl(e))NO.call(e,r)&&Io(t,r,e[r]);return t},Jl=(t,e)=>TO(t,CO(e)),Ar=(t,e,r)=>Io(t,typeof e!="symbol"?e+"":e,r);class jO{constructor(e){Ar(this,"name","eip155"),Ar(this,"client"),Ar(this,"chainId"),Ar(this,"namespace"),Ar(this,"httpProviders"),Ar(this,"events"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.httpProviders=this.createHttpProviders(),this.chainId=parseInt(this.getDefaultChain())}async request(e){switch(e.request.method){case"eth_requestAccounts":return this.getAccounts();case"eth_accounts":return this.getAccounts();case"wallet_switchEthereumChain":return await this.handleSwitchChain(e);case"eth_chainId":return parseInt(this.getDefaultChain());case"wallet_getCapabilities":return await this.getCapabilities(e);case"wallet_getCallsStatus":return await this.getCallStatus(e)}return this.namespace.methods.includes(e.request.method)?await this.client.request(e):this.getHttpProvider().request(e.request)}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(parseInt(e),r),this.chainId=parseInt(e),this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId.toString();if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}createHttpProvider(e,r){const i=r||Ge(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=parseInt(mr(r));e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}getHttpProvider(){const e=this.chainId,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}async handleSwitchChain(e){var r,i;let s=e.request.params?(r=e.request.params[0])==null?void 0:r.chainId:"0x0";s=s.startsWith("0x")?s:`0x${s}`;const n=parseInt(s,16);if(this.isChainApproved(n))this.setDefaultChain(`${n}`);else if(this.namespace.methods.includes("wallet_switchEthereumChain"))await this.client.request({topic:e.topic,request:{method:e.request.method,params:[{chainId:s}]},chainId:(i=this.namespace.chains)==null?void 0:i[0]}),this.setDefaultChain(`${n}`);else throw new Error(`Failed to switch to chain 'eip155:${n}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);return null}isChainApproved(e){return this.namespace.chains.includes(`${this.name}:${e}`)}async getCapabilities(e){var r,i,s,n,o;const a=(i=(r=e.request)==null?void 0:r.params)==null?void 0:i[0],c=((n=(s=e.request)==null?void 0:s.params)==null?void 0:n[1])||[],l=`${a}${c.join(",")}`;if(!a)throw new Error("Missing address parameter in `wallet_getCapabilities` request");const h=this.client.session.get(e.topic),u=((o=h==null?void 0:h.sessionProperties)==null?void 0:o.capabilities)||{};if(u!=null&&u[l])return u==null?void 0:u[l];const d=await this.client.request(e);try{await this.client.session.update(e.topic,{sessionProperties:Jl(Yl({},h.sessionProperties||{}),{capabilities:Jl(Yl({},u||{}),{[l]:d})})})}catch(f){console.warn("Failed to update session with capabilities",f)}return d}async getCallStatus(e){var r,i;const s=this.client.session.get(e.topic),n=(r=s.sessionProperties)==null?void 0:r.bundler_name;if(n){const a=this.getBundlerUrl(e.chainId,n);try{return await this.getUserOperationReceipt(a,e)}catch(c){console.warn("Failed to fetch call status from bundler",c,a)}}const o=(i=s.sessionProperties)==null?void 0:i.bundler_url;if(o)try{return await this.getUserOperationReceipt(o,e)}catch(a){console.warn("Failed to fetch call status from custom bundler",a,o)}if(this.namespace.methods.includes(e.request.method))return await this.client.request(e);throw new Error("Fetching call status not approved by the wallet.")}async getUserOperationReceipt(e,r){var i;const s=new URL(e),n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Ht("eth_getUserOperationReceipt",[(i=r.request.params)==null?void 0:i[0]]))});if(!n.ok)throw new Error(`Failed to fetch user operation receipt - ${n.status}`);return await n.json()}getBundlerUrl(e,r){return`${VS}?projectId=${this.client.core.projectId}&chainId=${e}&bundler=${r}`}}var BO=Object.defineProperty,DO=(t,e,r)=>e in t?BO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Tr=(t,e,r)=>DO(t,typeof e!="symbol"?e+"":e,r);class UO{constructor(e){Tr(this,"name","solana"),Tr(this,"client"),Tr(this,"httpProviders"),Tr(this,"events"),Tr(this,"namespace"),Tr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=mr(r);e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var LO=Object.defineProperty,kO=(t,e,r)=>e in t?LO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Cr=(t,e,r)=>kO(t,typeof e!="symbol"?e+"":e,r);class qO{constructor(e){Cr(this,"name","cosmos"),Cr(this,"client"),Cr(this,"httpProviders"),Cr(this,"events"),Cr(this,"namespace"),Cr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=mr(r);e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var MO=Object.defineProperty,FO=(t,e,r)=>e in t?MO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Rr=(t,e,r)=>FO(t,typeof e!="symbol"?e+"":e,r);class zO{constructor(e){Rr(this,"name","algorand"),Rr(this,"client"),Rr(this,"httpProviders"),Rr(this,"events"),Rr(this,"namespace"),Rr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(!this.httpProviders[e]){const i=r||Ge(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,i)}this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;e[r]=this.createHttpProvider(r,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);return typeof i>"u"?void 0:new it(new dt(i,Q("disableProviderPing")))}}var HO=Object.defineProperty,VO=(t,e,r)=>e in t?HO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Nr=(t,e,r)=>VO(t,typeof e!="symbol"?e+"":e,r);class KO{constructor(e){Nr(this,"name","cip34"),Nr(this,"client"),Nr(this,"httpProviders"),Nr(this,"events"),Nr(this,"namespace"),Nr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{const i=this.getCardanoRPCUrl(r),s=mr(r);e[s]=this.createHttpProvider(s,i)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}getCardanoRPCUrl(e){const r=this.namespace.rpcMap;if(r)return r[e]}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||this.getCardanoRPCUrl(e);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var WO=Object.defineProperty,GO=(t,e,r)=>e in t?WO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,jr=(t,e,r)=>GO(t,typeof e!="symbol"?e+"":e,r);class YO{constructor(e){jr(this,"name","elrond"),jr(this,"client"),jr(this,"httpProviders"),jr(this,"events"),jr(this,"namespace"),jr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=mr(r);e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var JO=Object.defineProperty,ZO=(t,e,r)=>e in t?JO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Br=(t,e,r)=>ZO(t,typeof e!="symbol"?e+"":e,r);class QO{constructor(e){Br(this,"name","multiversx"),Br(this,"client"),Br(this,"httpProviders"),Br(this,"events"),Br(this,"namespace"),Br(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;const s=mr(r);e[s]=this.createHttpProvider(s,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var XO=Object.defineProperty,eP=(t,e,r)=>e in t?XO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Dr=(t,e,r)=>eP(t,typeof e!="symbol"?e+"":e,r);class tP{constructor(e){Dr(this,"name","near"),Dr(this,"client"),Dr(this,"httpProviders"),Dr(this,"events"),Dr(this,"namespace"),Dr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(this.chainId=e,!this.httpProviders[e]){const i=r||Ge(`${this.name}:${e}`,this.namespace);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,i)}this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var i;e[r]=this.createHttpProvider(r,(i=this.namespace.rpcMap)==null?void 0:i[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace);return typeof i>"u"?void 0:new it(new dt(i,Q("disableProviderPing")))}}var rP=Object.defineProperty,iP=(t,e,r)=>e in t?rP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ur=(t,e,r)=>iP(t,typeof e!="symbol"?e+"":e,r);class sP{constructor(e){Ur(this,"name","tezos"),Ur(this,"client"),Ur(this,"httpProviders"),Ur(this,"events"),Ur(this,"namespace"),Ur(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(this.chainId=e,!this.httpProviders[e]){const i=r||Ge(`${this.name}:${e}`,this.namespace);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,i)}this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{e[r]=this.createHttpProvider(r)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace);return typeof i>"u"?void 0:new it(new dt(i))}}var nP=Object.defineProperty,oP=(t,e,r)=>e in t?nP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Lr=(t,e,r)=>oP(t,typeof e!="symbol"?e+"":e,r);class aP{constructor(e){Lr(this,"name",qr),Lr(this,"client"),Lr(this,"httpProviders"),Lr(this,"events"),Lr(this,"namespace"),Lr(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace.chains=[...new Set((this.namespace.chains||[]).concat(e.chains||[]))],this.namespace.accounts=[...new Set((this.namespace.accounts||[]).concat(e.accounts||[]))],this.namespace.methods=[...new Set((this.namespace.methods||[]).concat(e.methods||[]))],this.namespace.events=[...new Set((this.namespace.events||[]).concat(e.events||[]))],this.httpProviders=this.createHttpProviders()}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider(e.chainId).request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(st.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){var e,r;const i={};return(r=(e=this.namespace)==null?void 0:e.accounts)==null||r.forEach(s=>{const n=Hr(s);i[`${n.namespace}:${n.reference}`]=this.createHttpProvider(s)}),i}getHttpProvider(e){const r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const i=this.createHttpProvider(e,r);i&&(this.httpProviders[e]=i)}createHttpProvider(e,r){const i=r||Ge(e,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${e}`);return new it(new dt(i,Q("disableProviderPing")))}}var cP=Object.defineProperty,lP=Object.defineProperties,hP=Object.getOwnPropertyDescriptors,Zl=Object.getOwnPropertySymbols,uP=Object.prototype.hasOwnProperty,dP=Object.prototype.propertyIsEnumerable,$o=(t,e,r)=>e in t?cP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,as=(t,e)=>{for(var r in e||(e={}))uP.call(e,r)&&$o(t,r,e[r]);if(Zl)for(var r of Zl(e))dP.call(e,r)&&$o(t,r,e[r]);return t},Nn=(t,e)=>lP(t,hP(e)),Qe=(t,e,r)=>$o(t,typeof e!="symbol"?e+"":e,r);let pP=class Od{constructor(e){Qe(this,"client"),Qe(this,"namespaces"),Qe(this,"optionalNamespaces"),Qe(this,"sessionProperties"),Qe(this,"scopedProperties"),Qe(this,"events",new Oo),Qe(this,"rpcProviders",{}),Qe(this,"session"),Qe(this,"providerOpts"),Qe(this,"logger"),Qe(this,"uri"),Qe(this,"disableProviderPing",!1),this.providerOpts=e,this.logger=typeof(e==null?void 0:e.logger)<"u"&&typeof(e==null?void 0:e.logger)!="string"?e.logger:zi(Ms({level:(e==null?void 0:e.logger)||kl})),this.disableProviderPing=(e==null?void 0:e.disableProviderPing)||!1}static async init(e){const r=new Od(e);return await r.initialize(),r}async request(e,r,i){const[s,n]=this.validateChain(r);if(!this.session)throw new Error("Please call connect() before request()");return await this.getProvider(s).request({request:as({},e),chainId:`${s}:${n}`,topic:this.session.topic,expiry:i})}sendAsync(e,r,i,s){const n=new Date().getTime();this.request(e,i,s).then(o=>r(null,Fs(n,o))).catch(o=>r(o,void 0))}async enable(){if(!this.client)throw new Error("Sign Client not initialized");return this.session||await this.connect({namespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties}),await this.requestAccounts()}async disconnect(){var e;if(!this.session)throw new Error("Please call connect() before enable()");await this.client.disconnect({topic:(e=this.session)==null?void 0:e.topic,reason:te("USER_DISCONNECTED")}),await this.cleanup()}async connect(e){if(!this.client)throw new Error("Sign Client not initialized");if(this.setNamespaces(e),await this.cleanupPendingPairings(),!e.skipPairing)return await this.pair(e.pairingTopic)}async authenticate(e,r){if(!this.client)throw new Error("Sign Client not initialized");this.setNamespaces(e),await this.cleanupPendingPairings();const{uri:i,response:s}=await this.client.authenticate(e,r);i&&(this.uri=i,this.events.emit("display_uri",i));const n=await s();if(this.session=n.session,this.session){const o=Wl(this.session.namespaces);this.namespaces=os(this.namespaces,o),await this.persist("namespaces",this.namespaces),this.onConnect()}return n}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}removeListener(e,r){this.events.removeListener(e,r)}off(e,r){this.events.off(e,r)}get isWalletConnect(){return!0}async pair(e){const{uri:r,approval:i}=await this.client.connect({pairingTopic:e,requiredNamespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties});r&&(this.uri=r,this.events.emit("display_uri",r));const s=await i();this.session=s;const n=Wl(s.namespaces);return this.namespaces=os(this.namespaces,n),await this.persist("namespaces",this.namespaces),await this.persist("optionalNamespaces",this.optionalNamespaces),this.onConnect(),this.session}setDefaultChain(e,r){try{if(!this.session)return;const[i,s]=this.validateChain(e),n=this.getProvider(i);n.name===qr?n.setDefaultChain(`${i}:${s}`,r):n.setDefaultChain(s,r)}catch(i){if(!/Please call connect/.test(i.message))throw i}}async cleanupPendingPairings(e={}){this.logger.info("Cleaning up inactive pairings...");const r=this.client.pairing.getAll();if(ut(r)){for(const i of r)e.deletePairings?this.client.core.expirer.set(i.topic,0):await this.client.core.relayer.subscriber.unsubscribe(i.topic);this.logger.info(`Inactive pairings cleared: ${r.length}`)}}abortPairingAttempt(){this.logger.warn("abortPairingAttempt is deprecated. This is now a no-op.")}async checkStorage(){this.namespaces=await this.getFromStore("namespaces")||{},this.optionalNamespaces=await this.getFromStore("optionalNamespaces")||{},this.session&&this.createProviders()}async initialize(){this.logger.trace("Initialized"),await this.createClient(),await this.checkStorage(),this.registerEventListeners()}async createClient(){var e,r;if(this.client=this.providerOpts.client||await NS.init({core:this.providerOpts.core,logger:this.providerOpts.logger||kl,relayUrl:this.providerOpts.relayUrl||FS,projectId:this.providerOpts.projectId,metadata:this.providerOpts.metadata,storageOptions:this.providerOpts.storageOptions,storage:this.providerOpts.storage,name:this.providerOpts.name,customStoragePrefix:this.providerOpts.customStoragePrefix,telemetryEnabled:this.providerOpts.telemetryEnabled}),this.providerOpts.session)try{this.session=this.client.session.get(this.providerOpts.session.topic)}catch(i){throw this.logger.error("Failed to get session",i),new Error(`The provided session: ${(r=(e=this.providerOpts)==null?void 0:e.session)==null?void 0:r.topic} doesn't exist in the Sign client`)}else{const i=this.client.session.getAll();this.session=i[0]}this.logger.trace("SignClient Initialized")}createProviders(){if(!this.client)throw new Error("Sign Client not initialized");if(!this.session)throw new Error("Session not initialized. Please call connect() before enable()");const e=[...new Set(Object.keys(this.session.namespaces).map(r=>Fr(r)))];Rn("client",this.client),Rn("events",this.events),Rn("disableProviderPing",this.disableProviderPing),e.forEach(r=>{if(!this.session)return;const i=SO(r,this.session),s=$d(i),n=os(this.namespaces,this.optionalNamespaces),o=Nn(as({},n[r]),{accounts:i,chains:s});switch(r){case"eip155":this.rpcProviders[r]=new jO({namespace:o});break;case"algorand":this.rpcProviders[r]=new zO({namespace:o});break;case"solana":this.rpcProviders[r]=new UO({namespace:o});break;case"cosmos":this.rpcProviders[r]=new qO({namespace:o});break;case"polkadot":this.rpcProviders[r]=new xO({namespace:o});break;case"cip34":this.rpcProviders[r]=new KO({namespace:o});break;case"elrond":this.rpcProviders[r]=new YO({namespace:o});break;case"multiversx":this.rpcProviders[r]=new QO({namespace:o});break;case"near":this.rpcProviders[r]=new tP({namespace:o});break;case"tezos":this.rpcProviders[r]=new sP({namespace:o});break;default:this.rpcProviders[qr]?this.rpcProviders[qr].updateNamespace(o):this.rpcProviders[qr]=new aP({namespace:o})}})}registerEventListeners(){if(typeof this.client>"u")throw new Error("Sign Client is not initialized");this.client.on("session_ping",e=>{var r;const{topic:i}=e;i===((r=this.session)==null?void 0:r.topic)&&this.events.emit("session_ping",e)}),this.client.on("session_event",e=>{var r;const{params:i,topic:s}=e;if(s!==((r=this.session)==null?void 0:r.topic))return;const{event:n}=i;if(n.name==="accountsChanged"){const o=n.data;o&&ut(o)&&this.events.emit("accountsChanged",o.map(Kl))}else if(n.name==="chainChanged"){const o=i.chainId,a=i.event.data,c=Fr(o),l=Cn(o)!==Cn(a)?`${c}:${Cn(a)}`:o;this.onChainChanged(l)}else this.events.emit(n.name,n.data);this.events.emit("session_event",e)}),this.client.on("session_update",({topic:e,params:r})=>{var i,s;if(e!==((i=this.session)==null?void 0:i.topic))return;const{namespaces:n}=r,o=(s=this.client)==null?void 0:s.session.get(e);this.session=Nn(as({},o),{namespaces:n}),this.onSessionUpdate(),this.events.emit("session_update",{topic:e,params:r})}),this.client.on("session_delete",async e=>{var r;e.topic===((r=this.session)==null?void 0:r.topic)&&(await this.cleanup(),this.events.emit("session_delete",e),this.events.emit("disconnect",Nn(as({},te("USER_DISCONNECTED")),{data:e.topic})))}),this.on(st.DEFAULT_CHAIN_CHANGED,e=>{this.onChainChanged(e,!0)})}getProvider(e){return this.rpcProviders[e]||this.rpcProviders[qr]}onSessionUpdate(){Object.keys(this.rpcProviders).forEach(e=>{var r;this.getProvider(e).updateNamespace((r=this.session)==null?void 0:r.namespaces[e])})}setNamespaces(e){const{namespaces:r={},optionalNamespaces:i={},sessionProperties:s,scopedProperties:n}=e;this.optionalNamespaces=os(r,i),this.sessionProperties=s,this.scopedProperties=n}validateChain(e){const[r,i]=(e==null?void 0:e.split(":"))||["",""];if(!this.namespaces||!Object.keys(this.namespaces).length)return[r,i];if(r&&!Object.keys(this.namespaces||{}).map(o=>Fr(o)).includes(r))throw new Error(`Namespace '${r}' is not configured. Please call connect() first with namespace config.`);if(r&&i)return[r,i];const s=Fr(Object.keys(this.namespaces)[0]),n=this.rpcProviders[s].getDefaultChain();return[s,n]}async requestAccounts(){const[e]=this.validateChain();return await this.getProvider(e).requestAccounts()}async onChainChanged(e,r=!1){if(!this.namespaces)return;const[i,s]=this.validateChain(e);if(!s)return;this.updateNamespaceChain(i,s),this.events.emit("chainChanged",s);const n=this.getProvider(i).getDefaultChain();r||this.getProvider(i).setDefaultChain(s),this.emitAccountsChangedOnChainChange({namespace:i,previousChainId:n,newChainId:e}),await this.persist("namespaces",this.namespaces)}emitAccountsChangedOnChainChange({namespace:e,previousChainId:r,newChainId:i}){var s,n;try{if(r===i)return;const o=(n=(s=this.session)==null?void 0:s.namespaces[e])==null?void 0:n.accounts;if(!o)return;const a=o.filter(c=>c.includes(`${i}:`)).map(Kl);if(!ut(a))return;this.events.emit("accountsChanged",a)}catch(o){this.logger.warn("Failed to emit accountsChanged on chain change",o)}}updateNamespaceChain(e,r){if(!this.namespaces)return;const i=this.namespaces[e]?e:`${e}:${r}`,s={chains:[],methods:[],events:[],defaultChain:r};this.namespaces[i]?this.namespaces[i]&&(this.namespaces[i].defaultChain=r):this.namespaces[i]=s}onConnect(){this.createProviders(),this.events.emit("connect",{session:this.session})}async cleanup(){this.namespaces=void 0,this.optionalNamespaces=void 0,this.sessionProperties=void 0,await this.deleteFromStore("namespaces"),await this.deleteFromStore("optionalNamespaces"),await this.deleteFromStore("sessionProperties"),this.session=void 0,await this.cleanupPendingPairings({deletePairings:!0}),await this.cleanupStorage()}async persist(e,r){var i;const s=((i=this.session)==null?void 0:i.topic)||"";await this.client.core.storage.setItem(`${ss}/${e}${s}`,r)}async getFromStore(e){var r;const i=((r=this.session)==null?void 0:r.topic)||"";return await this.client.core.storage.getItem(`${ss}/${e}${i}`)}async deleteFromStore(e){var r;const i=((r=this.session)==null?void 0:r.topic)||"";await this.client.core.storage.removeItem(`${ss}/${e}${i}`)}async cleanupStorage(){var e;try{if(((e=this.client)==null?void 0:e.session.length)>0)return;const r=await this.client.core.storage.getKeys();for(const i of r)i.startsWith(ss)&&await this.client.core.storage.removeItem(i)}catch(r){this.logger.warn("Failed to cleanup storage",r)}}};const fP=pP,gP="wc",yP="ethereum_provider",mP=`${gP}@2:${yP}:`,wP="https://rpc.walletconnect.org/v1/",Ss=["eth_sendTransaction","personal_sign"],Pd=["eth_accounts","eth_requestAccounts","eth_sendRawTransaction","eth_sign","eth_signTransaction","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sendTransaction","personal_sign","wallet_switchEthereumChain","wallet_addEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode","wallet_sendCalls","wallet_getCapabilities","wallet_getCallsStatus","wallet_showCallsStatus"],Os=["chainChanged","accountsChanged"],xd=["chainChanged","accountsChanged","message","disconnect","connect"],bP=async()=>{const{createAppKit:t}=await rh(()=>import("./core-C0olQNtY.js").then(e=>e.X),__vite__mapDeps([5,1,2,3,4]));return t};var vP=Object.defineProperty,EP=Object.defineProperties,_P=Object.getOwnPropertyDescriptors,Ql=Object.getOwnPropertySymbols,IP=Object.prototype.hasOwnProperty,$P=Object.prototype.propertyIsEnumerable,So=(t,e,r)=>e in t?vP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Xt=(t,e)=>{for(var r in e||(e={}))IP.call(e,r)&&So(t,r,e[r]);if(Ql)for(var r of Ql(e))$P.call(e,r)&&So(t,r,e[r]);return t},mi=(t,e)=>EP(t,_P(e)),ze=(t,e,r)=>So(t,typeof e!="symbol"?e+"":e,r);function Ps(t){return Number(t[0].split(":")[1])}function cs(t){return`0x${t.toString(16)}`}function SP(t){const{chains:e,optionalChains:r,methods:i,optionalMethods:s,events:n,optionalEvents:o,rpcMap:a}=t;if(!ut(e))throw new Error("Invalid chains");const c={chains:e,methods:i||Ss,events:n||Os,rpcMap:Xt({},e.length?{[Ps(e)]:a[Ps(e)]}:{})},l=n==null?void 0:n.filter(f=>!Os.includes(f)),h=i==null?void 0:i.filter(f=>!Ss.includes(f));if(!r&&!o&&!s&&!(l!=null&&l.length)&&!(h!=null&&h.length))return{required:e.length?c:void 0};const u=(l==null?void 0:l.length)&&(h==null?void 0:h.length)||!r,d={chains:[...new Set(u?c.chains.concat(r||[]):r)],methods:[...new Set(c.methods.concat(s!=null&&s.length?s:Pd))],events:[...new Set(c.events.concat(o!=null&&o.length?o:xd))],rpcMap:a};return{required:e.length?c:void 0,optional:r.length?d:void 0}}class Ws{constructor(){ze(this,"events",new rt.EventEmitter),ze(this,"namespace","eip155"),ze(this,"accounts",[]),ze(this,"signer"),ze(this,"chainId",1),ze(this,"modal"),ze(this,"rpc"),ze(this,"STORAGE_KEY",mP),ze(this,"on",(e,r)=>(this.events.on(e,r),this)),ze(this,"once",(e,r)=>(this.events.once(e,r),this)),ze(this,"removeListener",(e,r)=>(this.events.removeListener(e,r),this)),ze(this,"off",(e,r)=>(this.events.off(e,r),this)),ze(this,"parseAccount",e=>this.isCompatibleChainId(e)?this.parseAccountId(e).address:e),this.signer={},this.rpc={}}static async init(e){const r=new Ws;return await r.initialize(e),r}async request(e,r){return await this.signer.request(e,this.formatChainId(this.chainId),r)}sendAsync(e,r,i){this.signer.sendAsync(e,r,this.formatChainId(this.chainId),i)}get connected(){return this.signer.client?this.signer.client.core.relayer.connected:!1}get connecting(){return this.signer.client?this.signer.client.core.relayer.connecting:!1}async enable(){return this.session||await this.connect(),await this.request({method:"eth_requestAccounts"})}async connect(e){var r;if(!this.signer.client)throw new Error("Provider not initialized. Call init() first");this.loadConnectOpts(e);const{required:i,optional:s}=SP(this.rpc);try{const n=await new Promise(async(a,c)=>{var l,h;this.rpc.showQrModal&&((l=this.modal)==null||l.open(),(h=this.modal)==null||h.subscribeState(d=>{!d.open&&!this.signer.session&&(this.signer.abortPairingAttempt(),c(new Error("Connection request reset. Please try again.")))}));const u=e!=null&&e.scopedProperties?{[this.namespace]:e.scopedProperties}:void 0;await this.signer.connect(mi(Xt({namespaces:Xt({},i&&{[this.namespace]:i})},s&&{optionalNamespaces:{[this.namespace]:s}}),{pairingTopic:e==null?void 0:e.pairingTopic,scopedProperties:u})).then(d=>{a(d)}).catch(d=>{var f;(f=this.modal)==null||f.showErrorMessage("Unable to connect"),c(new Error(d.message))})});if(!n)return;const o=Ga(n.namespaces,[this.namespace]);this.setChainIds(this.rpc.chains.length?this.rpc.chains:o),this.setAccounts(o),this.events.emit("connect",{chainId:cs(this.chainId)})}catch(n){throw this.signer.logger.error(n),n}finally{(r=this.modal)==null||r.close()}}async authenticate(e,r){var i;if(!this.signer.client)throw new Error("Provider not initialized. Call init() first");this.loadConnectOpts({chains:e==null?void 0:e.chains});try{const s=await new Promise(async(o,a)=>{var c,l;this.rpc.showQrModal&&((c=this.modal)==null||c.open(),(l=this.modal)==null||l.subscribeState(h=>{!h.open&&!this.signer.session&&(this.signer.abortPairingAttempt(),a(new Error("Connection request reset. Please try again.")))})),await this.signer.authenticate(mi(Xt({},e),{chains:this.rpc.chains}),r).then(h=>{o(h)}).catch(h=>{var u;(u=this.modal)==null||u.showErrorMessage("Unable to connect"),a(new Error(h.message))})}),n=s.session;if(n){const o=Ga(n.namespaces,[this.namespace]);this.setChainIds(this.rpc.chains.length?this.rpc.chains:o),this.setAccounts(o),this.events.emit("connect",{chainId:cs(this.chainId)})}return s}catch(s){throw this.signer.logger.error(s),s}finally{(i=this.modal)==null||i.close()}}async disconnect(){this.session&&await this.signer.disconnect(),this.reset()}get isWalletConnect(){return!0}get session(){return this.signer.session}registerEventListeners(){this.signer.on("session_event",e=>{const{params:r}=e,{event:i}=r;i.name==="accountsChanged"?(this.accounts=this.parseAccounts(i.data),this.events.emit("accountsChanged",this.accounts)):i.name==="chainChanged"?this.setChainId(this.formatChainId(i.data)):this.events.emit(i.name,i.data),this.events.emit("session_event",e)}),this.signer.on("accountsChanged",e=>{this.accounts=this.parseAccounts(e),this.events.emit("accountsChanged",this.accounts)}),this.signer.on("chainChanged",e=>{const r=parseInt(e);this.chainId=r,this.events.emit("chainChanged",cs(this.chainId)),this.persist()}),this.signer.on("session_update",e=>{this.events.emit("session_update",e)}),this.signer.on("session_delete",e=>{this.reset(),this.events.emit("session_delete",e),this.events.emit("disconnect",mi(Xt({},te("USER_DISCONNECTED")),{data:e.topic,name:"USER_DISCONNECTED"}))}),this.signer.on("display_uri",e=>{this.events.emit("display_uri",e)})}switchEthereumChain(e){this.request({method:"wallet_switchEthereumChain",params:[{chainId:e.toString(16)}]})}isCompatibleChainId(e){return typeof e=="string"?e.startsWith(`${this.namespace}:`):!1}formatChainId(e){return`${this.namespace}:${e}`}parseChainId(e){return Number(e.split(":")[1])}setChainIds(e){const r=e.filter(i=>this.isCompatibleChainId(i)).map(i=>this.parseChainId(i));r.length&&(this.chainId=r[0],this.events.emit("chainChanged",cs(this.chainId)),this.persist())}setChainId(e){if(this.isCompatibleChainId(e)){const r=this.parseChainId(e);this.chainId=r,this.switchEthereumChain(r)}}parseAccountId(e){const[r,i,s]=e.split(":");return{chainId:`${r}:${i}`,address:s}}setAccounts(e){this.accounts=e.filter(r=>this.parseChainId(this.parseAccountId(r).chainId)===this.chainId).map(r=>this.parseAccountId(r).address),this.events.emit("accountsChanged",this.accounts)}getRpcConfig(e){var r,i;const s=(r=e==null?void 0:e.chains)!=null?r:[],n=(i=e==null?void 0:e.optionalChains)!=null?i:[],o=s.concat(n);if(!o.length)throw new Error("No chains specified in either `chains` or `optionalChains`");const a=s.length?(e==null?void 0:e.methods)||Ss:[],c=s.length?(e==null?void 0:e.events)||Os:[],l=(e==null?void 0:e.optionalMethods)||[],h=(e==null?void 0:e.optionalEvents)||[],u=(e==null?void 0:e.rpcMap)||this.buildRpcMap(o,e.projectId),d=(e==null?void 0:e.qrModalOptions)||void 0;return{chains:s==null?void 0:s.map(f=>this.formatChainId(f)),optionalChains:n.map(f=>this.formatChainId(f)),methods:a,events:c,optionalMethods:l,optionalEvents:h,rpcMap:u,showQrModal:!!(e!=null&&e.showQrModal),qrModalOptions:d,projectId:e.projectId,metadata:e.metadata}}buildRpcMap(e,r){const i={};return e.forEach(s=>{i[s]=this.getRpcUrl(s,r)}),i}async initialize(e){if(this.rpc=this.getRpcConfig(e),this.chainId=this.rpc.chains.length?Ps(this.rpc.chains):Ps(this.rpc.optionalChains),this.signer=await fP.init({projectId:this.rpc.projectId,metadata:this.rpc.metadata,disableProviderPing:e.disableProviderPing,relayUrl:e.relayUrl,storage:e.storage,storageOptions:e.storageOptions,customStoragePrefix:e.customStoragePrefix,telemetryEnabled:e.telemetryEnabled,logger:e.logger}),this.registerEventListeners(),await this.loadPersistedSession(),this.rpc.showQrModal){let r;try{const i=await bP(),{convertWCMToAppKitOptions:s}=await Promise.resolve().then(function(){return DP}),n=s(mi(Xt({},this.rpc.qrModalOptions),{chains:[...new Set([...this.rpc.chains,...this.rpc.optionalChains])],metadata:this.rpc.metadata,projectId:this.rpc.projectId}));if(!n.networks.length)throw new Error("No networks found for WalletConnect·");r=i(mi(Xt({},n),{universalProvider:this.signer,manualWCControl:!0}))}catch(i){throw console.warn(i),new Error("To use QR modal, please install @reown/appkit package")}if(r)try{this.modal=r}catch(i){throw this.signer.logger.error(i),new Error("Could not generate WalletConnectModal Instance")}}}loadConnectOpts(e){if(!e)return;const{chains:r,optionalChains:i,rpcMap:s}=e;r&&ut(r)&&(this.rpc.chains=r.map(n=>this.formatChainId(n)),r.forEach(n=>{this.rpc.rpcMap[n]=(s==null?void 0:s[n])||this.getRpcUrl(n)})),i&&ut(i)&&(this.rpc.optionalChains=[],this.rpc.optionalChains=i==null?void 0:i.map(n=>this.formatChainId(n)),i.forEach(n=>{this.rpc.rpcMap[n]=(s==null?void 0:s[n])||this.getRpcUrl(n)}))}getRpcUrl(e,r){var i;return((i=this.rpc.rpcMap)==null?void 0:i[e])||`${wP}?chainId=eip155:${e}&projectId=${r||this.rpc.projectId}`}async loadPersistedSession(){if(this.session)try{const e=await this.signer.client.core.storage.getItem(`${this.STORAGE_KEY}/chainId`),r=this.session.namespaces[`${this.namespace}:${e}`]?this.session.namespaces[`${this.namespace}:${e}`]:this.session.namespaces[this.namespace];this.setChainIds(e?[this.formatChainId(e)]:r==null?void 0:r.accounts),this.setAccounts(r==null?void 0:r.accounts)}catch(e){this.signer.logger.error("Failed to load persisted session, clearing state..."),this.signer.logger.error(e),await this.disconnect().catch(r=>this.signer.logger.warn(r))}}reset(){this.chainId=1,this.accounts=[]}persist(){this.session&&this.signer.client.core.storage.setItem(`${this.STORAGE_KEY}/chainId`,this.chainId)}parseAccounts(e){return typeof e=="string"||e instanceof String?[this.parseAccount(e)]:e.map(r=>this.parseAccount(r))}}const OP=Ws;var PP=Object.defineProperty,xP=Object.defineProperties,AP=Object.getOwnPropertyDescriptors,Xl=Object.getOwnPropertySymbols,TP=Object.prototype.hasOwnProperty,CP=Object.prototype.propertyIsEnumerable,eh=(t,e,r)=>e in t?PP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ad=(t,e)=>{for(var r in e||(e={}))TP.call(e,r)&&eh(t,r,e[r]);if(Xl)for(var r of Xl(e))CP.call(e,r)&&eh(t,r,e[r]);return t},RP=(t,e)=>xP(t,AP(e));function NP(t){if(t)return{"--w3m-font-family":t["--wcm-font-family"],"--w3m-accent":t["--wcm-accent-color"],"--w3m-color-mix":t["--wcm-background-color"],"--w3m-z-index":t["--wcm-z-index"]?Number(t["--wcm-z-index"]):void 0,"--w3m-qr-color":t["--wcm-accent-color"],"--w3m-font-size-master":t["--wcm-text-medium-regular-size"],"--w3m-border-radius-master":t["--wcm-container-border-radius"],"--w3m-color-mix-strength":0}}const jP=t=>{const[e,r]=t.split(":");return Td({id:r,caipNetworkId:t,chainNamespace:e,name:"",nativeCurrency:{name:"",symbol:"",decimals:8},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}}})};function BP(t){var e,r,i,s,n,o,a;const c=(e=t.chains)==null?void 0:e.map(jP).filter(Boolean);if(c.length===0)throw new Error("At least one chain must be specified");const l=c.find(u=>{var d;return u.id===((d=t.defaultChain)==null?void 0:d.id)}),h={projectId:t.projectId,networks:c,themeMode:t.themeMode,themeVariables:NP(t.themeVariables),chainImages:t.chainImages,connectorImages:t.walletImages,defaultNetwork:l,metadata:RP(Ad({},t.metadata),{name:((r=t.metadata)==null?void 0:r.name)||"WalletConnect",description:((i=t.metadata)==null?void 0:i.description)||"Connect to WalletConnect-compatible wallets",url:((s=t.metadata)==null?void 0:s.url)||"https://walletconnect.org",icons:((n=t.metadata)==null?void 0:n.icons)||["https://walletconnect.org/walletconnect-logo.png"]}),showWallets:!0,featuredWalletIds:t.explorerRecommendedWalletIds==="NONE"?[]:Array.isArray(t.explorerRecommendedWalletIds)?t.explorerRecommendedWalletIds:[],excludeWalletIds:t.explorerExcludedWalletIds==="ALL"?[]:Array.isArray(t.explorerExcludedWalletIds)?t.explorerExcludedWalletIds:[],enableEIP6963:!1,enableInjected:!1,enableCoinbase:!0,enableWalletConnect:!0,features:{email:!1,socials:!1}};if((o=t.mobileWallets)!=null&&o.length||(a=t.desktopWallets)!=null&&a.length){const u=[...(t.mobileWallets||[]).map(p=>({id:p.id,name:p.name,links:p.links})),...(t.desktopWallets||[]).map(p=>({id:p.id,name:p.name,links:{native:p.links.native,universal:p.links.universal}}))],d=[...h.featuredWalletIds||[],...h.excludeWalletIds||[]],f=u.filter(p=>!d.includes(p.id));f.length&&(h.customWallets=f)}return h}function Td(t){return Ad({formatters:void 0,fees:void 0,serializers:void 0},t)}var DP=Object.freeze({__proto__:null,convertWCMToAppKitOptions:BP,defineChain:Td});const xx=Object.freeze(Object.defineProperty({__proto__:null,EthereumProvider:OP,OPTIONAL_EVENTS:xd,OPTIONAL_METHODS:Pd,REQUIRED_EVENTS:Os,REQUIRED_METHODS:Ss,default:Ws},Symbol.toStringTag,{value:"Module"}));export{fE as A,Me as B,Ew as C,Ht as D,je as E,gr as F,it as G,pf as H,fr as I,__ as J,Wo as K,Hs as L,Fs as M,$m as N,zi as O,Va as P,Gh as Q,nr as R,zs as S,bt as T,et as U,wt as V,dt as W,xx as X,va as a,rf as b,MP as c,qP as d,As as e,HP as f,zP as g,es as h,Dt as i,ah as j,tt as k,qe as l,j as m,ar as n,Po as o,Zd as p,oh as q,FP as r,lr as s,gh as t,vi as u,Hn as v,ff as w,Rf as x,Ms as y,u1 as z};
//# sourceMappingURL=index.es-CAssemqx.js.map
