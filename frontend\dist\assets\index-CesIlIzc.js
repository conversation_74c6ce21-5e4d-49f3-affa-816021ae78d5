import{k as rn,j as ee,ax as dh,g as ph}from"./index-2wea5Wgv.js";import{p as gh,a as yh}from"./hooks.module-Dz_XB4AG.js";import{e as Ei}from"./events-B2jzgt6q.js";import{b as mh}from"./browser-B0UiaHO7.js";var Xc={},mn={},Si={};Object.defineProperty(Si,"__esModule",{value:!0});Si.walletLogo=void 0;const vh=(e,t)=>{let r;switch(e){case"standard":return r=t,`data:image/svg+xml,%3Csvg width='${t}' height='${r}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `;case"circle":return r=t,`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${t}' height='${r}' viewBox='0 0 999.81 999.81'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052fe;%7D.cls-2%7Bfill:%23fefefe;%7D.cls-3%7Bfill:%230152fe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M655-115.9h56c.83,1.59,2.36.88,3.56,1a478,478,0,0,1,75.06,10.42C891.4-81.76,978.33-32.58,1049.19,44q116.7,126,131.94,297.61c.38,4.14-.34,8.53,1.78,12.45v59c-1.58.84-.91,2.35-1,3.56a482.05,482.05,0,0,1-10.38,74.05c-24,106.72-76.64,196.76-158.83,268.93s-178.18,112.82-287.2,122.6c-4.83.43-9.86-.25-14.51,1.77H654c-1-1.68-2.69-.91-4.06-1a496.89,496.89,0,0,1-105.9-18.59c-93.54-27.42-172.78-77.59-236.91-150.94Q199.34,590.1,184.87,426.58c-.47-5.19.25-10.56-1.77-15.59V355c1.68-1,.91-2.7,1-4.06a498.12,498.12,0,0,1,18.58-105.9c26-88.75,72.64-164.9,140.6-227.57q126-116.27,297.21-131.61C645.32-114.57,650.35-113.88,655-115.9Zm377.92,500c0-192.44-156.31-349.49-347.56-350.15-194.13-.68-350.94,155.13-352.29,347.42-1.37,194.55,155.51,352.1,348.56,352.47C876.15,734.23,1032.93,577.84,1032.93,384.11Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-2' d='M1032.93,384.11c0,193.73-156.78,350.12-351.29,349.74-193-.37-349.93-157.92-348.56-352.47C334.43,189.09,491.24,33.28,685.37,34,876.62,34.62,1032.94,191.67,1032.93,384.11ZM683,496.81q43.74,0,87.48,0c15.55,0,25.32-9.72,25.33-25.21q0-87.48,0-175c0-15.83-9.68-25.46-25.59-25.46H595.77c-15.88,0-25.57,9.64-25.58,25.46q0,87.23,0,174.45c0,16.18,9.59,25.7,25.84,25.71Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-3' d='M683,496.81H596c-16.25,0-25.84-9.53-25.84-25.71q0-87.23,0-174.45c0-15.82,9.7-25.46,25.58-25.46H770.22c15.91,0,25.59,9.63,25.59,25.46q0,87.47,0,175c0,15.49-9.78,25.2-25.33,25.21Q726.74,496.84,683,496.81Z' transform='translate(-183.1 115.9)'/%3E%3C/svg%3E`;case"text":return r=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogo":return r=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;case"textLight":return r=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogoLight":return r=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;default:return r=t,`data:image/svg+xml,%3Csvg width='${t}' height='${r}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `}};Si.walletLogo=vh;var Ri={};Object.defineProperty(Ri,"__esModule",{value:!0});Ri.LINK_API_URL=void 0;Ri.LINK_API_URL="https://www.walletlink.org";var Q={},xo={exports:{}};const bh={},wh=Object.freeze(Object.defineProperty({__proto__:null,default:bh},Symbol.toStringTag,{value:"Module"})),ko=rn(wh);xo.exports;(function(e){(function(t,r){function n(k,a){if(!k)throw new Error(a||"Assertion failed")}function i(k,a){k.super_=a;var d=function(){};d.prototype=a.prototype,k.prototype=new d,k.prototype.constructor=k}function s(k,a,d){if(s.isBN(k))return k;this.negative=0,this.words=null,this.length=0,this.red=null,k!==null&&((a==="le"||a==="be")&&(d=a,a=10),this._init(k||0,a||10,d||"be"))}typeof t=="object"?t.exports=s:r.BN=s,s.BN=s,s.wordSize=26;var c;try{typeof window<"u"&&typeof window.Buffer<"u"?c=window.Buffer:c=ko.Buffer}catch{}s.isBN=function(a){return a instanceof s?!0:a!==null&&typeof a=="object"&&a.constructor.wordSize===s.wordSize&&Array.isArray(a.words)},s.max=function(a,d){return a.cmp(d)>0?a:d},s.min=function(a,d){return a.cmp(d)<0?a:d},s.prototype._init=function(a,d,m){if(typeof a=="number")return this._initNumber(a,d,m);if(typeof a=="object")return this._initArray(a,d,m);d==="hex"&&(d=16),n(d===(d|0)&&d>=2&&d<=36),a=a.toString().replace(/\s+/g,"");var v=0;a[0]==="-"&&(v++,this.negative=1),v<a.length&&(d===16?this._parseHex(a,v,m):(this._parseBase(a,d,v),m==="le"&&this._initArray(this.toArray(),d,m)))},s.prototype._initNumber=function(a,d,m){a<0&&(this.negative=1,a=-a),a<67108864?(this.words=[a&67108863],this.length=1):a<4503599627370496?(this.words=[a&67108863,a/67108864&67108863],this.length=2):(n(a<9007199254740992),this.words=[a&67108863,a/67108864&67108863,1],this.length=3),m==="le"&&this._initArray(this.toArray(),d,m)},s.prototype._initArray=function(a,d,m){if(n(typeof a.length=="number"),a.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(a.length/3),this.words=new Array(this.length);for(var v=0;v<this.length;v++)this.words[v]=0;var S,x,B=0;if(m==="be")for(v=a.length-1,S=0;v>=0;v-=3)x=a[v]|a[v-1]<<8|a[v-2]<<16,this.words[S]|=x<<B&67108863,this.words[S+1]=x>>>26-B&67108863,B+=24,B>=26&&(B-=26,S++);else if(m==="le")for(v=0,S=0;v<a.length;v+=3)x=a[v]|a[v+1]<<8|a[v+2]<<16,this.words[S]|=x<<B&67108863,this.words[S+1]=x>>>26-B&67108863,B+=24,B>=26&&(B-=26,S++);return this._strip()};function o(k,a){var d=k.charCodeAt(a);if(d>=48&&d<=57)return d-48;if(d>=65&&d<=70)return d-55;if(d>=97&&d<=102)return d-87;n(!1,"Invalid character in "+k)}function u(k,a,d){var m=o(k,d);return d-1>=a&&(m|=o(k,d-1)<<4),m}s.prototype._parseHex=function(a,d,m){this.length=Math.ceil((a.length-d)/6),this.words=new Array(this.length);for(var v=0;v<this.length;v++)this.words[v]=0;var S=0,x=0,B;if(m==="be")for(v=a.length-1;v>=d;v-=2)B=u(a,d,v)<<S,this.words[x]|=B&67108863,S>=18?(S-=18,x+=1,this.words[x]|=B>>>26):S+=8;else{var b=a.length-d;for(v=b%2===0?d+1:d;v<a.length;v+=2)B=u(a,d,v)<<S,this.words[x]|=B&67108863,S>=18?(S-=18,x+=1,this.words[x]|=B>>>26):S+=8}this._strip()};function p(k,a,d,m){for(var v=0,S=0,x=Math.min(k.length,d),B=a;B<x;B++){var b=k.charCodeAt(B)-48;v*=m,b>=49?S=b-49+10:b>=17?S=b-17+10:S=b,n(b>=0&&S<m,"Invalid character"),v+=S}return v}s.prototype._parseBase=function(a,d,m){this.words=[0],this.length=1;for(var v=0,S=1;S<=67108863;S*=d)v++;v--,S=S/d|0;for(var x=a.length-m,B=x%v,b=Math.min(x,x-B)+m,h=0,R=m;R<b;R+=v)h=p(a,R,R+v,d),this.imuln(S),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(B!==0){var K=1;for(h=p(a,R,a.length,d),R=0;R<B;R++)K*=d;this.imuln(K),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}this._strip()},s.prototype.copy=function(a){a.words=new Array(this.length);for(var d=0;d<this.length;d++)a.words[d]=this.words[d];a.length=this.length,a.negative=this.negative,a.red=this.red};function g(k,a){k.words=a.words,k.length=a.length,k.negative=a.negative,k.red=a.red}if(s.prototype._move=function(a){g(a,this)},s.prototype.clone=function(){var a=new s(null);return this.copy(a),a},s.prototype._expand=function(a){for(;this.length<a;)this.words[this.length++]=0;return this},s.prototype._strip=function(){for(;this.length>1&&this.words[this.length-1]===0;)this.length--;return this._normSign()},s.prototype._normSign=function(){return this.length===1&&this.words[0]===0&&(this.negative=0),this},typeof Symbol<"u"&&typeof Symbol.for=="function")try{s.prototype[Symbol.for("nodejs.util.inspect.custom")]=w}catch{s.prototype.inspect=w}else s.prototype.inspect=w;function w(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var _=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],L=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],j=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];s.prototype.toString=function(a,d){a=a||10,d=d|0||1;var m;if(a===16||a==="hex"){m="";for(var v=0,S=0,x=0;x<this.length;x++){var B=this.words[x],b=((B<<v|S)&16777215).toString(16);S=B>>>24-v&16777215,v+=2,v>=26&&(v-=26,x--),S!==0||x!==this.length-1?m=_[6-b.length]+b+m:m=b+m}for(S!==0&&(m=S.toString(16)+m);m.length%d!==0;)m="0"+m;return this.negative!==0&&(m="-"+m),m}if(a===(a|0)&&a>=2&&a<=36){var h=L[a],R=j[a];m="";var K=this.clone();for(K.negative=0;!K.isZero();){var J=K.modrn(R).toString(a);K=K.idivn(R),K.isZero()?m=J+m:m=_[h-J.length]+J+m}for(this.isZero()&&(m="0"+m);m.length%d!==0;)m="0"+m;return this.negative!==0&&(m="-"+m),m}n(!1,"Base should be between 2 and 36")},s.prototype.toNumber=function(){var a=this.words[0];return this.length===2?a+=this.words[1]*67108864:this.length===3&&this.words[2]===1?a+=4503599627370496+this.words[1]*67108864:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),this.negative!==0?-a:a},s.prototype.toJSON=function(){return this.toString(16,2)},c&&(s.prototype.toBuffer=function(a,d){return this.toArrayLike(c,a,d)}),s.prototype.toArray=function(a,d){return this.toArrayLike(Array,a,d)};var $=function(a,d){return a.allocUnsafe?a.allocUnsafe(d):new a(d)};s.prototype.toArrayLike=function(a,d,m){this._strip();var v=this.byteLength(),S=m||Math.max(1,v);n(v<=S,"byte array longer than desired length"),n(S>0,"Requested array length <= 0");var x=$(a,S),B=d==="le"?"LE":"BE";return this["_toArrayLike"+B](x,v),x},s.prototype._toArrayLikeLE=function(a,d){for(var m=0,v=0,S=0,x=0;S<this.length;S++){var B=this.words[S]<<x|v;a[m++]=B&255,m<a.length&&(a[m++]=B>>8&255),m<a.length&&(a[m++]=B>>16&255),x===6?(m<a.length&&(a[m++]=B>>24&255),v=0,x=0):(v=B>>>24,x+=2)}if(m<a.length)for(a[m++]=v;m<a.length;)a[m++]=0},s.prototype._toArrayLikeBE=function(a,d){for(var m=a.length-1,v=0,S=0,x=0;S<this.length;S++){var B=this.words[S]<<x|v;a[m--]=B&255,m>=0&&(a[m--]=B>>8&255),m>=0&&(a[m--]=B>>16&255),x===6?(m>=0&&(a[m--]=B>>24&255),v=0,x=0):(v=B>>>24,x+=2)}if(m>=0)for(a[m--]=v;m>=0;)a[m--]=0},Math.clz32?s.prototype._countBits=function(a){return 32-Math.clz32(a)}:s.prototype._countBits=function(a){var d=a,m=0;return d>=4096&&(m+=13,d>>>=13),d>=64&&(m+=7,d>>>=7),d>=8&&(m+=4,d>>>=4),d>=2&&(m+=2,d>>>=2),m+d},s.prototype._zeroBits=function(a){if(a===0)return 26;var d=a,m=0;return d&8191||(m+=13,d>>>=13),d&127||(m+=7,d>>>=7),d&15||(m+=4,d>>>=4),d&3||(m+=2,d>>>=2),d&1||m++,m},s.prototype.bitLength=function(){var a=this.words[this.length-1],d=this._countBits(a);return(this.length-1)*26+d};function O(k){for(var a=new Array(k.bitLength()),d=0;d<a.length;d++){var m=d/26|0,v=d%26;a[d]=k.words[m]>>>v&1}return a}s.prototype.zeroBits=function(){if(this.isZero())return 0;for(var a=0,d=0;d<this.length;d++){var m=this._zeroBits(this.words[d]);if(a+=m,m!==26)break}return a},s.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},s.prototype.toTwos=function(a){return this.negative!==0?this.abs().inotn(a).iaddn(1):this.clone()},s.prototype.fromTwos=function(a){return this.testn(a-1)?this.notn(a).iaddn(1).ineg():this.clone()},s.prototype.isNeg=function(){return this.negative!==0},s.prototype.neg=function(){return this.clone().ineg()},s.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},s.prototype.iuor=function(a){for(;this.length<a.length;)this.words[this.length++]=0;for(var d=0;d<a.length;d++)this.words[d]=this.words[d]|a.words[d];return this._strip()},s.prototype.ior=function(a){return n((this.negative|a.negative)===0),this.iuor(a)},s.prototype.or=function(a){return this.length>a.length?this.clone().ior(a):a.clone().ior(this)},s.prototype.uor=function(a){return this.length>a.length?this.clone().iuor(a):a.clone().iuor(this)},s.prototype.iuand=function(a){var d;this.length>a.length?d=a:d=this;for(var m=0;m<d.length;m++)this.words[m]=this.words[m]&a.words[m];return this.length=d.length,this._strip()},s.prototype.iand=function(a){return n((this.negative|a.negative)===0),this.iuand(a)},s.prototype.and=function(a){return this.length>a.length?this.clone().iand(a):a.clone().iand(this)},s.prototype.uand=function(a){return this.length>a.length?this.clone().iuand(a):a.clone().iuand(this)},s.prototype.iuxor=function(a){var d,m;this.length>a.length?(d=this,m=a):(d=a,m=this);for(var v=0;v<m.length;v++)this.words[v]=d.words[v]^m.words[v];if(this!==d)for(;v<d.length;v++)this.words[v]=d.words[v];return this.length=d.length,this._strip()},s.prototype.ixor=function(a){return n((this.negative|a.negative)===0),this.iuxor(a)},s.prototype.xor=function(a){return this.length>a.length?this.clone().ixor(a):a.clone().ixor(this)},s.prototype.uxor=function(a){return this.length>a.length?this.clone().iuxor(a):a.clone().iuxor(this)},s.prototype.inotn=function(a){n(typeof a=="number"&&a>=0);var d=Math.ceil(a/26)|0,m=a%26;this._expand(d),m>0&&d--;for(var v=0;v<d;v++)this.words[v]=~this.words[v]&67108863;return m>0&&(this.words[v]=~this.words[v]&67108863>>26-m),this._strip()},s.prototype.notn=function(a){return this.clone().inotn(a)},s.prototype.setn=function(a,d){n(typeof a=="number"&&a>=0);var m=a/26|0,v=a%26;return this._expand(m+1),d?this.words[m]=this.words[m]|1<<v:this.words[m]=this.words[m]&~(1<<v),this._strip()},s.prototype.iadd=function(a){var d;if(this.negative!==0&&a.negative===0)return this.negative=0,d=this.isub(a),this.negative^=1,this._normSign();if(this.negative===0&&a.negative!==0)return a.negative=0,d=this.isub(a),a.negative=1,d._normSign();var m,v;this.length>a.length?(m=this,v=a):(m=a,v=this);for(var S=0,x=0;x<v.length;x++)d=(m.words[x]|0)+(v.words[x]|0)+S,this.words[x]=d&67108863,S=d>>>26;for(;S!==0&&x<m.length;x++)d=(m.words[x]|0)+S,this.words[x]=d&67108863,S=d>>>26;if(this.length=m.length,S!==0)this.words[this.length]=S,this.length++;else if(m!==this)for(;x<m.length;x++)this.words[x]=m.words[x];return this},s.prototype.add=function(a){var d;return a.negative!==0&&this.negative===0?(a.negative=0,d=this.sub(a),a.negative^=1,d):a.negative===0&&this.negative!==0?(this.negative=0,d=a.sub(this),this.negative=1,d):this.length>a.length?this.clone().iadd(a):a.clone().iadd(this)},s.prototype.isub=function(a){if(a.negative!==0){a.negative=0;var d=this.iadd(a);return a.negative=1,d._normSign()}else if(this.negative!==0)return this.negative=0,this.iadd(a),this.negative=1,this._normSign();var m=this.cmp(a);if(m===0)return this.negative=0,this.length=1,this.words[0]=0,this;var v,S;m>0?(v=this,S=a):(v=a,S=this);for(var x=0,B=0;B<S.length;B++)d=(v.words[B]|0)-(S.words[B]|0)+x,x=d>>26,this.words[B]=d&67108863;for(;x!==0&&B<v.length;B++)d=(v.words[B]|0)+x,x=d>>26,this.words[B]=d&67108863;if(x===0&&B<v.length&&v!==this)for(;B<v.length;B++)this.words[B]=v.words[B];return this.length=Math.max(this.length,B),v!==this&&(this.negative=1),this._strip()},s.prototype.sub=function(a){return this.clone().isub(a)};function A(k,a,d){d.negative=a.negative^k.negative;var m=k.length+a.length|0;d.length=m,m=m-1|0;var v=k.words[0]|0,S=a.words[0]|0,x=v*S,B=x&67108863,b=x/67108864|0;d.words[0]=B;for(var h=1;h<m;h++){for(var R=b>>>26,K=b&67108863,J=Math.min(h,a.length-1),T=Math.max(0,h-k.length+1);T<=J;T++){var F=h-T|0;v=k.words[F]|0,S=a.words[T]|0,x=v*S+K,R+=x/67108864|0,K=x&67108863}d.words[h]=K|0,b=R|0}return b!==0?d.words[h]=b|0:d.length--,d._strip()}var N=function(a,d,m){var v=a.words,S=d.words,x=m.words,B=0,b,h,R,K=v[0]|0,J=K&8191,T=K>>>13,F=v[1]|0,q=F&8191,Z=F>>>13,le=v[2]|0,M=le&8191,I=le>>>13,V=v[3]|0,G=V&8191,se=V>>>13,ue=v[4]|0,re=ue&8191,we=ue>>>13,dt=v[5]|0,Re=dt&8191,Se=dt>>>13,Ze=v[6]|0,_e=Ze&8191,Me=Ze>>>13,Qe=v[7]|0,Ee=Qe&8191,y=Qe>>>13,l=v[8]|0,f=l&8191,E=l>>>13,P=v[9]|0,D=P&8191,H=P>>>13,he=S[0]|0,ae=he&8191,ie=he>>>13,Ie=S[1]|0,ne=Ie&8191,Ae=Ie>>>13,Rr=S[2]|0,xe=Rr&8191,ke=Rr>>>13,Mr=S[3]|0,Ce=Mr&8191,Te=Mr>>>13,Ir=S[4]|0,Be=Ir&8191,Ne=Ir>>>13,Ar=S[5]|0,Pe=Ar&8191,Le=Ar>>>13,xr=S[6]|0,Oe=xr&8191,$e=xr>>>13,kr=S[7]|0,Fe=kr&8191,De=kr>>>13,Cr=S[8]|0,je=Cr&8191,Ue=Cr>>>13,Tr=S[9]|0,He=Tr&8191,We=Tr>>>13;m.negative=a.negative^d.negative,m.length=19,b=Math.imul(J,ae),h=Math.imul(J,ie),h=h+Math.imul(T,ae)|0,R=Math.imul(T,ie);var Jt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Jt>>>26)|0,Jt&=67108863,b=Math.imul(q,ae),h=Math.imul(q,ie),h=h+Math.imul(Z,ae)|0,R=Math.imul(Z,ie),b=b+Math.imul(J,ne)|0,h=h+Math.imul(J,Ae)|0,h=h+Math.imul(T,ne)|0,R=R+Math.imul(T,Ae)|0;var Zt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Zt>>>26)|0,Zt&=67108863,b=Math.imul(M,ae),h=Math.imul(M,ie),h=h+Math.imul(I,ae)|0,R=Math.imul(I,ie),b=b+Math.imul(q,ne)|0,h=h+Math.imul(q,Ae)|0,h=h+Math.imul(Z,ne)|0,R=R+Math.imul(Z,Ae)|0,b=b+Math.imul(J,xe)|0,h=h+Math.imul(J,ke)|0,h=h+Math.imul(T,xe)|0,R=R+Math.imul(T,ke)|0;var Kt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Kt>>>26)|0,Kt&=67108863,b=Math.imul(G,ae),h=Math.imul(G,ie),h=h+Math.imul(se,ae)|0,R=Math.imul(se,ie),b=b+Math.imul(M,ne)|0,h=h+Math.imul(M,Ae)|0,h=h+Math.imul(I,ne)|0,R=R+Math.imul(I,Ae)|0,b=b+Math.imul(q,xe)|0,h=h+Math.imul(q,ke)|0,h=h+Math.imul(Z,xe)|0,R=R+Math.imul(Z,ke)|0,b=b+Math.imul(J,Ce)|0,h=h+Math.imul(J,Te)|0,h=h+Math.imul(T,Ce)|0,R=R+Math.imul(T,Te)|0;var Yt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Yt>>>26)|0,Yt&=67108863,b=Math.imul(re,ae),h=Math.imul(re,ie),h=h+Math.imul(we,ae)|0,R=Math.imul(we,ie),b=b+Math.imul(G,ne)|0,h=h+Math.imul(G,Ae)|0,h=h+Math.imul(se,ne)|0,R=R+Math.imul(se,Ae)|0,b=b+Math.imul(M,xe)|0,h=h+Math.imul(M,ke)|0,h=h+Math.imul(I,xe)|0,R=R+Math.imul(I,ke)|0,b=b+Math.imul(q,Ce)|0,h=h+Math.imul(q,Te)|0,h=h+Math.imul(Z,Ce)|0,R=R+Math.imul(Z,Te)|0,b=b+Math.imul(J,Be)|0,h=h+Math.imul(J,Ne)|0,h=h+Math.imul(T,Be)|0,R=R+Math.imul(T,Ne)|0;var Qt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Qt>>>26)|0,Qt&=67108863,b=Math.imul(Re,ae),h=Math.imul(Re,ie),h=h+Math.imul(Se,ae)|0,R=Math.imul(Se,ie),b=b+Math.imul(re,ne)|0,h=h+Math.imul(re,Ae)|0,h=h+Math.imul(we,ne)|0,R=R+Math.imul(we,Ae)|0,b=b+Math.imul(G,xe)|0,h=h+Math.imul(G,ke)|0,h=h+Math.imul(se,xe)|0,R=R+Math.imul(se,ke)|0,b=b+Math.imul(M,Ce)|0,h=h+Math.imul(M,Te)|0,h=h+Math.imul(I,Ce)|0,R=R+Math.imul(I,Te)|0,b=b+Math.imul(q,Be)|0,h=h+Math.imul(q,Ne)|0,h=h+Math.imul(Z,Be)|0,R=R+Math.imul(Z,Ne)|0,b=b+Math.imul(J,Pe)|0,h=h+Math.imul(J,Le)|0,h=h+Math.imul(T,Pe)|0,R=R+Math.imul(T,Le)|0;var Xt=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Xt>>>26)|0,Xt&=67108863,b=Math.imul(_e,ae),h=Math.imul(_e,ie),h=h+Math.imul(Me,ae)|0,R=Math.imul(Me,ie),b=b+Math.imul(Re,ne)|0,h=h+Math.imul(Re,Ae)|0,h=h+Math.imul(Se,ne)|0,R=R+Math.imul(Se,Ae)|0,b=b+Math.imul(re,xe)|0,h=h+Math.imul(re,ke)|0,h=h+Math.imul(we,xe)|0,R=R+Math.imul(we,ke)|0,b=b+Math.imul(G,Ce)|0,h=h+Math.imul(G,Te)|0,h=h+Math.imul(se,Ce)|0,R=R+Math.imul(se,Te)|0,b=b+Math.imul(M,Be)|0,h=h+Math.imul(M,Ne)|0,h=h+Math.imul(I,Be)|0,R=R+Math.imul(I,Ne)|0,b=b+Math.imul(q,Pe)|0,h=h+Math.imul(q,Le)|0,h=h+Math.imul(Z,Pe)|0,R=R+Math.imul(Z,Le)|0,b=b+Math.imul(J,Oe)|0,h=h+Math.imul(J,$e)|0,h=h+Math.imul(T,Oe)|0,R=R+Math.imul(T,$e)|0;var er=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(er>>>26)|0,er&=67108863,b=Math.imul(Ee,ae),h=Math.imul(Ee,ie),h=h+Math.imul(y,ae)|0,R=Math.imul(y,ie),b=b+Math.imul(_e,ne)|0,h=h+Math.imul(_e,Ae)|0,h=h+Math.imul(Me,ne)|0,R=R+Math.imul(Me,Ae)|0,b=b+Math.imul(Re,xe)|0,h=h+Math.imul(Re,ke)|0,h=h+Math.imul(Se,xe)|0,R=R+Math.imul(Se,ke)|0,b=b+Math.imul(re,Ce)|0,h=h+Math.imul(re,Te)|0,h=h+Math.imul(we,Ce)|0,R=R+Math.imul(we,Te)|0,b=b+Math.imul(G,Be)|0,h=h+Math.imul(G,Ne)|0,h=h+Math.imul(se,Be)|0,R=R+Math.imul(se,Ne)|0,b=b+Math.imul(M,Pe)|0,h=h+Math.imul(M,Le)|0,h=h+Math.imul(I,Pe)|0,R=R+Math.imul(I,Le)|0,b=b+Math.imul(q,Oe)|0,h=h+Math.imul(q,$e)|0,h=h+Math.imul(Z,Oe)|0,R=R+Math.imul(Z,$e)|0,b=b+Math.imul(J,Fe)|0,h=h+Math.imul(J,De)|0,h=h+Math.imul(T,Fe)|0,R=R+Math.imul(T,De)|0;var tr=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(tr>>>26)|0,tr&=67108863,b=Math.imul(f,ae),h=Math.imul(f,ie),h=h+Math.imul(E,ae)|0,R=Math.imul(E,ie),b=b+Math.imul(Ee,ne)|0,h=h+Math.imul(Ee,Ae)|0,h=h+Math.imul(y,ne)|0,R=R+Math.imul(y,Ae)|0,b=b+Math.imul(_e,xe)|0,h=h+Math.imul(_e,ke)|0,h=h+Math.imul(Me,xe)|0,R=R+Math.imul(Me,ke)|0,b=b+Math.imul(Re,Ce)|0,h=h+Math.imul(Re,Te)|0,h=h+Math.imul(Se,Ce)|0,R=R+Math.imul(Se,Te)|0,b=b+Math.imul(re,Be)|0,h=h+Math.imul(re,Ne)|0,h=h+Math.imul(we,Be)|0,R=R+Math.imul(we,Ne)|0,b=b+Math.imul(G,Pe)|0,h=h+Math.imul(G,Le)|0,h=h+Math.imul(se,Pe)|0,R=R+Math.imul(se,Le)|0,b=b+Math.imul(M,Oe)|0,h=h+Math.imul(M,$e)|0,h=h+Math.imul(I,Oe)|0,R=R+Math.imul(I,$e)|0,b=b+Math.imul(q,Fe)|0,h=h+Math.imul(q,De)|0,h=h+Math.imul(Z,Fe)|0,R=R+Math.imul(Z,De)|0,b=b+Math.imul(J,je)|0,h=h+Math.imul(J,Ue)|0,h=h+Math.imul(T,je)|0,R=R+Math.imul(T,Ue)|0;var rr=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(rr>>>26)|0,rr&=67108863,b=Math.imul(D,ae),h=Math.imul(D,ie),h=h+Math.imul(H,ae)|0,R=Math.imul(H,ie),b=b+Math.imul(f,ne)|0,h=h+Math.imul(f,Ae)|0,h=h+Math.imul(E,ne)|0,R=R+Math.imul(E,Ae)|0,b=b+Math.imul(Ee,xe)|0,h=h+Math.imul(Ee,ke)|0,h=h+Math.imul(y,xe)|0,R=R+Math.imul(y,ke)|0,b=b+Math.imul(_e,Ce)|0,h=h+Math.imul(_e,Te)|0,h=h+Math.imul(Me,Ce)|0,R=R+Math.imul(Me,Te)|0,b=b+Math.imul(Re,Be)|0,h=h+Math.imul(Re,Ne)|0,h=h+Math.imul(Se,Be)|0,R=R+Math.imul(Se,Ne)|0,b=b+Math.imul(re,Pe)|0,h=h+Math.imul(re,Le)|0,h=h+Math.imul(we,Pe)|0,R=R+Math.imul(we,Le)|0,b=b+Math.imul(G,Oe)|0,h=h+Math.imul(G,$e)|0,h=h+Math.imul(se,Oe)|0,R=R+Math.imul(se,$e)|0,b=b+Math.imul(M,Fe)|0,h=h+Math.imul(M,De)|0,h=h+Math.imul(I,Fe)|0,R=R+Math.imul(I,De)|0,b=b+Math.imul(q,je)|0,h=h+Math.imul(q,Ue)|0,h=h+Math.imul(Z,je)|0,R=R+Math.imul(Z,Ue)|0,b=b+Math.imul(J,He)|0,h=h+Math.imul(J,We)|0,h=h+Math.imul(T,He)|0,R=R+Math.imul(T,We)|0;var nr=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(nr>>>26)|0,nr&=67108863,b=Math.imul(D,ne),h=Math.imul(D,Ae),h=h+Math.imul(H,ne)|0,R=Math.imul(H,Ae),b=b+Math.imul(f,xe)|0,h=h+Math.imul(f,ke)|0,h=h+Math.imul(E,xe)|0,R=R+Math.imul(E,ke)|0,b=b+Math.imul(Ee,Ce)|0,h=h+Math.imul(Ee,Te)|0,h=h+Math.imul(y,Ce)|0,R=R+Math.imul(y,Te)|0,b=b+Math.imul(_e,Be)|0,h=h+Math.imul(_e,Ne)|0,h=h+Math.imul(Me,Be)|0,R=R+Math.imul(Me,Ne)|0,b=b+Math.imul(Re,Pe)|0,h=h+Math.imul(Re,Le)|0,h=h+Math.imul(Se,Pe)|0,R=R+Math.imul(Se,Le)|0,b=b+Math.imul(re,Oe)|0,h=h+Math.imul(re,$e)|0,h=h+Math.imul(we,Oe)|0,R=R+Math.imul(we,$e)|0,b=b+Math.imul(G,Fe)|0,h=h+Math.imul(G,De)|0,h=h+Math.imul(se,Fe)|0,R=R+Math.imul(se,De)|0,b=b+Math.imul(M,je)|0,h=h+Math.imul(M,Ue)|0,h=h+Math.imul(I,je)|0,R=R+Math.imul(I,Ue)|0,b=b+Math.imul(q,He)|0,h=h+Math.imul(q,We)|0,h=h+Math.imul(Z,He)|0,R=R+Math.imul(Z,We)|0;var ir=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(ir>>>26)|0,ir&=67108863,b=Math.imul(D,xe),h=Math.imul(D,ke),h=h+Math.imul(H,xe)|0,R=Math.imul(H,ke),b=b+Math.imul(f,Ce)|0,h=h+Math.imul(f,Te)|0,h=h+Math.imul(E,Ce)|0,R=R+Math.imul(E,Te)|0,b=b+Math.imul(Ee,Be)|0,h=h+Math.imul(Ee,Ne)|0,h=h+Math.imul(y,Be)|0,R=R+Math.imul(y,Ne)|0,b=b+Math.imul(_e,Pe)|0,h=h+Math.imul(_e,Le)|0,h=h+Math.imul(Me,Pe)|0,R=R+Math.imul(Me,Le)|0,b=b+Math.imul(Re,Oe)|0,h=h+Math.imul(Re,$e)|0,h=h+Math.imul(Se,Oe)|0,R=R+Math.imul(Se,$e)|0,b=b+Math.imul(re,Fe)|0,h=h+Math.imul(re,De)|0,h=h+Math.imul(we,Fe)|0,R=R+Math.imul(we,De)|0,b=b+Math.imul(G,je)|0,h=h+Math.imul(G,Ue)|0,h=h+Math.imul(se,je)|0,R=R+Math.imul(se,Ue)|0,b=b+Math.imul(M,He)|0,h=h+Math.imul(M,We)|0,h=h+Math.imul(I,He)|0,R=R+Math.imul(I,We)|0;var sr=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(sr>>>26)|0,sr&=67108863,b=Math.imul(D,Ce),h=Math.imul(D,Te),h=h+Math.imul(H,Ce)|0,R=Math.imul(H,Te),b=b+Math.imul(f,Be)|0,h=h+Math.imul(f,Ne)|0,h=h+Math.imul(E,Be)|0,R=R+Math.imul(E,Ne)|0,b=b+Math.imul(Ee,Pe)|0,h=h+Math.imul(Ee,Le)|0,h=h+Math.imul(y,Pe)|0,R=R+Math.imul(y,Le)|0,b=b+Math.imul(_e,Oe)|0,h=h+Math.imul(_e,$e)|0,h=h+Math.imul(Me,Oe)|0,R=R+Math.imul(Me,$e)|0,b=b+Math.imul(Re,Fe)|0,h=h+Math.imul(Re,De)|0,h=h+Math.imul(Se,Fe)|0,R=R+Math.imul(Se,De)|0,b=b+Math.imul(re,je)|0,h=h+Math.imul(re,Ue)|0,h=h+Math.imul(we,je)|0,R=R+Math.imul(we,Ue)|0,b=b+Math.imul(G,He)|0,h=h+Math.imul(G,We)|0,h=h+Math.imul(se,He)|0,R=R+Math.imul(se,We)|0;var or=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(or>>>26)|0,or&=67108863,b=Math.imul(D,Be),h=Math.imul(D,Ne),h=h+Math.imul(H,Be)|0,R=Math.imul(H,Ne),b=b+Math.imul(f,Pe)|0,h=h+Math.imul(f,Le)|0,h=h+Math.imul(E,Pe)|0,R=R+Math.imul(E,Le)|0,b=b+Math.imul(Ee,Oe)|0,h=h+Math.imul(Ee,$e)|0,h=h+Math.imul(y,Oe)|0,R=R+Math.imul(y,$e)|0,b=b+Math.imul(_e,Fe)|0,h=h+Math.imul(_e,De)|0,h=h+Math.imul(Me,Fe)|0,R=R+Math.imul(Me,De)|0,b=b+Math.imul(Re,je)|0,h=h+Math.imul(Re,Ue)|0,h=h+Math.imul(Se,je)|0,R=R+Math.imul(Se,Ue)|0,b=b+Math.imul(re,He)|0,h=h+Math.imul(re,We)|0,h=h+Math.imul(we,He)|0,R=R+Math.imul(we,We)|0;var ar=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(ar>>>26)|0,ar&=67108863,b=Math.imul(D,Pe),h=Math.imul(D,Le),h=h+Math.imul(H,Pe)|0,R=Math.imul(H,Le),b=b+Math.imul(f,Oe)|0,h=h+Math.imul(f,$e)|0,h=h+Math.imul(E,Oe)|0,R=R+Math.imul(E,$e)|0,b=b+Math.imul(Ee,Fe)|0,h=h+Math.imul(Ee,De)|0,h=h+Math.imul(y,Fe)|0,R=R+Math.imul(y,De)|0,b=b+Math.imul(_e,je)|0,h=h+Math.imul(_e,Ue)|0,h=h+Math.imul(Me,je)|0,R=R+Math.imul(Me,Ue)|0,b=b+Math.imul(Re,He)|0,h=h+Math.imul(Re,We)|0,h=h+Math.imul(Se,He)|0,R=R+Math.imul(Se,We)|0;var bs=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(bs>>>26)|0,bs&=67108863,b=Math.imul(D,Oe),h=Math.imul(D,$e),h=h+Math.imul(H,Oe)|0,R=Math.imul(H,$e),b=b+Math.imul(f,Fe)|0,h=h+Math.imul(f,De)|0,h=h+Math.imul(E,Fe)|0,R=R+Math.imul(E,De)|0,b=b+Math.imul(Ee,je)|0,h=h+Math.imul(Ee,Ue)|0,h=h+Math.imul(y,je)|0,R=R+Math.imul(y,Ue)|0,b=b+Math.imul(_e,He)|0,h=h+Math.imul(_e,We)|0,h=h+Math.imul(Me,He)|0,R=R+Math.imul(Me,We)|0;var ws=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(ws>>>26)|0,ws&=67108863,b=Math.imul(D,Fe),h=Math.imul(D,De),h=h+Math.imul(H,Fe)|0,R=Math.imul(H,De),b=b+Math.imul(f,je)|0,h=h+Math.imul(f,Ue)|0,h=h+Math.imul(E,je)|0,R=R+Math.imul(E,Ue)|0,b=b+Math.imul(Ee,He)|0,h=h+Math.imul(Ee,We)|0,h=h+Math.imul(y,He)|0,R=R+Math.imul(y,We)|0;var _s=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(_s>>>26)|0,_s&=67108863,b=Math.imul(D,je),h=Math.imul(D,Ue),h=h+Math.imul(H,je)|0,R=Math.imul(H,Ue),b=b+Math.imul(f,He)|0,h=h+Math.imul(f,We)|0,h=h+Math.imul(E,He)|0,R=R+Math.imul(E,We)|0;var Es=(B+b|0)+((h&8191)<<13)|0;B=(R+(h>>>13)|0)+(Es>>>26)|0,Es&=67108863,b=Math.imul(D,He),h=Math.imul(D,We),h=h+Math.imul(H,He)|0,R=Math.imul(H,We);var Ss=(B+b|0)+((h&8191)<<13)|0;return B=(R+(h>>>13)|0)+(Ss>>>26)|0,Ss&=67108863,x[0]=Jt,x[1]=Zt,x[2]=Kt,x[3]=Yt,x[4]=Qt,x[5]=Xt,x[6]=er,x[7]=tr,x[8]=rr,x[9]=nr,x[10]=ir,x[11]=sr,x[12]=or,x[13]=ar,x[14]=bs,x[15]=ws,x[16]=_s,x[17]=Es,x[18]=Ss,B!==0&&(x[19]=B,m.length++),m};Math.imul||(N=A);function C(k,a,d){d.negative=a.negative^k.negative,d.length=k.length+a.length;for(var m=0,v=0,S=0;S<d.length-1;S++){var x=v;v=0;for(var B=m&67108863,b=Math.min(S,a.length-1),h=Math.max(0,S-k.length+1);h<=b;h++){var R=S-h,K=k.words[R]|0,J=a.words[h]|0,T=K*J,F=T&67108863;x=x+(T/67108864|0)|0,F=F+B|0,B=F&67108863,x=x+(F>>>26)|0,v+=x>>>26,x&=67108863}d.words[S]=B,m=x,x=v}return m!==0?d.words[S]=m:d.length--,d._strip()}function W(k,a,d){return C(k,a,d)}s.prototype.mulTo=function(a,d){var m,v=this.length+a.length;return this.length===10&&a.length===10?m=N(this,a,d):v<63?m=A(this,a,d):v<1024?m=C(this,a,d):m=W(this,a,d),m},s.prototype.mul=function(a){var d=new s(null);return d.words=new Array(this.length+a.length),this.mulTo(a,d)},s.prototype.mulf=function(a){var d=new s(null);return d.words=new Array(this.length+a.length),W(this,a,d)},s.prototype.imul=function(a){return this.clone().mulTo(a,this)},s.prototype.imuln=function(a){var d=a<0;d&&(a=-a),n(typeof a=="number"),n(a<67108864);for(var m=0,v=0;v<this.length;v++){var S=(this.words[v]|0)*a,x=(S&67108863)+(m&67108863);m>>=26,m+=S/67108864|0,m+=x>>>26,this.words[v]=x&67108863}return m!==0&&(this.words[v]=m,this.length++),this.length=a===0?1:this.length,d?this.ineg():this},s.prototype.muln=function(a){return this.clone().imuln(a)},s.prototype.sqr=function(){return this.mul(this)},s.prototype.isqr=function(){return this.imul(this.clone())},s.prototype.pow=function(a){var d=O(a);if(d.length===0)return new s(1);for(var m=this,v=0;v<d.length&&d[v]===0;v++,m=m.sqr());if(++v<d.length)for(var S=m.sqr();v<d.length;v++,S=S.sqr())d[v]!==0&&(m=m.mul(S));return m},s.prototype.iushln=function(a){n(typeof a=="number"&&a>=0);var d=a%26,m=(a-d)/26,v=67108863>>>26-d<<26-d,S;if(d!==0){var x=0;for(S=0;S<this.length;S++){var B=this.words[S]&v,b=(this.words[S]|0)-B<<d;this.words[S]=b|x,x=B>>>26-d}x&&(this.words[S]=x,this.length++)}if(m!==0){for(S=this.length-1;S>=0;S--)this.words[S+m]=this.words[S];for(S=0;S<m;S++)this.words[S]=0;this.length+=m}return this._strip()},s.prototype.ishln=function(a){return n(this.negative===0),this.iushln(a)},s.prototype.iushrn=function(a,d,m){n(typeof a=="number"&&a>=0);var v;d?v=(d-d%26)/26:v=0;var S=a%26,x=Math.min((a-S)/26,this.length),B=67108863^67108863>>>S<<S,b=m;if(v-=x,v=Math.max(0,v),b){for(var h=0;h<x;h++)b.words[h]=this.words[h];b.length=x}if(x!==0)if(this.length>x)for(this.length-=x,h=0;h<this.length;h++)this.words[h]=this.words[h+x];else this.words[0]=0,this.length=1;var R=0;for(h=this.length-1;h>=0&&(R!==0||h>=v);h--){var K=this.words[h]|0;this.words[h]=R<<26-S|K>>>S,R=K&B}return b&&R!==0&&(b.words[b.length++]=R),this.length===0&&(this.words[0]=0,this.length=1),this._strip()},s.prototype.ishrn=function(a,d,m){return n(this.negative===0),this.iushrn(a,d,m)},s.prototype.shln=function(a){return this.clone().ishln(a)},s.prototype.ushln=function(a){return this.clone().iushln(a)},s.prototype.shrn=function(a){return this.clone().ishrn(a)},s.prototype.ushrn=function(a){return this.clone().iushrn(a)},s.prototype.testn=function(a){n(typeof a=="number"&&a>=0);var d=a%26,m=(a-d)/26,v=1<<d;if(this.length<=m)return!1;var S=this.words[m];return!!(S&v)},s.prototype.imaskn=function(a){n(typeof a=="number"&&a>=0);var d=a%26,m=(a-d)/26;if(n(this.negative===0,"imaskn works only with positive numbers"),this.length<=m)return this;if(d!==0&&m++,this.length=Math.min(m,this.length),d!==0){var v=67108863^67108863>>>d<<d;this.words[this.length-1]&=v}return this._strip()},s.prototype.maskn=function(a){return this.clone().imaskn(a)},s.prototype.iaddn=function(a){return n(typeof a=="number"),n(a<67108864),a<0?this.isubn(-a):this.negative!==0?this.length===1&&(this.words[0]|0)<=a?(this.words[0]=a-(this.words[0]|0),this.negative=0,this):(this.negative=0,this.isubn(a),this.negative=1,this):this._iaddn(a)},s.prototype._iaddn=function(a){this.words[0]+=a;for(var d=0;d<this.length&&this.words[d]>=67108864;d++)this.words[d]-=67108864,d===this.length-1?this.words[d+1]=1:this.words[d+1]++;return this.length=Math.max(this.length,d+1),this},s.prototype.isubn=function(a){if(n(typeof a=="number"),n(a<67108864),a<0)return this.iaddn(-a);if(this.negative!==0)return this.negative=0,this.iaddn(a),this.negative=1,this;if(this.words[0]-=a,this.length===1&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var d=0;d<this.length&&this.words[d]<0;d++)this.words[d]+=67108864,this.words[d+1]-=1;return this._strip()},s.prototype.addn=function(a){return this.clone().iaddn(a)},s.prototype.subn=function(a){return this.clone().isubn(a)},s.prototype.iabs=function(){return this.negative=0,this},s.prototype.abs=function(){return this.clone().iabs()},s.prototype._ishlnsubmul=function(a,d,m){var v=a.length+m,S;this._expand(v);var x,B=0;for(S=0;S<a.length;S++){x=(this.words[S+m]|0)+B;var b=(a.words[S]|0)*d;x-=b&67108863,B=(x>>26)-(b/67108864|0),this.words[S+m]=x&67108863}for(;S<this.length-m;S++)x=(this.words[S+m]|0)+B,B=x>>26,this.words[S+m]=x&67108863;if(B===0)return this._strip();for(n(B===-1),B=0,S=0;S<this.length;S++)x=-(this.words[S]|0)+B,B=x>>26,this.words[S]=x&67108863;return this.negative=1,this._strip()},s.prototype._wordDiv=function(a,d){var m=this.length-a.length,v=this.clone(),S=a,x=S.words[S.length-1]|0,B=this._countBits(x);m=26-B,m!==0&&(S=S.ushln(m),v.iushln(m),x=S.words[S.length-1]|0);var b=v.length-S.length,h;if(d!=="mod"){h=new s(null),h.length=b+1,h.words=new Array(h.length);for(var R=0;R<h.length;R++)h.words[R]=0}var K=v.clone()._ishlnsubmul(S,1,b);K.negative===0&&(v=K,h&&(h.words[b]=1));for(var J=b-1;J>=0;J--){var T=(v.words[S.length+J]|0)*67108864+(v.words[S.length+J-1]|0);for(T=Math.min(T/x|0,67108863),v._ishlnsubmul(S,T,J);v.negative!==0;)T--,v.negative=0,v._ishlnsubmul(S,1,J),v.isZero()||(v.negative^=1);h&&(h.words[J]=T)}return h&&h._strip(),v._strip(),d!=="div"&&m!==0&&v.iushrn(m),{div:h||null,mod:v}},s.prototype.divmod=function(a,d,m){if(n(!a.isZero()),this.isZero())return{div:new s(0),mod:new s(0)};var v,S,x;return this.negative!==0&&a.negative===0?(x=this.neg().divmod(a,d),d!=="mod"&&(v=x.div.neg()),d!=="div"&&(S=x.mod.neg(),m&&S.negative!==0&&S.iadd(a)),{div:v,mod:S}):this.negative===0&&a.negative!==0?(x=this.divmod(a.neg(),d),d!=="mod"&&(v=x.div.neg()),{div:v,mod:x.mod}):this.negative&a.negative?(x=this.neg().divmod(a.neg(),d),d!=="div"&&(S=x.mod.neg(),m&&S.negative!==0&&S.isub(a)),{div:x.div,mod:S}):a.length>this.length||this.cmp(a)<0?{div:new s(0),mod:this}:a.length===1?d==="div"?{div:this.divn(a.words[0]),mod:null}:d==="mod"?{div:null,mod:new s(this.modrn(a.words[0]))}:{div:this.divn(a.words[0]),mod:new s(this.modrn(a.words[0]))}:this._wordDiv(a,d)},s.prototype.div=function(a){return this.divmod(a,"div",!1).div},s.prototype.mod=function(a){return this.divmod(a,"mod",!1).mod},s.prototype.umod=function(a){return this.divmod(a,"mod",!0).mod},s.prototype.divRound=function(a){var d=this.divmod(a);if(d.mod.isZero())return d.div;var m=d.div.negative!==0?d.mod.isub(a):d.mod,v=a.ushrn(1),S=a.andln(1),x=m.cmp(v);return x<0||S===1&&x===0?d.div:d.div.negative!==0?d.div.isubn(1):d.div.iaddn(1)},s.prototype.modrn=function(a){var d=a<0;d&&(a=-a),n(a<=67108863);for(var m=(1<<26)%a,v=0,S=this.length-1;S>=0;S--)v=(m*v+(this.words[S]|0))%a;return d?-v:v},s.prototype.modn=function(a){return this.modrn(a)},s.prototype.idivn=function(a){var d=a<0;d&&(a=-a),n(a<=67108863);for(var m=0,v=this.length-1;v>=0;v--){var S=(this.words[v]|0)+m*67108864;this.words[v]=S/a|0,m=S%a}return this._strip(),d?this.ineg():this},s.prototype.divn=function(a){return this.clone().idivn(a)},s.prototype.egcd=function(a){n(a.negative===0),n(!a.isZero());var d=this,m=a.clone();d.negative!==0?d=d.umod(a):d=d.clone();for(var v=new s(1),S=new s(0),x=new s(0),B=new s(1),b=0;d.isEven()&&m.isEven();)d.iushrn(1),m.iushrn(1),++b;for(var h=m.clone(),R=d.clone();!d.isZero();){for(var K=0,J=1;!(d.words[0]&J)&&K<26;++K,J<<=1);if(K>0)for(d.iushrn(K);K-- >0;)(v.isOdd()||S.isOdd())&&(v.iadd(h),S.isub(R)),v.iushrn(1),S.iushrn(1);for(var T=0,F=1;!(m.words[0]&F)&&T<26;++T,F<<=1);if(T>0)for(m.iushrn(T);T-- >0;)(x.isOdd()||B.isOdd())&&(x.iadd(h),B.isub(R)),x.iushrn(1),B.iushrn(1);d.cmp(m)>=0?(d.isub(m),v.isub(x),S.isub(B)):(m.isub(d),x.isub(v),B.isub(S))}return{a:x,b:B,gcd:m.iushln(b)}},s.prototype._invmp=function(a){n(a.negative===0),n(!a.isZero());var d=this,m=a.clone();d.negative!==0?d=d.umod(a):d=d.clone();for(var v=new s(1),S=new s(0),x=m.clone();d.cmpn(1)>0&&m.cmpn(1)>0;){for(var B=0,b=1;!(d.words[0]&b)&&B<26;++B,b<<=1);if(B>0)for(d.iushrn(B);B-- >0;)v.isOdd()&&v.iadd(x),v.iushrn(1);for(var h=0,R=1;!(m.words[0]&R)&&h<26;++h,R<<=1);if(h>0)for(m.iushrn(h);h-- >0;)S.isOdd()&&S.iadd(x),S.iushrn(1);d.cmp(m)>=0?(d.isub(m),v.isub(S)):(m.isub(d),S.isub(v))}var K;return d.cmpn(1)===0?K=v:K=S,K.cmpn(0)<0&&K.iadd(a),K},s.prototype.gcd=function(a){if(this.isZero())return a.abs();if(a.isZero())return this.abs();var d=this.clone(),m=a.clone();d.negative=0,m.negative=0;for(var v=0;d.isEven()&&m.isEven();v++)d.iushrn(1),m.iushrn(1);do{for(;d.isEven();)d.iushrn(1);for(;m.isEven();)m.iushrn(1);var S=d.cmp(m);if(S<0){var x=d;d=m,m=x}else if(S===0||m.cmpn(1)===0)break;d.isub(m)}while(!0);return m.iushln(v)},s.prototype.invm=function(a){return this.egcd(a).a.umod(a)},s.prototype.isEven=function(){return(this.words[0]&1)===0},s.prototype.isOdd=function(){return(this.words[0]&1)===1},s.prototype.andln=function(a){return this.words[0]&a},s.prototype.bincn=function(a){n(typeof a=="number");var d=a%26,m=(a-d)/26,v=1<<d;if(this.length<=m)return this._expand(m+1),this.words[m]|=v,this;for(var S=v,x=m;S!==0&&x<this.length;x++){var B=this.words[x]|0;B+=S,S=B>>>26,B&=67108863,this.words[x]=B}return S!==0&&(this.words[x]=S,this.length++),this},s.prototype.isZero=function(){return this.length===1&&this.words[0]===0},s.prototype.cmpn=function(a){var d=a<0;if(this.negative!==0&&!d)return-1;if(this.negative===0&&d)return 1;this._strip();var m;if(this.length>1)m=1;else{d&&(a=-a),n(a<=67108863,"Number is too big");var v=this.words[0]|0;m=v===a?0:v<a?-1:1}return this.negative!==0?-m|0:m},s.prototype.cmp=function(a){if(this.negative!==0&&a.negative===0)return-1;if(this.negative===0&&a.negative!==0)return 1;var d=this.ucmp(a);return this.negative!==0?-d|0:d},s.prototype.ucmp=function(a){if(this.length>a.length)return 1;if(this.length<a.length)return-1;for(var d=0,m=this.length-1;m>=0;m--){var v=this.words[m]|0,S=a.words[m]|0;if(v!==S){v<S?d=-1:v>S&&(d=1);break}}return d},s.prototype.gtn=function(a){return this.cmpn(a)===1},s.prototype.gt=function(a){return this.cmp(a)===1},s.prototype.gten=function(a){return this.cmpn(a)>=0},s.prototype.gte=function(a){return this.cmp(a)>=0},s.prototype.ltn=function(a){return this.cmpn(a)===-1},s.prototype.lt=function(a){return this.cmp(a)===-1},s.prototype.lten=function(a){return this.cmpn(a)<=0},s.prototype.lte=function(a){return this.cmp(a)<=0},s.prototype.eqn=function(a){return this.cmpn(a)===0},s.prototype.eq=function(a){return this.cmp(a)===0},s.red=function(a){return new oe(a)},s.prototype.toRed=function(a){return n(!this.red,"Already a number in reduction context"),n(this.negative===0,"red works only with positives"),a.convertTo(this)._forceRed(a)},s.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},s.prototype._forceRed=function(a){return this.red=a,this},s.prototype.forceRed=function(a){return n(!this.red,"Already a number in reduction context"),this._forceRed(a)},s.prototype.redAdd=function(a){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,a)},s.prototype.redIAdd=function(a){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,a)},s.prototype.redSub=function(a){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,a)},s.prototype.redISub=function(a){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,a)},s.prototype.redShl=function(a){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,a)},s.prototype.redMul=function(a){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,a),this.red.mul(this,a)},s.prototype.redIMul=function(a){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,a),this.red.imul(this,a)},s.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},s.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},s.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},s.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},s.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},s.prototype.redPow=function(a){return n(this.red&&!a.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,a)};var U={k256:null,p224:null,p192:null,p25519:null};function z(k,a){this.name=k,this.p=new s(a,16),this.n=this.p.bitLength(),this.k=new s(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}z.prototype._tmp=function(){var a=new s(null);return a.words=new Array(Math.ceil(this.n/13)),a},z.prototype.ireduce=function(a){var d=a,m;do this.split(d,this.tmp),d=this.imulK(d),d=d.iadd(this.tmp),m=d.bitLength();while(m>this.n);var v=m<this.n?-1:d.ucmp(this.p);return v===0?(d.words[0]=0,d.length=1):v>0?d.isub(this.p):d.strip!==void 0?d.strip():d._strip(),d},z.prototype.split=function(a,d){a.iushrn(this.n,0,d)},z.prototype.imulK=function(a){return a.imul(this.k)};function te(){z.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}i(te,z),te.prototype.split=function(a,d){for(var m=4194303,v=Math.min(a.length,9),S=0;S<v;S++)d.words[S]=a.words[S];if(d.length=v,a.length<=9){a.words[0]=0,a.length=1;return}var x=a.words[9];for(d.words[d.length++]=x&m,S=10;S<a.length;S++){var B=a.words[S]|0;a.words[S-10]=(B&m)<<4|x>>>22,x=B}x>>>=22,a.words[S-10]=x,x===0&&a.length>10?a.length-=10:a.length-=9},te.prototype.imulK=function(a){a.words[a.length]=0,a.words[a.length+1]=0,a.length+=2;for(var d=0,m=0;m<a.length;m++){var v=a.words[m]|0;d+=v*977,a.words[m]=d&67108863,d=v*64+(d/67108864|0)}return a.words[a.length-1]===0&&(a.length--,a.words[a.length-1]===0&&a.length--),a};function X(){z.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}i(X,z);function Y(){z.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}i(Y,z);function de(){z.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}i(de,z),de.prototype.imulK=function(a){for(var d=0,m=0;m<a.length;m++){var v=(a.words[m]|0)*19+d,S=v&67108863;v>>>=26,a.words[m]=S,d=v}return d!==0&&(a.words[a.length++]=d),a},s._prime=function(a){if(U[a])return U[a];var d;if(a==="k256")d=new te;else if(a==="p224")d=new X;else if(a==="p192")d=new Y;else if(a==="p25519")d=new de;else throw new Error("Unknown prime "+a);return U[a]=d,d};function oe(k){if(typeof k=="string"){var a=s._prime(k);this.m=a.p,this.prime=a}else n(k.gtn(1),"modulus must be greater than 1"),this.m=k,this.prime=null}oe.prototype._verify1=function(a){n(a.negative===0,"red works only with positives"),n(a.red,"red works only with red numbers")},oe.prototype._verify2=function(a,d){n((a.negative|d.negative)===0,"red works only with positives"),n(a.red&&a.red===d.red,"red works only with red numbers")},oe.prototype.imod=function(a){return this.prime?this.prime.ireduce(a)._forceRed(this):(g(a,a.umod(this.m)._forceRed(this)),a)},oe.prototype.neg=function(a){return a.isZero()?a.clone():this.m.sub(a)._forceRed(this)},oe.prototype.add=function(a,d){this._verify2(a,d);var m=a.add(d);return m.cmp(this.m)>=0&&m.isub(this.m),m._forceRed(this)},oe.prototype.iadd=function(a,d){this._verify2(a,d);var m=a.iadd(d);return m.cmp(this.m)>=0&&m.isub(this.m),m},oe.prototype.sub=function(a,d){this._verify2(a,d);var m=a.sub(d);return m.cmpn(0)<0&&m.iadd(this.m),m._forceRed(this)},oe.prototype.isub=function(a,d){this._verify2(a,d);var m=a.isub(d);return m.cmpn(0)<0&&m.iadd(this.m),m},oe.prototype.shl=function(a,d){return this._verify1(a),this.imod(a.ushln(d))},oe.prototype.imul=function(a,d){return this._verify2(a,d),this.imod(a.imul(d))},oe.prototype.mul=function(a,d){return this._verify2(a,d),this.imod(a.mul(d))},oe.prototype.isqr=function(a){return this.imul(a,a.clone())},oe.prototype.sqr=function(a){return this.mul(a,a)},oe.prototype.sqrt=function(a){if(a.isZero())return a.clone();var d=this.m.andln(3);if(n(d%2===1),d===3){var m=this.m.add(new s(1)).iushrn(2);return this.pow(a,m)}for(var v=this.m.subn(1),S=0;!v.isZero()&&v.andln(1)===0;)S++,v.iushrn(1);n(!v.isZero());var x=new s(1).toRed(this),B=x.redNeg(),b=this.m.subn(1).iushrn(1),h=this.m.bitLength();for(h=new s(2*h*h).toRed(this);this.pow(h,b).cmp(B)!==0;)h.redIAdd(B);for(var R=this.pow(h,v),K=this.pow(a,v.addn(1).iushrn(1)),J=this.pow(a,v),T=S;J.cmp(x)!==0;){for(var F=J,q=0;F.cmp(x)!==0;q++)F=F.redSqr();n(q<T);var Z=this.pow(R,new s(1).iushln(T-q-1));K=K.redMul(Z),R=Z.redSqr(),J=J.redMul(R),T=q}return K},oe.prototype.invm=function(a){var d=a._invmp(this.m);return d.negative!==0?(d.negative=0,this.imod(d).redNeg()):this.imod(d)},oe.prototype.pow=function(a,d){if(d.isZero())return new s(1).toRed(this);if(d.cmpn(1)===0)return a.clone();var m=4,v=new Array(1<<m);v[0]=new s(1).toRed(this),v[1]=a;for(var S=2;S<v.length;S++)v[S]=this.mul(v[S-1],a);var x=v[0],B=0,b=0,h=d.bitLength()%26;for(h===0&&(h=26),S=d.length-1;S>=0;S--){for(var R=d.words[S],K=h-1;K>=0;K--){var J=R>>K&1;if(x!==v[0]&&(x=this.sqr(x)),J===0&&B===0){b=0;continue}B<<=1,B|=J,b++,!(b!==m&&(S!==0||K!==0))&&(x=this.mul(x,v[B]),b=0,B=0)}h=26}return x},oe.prototype.convertTo=function(a){var d=a.umod(this.m);return d===a?d.clone():d},oe.prototype.convertFrom=function(a){var d=a.clone();return d.red=null,d},s.mont=function(a){return new pe(a)};function pe(k){oe.call(this,k),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new s(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}i(pe,oe),pe.prototype.convertTo=function(a){return this.imod(a.ushln(this.shift))},pe.prototype.convertFrom=function(a){var d=this.imod(a.mul(this.rinv));return d.red=null,d},pe.prototype.imul=function(a,d){if(a.isZero()||d.isZero())return a.words[0]=0,a.length=1,a;var m=a.imul(d),v=m.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),S=m.isub(v).iushrn(this.shift),x=S;return S.cmp(this.m)>=0?x=S.isub(this.m):S.cmpn(0)<0&&(x=S.iadd(this.m)),x._forceRed(this)},pe.prototype.mul=function(a,d){if(a.isZero()||d.isZero())return new s(0)._forceRed(this);var m=a.mul(d),v=m.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),S=m.isub(v).iushrn(this.shift),x=S;return S.cmp(this.m)>=0?x=S.isub(this.m):S.cmpn(0)<0&&(x=S.iadd(this.m)),x._forceRed(this)},pe.prototype.invm=function(a){var d=this.imod(a._invmp(this.m).mul(this.r2));return d._forceRed(this)}})(e,ee)})(xo);var Mi=xo.exports,En={},Dt={};Object.defineProperty(Dt,"__esModule",{value:!0});Dt.errorValues=Dt.standardErrorCodes=void 0;Dt.standardErrorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901,unsupportedChain:4902}};Dt.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."},4902:{standard:"EIP-3085",message:"Unrecognized chain ID."}};var Ii={},Ai={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.serialize=e.getErrorCode=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const t=Dt,r="Unspecified error message.";e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.";function n(_,L=r){if(_&&Number.isInteger(_)){const j=_.toString();if(g(t.errorValues,j))return t.errorValues[j].message;if(u(_))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return L}e.getMessageFromCode=n;function i(_){if(!Number.isInteger(_))return!1;const L=_.toString();return!!(t.errorValues[L]||u(_))}e.isValidCode=i;function s(_){var L;if(typeof _=="number")return _;if(c(_))return(L=_.code)!==null&&L!==void 0?L:_.errorCode}e.getErrorCode=s;function c(_){return typeof _=="object"&&_!==null&&(typeof _.code=="number"||typeof _.errorCode=="number")}function o(_,{shouldIncludeStack:L=!1}={}){const j={};if(_&&typeof _=="object"&&!Array.isArray(_)&&g(_,"code")&&i(_.code)){const $=_;j.code=$.code,$.message&&typeof $.message=="string"?(j.message=$.message,g($,"data")&&(j.data=$.data)):(j.message=n(j.code),j.data={originalError:p(_)})}else j.code=t.standardErrorCodes.rpc.internal,j.message=w(_,"message")?_.message:r,j.data={originalError:p(_)};return L&&(j.stack=w(_,"stack")?_.stack:void 0),j}e.serialize=o;function u(_){return _>=-32099&&_<=-32e3}function p(_){return _&&typeof _=="object"&&!Array.isArray(_)?Object.assign({},_):_}function g(_,L){return Object.prototype.hasOwnProperty.call(_,L)}function w(_,L){return typeof _=="object"&&_!==null&&L in _&&typeof _[L]=="string"}})(Ai);Object.defineProperty(Ii,"__esModule",{value:!0});Ii.standardErrors=void 0;const rt=Dt,el=Ai;Ii.standardErrors={rpc:{parse:e=>pt(rt.standardErrorCodes.rpc.parse,e),invalidRequest:e=>pt(rt.standardErrorCodes.rpc.invalidRequest,e),invalidParams:e=>pt(rt.standardErrorCodes.rpc.invalidParams,e),methodNotFound:e=>pt(rt.standardErrorCodes.rpc.methodNotFound,e),internal:e=>pt(rt.standardErrorCodes.rpc.internal,e),server:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return pt(t,e)},invalidInput:e=>pt(rt.standardErrorCodes.rpc.invalidInput,e),resourceNotFound:e=>pt(rt.standardErrorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>pt(rt.standardErrorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>pt(rt.standardErrorCodes.rpc.transactionRejected,e),methodNotSupported:e=>pt(rt.standardErrorCodes.rpc.methodNotSupported,e),limitExceeded:e=>pt(rt.standardErrorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>Br(rt.standardErrorCodes.provider.userRejectedRequest,e),unauthorized:e=>Br(rt.standardErrorCodes.provider.unauthorized,e),unsupportedMethod:e=>Br(rt.standardErrorCodes.provider.unsupportedMethod,e),disconnected:e=>Br(rt.standardErrorCodes.provider.disconnected,e),chainDisconnected:e=>Br(rt.standardErrorCodes.provider.chainDisconnected,e),unsupportedChain:e=>Br(rt.standardErrorCodes.provider.unsupportedChain,e),custom:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:r,data:n}=e;if(!r||typeof r!="string")throw new Error('"message" must be a nonempty string');return new nl(t,r,n)}}};function pt(e,t){const[r,n]=tl(t);return new rl(e,r||(0,el.getMessageFromCode)(e),n)}function Br(e,t){const[r,n]=tl(t);return new nl(e,r||(0,el.getMessageFromCode)(e),n)}function tl(e){if(e){if(typeof e=="string")return[e];if(typeof e=="object"&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&typeof t!="string")throw new Error("Must specify string message.");return[t||void 0,r]}}return[]}let rl=class extends Error{constructor(t,r,n){if(!Number.isInteger(t))throw new Error('"code" must be an integer.');if(!r||typeof r!="string")throw new Error('"message" must be a nonempty string.');super(r),this.code=t,n!==void 0&&(this.data=n)}},nl=class extends rl{constructor(t,r,n){if(!_h(t))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(t,r,n)}};function _h(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}var xi={},nn={};Object.defineProperty(nn,"__esModule",{value:!0});nn.isErrorResponse=void 0;function Eh(e){return e.errorMessage!==void 0}nn.isErrorResponse=Eh;var sn={};Object.defineProperty(sn,"__esModule",{value:!0});sn.LIB_VERSION=void 0;sn.LIB_VERSION="3.9.3";Object.defineProperty(xi,"__esModule",{value:!0});xi.serializeError=void 0;const Sh=nn,Rh=sn,Mh=Dt,Ih=Ai;function Ah(e,t){const r=(0,Ih.serialize)(xh(e),{shouldIncludeStack:!0}),n=new URL("https://docs.cloud.coinbase.com/wallet-sdk/docs/errors");n.searchParams.set("version",Rh.LIB_VERSION),n.searchParams.set("code",r.code.toString());const i=kh(r.data,t);return i&&n.searchParams.set("method",i),n.searchParams.set("message",r.message),Object.assign(Object.assign({},r),{docUrl:n.href})}xi.serializeError=Ah;function xh(e){return typeof e=="string"?{message:e,code:Mh.standardErrorCodes.rpc.internal}:(0,Sh.isErrorResponse)(e)?Object.assign(Object.assign({},e),{message:e.errorMessage,code:e.errorCode,data:{method:e.method}}):e}function kh(e,t){const r=e==null?void 0:e.method;if(r)return r;if(t!==void 0){if(typeof t=="string")return t;if(Array.isArray(t)){if(t.length>0)return t[0].method}else return t.method}}(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.standardErrors=e.standardErrorCodes=e.serializeError=e.getMessageFromCode=e.getErrorCode=void 0;const t=Dt;Object.defineProperty(e,"standardErrorCodes",{enumerable:!0,get:function(){return t.standardErrorCodes}});const r=Ii;Object.defineProperty(e,"standardErrors",{enumerable:!0,get:function(){return r.standardErrors}});const n=xi;Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return n.serializeError}});const i=Ai;Object.defineProperty(e,"getErrorCode",{enumerable:!0,get:function(){return i.getErrorCode}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return i.getMessageFromCode}})})(En);var Ke={};Object.defineProperty(Ke,"__esModule",{value:!0});Ke.ProviderType=Ke.RegExpString=Ke.IntNumber=Ke.BigIntString=Ke.AddressString=Ke.HexString=Ke.OpaqueType=void 0;function Sn(){return e=>e}Ke.OpaqueType=Sn;Ke.HexString=Sn();Ke.AddressString=Sn();Ke.BigIntString=Sn();function Ch(e){return Math.floor(e)}Ke.IntNumber=Ch;Ke.RegExpString=Sn();var ha;(function(e){e.CoinbaseWallet="CoinbaseWallet",e.MetaMask="MetaMask",e.Unselected=""})(ha||(Ke.ProviderType=ha={}));var Th=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Q,"__esModule",{value:!0});Q.isMobileWeb=Q.getLocation=Q.isInIFrame=Q.createQrUrl=Q.getFavicon=Q.range=Q.isBigNumber=Q.ensureParsedJSONObject=Q.ensureBN=Q.ensureRegExpString=Q.ensureIntNumber=Q.ensureBuffer=Q.ensureAddressString=Q.ensureEvenLengthHexString=Q.ensureHexString=Q.isHexString=Q.prepend0x=Q.strip0x=Q.has0xPrefix=Q.hexStringFromIntNumber=Q.intNumberFromHexString=Q.bigIntStringFromBN=Q.hexStringFromBuffer=Q.hexStringToUint8Array=Q.uint8ArrayToHex=Q.randomBytesHex=void 0;const qt=Th(Mi),wr=En,bt=Ke,il=/^[0-9]*$/,sl=/^[a-f0-9]*$/;function Bh(e){return ol(crypto.getRandomValues(new Uint8Array(e)))}Q.randomBytesHex=Bh;function ol(e){return[...e].map(t=>t.toString(16).padStart(2,"0")).join("")}Q.uint8ArrayToHex=ol;function Nh(e){return new Uint8Array(e.match(/.{1,2}/g).map(t=>parseInt(t,16)))}Q.hexStringToUint8Array=Nh;function Ph(e,t=!1){const r=e.toString("hex");return(0,bt.HexString)(t?`0x${r}`:r)}Q.hexStringFromBuffer=Ph;function Lh(e){return(0,bt.BigIntString)(e.toString(10))}Q.bigIntStringFromBN=Lh;function Oh(e){return(0,bt.IntNumber)(new qt.default(Mn(e,!1),16).toNumber())}Q.intNumberFromHexString=Oh;function $h(e){return(0,bt.HexString)(`0x${new qt.default(e).toString(16)}`)}Q.hexStringFromIntNumber=$h;function Co(e){return e.startsWith("0x")||e.startsWith("0X")}Q.has0xPrefix=Co;function ki(e){return Co(e)?e.slice(2):e}Q.strip0x=ki;function al(e){return Co(e)?`0x${e.slice(2)}`:`0x${e}`}Q.prepend0x=al;function Rn(e){if(typeof e!="string")return!1;const t=ki(e).toLowerCase();return sl.test(t)}Q.isHexString=Rn;function cl(e,t=!1){if(typeof e=="string"){const r=ki(e).toLowerCase();if(sl.test(r))return(0,bt.HexString)(t?`0x${r}`:r)}throw wr.standardErrors.rpc.invalidParams(`"${String(e)}" is not a hexadecimal string`)}Q.ensureHexString=cl;function Mn(e,t=!1){let r=cl(e,!1);return r.length%2===1&&(r=(0,bt.HexString)(`0${r}`)),t?(0,bt.HexString)(`0x${r}`):r}Q.ensureEvenLengthHexString=Mn;function Fh(e){if(typeof e=="string"){const t=ki(e).toLowerCase();if(Rn(t)&&t.length===40)return(0,bt.AddressString)(al(t))}throw wr.standardErrors.rpc.invalidParams(`Invalid Ethereum address: ${String(e)}`)}Q.ensureAddressString=Fh;function Dh(e){if(Buffer.isBuffer(e))return e;if(typeof e=="string"){if(Rn(e)){const t=Mn(e,!1);return Buffer.from(t,"hex")}return Buffer.from(e,"utf8")}throw wr.standardErrors.rpc.invalidParams(`Not binary data: ${String(e)}`)}Q.ensureBuffer=Dh;function ll(e){if(typeof e=="number"&&Number.isInteger(e))return(0,bt.IntNumber)(e);if(typeof e=="string"){if(il.test(e))return(0,bt.IntNumber)(Number(e));if(Rn(e))return(0,bt.IntNumber)(new qt.default(Mn(e,!1),16).toNumber())}throw wr.standardErrors.rpc.invalidParams(`Not an integer: ${String(e)}`)}Q.ensureIntNumber=ll;function jh(e){if(e instanceof RegExp)return(0,bt.RegExpString)(e.toString());throw wr.standardErrors.rpc.invalidParams(`Not a RegExp: ${String(e)}`)}Q.ensureRegExpString=jh;function Uh(e){if(e!==null&&(qt.default.isBN(e)||ul(e)))return new qt.default(e.toString(10),10);if(typeof e=="number")return new qt.default(ll(e));if(typeof e=="string"){if(il.test(e))return new qt.default(e,10);if(Rn(e))return new qt.default(Mn(e,!1),16)}throw wr.standardErrors.rpc.invalidParams(`Not an integer: ${String(e)}`)}Q.ensureBN=Uh;function Hh(e){if(typeof e=="string")return JSON.parse(e);if(typeof e=="object")return e;throw wr.standardErrors.rpc.invalidParams(`Not a JSON string or an object: ${String(e)}`)}Q.ensureParsedJSONObject=Hh;function ul(e){if(e==null||typeof e.constructor!="function")return!1;const{constructor:t}=e;return typeof t.config=="function"&&typeof t.EUCLID=="number"}Q.isBigNumber=ul;function Wh(e,t){return Array.from({length:t-e},(r,n)=>e+n)}Q.range=Wh;function qh(){const e=document.querySelector('link[sizes="192x192"]')||document.querySelector('link[sizes="180x180"]')||document.querySelector('link[rel="icon"]')||document.querySelector('link[rel="shortcut icon"]'),{protocol:t,host:r}=document.location,n=e?e.getAttribute("href"):null;return!n||n.startsWith("javascript:")||n.startsWith("vbscript:")?null:n.startsWith("http://")||n.startsWith("https://")||n.startsWith("data:")?n:n.startsWith("//")?t+n:`${t}//${r}${n}`}Q.getFavicon=qh;function Vh(e,t,r,n,i,s){const c=n?"parent-id":"id",o=new URLSearchParams({[c]:e,secret:t,server:r,v:i,chainId:s.toString()}).toString();return`${r}/#/link?${o}`}Q.createQrUrl=Vh;function hl(){try{return window.frameElement!==null}catch{return!1}}Q.isInIFrame=hl;function zh(){try{return hl()&&window.top?window.top.location:window.location}catch{return window.location}}Q.getLocation=zh;function Gh(){var e;return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test((e=window==null?void 0:window.navigator)===null||e===void 0?void 0:e.userAgent)}Q.isMobileWeb=Gh;var Ci={};Object.defineProperty(Ci,"__esModule",{value:!0});Ci.ScopedLocalStorage=void 0;class Jh{constructor(t){this.scope=t}setItem(t,r){localStorage.setItem(this.scopedKey(t),r)}getItem(t){return localStorage.getItem(this.scopedKey(t))}removeItem(t){localStorage.removeItem(this.scopedKey(t))}clear(){const t=this.scopedKey(""),r=[];for(let n=0;n<localStorage.length;n++){const i=localStorage.key(n);typeof i=="string"&&i.startsWith(t)&&r.push(i)}r.forEach(n=>localStorage.removeItem(n))}scopedKey(t){return`${this.scope}:${t}`}}Ci.ScopedLocalStorage=Jh;var Jr={},fl={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(u,p,g){this.fn=u,this.context=p,this.once=g||!1}function s(u,p,g,w,_){if(typeof g!="function")throw new TypeError("The listener must be a function");var L=new i(g,w||u,_),j=r?r+p:p;return u._events[j]?u._events[j].fn?u._events[j]=[u._events[j],L]:u._events[j].push(L):(u._events[j]=L,u._eventsCount++),u}function c(u,p){--u._eventsCount===0?u._events=new n:delete u._events[p]}function o(){this._events=new n,this._eventsCount=0}o.prototype.eventNames=function(){var p=[],g,w;if(this._eventsCount===0)return p;for(w in g=this._events)t.call(g,w)&&p.push(r?w.slice(1):w);return Object.getOwnPropertySymbols?p.concat(Object.getOwnPropertySymbols(g)):p},o.prototype.listeners=function(p){var g=r?r+p:p,w=this._events[g];if(!w)return[];if(w.fn)return[w.fn];for(var _=0,L=w.length,j=new Array(L);_<L;_++)j[_]=w[_].fn;return j},o.prototype.listenerCount=function(p){var g=r?r+p:p,w=this._events[g];return w?w.fn?1:w.length:0},o.prototype.emit=function(p,g,w,_,L,j){var $=r?r+p:p;if(!this._events[$])return!1;var O=this._events[$],A=arguments.length,N,C;if(O.fn){switch(O.once&&this.removeListener(p,O.fn,void 0,!0),A){case 1:return O.fn.call(O.context),!0;case 2:return O.fn.call(O.context,g),!0;case 3:return O.fn.call(O.context,g,w),!0;case 4:return O.fn.call(O.context,g,w,_),!0;case 5:return O.fn.call(O.context,g,w,_,L),!0;case 6:return O.fn.call(O.context,g,w,_,L,j),!0}for(C=1,N=new Array(A-1);C<A;C++)N[C-1]=arguments[C];O.fn.apply(O.context,N)}else{var W=O.length,U;for(C=0;C<W;C++)switch(O[C].once&&this.removeListener(p,O[C].fn,void 0,!0),A){case 1:O[C].fn.call(O[C].context);break;case 2:O[C].fn.call(O[C].context,g);break;case 3:O[C].fn.call(O[C].context,g,w);break;case 4:O[C].fn.call(O[C].context,g,w,_);break;default:if(!N)for(U=1,N=new Array(A-1);U<A;U++)N[U-1]=arguments[U];O[C].fn.apply(O[C].context,N)}}return!0},o.prototype.on=function(p,g,w){return s(this,p,g,w,!1)},o.prototype.once=function(p,g,w){return s(this,p,g,w,!0)},o.prototype.removeListener=function(p,g,w,_){var L=r?r+p:p;if(!this._events[L])return this;if(!g)return c(this,L),this;var j=this._events[L];if(j.fn)j.fn===g&&(!_||j.once)&&(!w||j.context===w)&&c(this,L);else{for(var $=0,O=[],A=j.length;$<A;$++)(j[$].fn!==g||_&&!j[$].once||w&&j[$].context!==w)&&O.push(j[$]);O.length?this._events[L]=O.length===1?O[0]:O:c(this,L)}return this},o.prototype.removeAllListeners=function(p){var g;return p?(g=r?r+p:p,this._events[g]&&c(this,g)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o})(fl);var Zh=fl.exports,In={},An={},on={};Object.defineProperty(on,"__esModule",{value:!0});on.EVENTS=void 0;on.EVENTS={STARTED_CONNECTING:"walletlink_sdk.started.connecting",CONNECTED_STATE_CHANGE:"walletlink_sdk.connected",DISCONNECTED:"walletlink_sdk.disconnected",METADATA_DESTROYED:"walletlink_sdk_metadata_destroyed",LINKED:"walletlink_sdk.linked",FAILURE:"walletlink_sdk.generic_failure",SESSION_CONFIG_RECEIVED:"walletlink_sdk.session_config_event_received",ETH_ACCOUNTS_STATE:"walletlink_sdk.eth_accounts_state",SESSION_STATE_CHANGE:"walletlink_sdk.session_state_change",UNLINKED_ERROR_STATE:"walletlink_sdk.unlinked_error_state",SKIPPED_CLEARING_SESSION:"walletlink_sdk.skipped_clearing_session",GENERAL_ERROR:"walletlink_sdk.general_error",WEB3_REQUEST:"walletlink_sdk.web3.request",WEB3_REQUEST_PUBLISHED:"walletlink_sdk.web3.request_published",WEB3_RESPONSE:"walletlink_sdk.web3.response",METHOD_NOT_IMPLEMENTED:"walletlink_sdk.method_not_implemented",UNKNOWN_ADDRESS_ENCOUNTERED:"walletlink_sdk.unknown_address_encountered"};var vt={};Object.defineProperty(vt,"__esModule",{value:!0});vt.RelayAbstract=vt.APP_VERSION_KEY=vt.LOCAL_STORAGE_ADDRESSES_KEY=vt.WALLET_USER_NAME_KEY=void 0;const fa=En;vt.WALLET_USER_NAME_KEY="walletUsername";vt.LOCAL_STORAGE_ADDRESSES_KEY="Addresses";vt.APP_VERSION_KEY="AppVersion";class Kh{async makeEthereumJSONRPCRequest(t,r){if(!r)throw new Error("Error: No jsonRpcUrl provided");return window.fetch(r,{method:"POST",body:JSON.stringify(t),mode:"cors",headers:{"Content-Type":"application/json"}}).then(n=>n.json()).then(n=>{if(!n)throw fa.standardErrors.rpc.parse({});const i=n,{error:s}=i;if(s)throw(0,fa.serializeError)(s,t.method);return i})}}vt.RelayAbstract=Kh;var an={},dl={exports:{}},bo={exports:{}};typeof Object.create=="function"?bo.exports=function(t,r){r&&(t.super_=r,t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:bo.exports=function(t,r){if(r){t.super_=r;var n=function(){};n.prototype=r.prototype,t.prototype=new n,t.prototype.constructor=t}};var Rt=bo.exports,wo={exports:{}},xn={},Ti={};Ti.byteLength=Xh;Ti.toByteArray=tf;Ti.fromByteArray=sf;var Bt=[],mt=[],Yh=typeof Uint8Array<"u"?Uint8Array:Array,Rs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var Nr=0,Qh=Rs.length;Nr<Qh;++Nr)Bt[Nr]=Rs[Nr],mt[Rs.charCodeAt(Nr)]=Nr;mt[45]=62;mt[95]=63;function pl(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");r===-1&&(r=t);var n=r===t?0:4-r%4;return[r,n]}function Xh(e){var t=pl(e),r=t[0],n=t[1];return(r+n)*3/4-n}function ef(e,t,r){return(t+r)*3/4-r}function tf(e){var t,r=pl(e),n=r[0],i=r[1],s=new Yh(ef(e,n,i)),c=0,o=i>0?n-4:n,u;for(u=0;u<o;u+=4)t=mt[e.charCodeAt(u)]<<18|mt[e.charCodeAt(u+1)]<<12|mt[e.charCodeAt(u+2)]<<6|mt[e.charCodeAt(u+3)],s[c++]=t>>16&255,s[c++]=t>>8&255,s[c++]=t&255;return i===2&&(t=mt[e.charCodeAt(u)]<<2|mt[e.charCodeAt(u+1)]>>4,s[c++]=t&255),i===1&&(t=mt[e.charCodeAt(u)]<<10|mt[e.charCodeAt(u+1)]<<4|mt[e.charCodeAt(u+2)]>>2,s[c++]=t>>8&255,s[c++]=t&255),s}function rf(e){return Bt[e>>18&63]+Bt[e>>12&63]+Bt[e>>6&63]+Bt[e&63]}function nf(e,t,r){for(var n,i=[],s=t;s<r;s+=3)n=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(e[s+2]&255),i.push(rf(n));return i.join("")}function sf(e){for(var t,r=e.length,n=r%3,i=[],s=16383,c=0,o=r-n;c<o;c+=s)i.push(nf(e,c,c+s>o?o:c+s));return n===1?(t=e[r-1],i.push(Bt[t>>2]+Bt[t<<4&63]+"==")):n===2&&(t=(e[r-2]<<8)+e[r-1],i.push(Bt[t>>10]+Bt[t>>4&63]+Bt[t<<2&63]+"=")),i.join("")}var To={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */To.read=function(e,t,r,n,i){var s,c,o=i*8-n-1,u=(1<<o)-1,p=u>>1,g=-7,w=r?i-1:0,_=r?-1:1,L=e[t+w];for(w+=_,s=L&(1<<-g)-1,L>>=-g,g+=o;g>0;s=s*256+e[t+w],w+=_,g-=8);for(c=s&(1<<-g)-1,s>>=-g,g+=n;g>0;c=c*256+e[t+w],w+=_,g-=8);if(s===0)s=1-p;else{if(s===u)return c?NaN:(L?-1:1)*(1/0);c=c+Math.pow(2,n),s=s-p}return(L?-1:1)*c*Math.pow(2,s-n)};To.write=function(e,t,r,n,i,s){var c,o,u,p=s*8-i-1,g=(1<<p)-1,w=g>>1,_=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,L=n?0:s-1,j=n?1:-1,$=t<0||t===0&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(o=isNaN(t)?1:0,c=g):(c=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-c))<1&&(c--,u*=2),c+w>=1?t+=_/u:t+=_*Math.pow(2,1-w),t*u>=2&&(c++,u/=2),c+w>=g?(o=0,c=g):c+w>=1?(o=(t*u-1)*Math.pow(2,i),c=c+w):(o=t*Math.pow(2,w-1)*Math.pow(2,i),c=0));i>=8;e[r+L]=o&255,L+=j,o/=256,i-=8);for(c=c<<i|o,p+=i;p>0;e[r+L]=c&255,L+=j,c/=256,p-=8);e[r+L-j]|=$*128};/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */(function(e){const t=Ti,r=To,n=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=o,e.SlowBuffer=N,e.INSPECT_MAX_BYTES=50;const i=**********;e.kMaxLength=i,o.TYPED_ARRAY_SUPPORT=s(),!o.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function s(){try{const y=new Uint8Array(1),l={foo:function(){return 42}};return Object.setPrototypeOf(l,Uint8Array.prototype),Object.setPrototypeOf(y,l),y.foo()===42}catch{return!1}}Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.buffer}}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.byteOffset}});function c(y){if(y>i)throw new RangeError('The value "'+y+'" is invalid for option "size"');const l=new Uint8Array(y);return Object.setPrototypeOf(l,o.prototype),l}function o(y,l,f){if(typeof y=="number"){if(typeof l=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return w(y)}return u(y,l,f)}o.poolSize=8192;function u(y,l,f){if(typeof y=="string")return _(y,l);if(ArrayBuffer.isView(y))return j(y);if(y==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof y);if(Ze(y,ArrayBuffer)||y&&Ze(y.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Ze(y,SharedArrayBuffer)||y&&Ze(y.buffer,SharedArrayBuffer)))return $(y,l,f);if(typeof y=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const E=y.valueOf&&y.valueOf();if(E!=null&&E!==y)return o.from(E,l,f);const P=O(y);if(P)return P;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof y[Symbol.toPrimitive]=="function")return o.from(y[Symbol.toPrimitive]("string"),l,f);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof y)}o.from=function(y,l,f){return u(y,l,f)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array);function p(y){if(typeof y!="number")throw new TypeError('"size" argument must be of type number');if(y<0)throw new RangeError('The value "'+y+'" is invalid for option "size"')}function g(y,l,f){return p(y),y<=0?c(y):l!==void 0?typeof f=="string"?c(y).fill(l,f):c(y).fill(l):c(y)}o.alloc=function(y,l,f){return g(y,l,f)};function w(y){return p(y),c(y<0?0:A(y)|0)}o.allocUnsafe=function(y){return w(y)},o.allocUnsafeSlow=function(y){return w(y)};function _(y,l){if((typeof l!="string"||l==="")&&(l="utf8"),!o.isEncoding(l))throw new TypeError("Unknown encoding: "+l);const f=C(y,l)|0;let E=c(f);const P=E.write(y,l);return P!==f&&(E=E.slice(0,P)),E}function L(y){const l=y.length<0?0:A(y.length)|0,f=c(l);for(let E=0;E<l;E+=1)f[E]=y[E]&255;return f}function j(y){if(Ze(y,Uint8Array)){const l=new Uint8Array(y);return $(l.buffer,l.byteOffset,l.byteLength)}return L(y)}function $(y,l,f){if(l<0||y.byteLength<l)throw new RangeError('"offset" is outside of buffer bounds');if(y.byteLength<l+(f||0))throw new RangeError('"length" is outside of buffer bounds');let E;return l===void 0&&f===void 0?E=new Uint8Array(y):f===void 0?E=new Uint8Array(y,l):E=new Uint8Array(y,l,f),Object.setPrototypeOf(E,o.prototype),E}function O(y){if(o.isBuffer(y)){const l=A(y.length)|0,f=c(l);return f.length===0||y.copy(f,0,0,l),f}if(y.length!==void 0)return typeof y.length!="number"||_e(y.length)?c(0):L(y);if(y.type==="Buffer"&&Array.isArray(y.data))return L(y.data)}function A(y){if(y>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return y|0}function N(y){return+y!=y&&(y=0),o.alloc(+y)}o.isBuffer=function(l){return l!=null&&l._isBuffer===!0&&l!==o.prototype},o.compare=function(l,f){if(Ze(l,Uint8Array)&&(l=o.from(l,l.offset,l.byteLength)),Ze(f,Uint8Array)&&(f=o.from(f,f.offset,f.byteLength)),!o.isBuffer(l)||!o.isBuffer(f))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(l===f)return 0;let E=l.length,P=f.length;for(let D=0,H=Math.min(E,P);D<H;++D)if(l[D]!==f[D]){E=l[D],P=f[D];break}return E<P?-1:P<E?1:0},o.isEncoding=function(l){switch(String(l).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(l,f){if(!Array.isArray(l))throw new TypeError('"list" argument must be an Array of Buffers');if(l.length===0)return o.alloc(0);let E;if(f===void 0)for(f=0,E=0;E<l.length;++E)f+=l[E].length;const P=o.allocUnsafe(f);let D=0;for(E=0;E<l.length;++E){let H=l[E];if(Ze(H,Uint8Array))D+H.length>P.length?(o.isBuffer(H)||(H=o.from(H)),H.copy(P,D)):Uint8Array.prototype.set.call(P,H,D);else if(o.isBuffer(H))H.copy(P,D);else throw new TypeError('"list" argument must be an Array of Buffers');D+=H.length}return P};function C(y,l){if(o.isBuffer(y))return y.length;if(ArrayBuffer.isView(y)||Ze(y,ArrayBuffer))return y.byteLength;if(typeof y!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof y);const f=y.length,E=arguments.length>2&&arguments[2]===!0;if(!E&&f===0)return 0;let P=!1;for(;;)switch(l){case"ascii":case"latin1":case"binary":return f;case"utf8":case"utf-8":return re(y).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return f*2;case"hex":return f>>>1;case"base64":return Re(y).length;default:if(P)return E?-1:re(y).length;l=(""+l).toLowerCase(),P=!0}}o.byteLength=C;function W(y,l,f){let E=!1;if((l===void 0||l<0)&&(l=0),l>this.length||((f===void 0||f>this.length)&&(f=this.length),f<=0)||(f>>>=0,l>>>=0,f<=l))return"";for(y||(y="utf8");;)switch(y){case"hex":return x(this,l,f);case"utf8":case"utf-8":return a(this,l,f);case"ascii":return v(this,l,f);case"latin1":case"binary":return S(this,l,f);case"base64":return k(this,l,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,l,f);default:if(E)throw new TypeError("Unknown encoding: "+y);y=(y+"").toLowerCase(),E=!0}}o.prototype._isBuffer=!0;function U(y,l,f){const E=y[l];y[l]=y[f],y[f]=E}o.prototype.swap16=function(){const l=this.length;if(l%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let f=0;f<l;f+=2)U(this,f,f+1);return this},o.prototype.swap32=function(){const l=this.length;if(l%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let f=0;f<l;f+=4)U(this,f,f+3),U(this,f+1,f+2);return this},o.prototype.swap64=function(){const l=this.length;if(l%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let f=0;f<l;f+=8)U(this,f,f+7),U(this,f+1,f+6),U(this,f+2,f+5),U(this,f+3,f+4);return this},o.prototype.toString=function(){const l=this.length;return l===0?"":arguments.length===0?a(this,0,l):W.apply(this,arguments)},o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=function(l){if(!o.isBuffer(l))throw new TypeError("Argument must be a Buffer");return this===l?!0:o.compare(this,l)===0},o.prototype.inspect=function(){let l="";const f=e.INSPECT_MAX_BYTES;return l=this.toString("hex",0,f).replace(/(.{2})/g,"$1 ").trim(),this.length>f&&(l+=" ... "),"<Buffer "+l+">"},n&&(o.prototype[n]=o.prototype.inspect),o.prototype.compare=function(l,f,E,P,D){if(Ze(l,Uint8Array)&&(l=o.from(l,l.offset,l.byteLength)),!o.isBuffer(l))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof l);if(f===void 0&&(f=0),E===void 0&&(E=l?l.length:0),P===void 0&&(P=0),D===void 0&&(D=this.length),f<0||E>l.length||P<0||D>this.length)throw new RangeError("out of range index");if(P>=D&&f>=E)return 0;if(P>=D)return-1;if(f>=E)return 1;if(f>>>=0,E>>>=0,P>>>=0,D>>>=0,this===l)return 0;let H=D-P,he=E-f;const ae=Math.min(H,he),ie=this.slice(P,D),Ie=l.slice(f,E);for(let ne=0;ne<ae;++ne)if(ie[ne]!==Ie[ne]){H=ie[ne],he=Ie[ne];break}return H<he?-1:he<H?1:0};function z(y,l,f,E,P){if(y.length===0)return-1;if(typeof f=="string"?(E=f,f=0):f>**********?f=**********:f<-2147483648&&(f=-2147483648),f=+f,_e(f)&&(f=P?0:y.length-1),f<0&&(f=y.length+f),f>=y.length){if(P)return-1;f=y.length-1}else if(f<0)if(P)f=0;else return-1;if(typeof l=="string"&&(l=o.from(l,E)),o.isBuffer(l))return l.length===0?-1:te(y,l,f,E,P);if(typeof l=="number")return l=l&255,typeof Uint8Array.prototype.indexOf=="function"?P?Uint8Array.prototype.indexOf.call(y,l,f):Uint8Array.prototype.lastIndexOf.call(y,l,f):te(y,[l],f,E,P);throw new TypeError("val must be string, number or Buffer")}function te(y,l,f,E,P){let D=1,H=y.length,he=l.length;if(E!==void 0&&(E=String(E).toLowerCase(),E==="ucs2"||E==="ucs-2"||E==="utf16le"||E==="utf-16le")){if(y.length<2||l.length<2)return-1;D=2,H/=2,he/=2,f/=2}function ae(Ie,ne){return D===1?Ie[ne]:Ie.readUInt16BE(ne*D)}let ie;if(P){let Ie=-1;for(ie=f;ie<H;ie++)if(ae(y,ie)===ae(l,Ie===-1?0:ie-Ie)){if(Ie===-1&&(Ie=ie),ie-Ie+1===he)return Ie*D}else Ie!==-1&&(ie-=ie-Ie),Ie=-1}else for(f+he>H&&(f=H-he),ie=f;ie>=0;ie--){let Ie=!0;for(let ne=0;ne<he;ne++)if(ae(y,ie+ne)!==ae(l,ne)){Ie=!1;break}if(Ie)return ie}return-1}o.prototype.includes=function(l,f,E){return this.indexOf(l,f,E)!==-1},o.prototype.indexOf=function(l,f,E){return z(this,l,f,E,!0)},o.prototype.lastIndexOf=function(l,f,E){return z(this,l,f,E,!1)};function X(y,l,f,E){f=Number(f)||0;const P=y.length-f;E?(E=Number(E),E>P&&(E=P)):E=P;const D=l.length;E>D/2&&(E=D/2);let H;for(H=0;H<E;++H){const he=parseInt(l.substr(H*2,2),16);if(_e(he))return H;y[f+H]=he}return H}function Y(y,l,f,E){return Se(re(l,y.length-f),y,f,E)}function de(y,l,f,E){return Se(we(l),y,f,E)}function oe(y,l,f,E){return Se(Re(l),y,f,E)}function pe(y,l,f,E){return Se(dt(l,y.length-f),y,f,E)}o.prototype.write=function(l,f,E,P){if(f===void 0)P="utf8",E=this.length,f=0;else if(E===void 0&&typeof f=="string")P=f,E=this.length,f=0;else if(isFinite(f))f=f>>>0,isFinite(E)?(E=E>>>0,P===void 0&&(P="utf8")):(P=E,E=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");const D=this.length-f;if((E===void 0||E>D)&&(E=D),l.length>0&&(E<0||f<0)||f>this.length)throw new RangeError("Attempt to write outside buffer bounds");P||(P="utf8");let H=!1;for(;;)switch(P){case"hex":return X(this,l,f,E);case"utf8":case"utf-8":return Y(this,l,f,E);case"ascii":case"latin1":case"binary":return de(this,l,f,E);case"base64":return oe(this,l,f,E);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return pe(this,l,f,E);default:if(H)throw new TypeError("Unknown encoding: "+P);P=(""+P).toLowerCase(),H=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function k(y,l,f){return l===0&&f===y.length?t.fromByteArray(y):t.fromByteArray(y.slice(l,f))}function a(y,l,f){f=Math.min(y.length,f);const E=[];let P=l;for(;P<f;){const D=y[P];let H=null,he=D>239?4:D>223?3:D>191?2:1;if(P+he<=f){let ae,ie,Ie,ne;switch(he){case 1:D<128&&(H=D);break;case 2:ae=y[P+1],(ae&192)===128&&(ne=(D&31)<<6|ae&63,ne>127&&(H=ne));break;case 3:ae=y[P+1],ie=y[P+2],(ae&192)===128&&(ie&192)===128&&(ne=(D&15)<<12|(ae&63)<<6|ie&63,ne>2047&&(ne<55296||ne>57343)&&(H=ne));break;case 4:ae=y[P+1],ie=y[P+2],Ie=y[P+3],(ae&192)===128&&(ie&192)===128&&(Ie&192)===128&&(ne=(D&15)<<18|(ae&63)<<12|(ie&63)<<6|Ie&63,ne>65535&&ne<1114112&&(H=ne))}}H===null?(H=65533,he=1):H>65535&&(H-=65536,E.push(H>>>10&1023|55296),H=56320|H&1023),E.push(H),P+=he}return m(E)}const d=4096;function m(y){const l=y.length;if(l<=d)return String.fromCharCode.apply(String,y);let f="",E=0;for(;E<l;)f+=String.fromCharCode.apply(String,y.slice(E,E+=d));return f}function v(y,l,f){let E="";f=Math.min(y.length,f);for(let P=l;P<f;++P)E+=String.fromCharCode(y[P]&127);return E}function S(y,l,f){let E="";f=Math.min(y.length,f);for(let P=l;P<f;++P)E+=String.fromCharCode(y[P]);return E}function x(y,l,f){const E=y.length;(!l||l<0)&&(l=0),(!f||f<0||f>E)&&(f=E);let P="";for(let D=l;D<f;++D)P+=Me[y[D]];return P}function B(y,l,f){const E=y.slice(l,f);let P="";for(let D=0;D<E.length-1;D+=2)P+=String.fromCharCode(E[D]+E[D+1]*256);return P}o.prototype.slice=function(l,f){const E=this.length;l=~~l,f=f===void 0?E:~~f,l<0?(l+=E,l<0&&(l=0)):l>E&&(l=E),f<0?(f+=E,f<0&&(f=0)):f>E&&(f=E),f<l&&(f=l);const P=this.subarray(l,f);return Object.setPrototypeOf(P,o.prototype),P};function b(y,l,f){if(y%1!==0||y<0)throw new RangeError("offset is not uint");if(y+l>f)throw new RangeError("Trying to access beyond buffer length")}o.prototype.readUintLE=o.prototype.readUIntLE=function(l,f,E){l=l>>>0,f=f>>>0,E||b(l,f,this.length);let P=this[l],D=1,H=0;for(;++H<f&&(D*=256);)P+=this[l+H]*D;return P},o.prototype.readUintBE=o.prototype.readUIntBE=function(l,f,E){l=l>>>0,f=f>>>0,E||b(l,f,this.length);let P=this[l+--f],D=1;for(;f>0&&(D*=256);)P+=this[l+--f]*D;return P},o.prototype.readUint8=o.prototype.readUInt8=function(l,f){return l=l>>>0,f||b(l,1,this.length),this[l]},o.prototype.readUint16LE=o.prototype.readUInt16LE=function(l,f){return l=l>>>0,f||b(l,2,this.length),this[l]|this[l+1]<<8},o.prototype.readUint16BE=o.prototype.readUInt16BE=function(l,f){return l=l>>>0,f||b(l,2,this.length),this[l]<<8|this[l+1]},o.prototype.readUint32LE=o.prototype.readUInt32LE=function(l,f){return l=l>>>0,f||b(l,4,this.length),(this[l]|this[l+1]<<8|this[l+2]<<16)+this[l+3]*16777216},o.prototype.readUint32BE=o.prototype.readUInt32BE=function(l,f){return l=l>>>0,f||b(l,4,this.length),this[l]*16777216+(this[l+1]<<16|this[l+2]<<8|this[l+3])},o.prototype.readBigUInt64LE=Qe(function(l){l=l>>>0,V(l,"offset");const f=this[l],E=this[l+7];(f===void 0||E===void 0)&&G(l,this.length-8);const P=f+this[++l]*2**8+this[++l]*2**16+this[++l]*2**24,D=this[++l]+this[++l]*2**8+this[++l]*2**16+E*2**24;return BigInt(P)+(BigInt(D)<<BigInt(32))}),o.prototype.readBigUInt64BE=Qe(function(l){l=l>>>0,V(l,"offset");const f=this[l],E=this[l+7];(f===void 0||E===void 0)&&G(l,this.length-8);const P=f*2**24+this[++l]*2**16+this[++l]*2**8+this[++l],D=this[++l]*2**24+this[++l]*2**16+this[++l]*2**8+E;return(BigInt(P)<<BigInt(32))+BigInt(D)}),o.prototype.readIntLE=function(l,f,E){l=l>>>0,f=f>>>0,E||b(l,f,this.length);let P=this[l],D=1,H=0;for(;++H<f&&(D*=256);)P+=this[l+H]*D;return D*=128,P>=D&&(P-=Math.pow(2,8*f)),P},o.prototype.readIntBE=function(l,f,E){l=l>>>0,f=f>>>0,E||b(l,f,this.length);let P=f,D=1,H=this[l+--P];for(;P>0&&(D*=256);)H+=this[l+--P]*D;return D*=128,H>=D&&(H-=Math.pow(2,8*f)),H},o.prototype.readInt8=function(l,f){return l=l>>>0,f||b(l,1,this.length),this[l]&128?(255-this[l]+1)*-1:this[l]},o.prototype.readInt16LE=function(l,f){l=l>>>0,f||b(l,2,this.length);const E=this[l]|this[l+1]<<8;return E&32768?E|4294901760:E},o.prototype.readInt16BE=function(l,f){l=l>>>0,f||b(l,2,this.length);const E=this[l+1]|this[l]<<8;return E&32768?E|4294901760:E},o.prototype.readInt32LE=function(l,f){return l=l>>>0,f||b(l,4,this.length),this[l]|this[l+1]<<8|this[l+2]<<16|this[l+3]<<24},o.prototype.readInt32BE=function(l,f){return l=l>>>0,f||b(l,4,this.length),this[l]<<24|this[l+1]<<16|this[l+2]<<8|this[l+3]},o.prototype.readBigInt64LE=Qe(function(l){l=l>>>0,V(l,"offset");const f=this[l],E=this[l+7];(f===void 0||E===void 0)&&G(l,this.length-8);const P=this[l+4]+this[l+5]*2**8+this[l+6]*2**16+(E<<24);return(BigInt(P)<<BigInt(32))+BigInt(f+this[++l]*2**8+this[++l]*2**16+this[++l]*2**24)}),o.prototype.readBigInt64BE=Qe(function(l){l=l>>>0,V(l,"offset");const f=this[l],E=this[l+7];(f===void 0||E===void 0)&&G(l,this.length-8);const P=(f<<24)+this[++l]*2**16+this[++l]*2**8+this[++l];return(BigInt(P)<<BigInt(32))+BigInt(this[++l]*2**24+this[++l]*2**16+this[++l]*2**8+E)}),o.prototype.readFloatLE=function(l,f){return l=l>>>0,f||b(l,4,this.length),r.read(this,l,!0,23,4)},o.prototype.readFloatBE=function(l,f){return l=l>>>0,f||b(l,4,this.length),r.read(this,l,!1,23,4)},o.prototype.readDoubleLE=function(l,f){return l=l>>>0,f||b(l,8,this.length),r.read(this,l,!0,52,8)},o.prototype.readDoubleBE=function(l,f){return l=l>>>0,f||b(l,8,this.length),r.read(this,l,!1,52,8)};function h(y,l,f,E,P,D){if(!o.isBuffer(y))throw new TypeError('"buffer" argument must be a Buffer instance');if(l>P||l<D)throw new RangeError('"value" argument is out of bounds');if(f+E>y.length)throw new RangeError("Index out of range")}o.prototype.writeUintLE=o.prototype.writeUIntLE=function(l,f,E,P){if(l=+l,f=f>>>0,E=E>>>0,!P){const he=Math.pow(2,8*E)-1;h(this,l,f,E,he,0)}let D=1,H=0;for(this[f]=l&255;++H<E&&(D*=256);)this[f+H]=l/D&255;return f+E},o.prototype.writeUintBE=o.prototype.writeUIntBE=function(l,f,E,P){if(l=+l,f=f>>>0,E=E>>>0,!P){const he=Math.pow(2,8*E)-1;h(this,l,f,E,he,0)}let D=E-1,H=1;for(this[f+D]=l&255;--D>=0&&(H*=256);)this[f+D]=l/H&255;return f+E},o.prototype.writeUint8=o.prototype.writeUInt8=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,1,255,0),this[f]=l&255,f+1},o.prototype.writeUint16LE=o.prototype.writeUInt16LE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,2,65535,0),this[f]=l&255,this[f+1]=l>>>8,f+2},o.prototype.writeUint16BE=o.prototype.writeUInt16BE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,2,65535,0),this[f]=l>>>8,this[f+1]=l&255,f+2},o.prototype.writeUint32LE=o.prototype.writeUInt32LE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,4,**********,0),this[f+3]=l>>>24,this[f+2]=l>>>16,this[f+1]=l>>>8,this[f]=l&255,f+4},o.prototype.writeUint32BE=o.prototype.writeUInt32BE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,4,**********,0),this[f]=l>>>24,this[f+1]=l>>>16,this[f+2]=l>>>8,this[f+3]=l&255,f+4};function R(y,l,f,E,P){I(l,E,P,y,f,7);let D=Number(l&BigInt(**********));y[f++]=D,D=D>>8,y[f++]=D,D=D>>8,y[f++]=D,D=D>>8,y[f++]=D;let H=Number(l>>BigInt(32)&BigInt(**********));return y[f++]=H,H=H>>8,y[f++]=H,H=H>>8,y[f++]=H,H=H>>8,y[f++]=H,f}function K(y,l,f,E,P){I(l,E,P,y,f,7);let D=Number(l&BigInt(**********));y[f+7]=D,D=D>>8,y[f+6]=D,D=D>>8,y[f+5]=D,D=D>>8,y[f+4]=D;let H=Number(l>>BigInt(32)&BigInt(**********));return y[f+3]=H,H=H>>8,y[f+2]=H,H=H>>8,y[f+1]=H,H=H>>8,y[f]=H,f+8}o.prototype.writeBigUInt64LE=Qe(function(l,f=0){return R(this,l,f,BigInt(0),BigInt("0xffffffffffffffff"))}),o.prototype.writeBigUInt64BE=Qe(function(l,f=0){return K(this,l,f,BigInt(0),BigInt("0xffffffffffffffff"))}),o.prototype.writeIntLE=function(l,f,E,P){if(l=+l,f=f>>>0,!P){const ae=Math.pow(2,8*E-1);h(this,l,f,E,ae-1,-ae)}let D=0,H=1,he=0;for(this[f]=l&255;++D<E&&(H*=256);)l<0&&he===0&&this[f+D-1]!==0&&(he=1),this[f+D]=(l/H>>0)-he&255;return f+E},o.prototype.writeIntBE=function(l,f,E,P){if(l=+l,f=f>>>0,!P){const ae=Math.pow(2,8*E-1);h(this,l,f,E,ae-1,-ae)}let D=E-1,H=1,he=0;for(this[f+D]=l&255;--D>=0&&(H*=256);)l<0&&he===0&&this[f+D+1]!==0&&(he=1),this[f+D]=(l/H>>0)-he&255;return f+E},o.prototype.writeInt8=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,1,127,-128),l<0&&(l=255+l+1),this[f]=l&255,f+1},o.prototype.writeInt16LE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,2,32767,-32768),this[f]=l&255,this[f+1]=l>>>8,f+2},o.prototype.writeInt16BE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,2,32767,-32768),this[f]=l>>>8,this[f+1]=l&255,f+2},o.prototype.writeInt32LE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,4,**********,-2147483648),this[f]=l&255,this[f+1]=l>>>8,this[f+2]=l>>>16,this[f+3]=l>>>24,f+4},o.prototype.writeInt32BE=function(l,f,E){return l=+l,f=f>>>0,E||h(this,l,f,4,**********,-2147483648),l<0&&(l=**********+l+1),this[f]=l>>>24,this[f+1]=l>>>16,this[f+2]=l>>>8,this[f+3]=l&255,f+4},o.prototype.writeBigInt64LE=Qe(function(l,f=0){return R(this,l,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),o.prototype.writeBigInt64BE=Qe(function(l,f=0){return K(this,l,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function J(y,l,f,E,P,D){if(f+E>y.length)throw new RangeError("Index out of range");if(f<0)throw new RangeError("Index out of range")}function T(y,l,f,E,P){return l=+l,f=f>>>0,P||J(y,l,f,4),r.write(y,l,f,E,23,4),f+4}o.prototype.writeFloatLE=function(l,f,E){return T(this,l,f,!0,E)},o.prototype.writeFloatBE=function(l,f,E){return T(this,l,f,!1,E)};function F(y,l,f,E,P){return l=+l,f=f>>>0,P||J(y,l,f,8),r.write(y,l,f,E,52,8),f+8}o.prototype.writeDoubleLE=function(l,f,E){return F(this,l,f,!0,E)},o.prototype.writeDoubleBE=function(l,f,E){return F(this,l,f,!1,E)},o.prototype.copy=function(l,f,E,P){if(!o.isBuffer(l))throw new TypeError("argument should be a Buffer");if(E||(E=0),!P&&P!==0&&(P=this.length),f>=l.length&&(f=l.length),f||(f=0),P>0&&P<E&&(P=E),P===E||l.length===0||this.length===0)return 0;if(f<0)throw new RangeError("targetStart out of bounds");if(E<0||E>=this.length)throw new RangeError("Index out of range");if(P<0)throw new RangeError("sourceEnd out of bounds");P>this.length&&(P=this.length),l.length-f<P-E&&(P=l.length-f+E);const D=P-E;return this===l&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(f,E,P):Uint8Array.prototype.set.call(l,this.subarray(E,P),f),D},o.prototype.fill=function(l,f,E,P){if(typeof l=="string"){if(typeof f=="string"?(P=f,f=0,E=this.length):typeof E=="string"&&(P=E,E=this.length),P!==void 0&&typeof P!="string")throw new TypeError("encoding must be a string");if(typeof P=="string"&&!o.isEncoding(P))throw new TypeError("Unknown encoding: "+P);if(l.length===1){const H=l.charCodeAt(0);(P==="utf8"&&H<128||P==="latin1")&&(l=H)}}else typeof l=="number"?l=l&255:typeof l=="boolean"&&(l=Number(l));if(f<0||this.length<f||this.length<E)throw new RangeError("Out of range index");if(E<=f)return this;f=f>>>0,E=E===void 0?this.length:E>>>0,l||(l=0);let D;if(typeof l=="number")for(D=f;D<E;++D)this[D]=l;else{const H=o.isBuffer(l)?l:o.from(l,P),he=H.length;if(he===0)throw new TypeError('The value "'+l+'" is invalid for argument "value"');for(D=0;D<E-f;++D)this[D+f]=H[D%he]}return this};const q={};function Z(y,l,f){q[y]=class extends f{constructor(){super(),Object.defineProperty(this,"message",{value:l.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${y}]`,this.stack,delete this.name}get code(){return y}set code(P){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:P,writable:!0})}toString(){return`${this.name} [${y}]: ${this.message}`}}}Z("ERR_BUFFER_OUT_OF_BOUNDS",function(y){return y?`${y} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),Z("ERR_INVALID_ARG_TYPE",function(y,l){return`The "${y}" argument must be of type number. Received type ${typeof l}`},TypeError),Z("ERR_OUT_OF_RANGE",function(y,l,f){let E=`The value of "${y}" is out of range.`,P=f;return Number.isInteger(f)&&Math.abs(f)>2**32?P=le(String(f)):typeof f=="bigint"&&(P=String(f),(f>BigInt(2)**BigInt(32)||f<-(BigInt(2)**BigInt(32)))&&(P=le(P)),P+="n"),E+=` It must be ${l}. Received ${P}`,E},RangeError);function le(y){let l="",f=y.length;const E=y[0]==="-"?1:0;for(;f>=E+4;f-=3)l=`_${y.slice(f-3,f)}${l}`;return`${y.slice(0,f)}${l}`}function M(y,l,f){V(l,"offset"),(y[l]===void 0||y[l+f]===void 0)&&G(l,y.length-(f+1))}function I(y,l,f,E,P,D){if(y>f||y<l){const H=typeof l=="bigint"?"n":"";let he;throw l===0||l===BigInt(0)?he=`>= 0${H} and < 2${H} ** ${(D+1)*8}${H}`:he=`>= -(2${H} ** ${(D+1)*8-1}${H}) and < 2 ** ${(D+1)*8-1}${H}`,new q.ERR_OUT_OF_RANGE("value",he,y)}M(E,P,D)}function V(y,l){if(typeof y!="number")throw new q.ERR_INVALID_ARG_TYPE(l,"number",y)}function G(y,l,f){throw Math.floor(y)!==y?(V(y,f),new q.ERR_OUT_OF_RANGE("offset","an integer",y)):l<0?new q.ERR_BUFFER_OUT_OF_BOUNDS:new q.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${l}`,y)}const se=/[^+/0-9A-Za-z-_]/g;function ue(y){if(y=y.split("=")[0],y=y.trim().replace(se,""),y.length<2)return"";for(;y.length%4!==0;)y=y+"=";return y}function re(y,l){l=l||1/0;let f;const E=y.length;let P=null;const D=[];for(let H=0;H<E;++H){if(f=y.charCodeAt(H),f>55295&&f<57344){if(!P){if(f>56319){(l-=3)>-1&&D.push(239,191,189);continue}else if(H+1===E){(l-=3)>-1&&D.push(239,191,189);continue}P=f;continue}if(f<56320){(l-=3)>-1&&D.push(239,191,189),P=f;continue}f=(P-55296<<10|f-56320)+65536}else P&&(l-=3)>-1&&D.push(239,191,189);if(P=null,f<128){if((l-=1)<0)break;D.push(f)}else if(f<2048){if((l-=2)<0)break;D.push(f>>6|192,f&63|128)}else if(f<65536){if((l-=3)<0)break;D.push(f>>12|224,f>>6&63|128,f&63|128)}else if(f<1114112){if((l-=4)<0)break;D.push(f>>18|240,f>>12&63|128,f>>6&63|128,f&63|128)}else throw new Error("Invalid code point")}return D}function we(y){const l=[];for(let f=0;f<y.length;++f)l.push(y.charCodeAt(f)&255);return l}function dt(y,l){let f,E,P;const D=[];for(let H=0;H<y.length&&!((l-=2)<0);++H)f=y.charCodeAt(H),E=f>>8,P=f%256,D.push(P),D.push(E);return D}function Re(y){return t.toByteArray(ue(y))}function Se(y,l,f,E){let P;for(P=0;P<E&&!(P+f>=l.length||P>=y.length);++P)l[P+f]=y[P];return P}function Ze(y,l){return y instanceof l||y!=null&&y.constructor!=null&&y.constructor.name!=null&&y.constructor.name===l.name}function _e(y){return y!==y}const Me=function(){const y="0123456789abcdef",l=new Array(256);for(let f=0;f<16;++f){const E=f*16;for(let P=0;P<16;++P)l[E+P]=y[f]+y[P]}return l}();function Qe(y){return typeof BigInt>"u"?Ee:y}function Ee(){throw new Error("BigInt not supported")}})(xn);/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */(function(e,t){var r=xn,n=r.Buffer;function i(c,o){for(var u in c)o[u]=c[u]}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?e.exports=r:(i(r,t),t.Buffer=s);function s(c,o,u){return n(c,o,u)}s.prototype=Object.create(n.prototype),i(n,s),s.from=function(c,o,u){if(typeof c=="number")throw new TypeError("Argument must not be a number");return n(c,o,u)},s.alloc=function(c,o,u){if(typeof c!="number")throw new TypeError("Argument must be a number");var p=n(c);return o!==void 0?typeof u=="string"?p.fill(o,u):p.fill(o):p.fill(0),p},s.allocUnsafe=function(c){if(typeof c!="number")throw new TypeError("Argument must be a number");return n(c)},s.allocUnsafeSlow=function(c){if(typeof c!="number")throw new TypeError("Argument must be a number");return r.SlowBuffer(c)}})(wo,wo.exports);var Ut=wo.exports,of={}.toString,af=Array.isArray||function(e){return of.call(e)=="[object Array]"},kn=TypeError,gl=Object,cf=Error,lf=EvalError,uf=RangeError,hf=ReferenceError,yl=SyntaxError,ff=URIError,df=Math.abs,pf=Math.floor,gf=Math.max,yf=Math.min,mf=Math.pow,vf=Math.round,bf=Number.isNaN||function(t){return t!==t},wf=bf,_f=function(t){return wf(t)||t===0?t:t<0?-1:1},Ef=Object.getOwnPropertyDescriptor,di=Ef;if(di)try{di([],"length")}catch{di=null}var Cn=di,pi=Object.defineProperty||!1;if(pi)try{pi({},"a",{value:1})}catch{pi=!1}var Bi=pi,Ms,da;function ml(){return da||(da=1,Ms=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var i=42;t[r]=i;for(var s in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var c=Object.getOwnPropertySymbols(t);if(c.length!==1||c[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var o=Object.getOwnPropertyDescriptor(t,r);if(o.value!==i||o.enumerable!==!0)return!1}return!0}),Ms}var Is,pa;function Sf(){if(pa)return Is;pa=1;var e=typeof Symbol<"u"&&Symbol,t=ml();return Is=function(){return typeof e!="function"||typeof Symbol!="function"||typeof e("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:t()},Is}var As,ga;function vl(){return ga||(ga=1,As=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),As}var xs,ya;function bl(){if(ya)return xs;ya=1;var e=gl;return xs=e.getPrototypeOf||null,xs}var Rf="Function.prototype.bind called on incompatible ",Mf=Object.prototype.toString,If=Math.max,Af="[object Function]",ma=function(t,r){for(var n=[],i=0;i<t.length;i+=1)n[i]=t[i];for(var s=0;s<r.length;s+=1)n[s+t.length]=r[s];return n},xf=function(t,r){for(var n=[],i=r,s=0;i<t.length;i+=1,s+=1)n[s]=t[i];return n},kf=function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r},Cf=function(t){var r=this;if(typeof r!="function"||Mf.apply(r)!==Af)throw new TypeError(Rf+r);for(var n=xf(arguments,1),i,s=function(){if(this instanceof i){var g=r.apply(this,ma(n,arguments));return Object(g)===g?g:this}return r.apply(t,ma(n,arguments))},c=If(0,r.length-n.length),o=[],u=0;u<c;u++)o[u]="$"+u;if(i=Function("binder","return function ("+kf(o,",")+"){ return binder.apply(this,arguments); }")(s),r.prototype){var p=function(){};p.prototype=r.prototype,i.prototype=new p,p.prototype=null}return i},Tf=Cf,Tn=Function.prototype.bind||Tf,Bo=Function.prototype.call,No=Function.prototype.apply,Bf=typeof Reflect<"u"&&Reflect&&Reflect.apply,Nf=Tn,Pf=No,Lf=Bo,Of=Bf,wl=Of||Nf.call(Lf,Pf),$f=Tn,Ff=kn,Df=Bo,jf=wl,Po=function(t){if(t.length<1||typeof t[0]!="function")throw new Ff("a function is required");return jf($f,Df,t)},ks,va;function Uf(){if(va)return ks;va=1;var e=Po,t=Cn,r;try{r=[].__proto__===Array.prototype}catch(c){if(!c||typeof c!="object"||!("code"in c)||c.code!=="ERR_PROTO_ACCESS")throw c}var n=!!r&&t&&t(Object.prototype,"__proto__"),i=Object,s=i.getPrototypeOf;return ks=n&&typeof n.get=="function"?e([n.get]):typeof s=="function"?function(o){return s(o==null?o:i(o))}:!1,ks}var Cs,ba;function _l(){if(ba)return Cs;ba=1;var e=vl(),t=bl(),r=Uf();return Cs=e?function(i){return e(i)}:t?function(i){if(!i||typeof i!="object"&&typeof i!="function")throw new TypeError("getProto: not an object");return t(i)}:r?function(i){return r(i)}:null,Cs}var Ts,wa;function Hf(){if(wa)return Ts;wa=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,r=Tn;return Ts=r.call(e,t),Ts}var ye,Wf=gl,qf=cf,Vf=lf,zf=uf,Gf=hf,Zr=yl,Wr=kn,Jf=ff,Zf=df,Kf=pf,Yf=gf,Qf=yf,Xf=mf,ed=vf,td=_f,El=Function,Bs=function(e){try{return El('"use strict"; return ('+e+").constructor;")()}catch{}},vn=Cn,rd=Bi,Ns=function(){throw new Wr},nd=vn?function(){try{return arguments.callee,Ns}catch{try{return vn(arguments,"callee").get}catch{return Ns}}}():Ns,Pr=Sf()(),et=_l(),id=bl(),sd=vl(),Sl=No,Bn=Bo,jr={},od=typeof Uint8Array>"u"||!et?ye:et(Uint8Array),yr={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?ye:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?ye:ArrayBuffer,"%ArrayIteratorPrototype%":Pr&&et?et([][Symbol.iterator]()):ye,"%AsyncFromSyncIteratorPrototype%":ye,"%AsyncFunction%":jr,"%AsyncGenerator%":jr,"%AsyncGeneratorFunction%":jr,"%AsyncIteratorPrototype%":jr,"%Atomics%":typeof Atomics>"u"?ye:Atomics,"%BigInt%":typeof BigInt>"u"?ye:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?ye:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?ye:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?ye:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":qf,"%eval%":eval,"%EvalError%":Vf,"%Float16Array%":typeof Float16Array>"u"?ye:Float16Array,"%Float32Array%":typeof Float32Array>"u"?ye:Float32Array,"%Float64Array%":typeof Float64Array>"u"?ye:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?ye:FinalizationRegistry,"%Function%":El,"%GeneratorFunction%":jr,"%Int8Array%":typeof Int8Array>"u"?ye:Int8Array,"%Int16Array%":typeof Int16Array>"u"?ye:Int16Array,"%Int32Array%":typeof Int32Array>"u"?ye:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Pr&&et?et(et([][Symbol.iterator]())):ye,"%JSON%":typeof JSON=="object"?JSON:ye,"%Map%":typeof Map>"u"?ye:Map,"%MapIteratorPrototype%":typeof Map>"u"||!Pr||!et?ye:et(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Wf,"%Object.getOwnPropertyDescriptor%":vn,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?ye:Promise,"%Proxy%":typeof Proxy>"u"?ye:Proxy,"%RangeError%":zf,"%ReferenceError%":Gf,"%Reflect%":typeof Reflect>"u"?ye:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?ye:Set,"%SetIteratorPrototype%":typeof Set>"u"||!Pr||!et?ye:et(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?ye:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Pr&&et?et(""[Symbol.iterator]()):ye,"%Symbol%":Pr?Symbol:ye,"%SyntaxError%":Zr,"%ThrowTypeError%":nd,"%TypedArray%":od,"%TypeError%":Wr,"%Uint8Array%":typeof Uint8Array>"u"?ye:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?ye:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?ye:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?ye:Uint32Array,"%URIError%":Jf,"%WeakMap%":typeof WeakMap>"u"?ye:WeakMap,"%WeakRef%":typeof WeakRef>"u"?ye:WeakRef,"%WeakSet%":typeof WeakSet>"u"?ye:WeakSet,"%Function.prototype.call%":Bn,"%Function.prototype.apply%":Sl,"%Object.defineProperty%":rd,"%Object.getPrototypeOf%":id,"%Math.abs%":Zf,"%Math.floor%":Kf,"%Math.max%":Yf,"%Math.min%":Qf,"%Math.pow%":Xf,"%Math.round%":ed,"%Math.sign%":td,"%Reflect.getPrototypeOf%":sd};if(et)try{null.error}catch(e){var ad=et(et(e));yr["%Error.prototype%"]=ad}var cd=function e(t){var r;if(t==="%AsyncFunction%")r=Bs("async function () {}");else if(t==="%GeneratorFunction%")r=Bs("function* () {}");else if(t==="%AsyncGeneratorFunction%")r=Bs("async function* () {}");else if(t==="%AsyncGenerator%"){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if(t==="%AsyncIteratorPrototype%"){var i=e("%AsyncGenerator%");i&&et&&(r=et(i.prototype))}return yr[t]=r,r},_a={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Nn=Tn,mi=Hf(),ld=Nn.call(Bn,Array.prototype.concat),ud=Nn.call(Sl,Array.prototype.splice),Ea=Nn.call(Bn,String.prototype.replace),vi=Nn.call(Bn,String.prototype.slice),hd=Nn.call(Bn,RegExp.prototype.exec),fd=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,dd=/\\(\\)?/g,pd=function(t){var r=vi(t,0,1),n=vi(t,-1);if(r==="%"&&n!=="%")throw new Zr("invalid intrinsic syntax, expected closing `%`");if(n==="%"&&r!=="%")throw new Zr("invalid intrinsic syntax, expected opening `%`");var i=[];return Ea(t,fd,function(s,c,o,u){i[i.length]=o?Ea(u,dd,"$1"):c||s}),i},gd=function(t,r){var n=t,i;if(mi(_a,n)&&(i=_a[n],n="%"+i[0]+"%"),mi(yr,n)){var s=yr[n];if(s===jr&&(s=cd(n)),typeof s>"u"&&!r)throw new Wr("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:i,name:n,value:s}}throw new Zr("intrinsic "+t+" does not exist!")},Rl=function(t,r){if(typeof t!="string"||t.length===0)throw new Wr("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof r!="boolean")throw new Wr('"allowMissing" argument must be a boolean');if(hd(/^%?[^%]*%?$/,t)===null)throw new Zr("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=pd(t),i=n.length>0?n[0]:"",s=gd("%"+i+"%",r),c=s.name,o=s.value,u=!1,p=s.alias;p&&(i=p[0],ud(n,ld([0,1],p)));for(var g=1,w=!0;g<n.length;g+=1){var _=n[g],L=vi(_,0,1),j=vi(_,-1);if((L==='"'||L==="'"||L==="`"||j==='"'||j==="'"||j==="`")&&L!==j)throw new Zr("property names with quotes must have matching quotes");if((_==="constructor"||!w)&&(u=!0),i+="."+_,c="%"+i+"%",mi(yr,c))o=yr[c];else if(o!=null){if(!(_ in o)){if(!r)throw new Wr("base intrinsic for "+t+" exists, but the property is not available.");return}if(vn&&g+1>=n.length){var $=vn(o,_);w=!!$,w&&"get"in $&&!("originalValue"in $.get)?o=$.get:o=o[_]}else w=mi(o,_),o=o[_];w&&!u&&(yr[c]=o)}}return o},Ml=Rl,Il=Po,yd=Il([Ml("%String.prototype.indexOf%")]),Al=function(t,r){var n=Ml(t,!!r);return typeof n=="function"&&yd(t,".prototype.")>-1?Il([n]):n},Ps,Sa;function md(){if(Sa)return Ps;Sa=1;var e=Function.prototype.toString,t=typeof Reflect=="object"&&Reflect!==null&&Reflect.apply,r,n;if(typeof t=="function"&&typeof Object.defineProperty=="function")try{r=Object.defineProperty({},"length",{get:function(){throw n}}),n={},t(function(){throw 42},null,r)}catch(N){N!==n&&(t=null)}else t=null;var i=/^\s*class\b/,s=function(C){try{var W=e.call(C);return i.test(W)}catch{return!1}},c=function(C){try{return s(C)?!1:(e.call(C),!0)}catch{return!1}},o=Object.prototype.toString,u="[object Object]",p="[object Function]",g="[object GeneratorFunction]",w="[object HTMLAllCollection]",_="[object HTML document.all class]",L="[object HTMLCollection]",j=typeof Symbol=="function"&&!!Symbol.toStringTag,$=!(0 in[,]),O=function(){return!1};if(typeof document=="object"){var A=document.all;o.call(A)===o.call(document.all)&&(O=function(C){if(($||!C)&&(typeof C>"u"||typeof C=="object"))try{var W=o.call(C);return(W===w||W===_||W===L||W===u)&&C("")==null}catch{}return!1})}return Ps=t?function(C){if(O(C))return!0;if(!C||typeof C!="function"&&typeof C!="object")return!1;try{t(C,null,r)}catch(W){if(W!==n)return!1}return!s(C)&&c(C)}:function(C){if(O(C))return!0;if(!C||typeof C!="function"&&typeof C!="object")return!1;if(j)return c(C);if(s(C))return!1;var W=o.call(C);return W!==p&&W!==g&&!/^\[object HTML/.test(W)?!1:c(C)},Ps}var Ls,Ra;function vd(){if(Ra)return Ls;Ra=1;var e=md(),t=Object.prototype.toString,r=Object.prototype.hasOwnProperty,n=function(u,p,g){for(var w=0,_=u.length;w<_;w++)r.call(u,w)&&(g==null?p(u[w],w,u):p.call(g,u[w],w,u))},i=function(u,p,g){for(var w=0,_=u.length;w<_;w++)g==null?p(u.charAt(w),w,u):p.call(g,u.charAt(w),w,u)},s=function(u,p,g){for(var w in u)r.call(u,w)&&(g==null?p(u[w],w,u):p.call(g,u[w],w,u))};function c(o){return t.call(o)==="[object Array]"}return Ls=function(u,p,g){if(!e(p))throw new TypeError("iterator must be a function");var w;arguments.length>=3&&(w=g),c(u)?n(u,p,w):typeof u=="string"?i(u,p,w):s(u,p,w)},Ls}var Os,Ma;function bd(){return Ma||(Ma=1,Os=["Float16Array","Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"]),Os}var $s,Ia;function wd(){if(Ia)return $s;Ia=1;var e=bd(),t=typeof globalThis>"u"?ee:globalThis;return $s=function(){for(var n=[],i=0;i<e.length;i++)typeof t[e[i]]=="function"&&(n[n.length]=e[i]);return n},$s}var Fs={exports:{}},Ds,Aa;function _d(){if(Aa)return Ds;Aa=1;var e=Bi,t=yl,r=kn,n=Cn;return Ds=function(s,c,o){if(!s||typeof s!="object"&&typeof s!="function")throw new r("`obj` must be an object or a function`");if(typeof c!="string"&&typeof c!="symbol")throw new r("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new r("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new r("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new r("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new r("`loose`, if provided, must be a boolean");var u=arguments.length>3?arguments[3]:null,p=arguments.length>4?arguments[4]:null,g=arguments.length>5?arguments[5]:null,w=arguments.length>6?arguments[6]:!1,_=!!n&&n(s,c);if(e)e(s,c,{configurable:g===null&&_?_.configurable:!g,enumerable:u===null&&_?_.enumerable:!u,value:o,writable:p===null&&_?_.writable:!p});else if(w||!u&&!p&&!g)s[c]=o;else throw new t("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},Ds}var js,xa;function Ed(){if(xa)return js;xa=1;var e=Bi,t=function(){return!!e};return t.hasArrayLengthDefineBug=function(){if(!e)return null;try{return e([],"length",{value:1}).length!==1}catch{return!0}},js=t,js}var Us,ka;function Sd(){if(ka)return Us;ka=1;var e=Rl,t=_d(),r=Ed()(),n=Cn,i=kn,s=e("%Math.floor%");return Us=function(o,u){if(typeof o!="function")throw new i("`fn` is not a function");if(typeof u!="number"||u<0||u>**********||s(u)!==u)throw new i("`length` must be a positive 32-bit integer");var p=arguments.length>2&&!!arguments[2],g=!0,w=!0;if("length"in o&&n){var _=n(o,"length");_&&!_.configurable&&(g=!1),_&&!_.writable&&(w=!1)}return(g||w||!p)&&(r?t(o,"length",u,!0,!0):t(o,"length",u)),o},Us}var Hs,Ca;function Rd(){if(Ca)return Hs;Ca=1;var e=Tn,t=No,r=wl;return Hs=function(){return r(e,t,arguments)},Hs}var Ta;function Md(){return Ta||(Ta=1,function(e){var t=Sd(),r=Bi,n=Po,i=Rd();e.exports=function(c){var o=n(arguments),u=c.length-(arguments.length-1);return t(o,1+(u>0?u:0),!0)},r?r(e.exports,"apply",{value:i}):e.exports.apply=i}(Fs)),Fs.exports}var Ws,Ba;function Id(){if(Ba)return Ws;Ba=1;var e=ml();return Ws=function(){return e()&&!!Symbol.toStringTag},Ws}var qs,Na;function Ad(){if(Na)return qs;Na=1;var e=vd(),t=wd(),r=Md(),n=Al,i=Cn,s=_l(),c=n("Object.prototype.toString"),o=Id()(),u=typeof globalThis>"u"?ee:globalThis,p=t(),g=n("String.prototype.slice"),w=n("Array.prototype.indexOf",!0)||function(O,A){for(var N=0;N<O.length;N+=1)if(O[N]===A)return N;return-1},_={__proto__:null};o&&i&&s?e(p,function($){var O=new u[$];if(Symbol.toStringTag in O&&s){var A=s(O),N=i(A,Symbol.toStringTag);if(!N&&A){var C=s(A);N=i(C,Symbol.toStringTag)}_["$"+$]=r(N.get)}}):e(p,function($){var O=new u[$],A=O.slice||O.set;A&&(_["$"+$]=r(A))});var L=function(O){var A=!1;return e(_,function(N,C){if(!A)try{"$"+N(O)===C&&(A=g(C,1))}catch{}}),A},j=function(O){var A=!1;return e(_,function(N,C){if(!A)try{N(O),A=g(C,1)}catch{}}),A};return qs=function(O){if(!O||typeof O!="object")return!1;if(!o){var A=g(c(O),8,-1);return w(p,A)>-1?A:A!=="Object"?!1:j(O)}return i?L(O):null},qs}var Vs,Pa;function xd(){if(Pa)return Vs;Pa=1;var e=Ad();return Vs=function(r){return!!e(r)},Vs}var kd=kn,Cd=Al,Td=Cd("TypedArray.prototype.buffer",!0),Bd=xd(),Nd=Td||function(t){if(!Bd(t))throw new kd("Not a Typed Array");return t.buffer},kt=Ut.Buffer,Pd=af,Ld=Nd,Od=ArrayBuffer.isView||function(t){try{return Ld(t),!0}catch{return!1}},$d=typeof Uint8Array<"u",xl=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u",Fd=xl&&(kt.prototype instanceof Uint8Array||kt.TYPED_ARRAY_SUPPORT),Dd=function(t,r){if(t instanceof kt)return t;if(typeof t=="string")return kt.from(t,r);if(xl&&Od(t)){if(t.byteLength===0)return kt.alloc(0);if(Fd){var n=kt.from(t.buffer,t.byteOffset,t.byteLength);if(n.byteLength===t.byteLength)return n}var i=t instanceof Uint8Array?t:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),s=kt.from(i);if(s.length===t.byteLength)return s}if($d&&t instanceof Uint8Array)return kt.from(t);var c=Pd(t);if(c)for(var o=0;o<t.length;o+=1){var u=t[o];if(typeof u!="number"||u<0||u>255||~~u!==u)throw new RangeError("Array items must be numbers in the range 0-255.")}if(c||kt.isBuffer(t)&&t.constructor&&typeof t.constructor.isBuffer=="function"&&t.constructor.isBuffer(t))return kt.from(t);throw new TypeError('The "data" argument must be a string, an Array, a Buffer, a Uint8Array, or a DataView.')},jd=Ut.Buffer,Ud=Dd;function Ni(e,t){this._block=jd.alloc(e),this._finalSize=t,this._blockSize=e,this._len=0}Ni.prototype.update=function(e,t){e=Ud(e,t||"utf8");for(var r=this._block,n=this._blockSize,i=e.length,s=this._len,c=0;c<i;){for(var o=s%n,u=Math.min(i-c,n-o),p=0;p<u;p++)r[o+p]=e[c+p];s+=u,c+=u,s%n===0&&this._update(r)}return this._len+=i,this};Ni.prototype.digest=function(e){var t=this._len%this._blockSize;this._block[t]=128,this._block.fill(0,t+1),t>=this._finalSize&&(this._update(this._block),this._block.fill(0));var r=this._len*8;if(r<=**********)this._block.writeUInt32BE(r,this._blockSize-4);else{var n=(r&**********)>>>0,i=(r-n)/4294967296;this._block.writeUInt32BE(i,this._blockSize-8),this._block.writeUInt32BE(n,this._blockSize-4)}this._update(this._block);var s=this._hash();return e?s.toString(e):s};Ni.prototype._update=function(){throw new Error("_update must be implemented by subclass")};var cn=Ni,Hd=Rt,kl=cn,Wd=Ut.Buffer,qd=[1518500249,1859775393,-1894007588,-899497514],Vd=new Array(80);function Pn(){this.init(),this._w=Vd,kl.call(this,64,56)}Hd(Pn,kl);Pn.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this};function zd(e){return e<<5|e>>>27}function Gd(e){return e<<30|e>>>2}function Jd(e,t,r,n){return e===0?t&r|~t&n:e===2?t&r|t&n|r&n:t^r^n}Pn.prototype._update=function(e){for(var t=this._w,r=this._a|0,n=this._b|0,i=this._c|0,s=this._d|0,c=this._e|0,o=0;o<16;++o)t[o]=e.readInt32BE(o*4);for(;o<80;++o)t[o]=t[o-3]^t[o-8]^t[o-14]^t[o-16];for(var u=0;u<80;++u){var p=~~(u/20),g=zd(r)+Jd(p,n,i,s)+c+t[u]+qd[p]|0;c=s,s=i,i=Gd(n),n=r,r=g}this._a=r+this._a|0,this._b=n+this._b|0,this._c=i+this._c|0,this._d=s+this._d|0,this._e=c+this._e|0};Pn.prototype._hash=function(){var e=Wd.allocUnsafe(20);return e.writeInt32BE(this._a|0,0),e.writeInt32BE(this._b|0,4),e.writeInt32BE(this._c|0,8),e.writeInt32BE(this._d|0,12),e.writeInt32BE(this._e|0,16),e};var Zd=Pn,Kd=Rt,Cl=cn,Yd=Ut.Buffer,Qd=[1518500249,1859775393,-1894007588,-899497514],Xd=new Array(80);function Ln(){this.init(),this._w=Xd,Cl.call(this,64,56)}Kd(Ln,Cl);Ln.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this};function e0(e){return e<<1|e>>>31}function t0(e){return e<<5|e>>>27}function r0(e){return e<<30|e>>>2}function n0(e,t,r,n){return e===0?t&r|~t&n:e===2?t&r|t&n|r&n:t^r^n}Ln.prototype._update=function(e){for(var t=this._w,r=this._a|0,n=this._b|0,i=this._c|0,s=this._d|0,c=this._e|0,o=0;o<16;++o)t[o]=e.readInt32BE(o*4);for(;o<80;++o)t[o]=e0(t[o-3]^t[o-8]^t[o-14]^t[o-16]);for(var u=0;u<80;++u){var p=~~(u/20),g=t0(r)+n0(p,n,i,s)+c+t[u]+Qd[p]|0;c=s,s=i,i=r0(n),n=r,r=g}this._a=r+this._a|0,this._b=n+this._b|0,this._c=i+this._c|0,this._d=s+this._d|0,this._e=c+this._e|0};Ln.prototype._hash=function(){var e=Yd.allocUnsafe(20);return e.writeInt32BE(this._a|0,0),e.writeInt32BE(this._b|0,4),e.writeInt32BE(this._c|0,8),e.writeInt32BE(this._d|0,12),e.writeInt32BE(this._e|0,16),e};var i0=Ln,s0=Rt,Tl=cn,o0=Ut.Buffer,a0=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],c0=new Array(64);function On(){this.init(),this._w=c0,Tl.call(this,64,56)}s0(On,Tl);On.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this};function l0(e,t,r){return r^e&(t^r)}function u0(e,t,r){return e&t|r&(e|t)}function h0(e){return(e>>>2|e<<30)^(e>>>13|e<<19)^(e>>>22|e<<10)}function f0(e){return(e>>>6|e<<26)^(e>>>11|e<<21)^(e>>>25|e<<7)}function d0(e){return(e>>>7|e<<25)^(e>>>18|e<<14)^e>>>3}function p0(e){return(e>>>17|e<<15)^(e>>>19|e<<13)^e>>>10}On.prototype._update=function(e){for(var t=this._w,r=this._a|0,n=this._b|0,i=this._c|0,s=this._d|0,c=this._e|0,o=this._f|0,u=this._g|0,p=this._h|0,g=0;g<16;++g)t[g]=e.readInt32BE(g*4);for(;g<64;++g)t[g]=p0(t[g-2])+t[g-7]+d0(t[g-15])+t[g-16]|0;for(var w=0;w<64;++w){var _=p+f0(c)+l0(c,o,u)+a0[w]+t[w]|0,L=h0(r)+u0(r,n,i)|0;p=u,u=o,o=c,c=s+_|0,s=i,i=n,n=r,r=_+L|0}this._a=r+this._a|0,this._b=n+this._b|0,this._c=i+this._c|0,this._d=s+this._d|0,this._e=c+this._e|0,this._f=o+this._f|0,this._g=u+this._g|0,this._h=p+this._h|0};On.prototype._hash=function(){var e=o0.allocUnsafe(32);return e.writeInt32BE(this._a,0),e.writeInt32BE(this._b,4),e.writeInt32BE(this._c,8),e.writeInt32BE(this._d,12),e.writeInt32BE(this._e,16),e.writeInt32BE(this._f,20),e.writeInt32BE(this._g,24),e.writeInt32BE(this._h,28),e};var Bl=On,g0=Rt,y0=Bl,m0=cn,v0=Ut.Buffer,b0=new Array(64);function Pi(){this.init(),this._w=b0,m0.call(this,64,56)}g0(Pi,y0);Pi.prototype.init=function(){return this._a=3238371032,this._b=914150663,this._c=812702999,this._d=4144912697,this._e=4290775857,this._f=1750603025,this._g=1694076839,this._h=3204075428,this};Pi.prototype._hash=function(){var e=v0.allocUnsafe(28);return e.writeInt32BE(this._a,0),e.writeInt32BE(this._b,4),e.writeInt32BE(this._c,8),e.writeInt32BE(this._d,12),e.writeInt32BE(this._e,16),e.writeInt32BE(this._f,20),e.writeInt32BE(this._g,24),e};var w0=Pi,_0=Rt,Nl=cn,E0=Ut.Buffer,La=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],S0=new Array(160);function $n(){this.init(),this._w=S0,Nl.call(this,128,112)}_0($n,Nl);$n.prototype.init=function(){return this._ah=1779033703,this._bh=3144134277,this._ch=1013904242,this._dh=2773480762,this._eh=1359893119,this._fh=2600822924,this._gh=528734635,this._hh=1541459225,this._al=4089235720,this._bl=2227873595,this._cl=4271175723,this._dl=1595750129,this._el=2917565137,this._fl=725511199,this._gl=4215389547,this._hl=327033209,this};function Oa(e,t,r){return r^e&(t^r)}function $a(e,t,r){return e&t|r&(e|t)}function Fa(e,t){return(e>>>28|t<<4)^(t>>>2|e<<30)^(t>>>7|e<<25)}function Da(e,t){return(e>>>14|t<<18)^(e>>>18|t<<14)^(t>>>9|e<<23)}function R0(e,t){return(e>>>1|t<<31)^(e>>>8|t<<24)^e>>>7}function M0(e,t){return(e>>>1|t<<31)^(e>>>8|t<<24)^(e>>>7|t<<25)}function I0(e,t){return(e>>>19|t<<13)^(t>>>29|e<<3)^e>>>6}function A0(e,t){return(e>>>19|t<<13)^(t>>>29|e<<3)^(e>>>6|t<<26)}function Xe(e,t){return e>>>0<t>>>0?1:0}$n.prototype._update=function(e){for(var t=this._w,r=this._ah|0,n=this._bh|0,i=this._ch|0,s=this._dh|0,c=this._eh|0,o=this._fh|0,u=this._gh|0,p=this._hh|0,g=this._al|0,w=this._bl|0,_=this._cl|0,L=this._dl|0,j=this._el|0,$=this._fl|0,O=this._gl|0,A=this._hl|0,N=0;N<32;N+=2)t[N]=e.readInt32BE(N*4),t[N+1]=e.readInt32BE(N*4+4);for(;N<160;N+=2){var C=t[N-30],W=t[N-15*2+1],U=R0(C,W),z=M0(W,C);C=t[N-2*2],W=t[N-2*2+1];var te=I0(C,W),X=A0(W,C),Y=t[N-7*2],de=t[N-7*2+1],oe=t[N-16*2],pe=t[N-16*2+1],k=z+de|0,a=U+Y+Xe(k,z)|0;k=k+X|0,a=a+te+Xe(k,X)|0,k=k+pe|0,a=a+oe+Xe(k,pe)|0,t[N]=a,t[N+1]=k}for(var d=0;d<160;d+=2){a=t[d],k=t[d+1];var m=$a(r,n,i),v=$a(g,w,_),S=Fa(r,g),x=Fa(g,r),B=Da(c,j),b=Da(j,c),h=La[d],R=La[d+1],K=Oa(c,o,u),J=Oa(j,$,O),T=A+b|0,F=p+B+Xe(T,A)|0;T=T+J|0,F=F+K+Xe(T,J)|0,T=T+R|0,F=F+h+Xe(T,R)|0,T=T+k|0,F=F+a+Xe(T,k)|0;var q=x+v|0,Z=S+m+Xe(q,x)|0;p=u,A=O,u=o,O=$,o=c,$=j,j=L+T|0,c=s+F+Xe(j,L)|0,s=i,L=_,i=n,_=w,n=r,w=g,g=T+q|0,r=F+Z+Xe(g,T)|0}this._al=this._al+g|0,this._bl=this._bl+w|0,this._cl=this._cl+_|0,this._dl=this._dl+L|0,this._el=this._el+j|0,this._fl=this._fl+$|0,this._gl=this._gl+O|0,this._hl=this._hl+A|0,this._ah=this._ah+r+Xe(this._al,g)|0,this._bh=this._bh+n+Xe(this._bl,w)|0,this._ch=this._ch+i+Xe(this._cl,_)|0,this._dh=this._dh+s+Xe(this._dl,L)|0,this._eh=this._eh+c+Xe(this._el,j)|0,this._fh=this._fh+o+Xe(this._fl,$)|0,this._gh=this._gh+u+Xe(this._gl,O)|0,this._hh=this._hh+p+Xe(this._hl,A)|0};$n.prototype._hash=function(){var e=E0.allocUnsafe(64);function t(r,n,i){e.writeInt32BE(r,i),e.writeInt32BE(n,i+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),t(this._gh,this._gl,48),t(this._hh,this._hl,56),e};var Pl=$n,x0=Rt,k0=Pl,C0=cn,T0=Ut.Buffer,B0=new Array(160);function Li(){this.init(),this._w=B0,C0.call(this,128,112)}x0(Li,k0);Li.prototype.init=function(){return this._ah=3418070365,this._bh=1654270250,this._ch=2438529370,this._dh=355462360,this._eh=1731405415,this._fh=2394180231,this._gh=3675008525,this._hh=1203062813,this._al=3238371032,this._bl=914150663,this._cl=812702999,this._dl=4144912697,this._el=4290775857,this._fl=1750603025,this._gl=1694076839,this._hl=3204075428,this};Li.prototype._hash=function(){var e=T0.allocUnsafe(48);function t(r,n,i){e.writeInt32BE(r,i),e.writeInt32BE(n,i+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),e};var N0=Li;(function(e){e.exports=function(r){var n=r.toLowerCase(),i=e.exports[n];if(!i)throw new Error(n+" is not supported (we accept pull requests)");return new i},e.exports.sha=Zd,e.exports.sha1=i0,e.exports.sha224=w0,e.exports.sha256=Bl,e.exports.sha384=N0,e.exports.sha512=Pl})(dl);var P0=dl.exports;Object.defineProperty(an,"__esModule",{value:!0});an.Session=void 0;const ja=P0,Ua=Q,Ha="session:id",Wa="session:secret",qa="session:linked";class Lo{constructor(t,r,n,i){this._storage=t,this._id=r||(0,Ua.randomBytesHex)(16),this._secret=n||(0,Ua.randomBytesHex)(32),this._key=new ja.sha256().update(`${this._id}, ${this._secret} WalletLink`).digest("hex"),this._linked=!!i}static load(t){const r=t.getItem(Ha),n=t.getItem(qa),i=t.getItem(Wa);return r&&i?new Lo(t,r,i,n==="1"):null}static hash(t){return new ja.sha256().update(t).digest("hex")}get id(){return this._id}get secret(){return this._secret}get key(){return this._key}get linked(){return this._linked}set linked(t){this._linked=t,this.persistLinked()}save(){return this._storage.setItem(Ha,this._id),this._storage.setItem(Wa,this._secret),this.persistLinked(),this}persistLinked(){this._storage.setItem(qa,this._linked?"1":"0")}}an.Session=Lo;var Oi={},$i={};Object.defineProperty($i,"__esModule",{value:!0});$i.Cipher=void 0;const ei=Q;class L0{constructor(t){this.secret=t}async encrypt(t){const r=this.secret;if(r.length!==64)throw Error("secret must be 256 bits");const n=crypto.getRandomValues(new Uint8Array(12)),i=await crypto.subtle.importKey("raw",(0,ei.hexStringToUint8Array)(r),{name:"aes-gcm"},!1,["encrypt","decrypt"]),s=new TextEncoder,c=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:n},i,s.encode(t)),o=16,u=c.slice(c.byteLength-o),p=c.slice(0,c.byteLength-o),g=new Uint8Array(u),w=new Uint8Array(p),_=new Uint8Array([...n,...g,...w]);return(0,ei.uint8ArrayToHex)(_)}async decrypt(t){const r=this.secret;if(r.length!==64)throw Error("secret must be 256 bits");return new Promise((n,i)=>{(async function(){const s=await crypto.subtle.importKey("raw",(0,ei.hexStringToUint8Array)(r),{name:"aes-gcm"},!1,["encrypt","decrypt"]),c=(0,ei.hexStringToUint8Array)(t),o=c.slice(0,12),u=c.slice(12,28),p=c.slice(28),g=new Uint8Array([...p,...u]),w={name:"AES-GCM",iv:new Uint8Array(o)};try{const _=await window.crypto.subtle.decrypt(w,s,g),L=new TextDecoder;n(L.decode(_))}catch(_){i(_)}})()})}}$i.Cipher=L0;var Fi={};Object.defineProperty(Fi,"__esModule",{value:!0});Fi.WalletLinkHTTP=void 0;class O0{constructor(t,r,n){this.linkAPIUrl=t,this.sessionId=r;const i=`${r}:${n}`;this.auth=`Basic ${btoa(i)}`}async markUnseenEventsAsSeen(t){return Promise.all(t.map(r=>fetch(`${this.linkAPIUrl}/events/${r.eventId}/seen`,{method:"POST",headers:{Authorization:this.auth}}))).catch(r=>console.error("Unabled to mark event as failed:",r))}async fetchUnseenEvents(){var t;const r=await fetch(`${this.linkAPIUrl}/events?unseen=true`,{headers:{Authorization:this.auth}});if(r.ok){const{events:n,error:i}=await r.json();if(i)throw new Error(`Check unseen events failed: ${i}`);const s=(t=n==null?void 0:n.filter(c=>c.event==="Web3Response").map(c=>({type:"Event",sessionId:this.sessionId,eventId:c.id,event:c.event,data:c.data})))!==null&&t!==void 0?t:[];return this.markUnseenEventsAsSeen(s),s}throw new Error(`Check unseen events failed: ${r.status}`)}}Fi.WalletLinkHTTP=O0;var Kr={};Object.defineProperty(Kr,"__esModule",{value:!0});Kr.WalletLinkWebSocket=Kr.ConnectionState=void 0;var Hr;(function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED"})(Hr||(Kr.ConnectionState=Hr={}));class $0{setConnectionStateListener(t){this.connectionStateListener=t}setIncomingDataListener(t){this.incomingDataListener=t}constructor(t,r=WebSocket){this.WebSocketClass=r,this.webSocket=null,this.pendingData=[],this.url=t.replace(/^http/,"ws")}async connect(){if(this.webSocket)throw new Error("webSocket object is not null");return new Promise((t,r)=>{var n;let i;try{this.webSocket=i=new this.WebSocketClass(this.url)}catch(s){r(s);return}(n=this.connectionStateListener)===null||n===void 0||n.call(this,Hr.CONNECTING),i.onclose=s=>{var c;this.clearWebSocket(),r(new Error(`websocket error ${s.code}: ${s.reason}`)),(c=this.connectionStateListener)===null||c===void 0||c.call(this,Hr.DISCONNECTED)},i.onopen=s=>{var c;t(),(c=this.connectionStateListener)===null||c===void 0||c.call(this,Hr.CONNECTED),this.pendingData.length>0&&([...this.pendingData].forEach(u=>this.sendData(u)),this.pendingData=[])},i.onmessage=s=>{var c,o;if(s.data==="h")(c=this.incomingDataListener)===null||c===void 0||c.call(this,{type:"Heartbeat"});else try{const u=JSON.parse(s.data);(o=this.incomingDataListener)===null||o===void 0||o.call(this,u)}catch{}}})}disconnect(){var t;const{webSocket:r}=this;if(r){this.clearWebSocket(),(t=this.connectionStateListener)===null||t===void 0||t.call(this,Hr.DISCONNECTED),this.connectionStateListener=void 0,this.incomingDataListener=void 0;try{r.close()}catch{}}}sendData(t){const{webSocket:r}=this;if(!r){this.pendingData.push(t),this.connect();return}r.send(t)}clearWebSocket(){const{webSocket:t}=this;t&&(this.webSocket=null,t.onclose=null,t.onerror=null,t.onmessage=null,t.onopen=null)}}Kr.WalletLinkWebSocket=$0;Object.defineProperty(Oi,"__esModule",{value:!0});Oi.WalletLinkConnection=void 0;const Lr=Ke,F0=$i,It=on,Va=vt,Or=an,D0=Fi,ti=Kr,za=1e4,j0=6e4;class U0{constructor({session:t,linkAPIUrl:r,listener:n,diagnostic:i,WebSocketClass:s=WebSocket}){this.destroyed=!1,this.lastHeartbeatResponse=0,this.nextReqId=(0,Lr.IntNumber)(1),this._connected=!1,this._linked=!1,this.shouldFetchUnseenEventsOnConnect=!1,this.requestResolutions=new Map,this.handleSessionMetadataUpdated=o=>{if(!o)return;new Map([["__destroyed",this.handleDestroyed],["EthereumAddress",this.handleAccountUpdated],["WalletUsername",this.handleWalletUsernameUpdated],["AppVersion",this.handleAppVersionUpdated],["ChainId",p=>o.JsonRpcUrl&&this.handleChainUpdated(p,o.JsonRpcUrl)]]).forEach((p,g)=>{const w=o[g];w!==void 0&&p(w)})},this.handleDestroyed=o=>{var u,p;o==="1"&&((u=this.listener)===null||u===void 0||u.resetAndReload(),(p=this.diagnostic)===null||p===void 0||p.log(It.EVENTS.METADATA_DESTROYED,{alreadyDestroyed:this.isDestroyed,sessionIdHash:Or.Session.hash(this.session.id)}))},this.handleAccountUpdated=async o=>{var u,p;try{const g=await this.cipher.decrypt(o);(u=this.listener)===null||u===void 0||u.accountUpdated(g)}catch{(p=this.diagnostic)===null||p===void 0||p.log(It.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"selectedAddress"})}},this.handleMetadataUpdated=async(o,u)=>{var p,g;try{const w=await this.cipher.decrypt(u);(p=this.listener)===null||p===void 0||p.metadataUpdated(o,w)}catch{(g=this.diagnostic)===null||g===void 0||g.log(It.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:o})}},this.handleWalletUsernameUpdated=async o=>{this.handleMetadataUpdated(Va.WALLET_USER_NAME_KEY,o)},this.handleAppVersionUpdated=async o=>{this.handleMetadataUpdated(Va.APP_VERSION_KEY,o)},this.handleChainUpdated=async(o,u)=>{var p,g;try{const w=await this.cipher.decrypt(o),_=await this.cipher.decrypt(u);(p=this.listener)===null||p===void 0||p.chainUpdated(w,_)}catch{(g=this.diagnostic)===null||g===void 0||g.log(It.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"chainId|jsonRpcUrl"})}},this.session=t,this.cipher=new F0.Cipher(t.secret),this.diagnostic=i,this.listener=n;const c=new ti.WalletLinkWebSocket(`${r}/rpc`,s);c.setConnectionStateListener(async o=>{var u;(u=this.diagnostic)===null||u===void 0||u.log(It.EVENTS.CONNECTED_STATE_CHANGE,{state:o,sessionIdHash:Or.Session.hash(t.id)});let p=!1;switch(o){case ti.ConnectionState.DISCONNECTED:if(!this.destroyed){const g=async()=>{await new Promise(w=>setTimeout(w,5e3)),this.destroyed||c.connect().catch(()=>{g()})};g()}break;case ti.ConnectionState.CONNECTED:try{await this.authenticate(),this.sendIsLinked(),this.sendGetSessionConfig(),p=!0}catch{}this.updateLastHeartbeat(),setInterval(()=>{this.heartbeat()},za),this.shouldFetchUnseenEventsOnConnect&&this.fetchUnseenEventsAPI();break;case ti.ConnectionState.CONNECTING:break}this.connected!==p&&(this.connected=p)}),c.setIncomingDataListener(o=>{var u,p,g;switch(o.type){case"Heartbeat":this.updateLastHeartbeat();return;case"IsLinkedOK":case"Linked":{const w=o.type==="IsLinkedOK"?o.linked:void 0;(u=this.diagnostic)===null||u===void 0||u.log(It.EVENTS.LINKED,{sessionIdHash:Or.Session.hash(t.id),linked:w,type:o.type,onlineGuests:o.onlineGuests}),this.linked=w||o.onlineGuests>0;break}case"GetSessionConfigOK":case"SessionConfigUpdated":{(p=this.diagnostic)===null||p===void 0||p.log(It.EVENTS.SESSION_CONFIG_RECEIVED,{sessionIdHash:Or.Session.hash(t.id),metadata_keys:o&&o.metadata?Object.keys(o.metadata):void 0}),this.handleSessionMetadataUpdated(o.metadata);break}case"Event":{this.handleIncomingEvent(o);break}}o.id!==void 0&&((g=this.requestResolutions.get(o.id))===null||g===void 0||g(o))}),this.ws=c,this.http=new D0.WalletLinkHTTP(r,t.id,t.key)}connect(){var t;if(this.destroyed)throw new Error("instance is destroyed");(t=this.diagnostic)===null||t===void 0||t.log(It.EVENTS.STARTED_CONNECTING,{sessionIdHash:Or.Session.hash(this.session.id)}),this.ws.connect()}destroy(){var t;this.destroyed=!0,this.ws.disconnect(),(t=this.diagnostic)===null||t===void 0||t.log(It.EVENTS.DISCONNECTED,{sessionIdHash:Or.Session.hash(this.session.id)}),this.listener=void 0}get isDestroyed(){return this.destroyed}get connected(){return this._connected}set connected(t){var r,n;this._connected=t,t&&((r=this.onceConnected)===null||r===void 0||r.call(this)),(n=this.listener)===null||n===void 0||n.connectedUpdated(t)}setOnceConnected(t){return new Promise(r=>{this.connected?t().then(r):this.onceConnected=()=>{t().then(r),this.onceConnected=void 0}})}get linked(){return this._linked}set linked(t){var r,n;this._linked=t,t&&((r=this.onceLinked)===null||r===void 0||r.call(this)),(n=this.listener)===null||n===void 0||n.linkedUpdated(t)}setOnceLinked(t){return new Promise(r=>{this.linked?t().then(r):this.onceLinked=()=>{t().then(r),this.onceLinked=void 0}})}async handleIncomingEvent(t){var r,n;if(!(t.type!=="Event"||t.event!=="Web3Response"))try{const i=await this.cipher.decrypt(t.data),s=JSON.parse(i);if(s.type!=="WEB3_RESPONSE")return;(r=this.listener)===null||r===void 0||r.handleWeb3ResponseMessage(s)}catch{(n=this.diagnostic)===null||n===void 0||n.log(It.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"incomingEvent"})}}async checkUnseenEvents(){if(!this.connected){this.shouldFetchUnseenEventsOnConnect=!0;return}await new Promise(t=>setTimeout(t,250));try{await this.fetchUnseenEventsAPI()}catch(t){console.error("Unable to check for unseen events",t)}}async fetchUnseenEventsAPI(){this.shouldFetchUnseenEventsOnConnect=!1,(await this.http.fetchUnseenEvents()).forEach(r=>this.handleIncomingEvent(r))}async setSessionMetadata(t,r){const n={type:"SetSessionConfig",id:(0,Lr.IntNumber)(this.nextReqId++),sessionId:this.session.id,metadata:{[t]:r}};return this.setOnceConnected(async()=>{const i=await this.makeRequest(n);if(i.type==="Fail")throw new Error(i.error||"failed to set session metadata")})}async publishEvent(t,r,n=!1){const i=await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({},r),{origin:location.origin,relaySource:window.coinbaseWalletExtension?"injected_sdk":"sdk"}))),s={type:"PublishEvent",id:(0,Lr.IntNumber)(this.nextReqId++),sessionId:this.session.id,event:t,data:i,callWebhook:n};return this.setOnceLinked(async()=>{const c=await this.makeRequest(s);if(c.type==="Fail")throw new Error(c.error||"failed to publish event");return c.eventId})}sendData(t){this.ws.sendData(JSON.stringify(t))}updateLastHeartbeat(){this.lastHeartbeatResponse=Date.now()}heartbeat(){if(Date.now()-this.lastHeartbeatResponse>za*2){this.ws.disconnect();return}try{this.ws.sendData("h")}catch{}}async makeRequest(t,r=j0){const n=t.id;this.sendData(t);let i;return Promise.race([new Promise((s,c)=>{i=window.setTimeout(()=>{c(new Error(`request ${n} timed out`))},r)}),new Promise(s=>{this.requestResolutions.set(n,c=>{clearTimeout(i),s(c),this.requestResolutions.delete(n)})})])}async authenticate(){const t={type:"HostSession",id:(0,Lr.IntNumber)(this.nextReqId++),sessionId:this.session.id,sessionKey:this.session.key},r=await this.makeRequest(t);if(r.type==="Fail")throw new Error(r.error||"failed to authentcate")}sendIsLinked(){const t={type:"IsLinked",id:(0,Lr.IntNumber)(this.nextReqId++),sessionId:this.session.id};this.sendData(t)}sendGetSessionConfig(){const t={type:"GetSessionConfig",id:(0,Lr.IntNumber)(this.nextReqId++),sessionId:this.session.id};this.sendData(t)}}Oi.WalletLinkConnection=U0;var Fn={},Dn={},Oo={};Object.defineProperty(Oo,"__esModule",{value:!0});Oo.default='@namespace svg "http://www.w3.org/2000/svg";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:"\\201C" "\\201D" "\\2018" "\\2019";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}';var H0=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Dn,"__esModule",{value:!0});Dn.injectCssReset=void 0;const W0=H0(Oo);function q0(){const e=document.createElement("style");e.type="text/css",e.appendChild(document.createTextNode(W0.default)),document.documentElement.appendChild(e)}Dn.injectCssReset=q0;var Di={};const ct=rn(gh);var ji={};function Ll(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=Ll(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function Ga(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=Ll(e))&&(n&&(n+=" "),n+=t);return n}const V0=Object.freeze(Object.defineProperty({__proto__:null,clsx:Ga,default:Ga},Symbol.toStringTag,{value:"Module"})),jn=rn(V0),Ui=rn(yh);var Yr={},Hi={};Object.defineProperty(Hi,"__esModule",{value:!0});Hi.CloseIcon=void 0;const Ja=ct;function z0(e){return(0,Ja.h)("svg",Object.assign({width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,Ja.h)("path",{d:"M13.7677 13L12.3535 14.4142L18.3535 20.4142L12.3535 26.4142L13.7677 27.8284L19.7677 21.8284L25.7677 27.8284L27.1819 26.4142L21.1819 20.4142L27.1819 14.4142L25.7677 13L19.7677 19L13.7677 13Z"}))}Hi.CloseIcon=z0;var Wi={};Object.defineProperty(Wi,"__esModule",{value:!0});Wi.CoinbaseWalletRound=void 0;const ri=ct;function G0(e){return(0,ri.h)("svg",Object.assign({width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,ri.h)("circle",{cx:"14",cy:"14",r:"14",fill:"#0052FF"}),(0,ri.h)("path",{d:"M23.8521 14.0003C23.8521 19.455 19.455 23.8521 14.0003 23.8521C8.54559 23.8521 4.14844 19.455 4.14844 14.0003C4.14844 8.54559 8.54559 4.14844 14.0003 4.14844C19.455 4.14844 23.8521 8.54559 23.8521 14.0003Z",fill:"white"}),(0,ri.h)("path",{d:"M11.1855 12.5042C11.1855 12.0477 11.1855 11.7942 11.2835 11.642C11.3814 11.4899 11.4793 11.3377 11.6261 11.287C11.8219 11.1855 12.0178 11.1855 12.5073 11.1855H15.4934C15.983 11.1855 16.1788 11.1855 16.3746 11.287C16.5215 11.3884 16.6683 11.4899 16.7173 11.642C16.8152 11.8449 16.8152 12.0477 16.8152 12.5042V15.4965C16.8152 15.953 16.8152 16.2066 16.7173 16.3587C16.6194 16.5109 16.5215 16.663 16.3746 16.7137C16.1788 16.8152 15.983 16.8152 15.4934 16.8152H12.5073C12.0178 16.8152 11.8219 16.8152 11.6261 16.7137C11.4793 16.6123 11.3324 16.5109 11.2835 16.3587C11.1855 16.1558 11.1855 15.953 11.1855 15.4965V12.5042Z",fill:"#0052FF"}))}Wi.CoinbaseWalletRound=G0;var qi={};Object.defineProperty(qi,"__esModule",{value:!0});qi.QRCodeIcon=void 0;const dn=ct;function J0(e){return(0,dn.h)("svg",Object.assign({width:"18",height:"18",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),(0,dn.h)("path",{d:"M3 3V8.99939L5 8.99996V5H9V3H3Z"}),(0,dn.h)("path",{d:"M15 21L21 21V15.0006L19 15V19L15 19V21Z"}),(0,dn.h)("path",{d:"M21 9H19V5H15.0006L15 3H21V9Z"}),(0,dn.h)("path",{d:"M3 15V21H8.99939L8.99996 19H5L5 15H3Z"}))}qi.QRCodeIcon=J0;var Vi={};function Ol(e){this.mode=ht.MODE_8BIT_BYTE,this.data=e,this.parsedData=[];for(var t=0,r=this.data.length;t<r;t++){var n=[],i=this.data.charCodeAt(t);i>65536?(n[0]=240|(i&1835008)>>>18,n[1]=128|(i&258048)>>>12,n[2]=128|(i&4032)>>>6,n[3]=128|i&63):i>2048?(n[0]=224|(i&61440)>>>12,n[1]=128|(i&4032)>>>6,n[2]=128|i&63):i>128?(n[0]=192|(i&1984)>>>6,n[1]=128|i&63):n[0]=i,this.parsedData.push(n)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}Ol.prototype={getLength:function(e){return this.parsedData.length},write:function(e){for(var t=0,r=this.parsedData.length;t<r;t++)e.put(this.parsedData[t],8)}};function Pt(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}Pt.prototype={addData:function(e){var t=new Ol(e);this.dataList.push(t),this.dataCache=null},isDark:function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,t){this.moduleCount=this.typeNumber*4+17,this.modules=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[r][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),this.dataCache==null&&(this.dataCache=Pt.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(e,t){for(var r=-1;r<=7;r++)if(!(e+r<=-1||this.moduleCount<=e+r))for(var n=-1;n<=7;n++)t+n<=-1||this.moduleCount<=t+n||(0<=r&&r<=6&&(n==0||n==6)||0<=n&&n<=6&&(r==0||r==6)||2<=r&&r<=4&&2<=n&&n<=4?this.modules[e+r][t+n]=!0:this.modules[e+r][t+n]=!1)},getBestMaskPattern:function(){for(var e=0,t=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=Ve.getLostPoint(this);(r==0||e>n)&&(e=n,t=r)}return t},createMovieClip:function(e,t,r){var n=e.createEmptyMovieClip(t,r),i=1;this.make();for(var s=0;s<this.modules.length;s++)for(var c=s*i,o=0;o<this.modules[s].length;o++){var u=o*i,p=this.modules[s][o];p&&(n.beginFill(0,100),n.moveTo(u,c),n.lineTo(u+i,c),n.lineTo(u+i,c+i),n.lineTo(u,c+i),n.endFill())}return n},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)this.modules[e][6]==null&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)this.modules[6][t]==null&&(this.modules[6][t]=t%2==0)},setupPositionAdjustPattern:function(){for(var e=Ve.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var r=0;r<e.length;r++){var n=e[t],i=e[r];if(this.modules[n][i]==null)for(var s=-2;s<=2;s++)for(var c=-2;c<=2;c++)s==-2||s==2||c==-2||c==2||s==0&&c==0?this.modules[n+s][i+c]=!0:this.modules[n+s][i+c]=!1}},setupTypeNumber:function(e){for(var t=Ve.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!e&&(t>>r&1)==1;this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(var r=0;r<18;r++){var n=!e&&(t>>r&1)==1;this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n}},setupTypeInfo:function(e,t){for(var r=this.errorCorrectLevel<<3|t,n=Ve.getBCHTypeInfo(r),i=0;i<15;i++){var s=!e&&(n>>i&1)==1;i<6?this.modules[i][8]=s:i<8?this.modules[i+1][8]=s:this.modules[this.moduleCount-15+i][8]=s}for(var i=0;i<15;i++){var s=!e&&(n>>i&1)==1;i<8?this.modules[8][this.moduleCount-i-1]=s:i<9?this.modules[8][15-i-1+1]=s:this.modules[8][15-i-1]=s}this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var r=-1,n=this.moduleCount-1,i=7,s=0,c=this.moduleCount-1;c>0;c-=2)for(c==6&&c--;;){for(var o=0;o<2;o++)if(this.modules[n][c-o]==null){var u=!1;s<e.length&&(u=(e[s]>>>i&1)==1);var p=Ve.getMask(t,n,c-o);p&&(u=!u),this.modules[n][c-o]=u,i--,i==-1&&(s++,i=7)}if(n+=r,n<0||this.moduleCount<=n){n-=r,r=-r;break}}}};Pt.PAD0=236;Pt.PAD1=17;Pt.createData=function(e,t,r){for(var n=Nt.getRSBlocks(e,t),i=new $l,s=0;s<r.length;s++){var c=r[s];i.put(c.mode,4),i.put(c.getLength(),Ve.getLengthInBits(c.mode,e)),c.write(i)}for(var o=0,s=0;s<n.length;s++)o+=n[s].dataCount;if(i.getLengthInBits()>o*8)throw new Error("code length overflow. ("+i.getLengthInBits()+">"+o*8+")");for(i.getLengthInBits()+4<=o*8&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=o*8||(i.put(Pt.PAD0,8),i.getLengthInBits()>=o*8));)i.put(Pt.PAD1,8);return Pt.createBytes(i,n)};Pt.createBytes=function(e,t){for(var r=0,n=0,i=0,s=new Array(t.length),c=new Array(t.length),o=0;o<t.length;o++){var u=t[o].dataCount,p=t[o].totalCount-u;n=Math.max(n,u),i=Math.max(i,p),s[o]=new Array(u);for(var g=0;g<s[o].length;g++)s[o][g]=255&e.buffer[g+r];r+=u;var w=Ve.getErrorCorrectPolynomial(p),_=new qr(s[o],w.getLength()-1),L=_.mod(w);c[o]=new Array(w.getLength()-1);for(var g=0;g<c[o].length;g++){var j=g+L.getLength()-c[o].length;c[o][g]=j>=0?L.get(j):0}}for(var $=0,g=0;g<t.length;g++)$+=t[g].totalCount;for(var O=new Array($),A=0,g=0;g<n;g++)for(var o=0;o<t.length;o++)g<s[o].length&&(O[A++]=s[o][g]);for(var g=0;g<i;g++)for(var o=0;o<t.length;o++)g<c[o].length&&(O[A++]=c[o][g]);return O};var ht={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},Vt={L:1,M:0,Q:3,H:2},Ht={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},Ve={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;Ve.getBCHDigit(t)-Ve.getBCHDigit(Ve.G15)>=0;)t^=Ve.G15<<Ve.getBCHDigit(t)-Ve.getBCHDigit(Ve.G15);return(e<<10|t)^Ve.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;Ve.getBCHDigit(t)-Ve.getBCHDigit(Ve.G18)>=0;)t^=Ve.G18<<Ve.getBCHDigit(t)-Ve.getBCHDigit(Ve.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;e!=0;)t++,e>>>=1;return t},getPatternPosition:function(e){return Ve.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,r){switch(e){case Ht.PATTERN000:return(t+r)%2==0;case Ht.PATTERN001:return t%2==0;case Ht.PATTERN010:return r%3==0;case Ht.PATTERN011:return(t+r)%3==0;case Ht.PATTERN100:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case Ht.PATTERN101:return t*r%2+t*r%3==0;case Ht.PATTERN110:return(t*r%2+t*r%3)%2==0;case Ht.PATTERN111:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new qr([1],0),r=0;r<e;r++)t=t.multiply(new qr([1,tt.gexp(r)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case ht.MODE_NUMBER:return 10;case ht.MODE_ALPHA_NUM:return 9;case ht.MODE_8BIT_BYTE:return 8;case ht.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case ht.MODE_NUMBER:return 12;case ht.MODE_ALPHA_NUM:return 11;case ht.MODE_8BIT_BYTE:return 16;case ht.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else if(t<41)switch(e){case ht.MODE_NUMBER:return 14;case ht.MODE_ALPHA_NUM:return 13;case ht.MODE_8BIT_BYTE:return 16;case ht.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}else throw new Error("type:"+t)},getLostPoint:function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n++)for(var i=0;i<t;i++){for(var s=0,c=e.isDark(n,i),o=-1;o<=1;o++)if(!(n+o<0||t<=n+o))for(var u=-1;u<=1;u++)i+u<0||t<=i+u||o==0&&u==0||c==e.isDark(n+o,i+u)&&s++;s>5&&(r+=3+s-5)}for(var n=0;n<t-1;n++)for(var i=0;i<t-1;i++){var p=0;e.isDark(n,i)&&p++,e.isDark(n+1,i)&&p++,e.isDark(n,i+1)&&p++,e.isDark(n+1,i+1)&&p++,(p==0||p==4)&&(r+=3)}for(var n=0;n<t;n++)for(var i=0;i<t-6;i++)e.isDark(n,i)&&!e.isDark(n,i+1)&&e.isDark(n,i+2)&&e.isDark(n,i+3)&&e.isDark(n,i+4)&&!e.isDark(n,i+5)&&e.isDark(n,i+6)&&(r+=40);for(var i=0;i<t;i++)for(var n=0;n<t-6;n++)e.isDark(n,i)&&!e.isDark(n+1,i)&&e.isDark(n+2,i)&&e.isDark(n+3,i)&&e.isDark(n+4,i)&&!e.isDark(n+5,i)&&e.isDark(n+6,i)&&(r+=40);for(var g=0,i=0;i<t;i++)for(var n=0;n<t;n++)e.isDark(n,i)&&g++;var w=Math.abs(100*g/t/t-50)/5;return r+=w*10,r}},tt={glog:function(e){if(e<1)throw new Error("glog("+e+")");return tt.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return tt.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(var nt=0;nt<8;nt++)tt.EXP_TABLE[nt]=1<<nt;for(var nt=8;nt<256;nt++)tt.EXP_TABLE[nt]=tt.EXP_TABLE[nt-4]^tt.EXP_TABLE[nt-5]^tt.EXP_TABLE[nt-6]^tt.EXP_TABLE[nt-8];for(var nt=0;nt<255;nt++)tt.LOG_TABLE[tt.EXP_TABLE[nt]]=nt;function qr(e,t){if(e.length==null)throw new Error(e.length+"/"+t);for(var r=0;r<e.length&&e[r]==0;)r++;this.num=new Array(e.length-r+t);for(var n=0;n<e.length-r;n++)this.num[n]=e[n+r]}qr.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),r=0;r<this.getLength();r++)for(var n=0;n<e.getLength();n++)t[r+n]^=tt.gexp(tt.glog(this.get(r))+tt.glog(e.get(n)));return new qr(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=tt.glog(this.get(0))-tt.glog(e.get(0)),r=new Array(this.getLength()),n=0;n<this.getLength();n++)r[n]=this.get(n);for(var n=0;n<e.getLength();n++)r[n]^=tt.gexp(tt.glog(e.get(n))+t);return new qr(r,0).mod(e)}};function Nt(e,t){this.totalCount=e,this.dataCount=t}Nt.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];Nt.getRSBlocks=function(e,t){var r=Nt.getRsBlockTable(e,t);if(r==null)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var n=r.length/3,i=[],s=0;s<n;s++)for(var c=r[s*3+0],o=r[s*3+1],u=r[s*3+2],p=0;p<c;p++)i.push(new Nt(o,u));return i};Nt.getRsBlockTable=function(e,t){switch(t){case Vt.L:return Nt.RS_BLOCK_TABLE[(e-1)*4+0];case Vt.M:return Nt.RS_BLOCK_TABLE[(e-1)*4+1];case Vt.Q:return Nt.RS_BLOCK_TABLE[(e-1)*4+2];case Vt.H:return Nt.RS_BLOCK_TABLE[(e-1)*4+3];default:return}};function $l(){this.buffer=[],this.length=0}$l.prototype={get:function(e){var t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(var r=0;r<t;r++)this.putBit((e>>>t-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var zs=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function Fl(e){if(this.options={padding:4,width:256,height:256,typeNumber:4,color:"#000000",background:"#ffffff",ecl:"M",image:{svg:"",width:0,height:0}},typeof e=="string"&&(e={content:e}),e)for(var t in e)this.options[t]=e[t];if(typeof this.options.content!="string")throw new Error("Expected 'content' as string!");if(this.options.content.length===0)throw new Error("Expected 'content' to be non-empty!");if(!(this.options.padding>=0))throw new Error("Expected 'padding' value to be non-negative!");if(!(this.options.width>0)||!(this.options.height>0))throw new Error("Expected 'width' or 'height' value to be higher than zero!");function r(u){switch(u){case"L":return Vt.L;case"M":return Vt.M;case"Q":return Vt.Q;case"H":return Vt.H;default:throw new Error("Unknwon error correction level: "+u)}}function n(u,p){for(var g=i(u),w=1,_=0,L=0,j=zs.length;L<=j;L++){var $=zs[L];if(!$)throw new Error("Content too long: expected "+_+" but got "+g);switch(p){case"L":_=$[0];break;case"M":_=$[1];break;case"Q":_=$[2];break;case"H":_=$[3];break;default:throw new Error("Unknwon error correction level: "+p)}if(g<=_)break;w++}if(w>zs.length)throw new Error("Content too long");return w}function i(u){var p=encodeURI(u).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return p.length+(p.length!=u?3:0)}var s=this.options.content,c=n(s,this.options.ecl),o=r(this.options.ecl);this.qrcode=new Pt(c,o),this.qrcode.addData(s),this.qrcode.make()}Fl.prototype.svg=function(e){var t=this.options||{},r=this.qrcode.modules;typeof e>"u"&&(e={container:t.container||"svg"});for(var n=typeof t.pretty<"u"?!!t.pretty:!0,i=n?"  ":"",s=n?`\r
`:"",c=t.width,o=t.height,u=r.length,p=c/(u+2*t.padding),g=o/(u+2*t.padding),w=typeof t.join<"u"?!!t.join:!1,_=typeof t.swap<"u"?!!t.swap:!1,L=typeof t.xmlDeclaration<"u"?!!t.xmlDeclaration:!0,j=typeof t.predefined<"u"?!!t.predefined:!1,$=j?i+'<defs><path id="qrmodule" d="M0 0 h'+g+" v"+p+' H0 z" style="fill:'+t.color+';shape-rendering:crispEdges;" /></defs>'+s:"",O=i+'<rect x="0" y="0" width="'+c+'" height="'+o+'" style="fill:'+t.background+';shape-rendering:crispEdges;"/>'+s,A="",N="",C=0;C<u;C++)for(var W=0;W<u;W++){var U=r[W][C];if(U){var z=W*p+t.padding*p,te=C*g+t.padding*g;if(_){var X=z;z=te,te=X}if(w){var Y=p+z,de=g+te;z=Number.isInteger(z)?Number(z):z.toFixed(2),te=Number.isInteger(te)?Number(te):te.toFixed(2),Y=Number.isInteger(Y)?Number(Y):Y.toFixed(2),de=Number.isInteger(de)?Number(de):de.toFixed(2),N+="M"+z+","+te+" V"+de+" H"+Y+" V"+te+" H"+z+" Z "}else j?A+=i+'<use x="'+z.toString()+'" y="'+te.toString()+'" href="#qrmodule" />'+s:A+=i+'<rect x="'+z.toString()+'" y="'+te.toString()+'" width="'+p+'" height="'+g+'" style="fill:'+t.color+';shape-rendering:crispEdges;"/>'+s}}w&&(A=i+'<path x="0" y="0" style="fill:'+t.color+';shape-rendering:crispEdges;" d="'+N+'" />');let oe="";if(this.options.image!==void 0&&this.options.image.svg){const k=c*this.options.image.width/100,a=o*this.options.image.height/100,d=c/2-k/2,m=o/2-a/2;oe+=`<svg x="${d}" y="${m}" width="${k}" height="${a}" viewBox="0 0 100 100" preserveAspectRatio="xMinYMin meet">`,oe+=this.options.image.svg+s,oe+="</svg>"}var pe="";switch(e.container){case"svg":L&&(pe+='<?xml version="1.0" standalone="yes"?>'+s),pe+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="'+c+'" height="'+o+'">'+s,pe+=$+O+A,pe+=oe,pe+="</svg>";break;case"svg-viewbox":L&&(pe+='<?xml version="1.0" standalone="yes"?>'+s),pe+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 '+c+" "+o+'">'+s,pe+=$+O+A,pe+=oe,pe+="</svg>";break;case"g":pe+='<g width="'+c+'" height="'+o+'">'+s,pe+=$+O+A,pe+=oe,pe+="</g>";break;default:pe+=($+O+A+oe).replace(/^\s+/,"");break}return pe};var Z0=Fl,K0=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Vi,"__esModule",{value:!0});Vi.QRCode=void 0;const Y0=ct,Za=Ui,Q0=K0(Z0),X0=e=>{const[t,r]=(0,Za.useState)("");return(0,Za.useEffect)(()=>{var n,i;const s=new Q0.default({content:e.content,background:e.bgColor||"#ffffff",color:e.fgColor||"#000000",container:"svg",ecl:"M",width:(n=e.width)!==null&&n!==void 0?n:256,height:(i=e.height)!==null&&i!==void 0?i:256,padding:0,image:e.image}),c=Buffer.from(s.svg(),"utf8").toString("base64");r(`data:image/svg+xml;base64,${c}`)},[e.bgColor,e.content,e.fgColor,e.height,e.image,e.width]),t?(0,Y0.h)("img",{src:t,alt:"QR Code"}):null};Vi.QRCode=X0;var zi={},$o={};Object.defineProperty($o,"__esModule",{value:!0});$o.default=".-cbwsdk-css-reset .-cbwsdk-spinner{display:inline-block}.-cbwsdk-css-reset .-cbwsdk-spinner svg{display:inline-block;animation:2s linear infinite -cbwsdk-spinner-svg}.-cbwsdk-css-reset .-cbwsdk-spinner svg circle{animation:1.9s ease-in-out infinite both -cbwsdk-spinner-circle;display:block;fill:rgba(0,0,0,0);stroke-dasharray:283;stroke-dashoffset:280;stroke-linecap:round;stroke-width:10px;transform-origin:50% 50%}@keyframes -cbwsdk-spinner-svg{0%{transform:rotateZ(0deg)}100%{transform:rotateZ(360deg)}}@keyframes -cbwsdk-spinner-circle{0%,25%{stroke-dashoffset:280;transform:rotate(0)}50%,75%{stroke-dashoffset:75;transform:rotate(45deg)}100%{stroke-dashoffset:280;transform:rotate(360deg)}}";var ep=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(zi,"__esModule",{value:!0});zi.Spinner=void 0;const ni=ct,tp=ep($o),rp=e=>{var t;const r=(t=e.size)!==null&&t!==void 0?t:64,n=e.color||"#000";return(0,ni.h)("div",{class:"-cbwsdk-spinner"},(0,ni.h)("style",null,tp.default),(0,ni.h)("svg",{viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",style:{width:r,height:r}},(0,ni.h)("circle",{style:{cx:50,cy:50,r:45,stroke:n}})))};zi.Spinner=rp;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0});Fo.default=".-cbwsdk-css-reset .-cbwsdk-connect-content{height:430px;width:700px;border-radius:12px;padding:30px}.-cbwsdk-css-reset .-cbwsdk-connect-content.light{background:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content.dark{background:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-header{display:flex;align-items:center;justify-content:space-between;margin:0 0 30px}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading{font-style:normal;font-weight:500;font-size:28px;line-height:36px;margin:0}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-layout{display:flex;flex-direction:row}.-cbwsdk-css-reset .-cbwsdk-connect-content-column-left{margin-right:30px;display:flex;flex-direction:column;justify-content:space-between}.-cbwsdk-css-reset .-cbwsdk-connect-content-column-right{flex:25%;margin-right:34px}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-wrapper{width:220px;height:220px;border-radius:12px;display:flex;justify-content:center;align-items:center;background:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting{position:absolute;top:0;bottom:0;left:0;right:0;display:flex;flex-direction:column;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.light{background-color:rgba(255,255,255,.95)}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.light>p{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.dark{background-color:rgba(10,11,13,.9)}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.dark>p{color:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting>p{font-size:12px;font-weight:bold;margin-top:16px}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app{border-radius:8px;font-size:14px;line-height:20px;padding:12px;width:339px}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app.light{background:#eef0f3;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app.dark{background:#1e2025;color:#8a919e}.-cbwsdk-css-reset .-cbwsdk-cancel-button{-webkit-appearance:none;border:none;background:none;cursor:pointer;padding:0;margin:0}.-cbwsdk-css-reset .-cbwsdk-cancel-button-x{position:relative;display:block;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-wallet-steps{padding:0 0 0 16px;margin:0;width:100%;list-style:decimal}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item{list-style-type:decimal;display:list-item;font-style:normal;font-weight:400;font-size:16px;line-height:24px;margin-top:20px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item-wrapper{display:flex;align-items:center}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-pad-left{margin-left:6px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon{display:flex;border-radius:50%;height:24px;width:24px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon svg{margin:auto;display:block}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon.light{background:#0052ff}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon.dark{background:#588af5}.-cbwsdk-css-reset .-cbwsdk-connect-item{align-items:center;display:flex;flex-direction:row;padding:16px 24px;gap:12px;cursor:pointer;border-radius:100px;font-weight:600}.-cbwsdk-css-reset .-cbwsdk-connect-item.light{background:#f5f8ff;color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-connect-item.dark{background:#001033;color:#588af5}.-cbwsdk-css-reset .-cbwsdk-connect-item-copy-wrapper{margin:0 4px 0 8px}.-cbwsdk-css-reset .-cbwsdk-connect-item-title{margin:0 0 0;font-size:16px;line-height:24px;font-weight:500}.-cbwsdk-css-reset .-cbwsdk-connect-item-description{font-weight:400;font-size:14px;line-height:20px;margin:0}";var Dl=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Yr,"__esModule",{value:!0});Yr.CoinbaseWalletSteps=Yr.ConnectContent=void 0;const mr=Dl(jn),ve=ct,np=Q,ip=sn,sp=Hi,op=Wi,ap=qi,cp=Vi,lp=zi,up=Dl(Fo),Gs={title:"Coinbase Wallet app",description:"Connect with your self-custody wallet",steps:jl},hp=e=>e==="light"?"#FFFFFF":"#0A0B0D";function fp(e){const{theme:t}=e,r=(0,np.createQrUrl)(e.sessionId,e.sessionSecret,e.linkAPIUrl,e.isParentConnection,e.version,e.chainId),n=Gs.steps;return(0,ve.h)("div",{"data-testid":"connect-content",className:(0,mr.default)("-cbwsdk-connect-content",t)},(0,ve.h)("style",null,up.default),(0,ve.h)("div",{className:"-cbwsdk-connect-content-header"},(0,ve.h)("h2",{className:(0,mr.default)("-cbwsdk-connect-content-heading",t)},"Scan to connect with our mobile app"),e.onCancel&&(0,ve.h)("button",{type:"button",className:"-cbwsdk-cancel-button",onClick:e.onCancel},(0,ve.h)(sp.CloseIcon,{fill:t==="light"?"#0A0B0D":"#FFFFFF"}))),(0,ve.h)("div",{className:"-cbwsdk-connect-content-layout"},(0,ve.h)("div",{className:"-cbwsdk-connect-content-column-left"},(0,ve.h)(dp,{title:Gs.title,description:Gs.description,theme:t})),(0,ve.h)("div",{className:"-cbwsdk-connect-content-column-right"},(0,ve.h)("div",{className:"-cbwsdk-connect-content-qr-wrapper"},(0,ve.h)(cp.QRCode,{content:r,width:200,height:200,fgColor:"#000",bgColor:"transparent"}),(0,ve.h)("input",{type:"hidden",name:"cbw-cbwsdk-version",value:ip.LIB_VERSION}),(0,ve.h)("input",{type:"hidden",value:r})),(0,ve.h)(n,{theme:t}),!e.isConnected&&(0,ve.h)("div",{"data-testid":"connecting-spinner",className:(0,mr.default)("-cbwsdk-connect-content-qr-connecting",t)},(0,ve.h)(lp.Spinner,{size:36,color:t==="dark"?"#FFF":"#000"}),(0,ve.h)("p",null,"Connecting...")))))}Yr.ConnectContent=fp;function dp({title:e,description:t,theme:r}){return(0,ve.h)("div",{className:(0,mr.default)("-cbwsdk-connect-item",r)},(0,ve.h)("div",null,(0,ve.h)(op.CoinbaseWalletRound,null)),(0,ve.h)("div",{className:"-cbwsdk-connect-item-copy-wrapper"},(0,ve.h)("h3",{className:"-cbwsdk-connect-item-title"},e),(0,ve.h)("p",{className:"-cbwsdk-connect-item-description"},t)))}function jl({theme:e}){return(0,ve.h)("ol",{className:"-cbwsdk-wallet-steps"},(0,ve.h)("li",{className:(0,mr.default)("-cbwsdk-wallet-steps-item",e)},(0,ve.h)("div",{className:"-cbwsdk-wallet-steps-item-wrapper"},"Open Coinbase Wallet app")),(0,ve.h)("li",{className:(0,mr.default)("-cbwsdk-wallet-steps-item",e)},(0,ve.h)("div",{className:"-cbwsdk-wallet-steps-item-wrapper"},(0,ve.h)("span",null,"Tap ",(0,ve.h)("strong",null,"Scan")," "),(0,ve.h)("span",{className:(0,mr.default)("-cbwsdk-wallet-steps-pad-left","-cbwsdk-wallet-steps-icon",e)},(0,ve.h)(ap.QRCodeIcon,{fill:hp(e)})))))}Yr.CoinbaseWalletSteps=jl;var Gi={},Ji={};Object.defineProperty(Ji,"__esModule",{value:!0});Ji.ArrowLeftIcon=void 0;const Ka=ct;function pp(e){return(0,Ka.h)("svg",Object.assign({width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},e),(0,Ka.h)("path",{d:"M8.60675 0.155884L7.37816 1.28209L12.7723 7.16662H0V8.83328H12.6548L6.82149 14.6666L8 15.8451L15.8201 8.02501L8.60675 0.155884Z"}))}Ji.ArrowLeftIcon=pp;var Zi={};Object.defineProperty(Zi,"__esModule",{value:!0});Zi.LaptopIcon=void 0;const Js=ct;function gp(e){return(0,Js.h)("svg",Object.assign({width:"14",height:"14",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg"},e),(0,Js.h)("path",{d:"M1.8001 2.2002H12.2001V9.40019H1.8001V2.2002ZM3.4001 3.8002V7.80019H10.6001V3.8002H3.4001Z"}),(0,Js.h)("path",{d:"M13.4001 10.2002H0.600098C0.600098 11.0838 1.31644 11.8002 2.2001 11.8002H11.8001C12.6838 11.8002 13.4001 11.0838 13.4001 10.2002Z"}))}Zi.LaptopIcon=gp;var Ki={};Object.defineProperty(Ki,"__esModule",{value:!0});Ki.SafeIcon=void 0;const Ya=ct;function yp(e){return(0,Ya.h)("svg",Object.assign({width:"14",height:"14",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg"},e),(0,Ya.h)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.600098 0.600098V11.8001H13.4001V0.600098H0.600098ZM7.0001 9.2001C5.3441 9.2001 4.0001 7.8561 4.0001 6.2001C4.0001 4.5441 5.3441 3.2001 7.0001 3.2001C8.6561 3.2001 10.0001 4.5441 10.0001 6.2001C10.0001 7.8561 8.6561 9.2001 7.0001 9.2001ZM0.600098 12.6001H3.8001V13.4001H0.600098V12.6001ZM10.2001 12.6001H13.4001V13.4001H10.2001V12.6001ZM8.8001 6.2001C8.8001 7.19421 7.99421 8.0001 7.0001 8.0001C6.00598 8.0001 5.2001 7.19421 5.2001 6.2001C5.2001 5.20598 6.00598 4.4001 7.0001 4.4001C7.99421 4.4001 8.8001 5.20598 8.8001 6.2001Z"}))}Ki.SafeIcon=yp;var Do={};Object.defineProperty(Do,"__esModule",{value:!0});Do.default=".-cbwsdk-css-reset .-cbwsdk-try-extension{display:flex;margin-top:12px;height:202px;width:700px;border-radius:12px;padding:30px}.-cbwsdk-css-reset .-cbwsdk-try-extension.light{background:#fff}.-cbwsdk-css-reset .-cbwsdk-try-extension.dark{background:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-try-extension-column-half{flex:50%}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading{font-style:normal;font-weight:500;font-size:25px;line-height:32px;margin:0;max-width:204px}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta{appearance:none;border:none;background:none;color:#0052ff;cursor:pointer;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta.light{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta.dark{color:#588af5}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta-wrapper{display:flex;align-items:center;margin-top:12px}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta-icon{display:block;margin-left:4px;height:14px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list{display:flex;flex-direction:column;justify-content:center;align-items:center;margin:0;padding:0;list-style:none;height:100%}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item{display:flex;align-items:center;flex-flow:nowrap;margin-top:24px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item:first-of-type{margin-top:0}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon-wrapper{display:block}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon{display:flex;height:32px;width:32px;border-radius:50%}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon svg{margin:auto;display:block}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon.light{background:#eef0f3}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon.dark{background:#1e2025}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy{display:block;font-weight:400;font-size:14px;line-height:20px;padding-left:12px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy.light{color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy.dark{color:#8a919e}";var Ul=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Gi,"__esModule",{value:!0});Gi.TryExtensionContent=void 0;const cr=Ul(jn),Je=ct,Zs=Ui,mp=Ji,vp=Zi,bp=Ki,wp=Ul(Do);function _p({theme:e}){const[t,r]=(0,Zs.useState)(!1),n=(0,Zs.useCallback)(()=>{window.open("https://api.wallet.coinbase.com/rpc/v2/desktop/chrome","_blank")},[]),i=(0,Zs.useCallback)(()=>{t?window.location.reload():(n(),r(!0))},[n,t]);return(0,Je.h)("div",{class:(0,cr.default)("-cbwsdk-try-extension",e)},(0,Je.h)("style",null,wp.default),(0,Je.h)("div",{class:"-cbwsdk-try-extension-column-half"},(0,Je.h)("h3",{class:(0,cr.default)("-cbwsdk-try-extension-heading",e)},"Or try the Coinbase Wallet browser extension"),(0,Je.h)("div",{class:"-cbwsdk-try-extension-cta-wrapper"},(0,Je.h)("button",{class:(0,cr.default)("-cbwsdk-try-extension-cta",e),onClick:i},t?"Refresh":"Install"),(0,Je.h)("div",null,!t&&(0,Je.h)(mp.ArrowLeftIcon,{class:"-cbwsdk-try-extension-cta-icon",fill:e==="light"?"#0052FF":"#588AF5"})))),(0,Je.h)("div",{class:"-cbwsdk-try-extension-column-half"},(0,Je.h)("ul",{class:"-cbwsdk-try-extension-list"},(0,Je.h)("li",{class:"-cbwsdk-try-extension-list-item"},(0,Je.h)("div",{class:"-cbwsdk-try-extension-list-item-icon-wrapper"},(0,Je.h)("span",{class:(0,cr.default)("-cbwsdk-try-extension-list-item-icon",e)},(0,Je.h)(vp.LaptopIcon,{fill:e==="light"?"#0A0B0D":"#FFFFFF"}))),(0,Je.h)("div",{class:(0,cr.default)("-cbwsdk-try-extension-list-item-copy",e)},"Connect with dapps with just one click on your desktop browser")),(0,Je.h)("li",{class:"-cbwsdk-try-extension-list-item"},(0,Je.h)("div",{class:"-cbwsdk-try-extension-list-item-icon-wrapper"},(0,Je.h)("span",{class:(0,cr.default)("-cbwsdk-try-extension-list-item-icon",e)},(0,Je.h)(bp.SafeIcon,{fill:e==="light"?"#0A0B0D":"#FFFFFF"}))),(0,Je.h)("div",{class:(0,cr.default)("-cbwsdk-try-extension-list-item-copy",e)},"Add an additional layer of security by using a supported Ledger hardware wallet")))))}Gi.TryExtensionContent=_p;var jo={};Object.defineProperty(jo,"__esModule",{value:!0});jo.default=".-cbwsdk-css-reset .-cbwsdk-connect-dialog{z-index:**********;position:fixed;top:0;left:0;right:0;bottom:0;display:flex;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop{z-index:**********;position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop.light{background-color:rgba(0,0,0,.5)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop.dark{background-color:rgba(50,53,61,.4)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-box{display:flex;position:relative;flex-direction:column;transform:scale(1);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-box-hidden{opacity:0;transform:scale(0.85)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-container{display:block}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-container-hidden{display:none}";var Hl=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ji,"__esModule",{value:!0});ji.ConnectDialog=void 0;const Ks=Hl(jn),lr=ct,Ys=Ui,Ep=Yr,Sp=Gi,Rp=Hl(jo),Mp=e=>{const{isOpen:t,darkMode:r}=e,[n,i]=(0,Ys.useState)(!t),[s,c]=(0,Ys.useState)(!t);(0,Ys.useEffect)(()=>{const u=[window.setTimeout(()=>{c(!t)},10)];return t?i(!1):u.push(window.setTimeout(()=>{i(!0)},360)),()=>{u.forEach(window.clearTimeout)}},[t]);const o=r?"dark":"light";return(0,lr.h)("div",{class:(0,Ks.default)("-cbwsdk-connect-dialog-container",n&&"-cbwsdk-connect-dialog-container-hidden")},(0,lr.h)("style",null,Rp.default),(0,lr.h)("div",{class:(0,Ks.default)("-cbwsdk-connect-dialog-backdrop",o,s&&"-cbwsdk-connect-dialog-backdrop-hidden")}),(0,lr.h)("div",{class:"-cbwsdk-connect-dialog"},(0,lr.h)("div",{class:(0,Ks.default)("-cbwsdk-connect-dialog-box",s&&"-cbwsdk-connect-dialog-box-hidden")},e.connectDisabled?null:(0,lr.h)(Ep.ConnectContent,{theme:o,version:e.version,sessionId:e.sessionId,sessionSecret:e.sessionSecret,linkAPIUrl:e.linkAPIUrl,isConnected:e.isConnected,isParentConnection:e.isParentConnection,chainId:e.chainId,onCancel:e.onCancel}),(0,lr.h)(Sp.TryExtensionContent,{theme:o}))))};ji.ConnectDialog=Mp;Object.defineProperty(Di,"__esModule",{value:!0});Di.LinkFlow=void 0;const Qs=ct,Ip=ji;class Ap{constructor(t){this.connected=!1,this.chainId=1,this.isOpen=!1,this.onCancel=null,this.root=null,this.connectDisabled=!1,this.darkMode=t.darkMode,this.version=t.version,this.sessionId=t.sessionId,this.sessionSecret=t.sessionSecret,this.linkAPIUrl=t.linkAPIUrl,this.isParentConnection=t.isParentConnection}attach(t){this.root=document.createElement("div"),this.root.className="-cbwsdk-link-flow-root",t.appendChild(this.root),this.render()}setConnected(t){this.connected!==t&&(this.connected=t,this.render())}setChainId(t){this.chainId!==t&&(this.chainId=t,this.render())}detach(){var t;this.root&&((0,Qs.render)(null,this.root),(t=this.root.parentElement)===null||t===void 0||t.removeChild(this.root))}setConnectDisabled(t){this.connectDisabled=t}open(t){this.isOpen=!0,this.onCancel=t.onCancel,this.render()}close(){this.isOpen=!1,this.onCancel=null,this.render()}render(){this.root&&(0,Qs.render)((0,Qs.h)(Ip.ConnectDialog,{darkMode:this.darkMode,version:this.version,sessionId:this.sessionId,sessionSecret:this.sessionSecret,linkAPIUrl:this.linkAPIUrl,isOpen:this.isOpen,isConnected:this.connected,isParentConnection:this.isParentConnection,chainId:this.chainId,onCancel:this.onCancel,connectDisabled:this.connectDisabled}),this.root)}}Di.LinkFlow=Ap;var Uo={},Ho={};Object.defineProperty(Ho,"__esModule",{value:!0});Ho.default=".-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:**********}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}";(function(e){var t=ee&&ee.__importDefault||function(w){return w&&w.__esModule?w:{default:w}};Object.defineProperty(e,"__esModule",{value:!0}),e.SnackbarInstance=e.SnackbarContainer=e.Snackbar=void 0;const r=t(jn),n=ct,i=Ui,s=t(Ho),c="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+",o="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";class u{constructor(_){this.items=new Map,this.nextItemKey=0,this.root=null,this.darkMode=_.darkMode}attach(_){this.root=document.createElement("div"),this.root.className="-cbwsdk-snackbar-root",_.appendChild(this.root),this.render()}presentItem(_){const L=this.nextItemKey++;return this.items.set(L,_),this.render(),()=>{this.items.delete(L),this.render()}}clear(){this.items.clear(),this.render()}render(){this.root&&(0,n.render)((0,n.h)("div",null,(0,n.h)(e.SnackbarContainer,{darkMode:this.darkMode},Array.from(this.items.entries()).map(([_,L])=>(0,n.h)(e.SnackbarInstance,Object.assign({},L,{key:_}))))),this.root)}}e.Snackbar=u;const p=w=>(0,n.h)("div",{class:(0,r.default)("-cbwsdk-snackbar-container")},(0,n.h)("style",null,s.default),(0,n.h)("div",{class:"-cbwsdk-snackbar"},w.children));e.SnackbarContainer=p;const g=({autoExpand:w,message:_,menuItems:L})=>{const[j,$]=(0,i.useState)(!0),[O,A]=(0,i.useState)(w??!1);(0,i.useEffect)(()=>{const C=[window.setTimeout(()=>{$(!1)},1),window.setTimeout(()=>{A(!0)},1e4)];return()=>{C.forEach(window.clearTimeout)}});const N=()=>{A(!O)};return(0,n.h)("div",{class:(0,r.default)("-cbwsdk-snackbar-instance",j&&"-cbwsdk-snackbar-instance-hidden",O&&"-cbwsdk-snackbar-instance-expanded")},(0,n.h)("div",{class:"-cbwsdk-snackbar-instance-header",onClick:N},(0,n.h)("img",{src:c,class:"-cbwsdk-snackbar-instance-header-cblogo"})," ",(0,n.h)("div",{class:"-cbwsdk-snackbar-instance-header-message"},_),(0,n.h)("div",{class:"-gear-container"},!O&&(0,n.h)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,n.h)("circle",{cx:"12",cy:"12",r:"12",fill:"#F5F7F8"})),(0,n.h)("img",{src:o,class:"-gear-icon",title:"Expand"}))),L&&L.length>0&&(0,n.h)("div",{class:"-cbwsdk-snackbar-instance-menu"},L.map((C,W)=>(0,n.h)("div",{class:(0,r.default)("-cbwsdk-snackbar-instance-menu-item",C.isRed&&"-cbwsdk-snackbar-instance-menu-item-is-red"),onClick:C.onClick,key:W},(0,n.h)("svg",{width:C.svgWidth,height:C.svgHeight,viewBox:"0 0 10 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,n.h)("path",{"fill-rule":C.defaultFillRule,"clip-rule":C.defaultClipRule,d:C.path,fill:"#AAAAAA"})),(0,n.h)("span",{class:(0,r.default)("-cbwsdk-snackbar-instance-menu-item-info",C.isRed&&"-cbwsdk-snackbar-instance-menu-item-info-is-red")},C.info)))))};e.SnackbarInstance=g})(Uo);Object.defineProperty(Fn,"__esModule",{value:!0});Fn.WalletLinkRelayUI=void 0;const xp=Dn,kp=Di,Cp=Uo;class Tp{constructor(t){this.standalone=null,this.attached=!1,this.snackbar=new Cp.Snackbar({darkMode:t.darkMode}),this.linkFlow=new kp.LinkFlow({darkMode:t.darkMode,version:t.version,sessionId:t.session.id,sessionSecret:t.session.secret,linkAPIUrl:t.linkAPIUrl,isParentConnection:!1})}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");const t=document.documentElement,r=document.createElement("div");r.className="-cbwsdk-css-reset",t.appendChild(r),this.linkFlow.attach(r),this.snackbar.attach(r),this.attached=!0,(0,xp.injectCssReset)()}setConnected(t){this.linkFlow.setConnected(t)}setChainId(t){this.linkFlow.setChainId(t)}setConnectDisabled(t){this.linkFlow.setConnectDisabled(t)}addEthereumChain(){}watchAsset(){}switchEthereumChain(){}requestEthereumAccounts(t){this.linkFlow.open({onCancel:t.onCancel})}hideRequestEthereumAccounts(){this.linkFlow.close()}signEthereumMessage(){}signEthereumTransaction(){}submitEthereumTransaction(){}ethereumAddressFromSignedMessage(){}showConnecting(t){let r;return t.isUnlinkedErrorState?r={autoExpand:!0,message:"Connection lost",menuItems:[{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]}:r={message:"Confirm on phone",menuItems:[{isRed:!0,info:"Cancel transaction",svgWidth:"11",svgHeight:"11",path:"M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z",defaultFillRule:"inherit",defaultClipRule:"inherit",onClick:t.onCancel},{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]},this.snackbar.presentItem(r)}reloadUI(){document.location.reload()}inlineAccountsResponse(){return!1}inlineAddEthereumChain(){return!1}inlineWatchAsset(){return!1}inlineSwitchEthereumChain(){return!1}setStandalone(t){this.standalone=t}isStandalone(){var t;return(t=this.standalone)!==null&&t!==void 0?t:!1}}Fn.WalletLinkRelayUI=Tp;Object.defineProperty(An,"__esModule",{value:!0});An.WalletLinkRelay=void 0;const $r=En,Bp=Ke,qe=Q,ur=on,Qa=vt,Wt=an,Np=Oi,hr=nn,Pp=Fn;class Ft extends Qa.RelayAbstract{constructor(t){var r;super(),this.accountsCallback=null,this.chainCallbackParams={chainId:"",jsonRpcUrl:""},this.chainCallback=null,this.dappDefaultChain=1,this.appName="",this.appLogoUrl=null,this.linkedUpdated=c=>{var o;this.isLinked=c;const u=this.storage.getItem(Qa.LOCAL_STORAGE_ADDRESSES_KEY);if(c&&(this.session.linked=c),this.isUnlinkedErrorState=!1,u){const p=u.split(" "),g=this.storage.getItem("IsStandaloneSigning")==="true";if(p[0]!==""&&!c&&this.session.linked&&!g){this.isUnlinkedErrorState=!0;const w=this.getSessionIdHash();(o=this.diagnostic)===null||o===void 0||o.log(ur.EVENTS.UNLINKED_ERROR_STATE,{sessionIdHash:w})}}},this.metadataUpdated=(c,o)=>{this.storage.setItem(c,o)},this.chainUpdated=(c,o)=>{this.chainCallbackParams.chainId===c&&this.chainCallbackParams.jsonRpcUrl===o||(this.chainCallbackParams={chainId:c,jsonRpcUrl:o},this.chainCallback&&this.chainCallback(c,o))},this.accountUpdated=c=>{this.accountsCallback&&this.accountsCallback([c]),Ft.accountRequestCallbackIds.size>0&&(Array.from(Ft.accountRequestCallbackIds.values()).forEach(o=>{const u={type:"WEB3_RESPONSE",id:o,response:{method:"requestEthereumAccounts",result:[c]}};this.invokeCallback(Object.assign(Object.assign({},u),{id:o}))}),Ft.accountRequestCallbackIds.clear())},this.connectedUpdated=c=>{this.ui.setConnected(c)},this.resetAndReload=this.resetAndReload.bind(this),this.linkAPIUrl=t.linkAPIUrl,this.storage=t.storage,this.options=t;const{session:n,ui:i,connection:s}=this.subscribe();this._session=n,this.connection=s,this.relayEventManager=t.relayEventManager,this.diagnostic=t.diagnosticLogger,this._reloadOnDisconnect=(r=t.reloadOnDisconnect)!==null&&r!==void 0?r:!0,this.ui=i}subscribe(){const t=Wt.Session.load(this.storage)||new Wt.Session(this.storage).save(),{linkAPIUrl:r,diagnostic:n}=this,i=new Np.WalletLinkConnection({session:t,linkAPIUrl:r,diagnostic:n,listener:this}),{version:s,darkMode:c}=this.options,o=this.options.uiConstructor({linkAPIUrl:r,version:s,darkMode:c,session:t});return i.connect(),{session:t,ui:o,connection:i}}attachUI(){this.ui.attach()}resetAndReload(){Promise.race([this.connection.setSessionMetadata("__destroyed","1"),new Promise(t=>setTimeout(()=>t(null),1e3))]).then(()=>{var t,r;const n=this.ui.isStandalone();(t=this.diagnostic)===null||t===void 0||t.log(ur.EVENTS.SESSION_STATE_CHANGE,{method:"relay::resetAndReload",sessionMetadataChange:"__destroyed, 1",sessionIdHash:this.getSessionIdHash()}),this.connection.destroy();const i=Wt.Session.load(this.storage);if((i==null?void 0:i.id)===this._session.id?this.storage.clear():i&&((r=this.diagnostic)===null||r===void 0||r.log(ur.EVENTS.SKIPPED_CLEARING_SESSION,{sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:Wt.Session.hash(i.id)})),this._reloadOnDisconnect){this.ui.reloadUI();return}this.accountsCallback&&this.accountsCallback([],!0);const{session:s,ui:c,connection:o}=this.subscribe();this._session=s,this.connection=o,this.ui=c,n&&this.ui.setStandalone&&this.ui.setStandalone(!0),this.options.headlessMode||this.attachUI()}).catch(t=>{var r;(r=this.diagnostic)===null||r===void 0||r.log(ur.EVENTS.FAILURE,{method:"relay::resetAndReload",message:`failed to reset and reload with ${t}`,sessionIdHash:this.getSessionIdHash()})})}setAppInfo(t,r){this.appName=t,this.appLogoUrl=r}getStorageItem(t){return this.storage.getItem(t)}get session(){return this._session}setStorageItem(t,r){this.storage.setItem(t,r)}signEthereumMessage(t,r,n,i){return this.sendRequest({method:"signEthereumMessage",params:{message:(0,qe.hexStringFromBuffer)(t,!0),address:r,addPrefix:n,typedDataJson:i||null}})}ethereumAddressFromSignedMessage(t,r,n){return this.sendRequest({method:"ethereumAddressFromSignedMessage",params:{message:(0,qe.hexStringFromBuffer)(t,!0),signature:(0,qe.hexStringFromBuffer)(r,!0),addPrefix:n}})}signEthereumTransaction(t){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:(0,qe.bigIntStringFromBN)(t.weiValue),data:(0,qe.hexStringFromBuffer)(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?(0,qe.bigIntStringFromBN)(t.gasPriceInWei):null,maxFeePerGas:t.gasPriceInWei?(0,qe.bigIntStringFromBN)(t.gasPriceInWei):null,maxPriorityFeePerGas:t.gasPriceInWei?(0,qe.bigIntStringFromBN)(t.gasPriceInWei):null,gasLimit:t.gasLimit?(0,qe.bigIntStringFromBN)(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!1}})}signAndSubmitEthereumTransaction(t){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:(0,qe.bigIntStringFromBN)(t.weiValue),data:(0,qe.hexStringFromBuffer)(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?(0,qe.bigIntStringFromBN)(t.gasPriceInWei):null,maxFeePerGas:t.maxFeePerGas?(0,qe.bigIntStringFromBN)(t.maxFeePerGas):null,maxPriorityFeePerGas:t.maxPriorityFeePerGas?(0,qe.bigIntStringFromBN)(t.maxPriorityFeePerGas):null,gasLimit:t.gasLimit?(0,qe.bigIntStringFromBN)(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!0}})}submitEthereumTransaction(t,r){return this.sendRequest({method:"submitEthereumTransaction",params:{signedTransaction:(0,qe.hexStringFromBuffer)(t,!0),chainId:r}})}scanQRCode(t){return this.sendRequest({method:"scanQRCode",params:{regExp:t}})}getQRCodeUrl(){return(0,qe.createQrUrl)(this._session.id,this._session.secret,this.linkAPIUrl,!1,this.options.version,this.dappDefaultChain)}genericRequest(t,r){return this.sendRequest({method:"generic",params:{action:r,data:t}})}sendGenericMessage(t){return this.sendRequest(t)}sendRequest(t){let r=null;const n=(0,qe.randomBytesHex)(8),i=c=>{this.publishWeb3RequestCanceledEvent(n),this.handleErrorResponse(n,t.method,c),r==null||r()};return{promise:new Promise((c,o)=>{this.ui.isStandalone()||(r=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:i,onResetConnection:this.resetAndReload})),this.relayEventManager.callbacks.set(n,u=>{if(r==null||r(),(0,hr.isErrorResponse)(u))return o(new Error(u.errorMessage));c(u)}),this.ui.isStandalone()?this.sendRequestStandalone(n,t):this.publishWeb3RequestEvent(n,t)}),cancel:i}}setConnectDisabled(t){this.ui.setConnectDisabled(t)}setAccountsCallback(t){this.accountsCallback=t}setChainCallback(t){this.chainCallback=t}setDappDefaultChainCallback(t){this.dappDefaultChain=t,this.ui instanceof Pp.WalletLinkRelayUI&&this.ui.setChainId(t)}publishWeb3RequestEvent(t,r){var n;const i={type:"WEB3_REQUEST",id:t,request:r},s=Wt.Session.load(this.storage);(n=this.diagnostic)===null||n===void 0||n.log(ur.EVENTS.WEB3_REQUEST,{eventId:i.id,method:`relay::${r.method}`,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:s?Wt.Session.hash(s.id):"",isSessionMismatched:((s==null?void 0:s.id)!==this._session.id).toString()}),this.publishEvent("Web3Request",i,!0).then(c=>{var o;(o=this.diagnostic)===null||o===void 0||o.log(ur.EVENTS.WEB3_REQUEST_PUBLISHED,{eventId:i.id,method:`relay::${r.method}`,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:s?Wt.Session.hash(s.id):"",isSessionMismatched:((s==null?void 0:s.id)!==this._session.id).toString()})}).catch(c=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:i.id,response:{method:r.method,errorMessage:c.message}})})}publishWeb3RequestCanceledEvent(t){const r={type:"WEB3_REQUEST_CANCELED",id:t};this.publishEvent("Web3RequestCanceled",r,!1).then()}publishEvent(t,r,n){return this.connection.publishEvent(t,r,n)}handleWeb3ResponseMessage(t){var r;const{response:n}=t;if((r=this.diagnostic)===null||r===void 0||r.log(ur.EVENTS.WEB3_RESPONSE,{eventId:t.id,method:`relay::${n.method}`,sessionIdHash:this.getSessionIdHash()}),n.method==="requestEthereumAccounts"){Ft.accountRequestCallbackIds.forEach(i=>this.invokeCallback(Object.assign(Object.assign({},t),{id:i}))),Ft.accountRequestCallbackIds.clear();return}this.invokeCallback(t)}handleErrorResponse(t,r,n,i){var s;const c=(s=n==null?void 0:n.message)!==null&&s!==void 0?s:(0,$r.getMessageFromCode)(i);this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:t,response:{method:r,errorMessage:c,errorCode:i}})}invokeCallback(t){const r=this.relayEventManager.callbacks.get(t.id);r&&(r(t.response),this.relayEventManager.callbacks.delete(t.id))}requestEthereumAccounts(){const t={method:"requestEthereumAccounts",params:{appName:this.appName,appLogoUrl:this.appLogoUrl||null}},r=(0,qe.randomBytesHex)(8),n=s=>{this.publishWeb3RequestCanceledEvent(r),this.handleErrorResponse(r,t.method,s)};return{promise:new Promise((s,c)=>{if(this.relayEventManager.callbacks.set(r,o=>{if(this.ui.hideRequestEthereumAccounts(),(0,hr.isErrorResponse)(o))return c(new Error(o.errorMessage));s(o)}),this.ui.inlineAccountsResponse()){const o=u=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:r,response:{method:"requestEthereumAccounts",result:u}})};this.ui.requestEthereumAccounts({onCancel:n,onAccounts:o})}else{const o=$r.standardErrors.provider.userRejectedRequest("User denied account authorization");this.ui.requestEthereumAccounts({onCancel:()=>n(o)})}Ft.accountRequestCallbackIds.add(r),!this.ui.inlineAccountsResponse()&&!this.ui.isStandalone()&&this.publishWeb3RequestEvent(r,t)}),cancel:n}}selectProvider(t){const r={method:"selectProvider"},n=(0,qe.randomBytesHex)(8),i=c=>{this.publishWeb3RequestCanceledEvent(n),this.handleErrorResponse(n,r.method,c)},s=new Promise((c,o)=>{this.relayEventManager.callbacks.set(n,g=>{if((0,hr.isErrorResponse)(g))return o(new Error(g.errorMessage));c(g)});const u=g=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:n,response:{method:"selectProvider",result:Bp.ProviderType.Unselected}})},p=g=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:n,response:{method:"selectProvider",result:g}})};this.ui.selectProvider&&this.ui.selectProvider({onApprove:p,onCancel:u,providerOptions:t})});return{cancel:i,promise:s}}watchAsset(t,r,n,i,s,c){const o={method:"watchAsset",params:{type:t,options:{address:r,symbol:n,decimals:i,image:s},chainId:c}};let u=null;const p=(0,qe.randomBytesHex)(8),g=_=>{this.publishWeb3RequestCanceledEvent(p),this.handleErrorResponse(p,o.method,_),u==null||u()};this.ui.inlineWatchAsset()||(u=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:g,onResetConnection:this.resetAndReload}));const w=new Promise((_,L)=>{this.relayEventManager.callbacks.set(p,O=>{if(u==null||u(),(0,hr.isErrorResponse)(O))return L(new Error(O.errorMessage));_(O)});const j=O=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:p,response:{method:"watchAsset",result:!1}})},$=()=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:p,response:{method:"watchAsset",result:!0}})};this.ui.inlineWatchAsset()&&this.ui.watchAsset({onApprove:$,onCancel:j,type:t,address:r,symbol:n,decimals:i,image:s,chainId:c}),!this.ui.inlineWatchAsset()&&!this.ui.isStandalone()&&this.publishWeb3RequestEvent(p,o)});return{cancel:g,promise:w}}addEthereumChain(t,r,n,i,s,c){const o={method:"addEthereumChain",params:{chainId:t,rpcUrls:r,blockExplorerUrls:i,chainName:s,iconUrls:n,nativeCurrency:c}};let u=null;const p=(0,qe.randomBytesHex)(8),g=_=>{this.publishWeb3RequestCanceledEvent(p),this.handleErrorResponse(p,o.method,_),u==null||u()};return this.ui.inlineAddEthereumChain(t)||(u=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:g,onResetConnection:this.resetAndReload})),{promise:new Promise((_,L)=>{this.relayEventManager.callbacks.set(p,O=>{if(u==null||u(),(0,hr.isErrorResponse)(O))return L(new Error(O.errorMessage));_(O)});const j=O=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:p,response:{method:"addEthereumChain",result:{isApproved:!1,rpcUrl:""}}})},$=O=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:p,response:{method:"addEthereumChain",result:{isApproved:!0,rpcUrl:O}}})};this.ui.inlineAddEthereumChain(t)&&this.ui.addEthereumChain({onCancel:j,onApprove:$,chainId:o.params.chainId,rpcUrls:o.params.rpcUrls,blockExplorerUrls:o.params.blockExplorerUrls,chainName:o.params.chainName,iconUrls:o.params.iconUrls,nativeCurrency:o.params.nativeCurrency}),!this.ui.inlineAddEthereumChain(t)&&!this.ui.isStandalone()&&this.publishWeb3RequestEvent(p,o)}),cancel:g}}switchEthereumChain(t,r){const n={method:"switchEthereumChain",params:Object.assign({chainId:t},{address:r})},i=(0,qe.randomBytesHex)(8),s=o=>{this.publishWeb3RequestCanceledEvent(i),this.handleErrorResponse(i,n.method,o)};return{promise:new Promise((o,u)=>{this.relayEventManager.callbacks.set(i,w=>{if((0,hr.isErrorResponse)(w)&&w.errorCode)return u($r.standardErrors.provider.custom({code:w.errorCode,message:"Unrecognized chain ID. Try adding the chain using addEthereumChain first."}));if((0,hr.isErrorResponse)(w))return u(new Error(w.errorMessage));o(w)});const p=w=>{var _;if(w){const L=(_=(0,$r.getErrorCode)(w))!==null&&_!==void 0?_:$r.standardErrorCodes.provider.unsupportedChain;this.handleErrorResponse(i,"switchEthereumChain",w instanceof Error?w:$r.standardErrors.provider.unsupportedChain(t),L)}else this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:i,response:{method:"switchEthereumChain",result:{isApproved:!1,rpcUrl:""}}})},g=w=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:i,response:{method:"switchEthereumChain",result:{isApproved:!0,rpcUrl:w}}})};this.ui.switchEthereumChain({onCancel:p,onApprove:g,chainId:n.params.chainId,address:n.params.address}),!this.ui.inlineSwitchEthereumChain()&&!this.ui.isStandalone()&&this.publishWeb3RequestEvent(i,n)}),cancel:s}}inlineAddEthereumChain(t){return this.ui.inlineAddEthereumChain(t)}getSessionIdHash(){return Wt.Session.hash(this._session.id)}sendRequestStandalone(t,r){const n=s=>{this.handleErrorResponse(t,r.method,s)},i=s=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:t,response:s})};switch(r.method){case"signEthereumMessage":this.ui.signEthereumMessage({request:r,onSuccess:i,onCancel:n});break;case"signEthereumTransaction":this.ui.signEthereumTransaction({request:r,onSuccess:i,onCancel:n});break;case"submitEthereumTransaction":this.ui.submitEthereumTransaction({request:r,onSuccess:i,onCancel:n});break;case"ethereumAddressFromSignedMessage":this.ui.ethereumAddressFromSignedMessage({request:r,onSuccess:i});break;default:n();break}}}An.WalletLinkRelay=Ft;Ft.accountRequestCallbackIds=new Set;var Un={},Yi={},Wl={};(function(e){var t=ee&&ee.__createBinding||(Object.create?function(n,i,s,c){c===void 0&&(c=s);var o=Object.getOwnPropertyDescriptor(i,s);(!o||("get"in o?!i.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return i[s]}}),Object.defineProperty(n,c,o)}:function(n,i,s,c){c===void 0&&(c=s),n[c]=i[s]}),r=ee&&ee.__exportStar||function(n,i){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s)&&t(i,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(Uo,e)})(Wl);var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0});Wo.default=".-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}";var ql=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Yi,"__esModule",{value:!0});Yi.RedirectDialog=void 0;const Lp=ql(jn),Tt=ct,Op=Dn,$p=Wl,Fp=ql(Wo);class Dp{constructor(){this.root=null}attach(){const t=document.documentElement;this.root=document.createElement("div"),this.root.className="-cbwsdk-css-reset",t.appendChild(this.root),(0,Op.injectCssReset)()}present(t){this.render(t)}clear(){this.render(null)}render(t){this.root&&((0,Tt.render)(null,this.root),t&&(0,Tt.render)((0,Tt.h)(jp,Object.assign({},t,{onDismiss:()=>{this.clear()}})),this.root))}}Yi.RedirectDialog=Dp;const jp=({title:e,buttonText:t,darkMode:r,onButtonClick:n,onDismiss:i})=>{const s=r?"dark":"light";return(0,Tt.h)($p.SnackbarContainer,{darkMode:r},(0,Tt.h)("div",{class:"-cbwsdk-redirect-dialog"},(0,Tt.h)("style",null,Fp.default),(0,Tt.h)("div",{class:"-cbwsdk-redirect-dialog-backdrop",onClick:i}),(0,Tt.h)("div",{class:(0,Lp.default)("-cbwsdk-redirect-dialog-box",s)},(0,Tt.h)("p",null,e),(0,Tt.h)("button",{onClick:n},t))))};Object.defineProperty(Un,"__esModule",{value:!0});Un.MobileRelayUI=void 0;const Up=Yi;class Hp{constructor(t){this.attached=!1,this.darkMode=!1,this.redirectDialog=new Up.RedirectDialog,this.darkMode=t.darkMode}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");this.redirectDialog.attach(),this.attached=!0}setConnected(t){}redirectToCoinbaseWallet(t){const r=new URL("https://go.cb-w.com/walletlink");r.searchParams.append("redirect_url",window.location.href),t&&r.searchParams.append("wl_url",t);const n=document.createElement("a");n.target="cbw-opener",n.href=r.href,n.rel="noreferrer noopener",n.click()}openCoinbaseWalletDeeplink(t){this.redirectDialog.present({title:"Redirecting to Coinbase Wallet...",buttonText:"Open",darkMode:this.darkMode,onButtonClick:()=>{this.redirectToCoinbaseWallet(t)}}),setTimeout(()=>{this.redirectToCoinbaseWallet(t)},99)}showConnecting(t){return()=>{this.redirectDialog.clear()}}hideRequestEthereumAccounts(){this.redirectDialog.clear()}requestEthereumAccounts(){}addEthereumChain(){}watchAsset(){}selectProvider(){}switchEthereumChain(){}signEthereumMessage(){}signEthereumTransaction(){}submitEthereumTransaction(){}ethereumAddressFromSignedMessage(){}reloadUI(){}setStandalone(){}setConnectDisabled(){}inlineAccountsResponse(){return!1}inlineAddEthereumChain(){return!1}inlineWatchAsset(){return!1}inlineSwitchEthereumChain(){return!1}isStandalone(){return!1}}Un.MobileRelayUI=Hp;Object.defineProperty(In,"__esModule",{value:!0});In.MobileRelay=void 0;const Wp=Q,qp=An,Vp=Un;class zp extends qp.WalletLinkRelay{constructor(t){var r;super(t),this._enableMobileWalletLink=(r=t.enableMobileWalletLink)!==null&&r!==void 0?r:!1}requestEthereumAccounts(){return this._enableMobileWalletLink?super.requestEthereumAccounts():{promise:new Promise(()=>{const t=(0,Wp.getLocation)();t.href=`https://go.cb-w.com/dapp?cb_url=${encodeURIComponent(t.href)}`}),cancel:()=>{}}}publishWeb3RequestEvent(t,r){if(super.publishWeb3RequestEvent(t,r),!(this._enableMobileWalletLink&&this.ui instanceof Vp.MobileRelayUI))return;let n=!1;switch(r.method){case"requestEthereumAccounts":case"connectAndSignIn":n=!0,this.ui.openCoinbaseWalletDeeplink(this.getQRCodeUrl());break;case"switchEthereumChain":return;default:n=!0,this.ui.openCoinbaseWalletDeeplink();break}n&&window.addEventListener("blur",()=>{window.addEventListener("focus",()=>{this.connection.checkUnseenEvents()},{once:!0})},{once:!0})}handleWeb3ResponseMessage(t){super.handleWeb3ResponseMessage(t)}connectAndSignIn(t){if(!this._enableMobileWalletLink)throw new Error("connectAndSignIn is supported only when enableMobileWalletLink is on");return this.sendRequest({method:"connectAndSignIn",params:{appName:this.appName,appLogoUrl:this.appLogoUrl,domain:window.location.hostname,aud:window.location.href,version:"1",type:"eip4361",nonce:t.nonce,iat:new Date().toISOString(),chainId:`eip155:${this.dappDefaultChain}`,statement:t.statement,resources:t.resources}})}}In.MobileRelay=zp;var _o={exports:{}},Vl=Ei.EventEmitter,Xs,Xa;function Gp(){if(Xa)return Xs;Xa=1;function e(j,$){var O=Object.keys(j);if(Object.getOwnPropertySymbols){var A=Object.getOwnPropertySymbols(j);$&&(A=A.filter(function(N){return Object.getOwnPropertyDescriptor(j,N).enumerable})),O.push.apply(O,A)}return O}function t(j){for(var $=1;$<arguments.length;$++){var O=arguments[$]!=null?arguments[$]:{};$%2?e(Object(O),!0).forEach(function(A){r(j,A,O[A])}):Object.getOwnPropertyDescriptors?Object.defineProperties(j,Object.getOwnPropertyDescriptors(O)):e(Object(O)).forEach(function(A){Object.defineProperty(j,A,Object.getOwnPropertyDescriptor(O,A))})}return j}function r(j,$,O){return $=c($),$ in j?Object.defineProperty(j,$,{value:O,enumerable:!0,configurable:!0,writable:!0}):j[$]=O,j}function n(j,$){if(!(j instanceof $))throw new TypeError("Cannot call a class as a function")}function i(j,$){for(var O=0;O<$.length;O++){var A=$[O];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(j,c(A.key),A)}}function s(j,$,O){return $&&i(j.prototype,$),Object.defineProperty(j,"prototype",{writable:!1}),j}function c(j){var $=o(j,"string");return typeof $=="symbol"?$:String($)}function o(j,$){if(typeof j!="object"||j===null)return j;var O=j[Symbol.toPrimitive];if(O!==void 0){var A=O.call(j,$);if(typeof A!="object")return A;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(j)}var u=xn,p=u.Buffer,g=ko,w=g.inspect,_=w&&w.custom||"inspect";function L(j,$,O){p.prototype.copy.call(j,$,O)}return Xs=function(){function j(){n(this,j),this.head=null,this.tail=null,this.length=0}return s(j,[{key:"push",value:function(O){var A={data:O,next:null};this.length>0?this.tail.next=A:this.head=A,this.tail=A,++this.length}},{key:"unshift",value:function(O){var A={data:O,next:this.head};this.length===0&&(this.tail=A),this.head=A,++this.length}},{key:"shift",value:function(){if(this.length!==0){var O=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,O}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(O){if(this.length===0)return"";for(var A=this.head,N=""+A.data;A=A.next;)N+=O+A.data;return N}},{key:"concat",value:function(O){if(this.length===0)return p.alloc(0);for(var A=p.allocUnsafe(O>>>0),N=this.head,C=0;N;)L(N.data,A,C),C+=N.data.length,N=N.next;return A}},{key:"consume",value:function(O,A){var N;return O<this.head.data.length?(N=this.head.data.slice(0,O),this.head.data=this.head.data.slice(O)):O===this.head.data.length?N=this.shift():N=A?this._getString(O):this._getBuffer(O),N}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(O){var A=this.head,N=1,C=A.data;for(O-=C.length;A=A.next;){var W=A.data,U=O>W.length?W.length:O;if(U===W.length?C+=W:C+=W.slice(0,O),O-=U,O===0){U===W.length?(++N,A.next?this.head=A.next:this.head=this.tail=null):(this.head=A,A.data=W.slice(U));break}++N}return this.length-=N,C}},{key:"_getBuffer",value:function(O){var A=p.allocUnsafe(O),N=this.head,C=1;for(N.data.copy(A),O-=N.data.length;N=N.next;){var W=N.data,U=O>W.length?W.length:O;if(W.copy(A,A.length-O,0,U),O-=U,O===0){U===W.length?(++C,N.next?this.head=N.next:this.head=this.tail=null):(this.head=N,N.data=W.slice(U));break}++C}return this.length-=C,A}},{key:_,value:function(O,A){return w(this,t(t({},A),{},{depth:0,customInspect:!1}))}}]),j}(),Xs}function Jp(e,t){var r=this,n=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return n||i?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(Eo,this,e)):process.nextTick(Eo,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(s){!t&&s?r._writableState?r._writableState.errorEmitted?process.nextTick(gi,r):(r._writableState.errorEmitted=!0,process.nextTick(ec,r,s)):process.nextTick(ec,r,s):t?(process.nextTick(gi,r),t(s)):process.nextTick(gi,r)}),this)}function ec(e,t){Eo(e,t),gi(e)}function gi(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function Zp(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function Eo(e,t){e.emit("error",t)}function Kp(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}var zl={destroy:Jp,undestroy:Zp,errorOrDestroy:Kp},_r={};function Yp(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Gl={};function _t(e,t,r){r||(r=Error);function n(s,c,o){return typeof t=="string"?t:t(s,c,o)}var i=function(s){Yp(c,s);function c(o,u,p){return s.call(this,n(o,u,p))||this}return c}(r);i.prototype.name=r.name,i.prototype.code=e,Gl[e]=i}function tc(e,t){if(Array.isArray(e)){var r=e.length;return e=e.map(function(n){return String(n)}),r>2?"one of ".concat(t," ").concat(e.slice(0,r-1).join(", "),", or ")+e[r-1]:r===2?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}else return"of ".concat(t," ").concat(String(e))}function Qp(e,t,r){return e.substr(0,t.length)===t}function Xp(e,t,r){return(r===void 0||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}function e1(e,t,r){return typeof r!="number"&&(r=0),r+t.length>e.length?!1:e.indexOf(t,r)!==-1}_t("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError);_t("ERR_INVALID_ARG_TYPE",function(e,t,r){var n;typeof t=="string"&&Qp(t,"not ")?(n="must not be",t=t.replace(/^not /,"")):n="must be";var i;if(Xp(e," argument"))i="The ".concat(e," ").concat(n," ").concat(tc(t,"type"));else{var s=e1(e,".")?"property":"argument";i='The "'.concat(e,'" ').concat(s," ").concat(n," ").concat(tc(t,"type"))}return i+=". Received type ".concat(typeof r),i},TypeError);_t("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF");_t("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"});_t("ERR_STREAM_PREMATURE_CLOSE","Premature close");_t("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"});_t("ERR_MULTIPLE_CALLBACK","Callback called multiple times");_t("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable");_t("ERR_STREAM_WRITE_AFTER_END","write after end");_t("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);_t("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError);_t("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event");_r.codes=Gl;var t1=_r.codes.ERR_INVALID_OPT_VALUE;function r1(e,t,r){return e.highWaterMark!=null?e.highWaterMark:t?e[r]:null}function n1(e,t,r,n){var i=r1(t,n,r);if(i!=null){if(!(isFinite(i)&&Math.floor(i)===i)||i<0){var s=n?r:"highWaterMark";throw new t1(s,i)}return Math.floor(i)}return e.objectMode?16:16*1024}var Jl={getHighWaterMark:n1},i1=s1;function s1(e,t){if(eo("noDeprecation"))return e;var r=!1;function n(){if(!r){if(eo("throwDeprecation"))throw new Error(t);eo("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}return n}function eo(e){try{if(!ee.localStorage)return!1}catch{return!1}var t=ee.localStorage[e];return t==null?!1:String(t).toLowerCase()==="true"}var to,rc;function Zl(){if(rc)return to;rc=1,to=X;function e(T){var F=this;this.next=null,this.entry=null,this.finish=function(){J(F,T)}}var t;X.WritableState=z;var r={deprecate:i1},n=Vl,i=xn.Buffer,s=(typeof ee<"u"?ee:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function c(T){return i.from(T)}function o(T){return i.isBuffer(T)||T instanceof s}var u=zl,p=Jl,g=p.getHighWaterMark,w=_r.codes,_=w.ERR_INVALID_ARG_TYPE,L=w.ERR_METHOD_NOT_IMPLEMENTED,j=w.ERR_MULTIPLE_CALLBACK,$=w.ERR_STREAM_CANNOT_PIPE,O=w.ERR_STREAM_DESTROYED,A=w.ERR_STREAM_NULL_VALUES,N=w.ERR_STREAM_WRITE_AFTER_END,C=w.ERR_UNKNOWN_ENCODING,W=u.errorOrDestroy;Rt(X,n);function U(){}function z(T,F,q){t=t||Qr(),T=T||{},typeof q!="boolean"&&(q=F instanceof t),this.objectMode=!!T.objectMode,q&&(this.objectMode=this.objectMode||!!T.writableObjectMode),this.highWaterMark=g(this,T,"writableHighWaterMark",q),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var Z=T.decodeStrings===!1;this.decodeStrings=!Z,this.defaultEncoding=T.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(le){m(F,le)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=T.emitClose!==!1,this.autoDestroy=!!T.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new e(this)}z.prototype.getBuffer=function(){for(var F=this.bufferedRequest,q=[];F;)q.push(F),F=F.next;return q},function(){try{Object.defineProperty(z.prototype,"buffer",{get:r.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}}();var te;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(te=Function.prototype[Symbol.hasInstance],Object.defineProperty(X,Symbol.hasInstance,{value:function(F){return te.call(this,F)?!0:this!==X?!1:F&&F._writableState instanceof z}})):te=function(F){return F instanceof this};function X(T){t=t||Qr();var F=this instanceof t;if(!F&&!te.call(X,this))return new X(T);this._writableState=new z(T,this,F),this.writable=!0,T&&(typeof T.write=="function"&&(this._write=T.write),typeof T.writev=="function"&&(this._writev=T.writev),typeof T.destroy=="function"&&(this._destroy=T.destroy),typeof T.final=="function"&&(this._final=T.final)),n.call(this)}X.prototype.pipe=function(){W(this,new $)};function Y(T,F){var q=new N;W(T,q),process.nextTick(F,q)}function de(T,F,q,Z){var le;return q===null?le=new A:typeof q!="string"&&!F.objectMode&&(le=new _("chunk",["string","Buffer"],q)),le?(W(T,le),process.nextTick(Z,le),!1):!0}X.prototype.write=function(T,F,q){var Z=this._writableState,le=!1,M=!Z.objectMode&&o(T);return M&&!i.isBuffer(T)&&(T=c(T)),typeof F=="function"&&(q=F,F=null),M?F="buffer":F||(F=Z.defaultEncoding),typeof q!="function"&&(q=U),Z.ending?Y(this,q):(M||de(this,Z,T,q))&&(Z.pendingcb++,le=pe(this,Z,M,T,F,q)),le},X.prototype.cork=function(){this._writableState.corked++},X.prototype.uncork=function(){var T=this._writableState;T.corked&&(T.corked--,!T.writing&&!T.corked&&!T.bufferProcessing&&T.bufferedRequest&&x(this,T))},X.prototype.setDefaultEncoding=function(F){if(typeof F=="string"&&(F=F.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((F+"").toLowerCase())>-1))throw new C(F);return this._writableState.defaultEncoding=F,this},Object.defineProperty(X.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});function oe(T,F,q){return!T.objectMode&&T.decodeStrings!==!1&&typeof F=="string"&&(F=i.from(F,q)),F}Object.defineProperty(X.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function pe(T,F,q,Z,le,M){if(!q){var I=oe(F,Z,le);Z!==I&&(q=!0,le="buffer",Z=I)}var V=F.objectMode?1:Z.length;F.length+=V;var G=F.length<F.highWaterMark;if(G||(F.needDrain=!0),F.writing||F.corked){var se=F.lastBufferedRequest;F.lastBufferedRequest={chunk:Z,encoding:le,isBuf:q,callback:M,next:null},se?se.next=F.lastBufferedRequest:F.bufferedRequest=F.lastBufferedRequest,F.bufferedRequestCount+=1}else k(T,F,!1,V,Z,le,M);return G}function k(T,F,q,Z,le,M,I){F.writelen=Z,F.writecb=I,F.writing=!0,F.sync=!0,F.destroyed?F.onwrite(new O("write")):q?T._writev(le,F.onwrite):T._write(le,M,F.onwrite),F.sync=!1}function a(T,F,q,Z,le){--F.pendingcb,q?(process.nextTick(le,Z),process.nextTick(R,T,F),T._writableState.errorEmitted=!0,W(T,Z)):(le(Z),T._writableState.errorEmitted=!0,W(T,Z),R(T,F))}function d(T){T.writing=!1,T.writecb=null,T.length-=T.writelen,T.writelen=0}function m(T,F){var q=T._writableState,Z=q.sync,le=q.writecb;if(typeof le!="function")throw new j;if(d(q),F)a(T,q,Z,F,le);else{var M=B(q)||T.destroyed;!M&&!q.corked&&!q.bufferProcessing&&q.bufferedRequest&&x(T,q),Z?process.nextTick(v,T,q,M,le):v(T,q,M,le)}}function v(T,F,q,Z){q||S(T,F),F.pendingcb--,Z(),R(T,F)}function S(T,F){F.length===0&&F.needDrain&&(F.needDrain=!1,T.emit("drain"))}function x(T,F){F.bufferProcessing=!0;var q=F.bufferedRequest;if(T._writev&&q&&q.next){var Z=F.bufferedRequestCount,le=new Array(Z),M=F.corkedRequestsFree;M.entry=q;for(var I=0,V=!0;q;)le[I]=q,q.isBuf||(V=!1),q=q.next,I+=1;le.allBuffers=V,k(T,F,!0,F.length,le,"",M.finish),F.pendingcb++,F.lastBufferedRequest=null,M.next?(F.corkedRequestsFree=M.next,M.next=null):F.corkedRequestsFree=new e(F),F.bufferedRequestCount=0}else{for(;q;){var G=q.chunk,se=q.encoding,ue=q.callback,re=F.objectMode?1:G.length;if(k(T,F,!1,re,G,se,ue),q=q.next,F.bufferedRequestCount--,F.writing)break}q===null&&(F.lastBufferedRequest=null)}F.bufferedRequest=q,F.bufferProcessing=!1}X.prototype._write=function(T,F,q){q(new L("_write()"))},X.prototype._writev=null,X.prototype.end=function(T,F,q){var Z=this._writableState;return typeof T=="function"?(q=T,T=null,F=null):typeof F=="function"&&(q=F,F=null),T!=null&&this.write(T,F),Z.corked&&(Z.corked=1,this.uncork()),Z.ending||K(this,Z,q),this},Object.defineProperty(X.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function B(T){return T.ending&&T.length===0&&T.bufferedRequest===null&&!T.finished&&!T.writing}function b(T,F){T._final(function(q){F.pendingcb--,q&&W(T,q),F.prefinished=!0,T.emit("prefinish"),R(T,F)})}function h(T,F){!F.prefinished&&!F.finalCalled&&(typeof T._final=="function"&&!F.destroyed?(F.pendingcb++,F.finalCalled=!0,process.nextTick(b,T,F)):(F.prefinished=!0,T.emit("prefinish")))}function R(T,F){var q=B(F);if(q&&(h(T,F),F.pendingcb===0&&(F.finished=!0,T.emit("finish"),F.autoDestroy))){var Z=T._readableState;(!Z||Z.autoDestroy&&Z.endEmitted)&&T.destroy()}return q}function K(T,F,q){F.ending=!0,R(T,F),q&&(F.finished?process.nextTick(q):T.once("finish",q)),F.ended=!0,T.writable=!1}function J(T,F,q){var Z=T.entry;for(T.entry=null;Z;){var le=Z.callback;F.pendingcb--,le(q),Z=Z.next}F.corkedRequestsFree.next=T}return Object.defineProperty(X.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(F){this._writableState&&(this._writableState.destroyed=F)}}),X.prototype.destroy=u.destroy,X.prototype._undestroy=u.undestroy,X.prototype._destroy=function(T,F){F(T)},to}var ro,nc;function Qr(){if(nc)return ro;nc=1;var e=Object.keys||function(p){var g=[];for(var w in p)g.push(w);return g};ro=c;var t=Yl(),r=Zl();Rt(c,t);for(var n=e(r.prototype),i=0;i<n.length;i++){var s=n[i];c.prototype[s]||(c.prototype[s]=r.prototype[s])}function c(p){if(!(this instanceof c))return new c(p);t.call(this,p),r.call(this,p),this.allowHalfOpen=!0,p&&(p.readable===!1&&(this.readable=!1),p.writable===!1&&(this.writable=!1),p.allowHalfOpen===!1&&(this.allowHalfOpen=!1,this.once("end",o)))}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(c.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function o(){this._writableState.ended||process.nextTick(u,this)}function u(p){p.end()}return Object.defineProperty(c.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(g){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=g,this._writableState.destroyed=g)}}),ro}var no={},ic;function sc(){if(ic)return no;ic=1;var e=Ut.Buffer,t=e.isEncoding||function(A){switch(A=""+A,A&&A.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function r(A){if(!A)return"utf8";for(var N;;)switch(A){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return A;default:if(N)return;A=(""+A).toLowerCase(),N=!0}}function n(A){var N=r(A);if(typeof N!="string"&&(e.isEncoding===t||!t(A)))throw new Error("Unknown encoding: "+A);return N||A}no.StringDecoder=i;function i(A){this.encoding=n(A);var N;switch(this.encoding){case"utf16le":this.text=w,this.end=_,N=4;break;case"utf8":this.fillLast=u,N=4;break;case"base64":this.text=L,this.end=j,N=3;break;default:this.write=$,this.end=O;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(N)}i.prototype.write=function(A){if(A.length===0)return"";var N,C;if(this.lastNeed){if(N=this.fillLast(A),N===void 0)return"";C=this.lastNeed,this.lastNeed=0}else C=0;return C<A.length?N?N+this.text(A,C):this.text(A,C):N||""},i.prototype.end=g,i.prototype.text=p,i.prototype.fillLast=function(A){if(this.lastNeed<=A.length)return A.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);A.copy(this.lastChar,this.lastTotal-this.lastNeed,0,A.length),this.lastNeed-=A.length};function s(A){return A<=127?0:A>>5===6?2:A>>4===14?3:A>>3===30?4:A>>6===2?-1:-2}function c(A,N,C){var W=N.length-1;if(W<C)return 0;var U=s(N[W]);return U>=0?(U>0&&(A.lastNeed=U-1),U):--W<C||U===-2?0:(U=s(N[W]),U>=0?(U>0&&(A.lastNeed=U-2),U):--W<C||U===-2?0:(U=s(N[W]),U>=0?(U>0&&(U===2?U=0:A.lastNeed=U-3),U):0))}function o(A,N,C){if((N[0]&192)!==128)return A.lastNeed=0,"�";if(A.lastNeed>1&&N.length>1){if((N[1]&192)!==128)return A.lastNeed=1,"�";if(A.lastNeed>2&&N.length>2&&(N[2]&192)!==128)return A.lastNeed=2,"�"}}function u(A){var N=this.lastTotal-this.lastNeed,C=o(this,A);if(C!==void 0)return C;if(this.lastNeed<=A.length)return A.copy(this.lastChar,N,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);A.copy(this.lastChar,N,0,A.length),this.lastNeed-=A.length}function p(A,N){var C=c(this,A,N);if(!this.lastNeed)return A.toString("utf8",N);this.lastTotal=C;var W=A.length-(C-this.lastNeed);return A.copy(this.lastChar,0,W),A.toString("utf8",N,W)}function g(A){var N=A&&A.length?this.write(A):"";return this.lastNeed?N+"�":N}function w(A,N){if((A.length-N)%2===0){var C=A.toString("utf16le",N);if(C){var W=C.charCodeAt(C.length-1);if(W>=55296&&W<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=A[A.length-2],this.lastChar[1]=A[A.length-1],C.slice(0,-1)}return C}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=A[A.length-1],A.toString("utf16le",N,A.length-1)}function _(A){var N=A&&A.length?this.write(A):"";if(this.lastNeed){var C=this.lastTotal-this.lastNeed;return N+this.lastChar.toString("utf16le",0,C)}return N}function L(A,N){var C=(A.length-N)%3;return C===0?A.toString("base64",N):(this.lastNeed=3-C,this.lastTotal=3,C===1?this.lastChar[0]=A[A.length-1]:(this.lastChar[0]=A[A.length-2],this.lastChar[1]=A[A.length-1]),A.toString("base64",N,A.length-C))}function j(A){var N=A&&A.length?this.write(A):"";return this.lastNeed?N+this.lastChar.toString("base64",0,3-this.lastNeed):N}function $(A){return A.toString(this.encoding)}function O(A){return A&&A.length?this.write(A):""}return no}var oc=_r.codes.ERR_STREAM_PREMATURE_CLOSE;function o1(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}function a1(){}function c1(e){return e.setHeader&&typeof e.abort=="function"}function Kl(e,t,r){if(typeof t=="function")return Kl(e,null,t);t||(t={}),r=o1(r||a1);var n=t.readable||t.readable!==!1&&e.readable,i=t.writable||t.writable!==!1&&e.writable,s=function(){e.writable||o()},c=e._writableState&&e._writableState.finished,o=function(){i=!1,c=!0,n||r.call(e)},u=e._readableState&&e._readableState.endEmitted,p=function(){n=!1,u=!0,i||r.call(e)},g=function(j){r.call(e,j)},w=function(){var j;if(n&&!u)return(!e._readableState||!e._readableState.ended)&&(j=new oc),r.call(e,j);if(i&&!c)return(!e._writableState||!e._writableState.ended)&&(j=new oc),r.call(e,j)},_=function(){e.req.on("finish",o)};return c1(e)?(e.on("complete",o),e.on("abort",w),e.req?_():e.on("request",_)):i&&!e._writableState&&(e.on("end",s),e.on("close",s)),e.on("end",p),e.on("finish",o),t.error!==!1&&e.on("error",g),e.on("close",w),function(){e.removeListener("complete",o),e.removeListener("abort",w),e.removeListener("request",_),e.req&&e.req.removeListener("finish",o),e.removeListener("end",s),e.removeListener("close",s),e.removeListener("finish",o),e.removeListener("end",p),e.removeListener("error",g),e.removeListener("close",w)}}var qo=Kl,io,ac;function l1(){if(ac)return io;ac=1;var e;function t(C,W,U){return W=r(W),W in C?Object.defineProperty(C,W,{value:U,enumerable:!0,configurable:!0,writable:!0}):C[W]=U,C}function r(C){var W=n(C,"string");return typeof W=="symbol"?W:String(W)}function n(C,W){if(typeof C!="object"||C===null)return C;var U=C[Symbol.toPrimitive];if(U!==void 0){var z=U.call(C,W);if(typeof z!="object")return z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(W==="string"?String:Number)(C)}var i=qo,s=Symbol("lastResolve"),c=Symbol("lastReject"),o=Symbol("error"),u=Symbol("ended"),p=Symbol("lastPromise"),g=Symbol("handlePromise"),w=Symbol("stream");function _(C,W){return{value:C,done:W}}function L(C){var W=C[s];if(W!==null){var U=C[w].read();U!==null&&(C[p]=null,C[s]=null,C[c]=null,W(_(U,!1)))}}function j(C){process.nextTick(L,C)}function $(C,W){return function(U,z){C.then(function(){if(W[u]){U(_(void 0,!0));return}W[g](U,z)},z)}}var O=Object.getPrototypeOf(function(){}),A=Object.setPrototypeOf((e={get stream(){return this[w]},next:function(){var W=this,U=this[o];if(U!==null)return Promise.reject(U);if(this[u])return Promise.resolve(_(void 0,!0));if(this[w].destroyed)return new Promise(function(Y,de){process.nextTick(function(){W[o]?de(W[o]):Y(_(void 0,!0))})});var z=this[p],te;if(z)te=new Promise($(z,this));else{var X=this[w].read();if(X!==null)return Promise.resolve(_(X,!1));te=new Promise(this[g])}return this[p]=te,te}},t(e,Symbol.asyncIterator,function(){return this}),t(e,"return",function(){var W=this;return new Promise(function(U,z){W[w].destroy(null,function(te){if(te){z(te);return}U(_(void 0,!0))})})}),e),O),N=function(W){var U,z=Object.create(A,(U={},t(U,w,{value:W,writable:!0}),t(U,s,{value:null,writable:!0}),t(U,c,{value:null,writable:!0}),t(U,o,{value:null,writable:!0}),t(U,u,{value:W._readableState.endEmitted,writable:!0}),t(U,g,{value:function(X,Y){var de=z[w].read();de?(z[p]=null,z[s]=null,z[c]=null,X(_(de,!1))):(z[s]=X,z[c]=Y)},writable:!0}),U));return z[p]=null,i(W,function(te){if(te&&te.code!=="ERR_STREAM_PREMATURE_CLOSE"){var X=z[c];X!==null&&(z[p]=null,z[s]=null,z[c]=null,X(te)),z[o]=te;return}var Y=z[s];Y!==null&&(z[p]=null,z[s]=null,z[c]=null,Y(_(void 0,!0))),z[u]=!0}),W.on("readable",j.bind(null,z)),z};return io=N,io}var so,cc;function u1(){return cc||(cc=1,so=function(){throw new Error("Readable.from is not available in the browser")}),so}var oo,lc;function Yl(){if(lc)return oo;lc=1,oo=Y;var e;Y.ReadableState=X,Ei.EventEmitter;var t=function(I,V){return I.listeners(V).length},r=Vl,n=xn.Buffer,i=(typeof ee<"u"?ee:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function s(M){return n.from(M)}function c(M){return n.isBuffer(M)||M instanceof i}var o=ko,u;o&&o.debuglog?u=o.debuglog("stream"):u=function(){};var p=Gp(),g=zl,w=Jl,_=w.getHighWaterMark,L=_r.codes,j=L.ERR_INVALID_ARG_TYPE,$=L.ERR_STREAM_PUSH_AFTER_EOF,O=L.ERR_METHOD_NOT_IMPLEMENTED,A=L.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,N,C,W;Rt(Y,r);var U=g.errorOrDestroy,z=["error","close","destroy","pause","resume"];function te(M,I,V){if(typeof M.prependListener=="function")return M.prependListener(I,V);!M._events||!M._events[I]?M.on(I,V):Array.isArray(M._events[I])?M._events[I].unshift(V):M._events[I]=[V,M._events[I]]}function X(M,I,V){e=e||Qr(),M=M||{},typeof V!="boolean"&&(V=I instanceof e),this.objectMode=!!M.objectMode,V&&(this.objectMode=this.objectMode||!!M.readableObjectMode),this.highWaterMark=_(this,M,"readableHighWaterMark",V),this.buffer=new p,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=M.emitClose!==!1,this.autoDestroy=!!M.autoDestroy,this.destroyed=!1,this.defaultEncoding=M.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,M.encoding&&(N||(N=sc().StringDecoder),this.decoder=new N(M.encoding),this.encoding=M.encoding)}function Y(M){if(e=e||Qr(),!(this instanceof Y))return new Y(M);var I=this instanceof e;this._readableState=new X(M,this,I),this.readable=!0,M&&(typeof M.read=="function"&&(this._read=M.read),typeof M.destroy=="function"&&(this._destroy=M.destroy)),r.call(this)}Object.defineProperty(Y.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(I){this._readableState&&(this._readableState.destroyed=I)}}),Y.prototype.destroy=g.destroy,Y.prototype._undestroy=g.undestroy,Y.prototype._destroy=function(M,I){I(M)},Y.prototype.push=function(M,I){var V=this._readableState,G;return V.objectMode?G=!0:typeof M=="string"&&(I=I||V.defaultEncoding,I!==V.encoding&&(M=n.from(M,I),I=""),G=!0),de(this,M,I,!1,G)},Y.prototype.unshift=function(M){return de(this,M,null,!0,!1)};function de(M,I,V,G,se){u("readableAddChunk",I);var ue=M._readableState;if(I===null)ue.reading=!1,m(M,ue);else{var re;if(se||(re=pe(ue,I)),re)U(M,re);else if(ue.objectMode||I&&I.length>0)if(typeof I!="string"&&!ue.objectMode&&Object.getPrototypeOf(I)!==n.prototype&&(I=s(I)),G)ue.endEmitted?U(M,new A):oe(M,ue,I,!0);else if(ue.ended)U(M,new $);else{if(ue.destroyed)return!1;ue.reading=!1,ue.decoder&&!V?(I=ue.decoder.write(I),ue.objectMode||I.length!==0?oe(M,ue,I,!1):x(M,ue)):oe(M,ue,I,!1)}else G||(ue.reading=!1,x(M,ue))}return!ue.ended&&(ue.length<ue.highWaterMark||ue.length===0)}function oe(M,I,V,G){I.flowing&&I.length===0&&!I.sync?(I.awaitDrain=0,M.emit("data",V)):(I.length+=I.objectMode?1:V.length,G?I.buffer.unshift(V):I.buffer.push(V),I.needReadable&&v(M)),x(M,I)}function pe(M,I){var V;return!c(I)&&typeof I!="string"&&I!==void 0&&!M.objectMode&&(V=new j("chunk",["string","Buffer","Uint8Array"],I)),V}Y.prototype.isPaused=function(){return this._readableState.flowing===!1},Y.prototype.setEncoding=function(M){N||(N=sc().StringDecoder);var I=new N(M);this._readableState.decoder=I,this._readableState.encoding=this._readableState.decoder.encoding;for(var V=this._readableState.buffer.head,G="";V!==null;)G+=I.write(V.data),V=V.next;return this._readableState.buffer.clear(),G!==""&&this._readableState.buffer.push(G),this._readableState.length=G.length,this};var k=1073741824;function a(M){return M>=k?M=k:(M--,M|=M>>>1,M|=M>>>2,M|=M>>>4,M|=M>>>8,M|=M>>>16,M++),M}function d(M,I){return M<=0||I.length===0&&I.ended?0:I.objectMode?1:M!==M?I.flowing&&I.length?I.buffer.head.data.length:I.length:(M>I.highWaterMark&&(I.highWaterMark=a(M)),M<=I.length?M:I.ended?I.length:(I.needReadable=!0,0))}Y.prototype.read=function(M){u("read",M),M=parseInt(M,10);var I=this._readableState,V=M;if(M!==0&&(I.emittedReadable=!1),M===0&&I.needReadable&&((I.highWaterMark!==0?I.length>=I.highWaterMark:I.length>0)||I.ended))return u("read: emitReadable",I.length,I.ended),I.length===0&&I.ended?q(this):v(this),null;if(M=d(M,I),M===0&&I.ended)return I.length===0&&q(this),null;var G=I.needReadable;u("need readable",G),(I.length===0||I.length-M<I.highWaterMark)&&(G=!0,u("length less than watermark",G)),I.ended||I.reading?(G=!1,u("reading or ended",G)):G&&(u("do read"),I.reading=!0,I.sync=!0,I.length===0&&(I.needReadable=!0),this._read(I.highWaterMark),I.sync=!1,I.reading||(M=d(V,I)));var se;return M>0?se=F(M,I):se=null,se===null?(I.needReadable=I.length<=I.highWaterMark,M=0):(I.length-=M,I.awaitDrain=0),I.length===0&&(I.ended||(I.needReadable=!0),V!==M&&I.ended&&q(this)),se!==null&&this.emit("data",se),se};function m(M,I){if(u("onEofChunk"),!I.ended){if(I.decoder){var V=I.decoder.end();V&&V.length&&(I.buffer.push(V),I.length+=I.objectMode?1:V.length)}I.ended=!0,I.sync?v(M):(I.needReadable=!1,I.emittedReadable||(I.emittedReadable=!0,S(M)))}}function v(M){var I=M._readableState;u("emitReadable",I.needReadable,I.emittedReadable),I.needReadable=!1,I.emittedReadable||(u("emitReadable",I.flowing),I.emittedReadable=!0,process.nextTick(S,M))}function S(M){var I=M._readableState;u("emitReadable_",I.destroyed,I.length,I.ended),!I.destroyed&&(I.length||I.ended)&&(M.emit("readable"),I.emittedReadable=!1),I.needReadable=!I.flowing&&!I.ended&&I.length<=I.highWaterMark,T(M)}function x(M,I){I.readingMore||(I.readingMore=!0,process.nextTick(B,M,I))}function B(M,I){for(;!I.reading&&!I.ended&&(I.length<I.highWaterMark||I.flowing&&I.length===0);){var V=I.length;if(u("maybeReadMore read 0"),M.read(0),V===I.length)break}I.readingMore=!1}Y.prototype._read=function(M){U(this,new O("_read()"))},Y.prototype.pipe=function(M,I){var V=this,G=this._readableState;switch(G.pipesCount){case 0:G.pipes=M;break;case 1:G.pipes=[G.pipes,M];break;default:G.pipes.push(M);break}G.pipesCount+=1,u("pipe count=%d opts=%j",G.pipesCount,I);var se=(!I||I.end!==!1)&&M!==process.stdout&&M!==process.stderr,ue=se?we:Ee;G.endEmitted?process.nextTick(ue):V.once("end",ue),M.on("unpipe",re);function re(y,l){u("onunpipe"),y===V&&l&&l.hasUnpiped===!1&&(l.hasUnpiped=!0,Se())}function we(){u("onend"),M.end()}var dt=b(V);M.on("drain",dt);var Re=!1;function Se(){u("cleanup"),M.removeListener("close",Me),M.removeListener("finish",Qe),M.removeListener("drain",dt),M.removeListener("error",_e),M.removeListener("unpipe",re),V.removeListener("end",we),V.removeListener("end",Ee),V.removeListener("data",Ze),Re=!0,G.awaitDrain&&(!M._writableState||M._writableState.needDrain)&&dt()}V.on("data",Ze);function Ze(y){u("ondata");var l=M.write(y);u("dest.write",l),l===!1&&((G.pipesCount===1&&G.pipes===M||G.pipesCount>1&&le(G.pipes,M)!==-1)&&!Re&&(u("false write response, pause",G.awaitDrain),G.awaitDrain++),V.pause())}function _e(y){u("onerror",y),Ee(),M.removeListener("error",_e),t(M,"error")===0&&U(M,y)}te(M,"error",_e);function Me(){M.removeListener("finish",Qe),Ee()}M.once("close",Me);function Qe(){u("onfinish"),M.removeListener("close",Me),Ee()}M.once("finish",Qe);function Ee(){u("unpipe"),V.unpipe(M)}return M.emit("pipe",V),G.flowing||(u("pipe resume"),V.resume()),M};function b(M){return function(){var V=M._readableState;u("pipeOnDrain",V.awaitDrain),V.awaitDrain&&V.awaitDrain--,V.awaitDrain===0&&t(M,"data")&&(V.flowing=!0,T(M))}}Y.prototype.unpipe=function(M){var I=this._readableState,V={hasUnpiped:!1};if(I.pipesCount===0)return this;if(I.pipesCount===1)return M&&M!==I.pipes?this:(M||(M=I.pipes),I.pipes=null,I.pipesCount=0,I.flowing=!1,M&&M.emit("unpipe",this,V),this);if(!M){var G=I.pipes,se=I.pipesCount;I.pipes=null,I.pipesCount=0,I.flowing=!1;for(var ue=0;ue<se;ue++)G[ue].emit("unpipe",this,{hasUnpiped:!1});return this}var re=le(I.pipes,M);return re===-1?this:(I.pipes.splice(re,1),I.pipesCount-=1,I.pipesCount===1&&(I.pipes=I.pipes[0]),M.emit("unpipe",this,V),this)},Y.prototype.on=function(M,I){var V=r.prototype.on.call(this,M,I),G=this._readableState;return M==="data"?(G.readableListening=this.listenerCount("readable")>0,G.flowing!==!1&&this.resume()):M==="readable"&&!G.endEmitted&&!G.readableListening&&(G.readableListening=G.needReadable=!0,G.flowing=!1,G.emittedReadable=!1,u("on readable",G.length,G.reading),G.length?v(this):G.reading||process.nextTick(R,this)),V},Y.prototype.addListener=Y.prototype.on,Y.prototype.removeListener=function(M,I){var V=r.prototype.removeListener.call(this,M,I);return M==="readable"&&process.nextTick(h,this),V},Y.prototype.removeAllListeners=function(M){var I=r.prototype.removeAllListeners.apply(this,arguments);return(M==="readable"||M===void 0)&&process.nextTick(h,this),I};function h(M){var I=M._readableState;I.readableListening=M.listenerCount("readable")>0,I.resumeScheduled&&!I.paused?I.flowing=!0:M.listenerCount("data")>0&&M.resume()}function R(M){u("readable nexttick read 0"),M.read(0)}Y.prototype.resume=function(){var M=this._readableState;return M.flowing||(u("resume"),M.flowing=!M.readableListening,K(this,M)),M.paused=!1,this};function K(M,I){I.resumeScheduled||(I.resumeScheduled=!0,process.nextTick(J,M,I))}function J(M,I){u("resume",I.reading),I.reading||M.read(0),I.resumeScheduled=!1,M.emit("resume"),T(M),I.flowing&&!I.reading&&M.read(0)}Y.prototype.pause=function(){return u("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(u("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this};function T(M){var I=M._readableState;for(u("flow",I.flowing);I.flowing&&M.read()!==null;);}Y.prototype.wrap=function(M){var I=this,V=this._readableState,G=!1;M.on("end",function(){if(u("wrapped end"),V.decoder&&!V.ended){var re=V.decoder.end();re&&re.length&&I.push(re)}I.push(null)}),M.on("data",function(re){if(u("wrapped data"),V.decoder&&(re=V.decoder.write(re)),!(V.objectMode&&re==null)&&!(!V.objectMode&&(!re||!re.length))){var we=I.push(re);we||(G=!0,M.pause())}});for(var se in M)this[se]===void 0&&typeof M[se]=="function"&&(this[se]=function(we){return function(){return M[we].apply(M,arguments)}}(se));for(var ue=0;ue<z.length;ue++)M.on(z[ue],this.emit.bind(this,z[ue]));return this._read=function(re){u("wrapped _read",re),G&&(G=!1,M.resume())},this},typeof Symbol=="function"&&(Y.prototype[Symbol.asyncIterator]=function(){return C===void 0&&(C=l1()),C(this)}),Object.defineProperty(Y.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(Y.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(Y.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(I){this._readableState&&(this._readableState.flowing=I)}}),Y._fromList=F,Object.defineProperty(Y.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}});function F(M,I){if(I.length===0)return null;var V;return I.objectMode?V=I.buffer.shift():!M||M>=I.length?(I.decoder?V=I.buffer.join(""):I.buffer.length===1?V=I.buffer.first():V=I.buffer.concat(I.length),I.buffer.clear()):V=I.buffer.consume(M,I.decoder),V}function q(M){var I=M._readableState;u("endReadable",I.endEmitted),I.endEmitted||(I.ended=!0,process.nextTick(Z,I,M))}function Z(M,I){if(u("endReadableNT",M.endEmitted,M.length),!M.endEmitted&&M.length===0&&(M.endEmitted=!0,I.readable=!1,I.emit("end"),M.autoDestroy)){var V=I._writableState;(!V||V.autoDestroy&&V.finished)&&I.destroy()}}typeof Symbol=="function"&&(Y.from=function(M,I){return W===void 0&&(W=u1()),W(Y,M,I)});function le(M,I){for(var V=0,G=M.length;V<G;V++)if(M[V]===I)return V;return-1}return oo}var Ql=jt,Qi=_r.codes,h1=Qi.ERR_METHOD_NOT_IMPLEMENTED,f1=Qi.ERR_MULTIPLE_CALLBACK,d1=Qi.ERR_TRANSFORM_ALREADY_TRANSFORMING,p1=Qi.ERR_TRANSFORM_WITH_LENGTH_0,Xi=Qr();Rt(jt,Xi);function g1(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(n===null)return this.emit("error",new f1);r.writechunk=null,r.writecb=null,t!=null&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function jt(e){if(!(this instanceof jt))return new jt(e);Xi.call(this,e),this._transformState={afterTransform:g1.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(typeof e.transform=="function"&&(this._transform=e.transform),typeof e.flush=="function"&&(this._flush=e.flush)),this.on("prefinish",y1)}function y1(){var e=this;typeof this._flush=="function"&&!this._readableState.destroyed?this._flush(function(t,r){uc(e,t,r)}):uc(this,null,null)}jt.prototype.push=function(e,t){return this._transformState.needTransform=!1,Xi.prototype.push.call(this,e,t)};jt.prototype._transform=function(e,t,r){r(new h1("_transform()"))};jt.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}};jt.prototype._read=function(e){var t=this._transformState;t.writechunk!==null&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0};jt.prototype._destroy=function(e,t){Xi.prototype._destroy.call(this,e,function(r){t(r)})};function uc(e,t,r){if(t)return e.emit("error",t);if(r!=null&&e.push(r),e._writableState.length)throw new p1;if(e._transformState.transforming)throw new d1;return e.push(null)}var m1=bn,Xl=Ql;Rt(bn,Xl);function bn(e){if(!(this instanceof bn))return new bn(e);Xl.call(this,e)}bn.prototype._transform=function(e,t,r){r(null,e)};var ao;function v1(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}var eu=_r.codes,b1=eu.ERR_MISSING_ARGS,w1=eu.ERR_STREAM_DESTROYED;function hc(e){if(e)throw e}function _1(e){return e.setHeader&&typeof e.abort=="function"}function E1(e,t,r,n){n=v1(n);var i=!1;e.on("close",function(){i=!0}),ao===void 0&&(ao=qo),ao(e,{readable:t,writable:r},function(c){if(c)return n(c);i=!0,n()});var s=!1;return function(c){if(!i&&!s){if(s=!0,_1(e))return e.abort();if(typeof e.destroy=="function")return e.destroy();n(c||new w1("pipe"))}}}function fc(e){e()}function S1(e,t){return e.pipe(t)}function R1(e){return!e.length||typeof e[e.length-1]!="function"?hc:e.pop()}function M1(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=R1(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new b1("streams");var i,s=t.map(function(c,o){var u=o<t.length-1,p=o>0;return E1(c,u,p,function(g){i||(i=g),g&&s.forEach(fc),!u&&(s.forEach(fc),n(i))})});return t.reduce(S1)}var I1=M1;(function(e,t){t=e.exports=Yl(),t.Stream=t,t.Readable=t,t.Writable=Zl(),t.Duplex=Qr(),t.Transform=Ql,t.PassThrough=m1,t.finished=qo,t.pipeline=I1})(_o,_o.exports);var tu=_o.exports;const{Transform:A1}=tu;var x1=e=>class ru extends A1{constructor(r,n,i,s,c){super(c),this._rate=r,this._capacity=n,this._delimitedSuffix=i,this._hashBitLength=s,this._options=c,this._state=new e,this._state.initialize(r,n),this._finalized=!1}_transform(r,n,i){let s=null;try{this.update(r,n)}catch(c){s=c}i(s)}_flush(r){let n=null;try{this.push(this.digest())}catch(i){n=i}r(n)}update(r,n){if(!Buffer.isBuffer(r)&&typeof r!="string")throw new TypeError("Data must be a string or a buffer");if(this._finalized)throw new Error("Digest already called");return Buffer.isBuffer(r)||(r=Buffer.from(r,n)),this._state.absorb(r),this}digest(r){if(this._finalized)throw new Error("Digest already called");this._finalized=!0,this._delimitedSuffix&&this._state.absorbLastFewBits(this._delimitedSuffix);let n=this._state.squeeze(this._hashBitLength/8);return r!==void 0&&(n=n.toString(r)),this._resetState(),n}_resetState(){return this._state.initialize(this._rate,this._capacity),this}_clone(){const r=new ru(this._rate,this._capacity,this._delimitedSuffix,this._hashBitLength,this._options);return this._state.copy(r._state),r._finalized=this._finalized,r}};const{Transform:k1}=tu;var C1=e=>class nu extends k1{constructor(r,n,i,s){super(s),this._rate=r,this._capacity=n,this._delimitedSuffix=i,this._options=s,this._state=new e,this._state.initialize(r,n),this._finalized=!1}_transform(r,n,i){let s=null;try{this.update(r,n)}catch(c){s=c}i(s)}_flush(){}_read(r){this.push(this.squeeze(r))}update(r,n){if(!Buffer.isBuffer(r)&&typeof r!="string")throw new TypeError("Data must be a string or a buffer");if(this._finalized)throw new Error("Squeeze already called");return Buffer.isBuffer(r)||(r=Buffer.from(r,n)),this._state.absorb(r),this}squeeze(r,n){this._finalized||(this._finalized=!0,this._state.absorbLastFewBits(this._delimitedSuffix));let i=this._state.squeeze(r);return n!==void 0&&(i=i.toString(n)),i}_resetState(){return this._state.initialize(this._rate,this._capacity),this}_clone(){const r=new nu(this._rate,this._capacity,this._delimitedSuffix,this._options);return this._state.copy(r._state),r._finalized=this._finalized,r}};const T1=x1,B1=C1;var N1=function(e){const t=T1(e),r=B1(e);return function(n,i){switch(typeof n=="string"?n.toLowerCase():n){case"keccak224":return new t(1152,448,null,224,i);case"keccak256":return new t(1088,512,null,256,i);case"keccak384":return new t(832,768,null,384,i);case"keccak512":return new t(576,1024,null,512,i);case"sha3-224":return new t(1152,448,6,224,i);case"sha3-256":return new t(1088,512,6,256,i);case"sha3-384":return new t(832,768,6,384,i);case"sha3-512":return new t(576,1024,6,512,i);case"shake128":return new r(1344,256,31,i);case"shake256":return new r(1088,512,31,i);default:throw new Error("Invald algorithm: "+n)}}},iu={};const dc=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648];iu.p1600=function(e){for(let t=0;t<24;++t){const r=e[0]^e[10]^e[20]^e[30]^e[40],n=e[1]^e[11]^e[21]^e[31]^e[41],i=e[2]^e[12]^e[22]^e[32]^e[42],s=e[3]^e[13]^e[23]^e[33]^e[43],c=e[4]^e[14]^e[24]^e[34]^e[44],o=e[5]^e[15]^e[25]^e[35]^e[45],u=e[6]^e[16]^e[26]^e[36]^e[46],p=e[7]^e[17]^e[27]^e[37]^e[47],g=e[8]^e[18]^e[28]^e[38]^e[48],w=e[9]^e[19]^e[29]^e[39]^e[49];let _=g^(i<<1|s>>>31),L=w^(s<<1|i>>>31);const j=e[0]^_,$=e[1]^L,O=e[10]^_,A=e[11]^L,N=e[20]^_,C=e[21]^L,W=e[30]^_,U=e[31]^L,z=e[40]^_,te=e[41]^L;_=r^(c<<1|o>>>31),L=n^(o<<1|c>>>31);const X=e[2]^_,Y=e[3]^L,de=e[12]^_,oe=e[13]^L,pe=e[22]^_,k=e[23]^L,a=e[32]^_,d=e[33]^L,m=e[42]^_,v=e[43]^L;_=i^(u<<1|p>>>31),L=s^(p<<1|u>>>31);const S=e[4]^_,x=e[5]^L,B=e[14]^_,b=e[15]^L,h=e[24]^_,R=e[25]^L,K=e[34]^_,J=e[35]^L,T=e[44]^_,F=e[45]^L;_=c^(g<<1|w>>>31),L=o^(w<<1|g>>>31);const q=e[6]^_,Z=e[7]^L,le=e[16]^_,M=e[17]^L,I=e[26]^_,V=e[27]^L,G=e[36]^_,se=e[37]^L,ue=e[46]^_,re=e[47]^L;_=u^(r<<1|n>>>31),L=p^(n<<1|r>>>31);const we=e[8]^_,dt=e[9]^L,Re=e[18]^_,Se=e[19]^L,Ze=e[28]^_,_e=e[29]^L,Me=e[38]^_,Qe=e[39]^L,Ee=e[48]^_,y=e[49]^L,l=j,f=$,E=A<<4|O>>>28,P=O<<4|A>>>28,D=N<<3|C>>>29,H=C<<3|N>>>29,he=U<<9|W>>>23,ae=W<<9|U>>>23,ie=z<<18|te>>>14,Ie=te<<18|z>>>14,ne=X<<1|Y>>>31,Ae=Y<<1|X>>>31,Rr=oe<<12|de>>>20,xe=de<<12|oe>>>20,ke=pe<<10|k>>>22,Mr=k<<10|pe>>>22,Ce=d<<13|a>>>19,Te=a<<13|d>>>19,Ir=m<<2|v>>>30,Be=v<<2|m>>>30,Ne=x<<30|S>>>2,Ar=S<<30|x>>>2,Pe=B<<6|b>>>26,Le=b<<6|B>>>26,xr=R<<11|h>>>21,Oe=h<<11|R>>>21,$e=K<<15|J>>>17,kr=J<<15|K>>>17,Fe=F<<29|T>>>3,De=T<<29|F>>>3,Cr=q<<28|Z>>>4,je=Z<<28|q>>>4,Ue=M<<23|le>>>9,Tr=le<<23|M>>>9,He=I<<25|V>>>7,We=V<<25|I>>>7,Jt=G<<21|se>>>11,Zt=se<<21|G>>>11,Kt=re<<24|ue>>>8,Yt=ue<<24|re>>>8,Qt=we<<27|dt>>>5,Xt=dt<<27|we>>>5,er=Re<<20|Se>>>12,tr=Se<<20|Re>>>12,rr=_e<<7|Ze>>>25,nr=Ze<<7|_e>>>25,ir=Me<<8|Qe>>>24,sr=Qe<<8|Me>>>24,or=Ee<<14|y>>>18,ar=y<<14|Ee>>>18;e[0]=l^~Rr&xr,e[1]=f^~xe&Oe,e[10]=Cr^~er&D,e[11]=je^~tr&H,e[20]=ne^~Pe&He,e[21]=Ae^~Le&We,e[30]=Qt^~E&ke,e[31]=Xt^~P&Mr,e[40]=Ne^~Ue&rr,e[41]=Ar^~Tr&nr,e[2]=Rr^~xr&Jt,e[3]=xe^~Oe&Zt,e[12]=er^~D&Ce,e[13]=tr^~H&Te,e[22]=Pe^~He&ir,e[23]=Le^~We&sr,e[32]=E^~ke&$e,e[33]=P^~Mr&kr,e[42]=Ue^~rr&he,e[43]=Tr^~nr&ae,e[4]=xr^~Jt&or,e[5]=Oe^~Zt&ar,e[14]=D^~Ce&Fe,e[15]=H^~Te&De,e[24]=He^~ir&ie,e[25]=We^~sr&Ie,e[34]=ke^~$e&Kt,e[35]=Mr^~kr&Yt,e[44]=rr^~he&Ir,e[45]=nr^~ae&Be,e[6]=Jt^~or&l,e[7]=Zt^~ar&f,e[16]=Ce^~Fe&Cr,e[17]=Te^~De&je,e[26]=ir^~ie&ne,e[27]=sr^~Ie&Ae,e[36]=$e^~Kt&Qt,e[37]=kr^~Yt&Xt,e[46]=he^~Ir&Ne,e[47]=ae^~Be&Ar,e[8]=or^~l&Rr,e[9]=ar^~f&xe,e[18]=Fe^~Cr&er,e[19]=De^~je&tr,e[28]=ie^~ne&Pe,e[29]=Ie^~Ae&Le,e[38]=Kt^~Qt&E,e[39]=Yt^~Xt&P,e[48]=Ir^~Ne&Ue,e[49]=Be^~Ar&Tr,e[0]^=dc[t*2],e[1]^=dc[t*2+1]}};const bi=iu;function ln(){this.state=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.blockSize=null,this.count=0,this.squeezing=!1}ln.prototype.initialize=function(e,t){for(let r=0;r<50;++r)this.state[r]=0;this.blockSize=e/8,this.count=0,this.squeezing=!1};ln.prototype.absorb=function(e){for(let t=0;t<e.length;++t)this.state[~~(this.count/4)]^=e[t]<<8*(this.count%4),this.count+=1,this.count===this.blockSize&&(bi.p1600(this.state),this.count=0)};ln.prototype.absorbLastFewBits=function(e){this.state[~~(this.count/4)]^=e<<8*(this.count%4),e&128&&this.count===this.blockSize-1&&bi.p1600(this.state),this.state[~~((this.blockSize-1)/4)]^=128<<8*((this.blockSize-1)%4),bi.p1600(this.state),this.count=0,this.squeezing=!0};ln.prototype.squeeze=function(e){this.squeezing||this.absorbLastFewBits(1);const t=Buffer.alloc(e);for(let r=0;r<e;++r)t[r]=this.state[~~(this.count/4)]>>>8*(this.count%4)&255,this.count+=1,this.count===this.blockSize&&(bi.p1600(this.state),this.count=0);return t};ln.prototype.copy=function(e){for(let t=0;t<50;++t)e.state[t]=this.state[t];e.blockSize=this.blockSize,e.count=this.count,e.squeezing=this.squeezing};var P1=ln,L1=N1(P1);const O1=L1,$1=Mi;function su(e){return Buffer.allocUnsafe(e).fill(0)}function ou(e,t,r){const n=su(t);return e=es(e),r?e.length<t?(e.copy(n),n):e.slice(0,t):e.length<t?(e.copy(n,t-e.length),n):e.slice(-t)}function F1(e,t){return ou(e,t,!0)}function es(e){if(!Buffer.isBuffer(e))if(Array.isArray(e))e=Buffer.from(e);else if(typeof e=="string")au(e)?e=Buffer.from(U1(cu(e)),"hex"):e=Buffer.from(e);else if(typeof e=="number")e=intToBuffer(e);else if(e==null)e=Buffer.allocUnsafe(0);else if($1.isBN(e))e=e.toArrayLike(Buffer);else if(e.toArray)e=Buffer.from(e.toArray());else throw new Error("invalid type");return e}function D1(e){return e=es(e),"0x"+e.toString("hex")}function j1(e,t){return e=es(e),t||(t=256),O1("keccak"+t).update(e).digest()}function U1(e){return e.length%2?"0"+e:e}function au(e){return typeof e=="string"&&e.match(/^0x[0-9A-Fa-f]*$/)}function cu(e){return typeof e=="string"&&e.startsWith("0x")?e.slice(2):e}var lu={zeros:su,setLength:ou,setLengthRight:F1,isHexString:au,stripHexPrefix:cu,toBuffer:es,bufferToHex:D1,keccak:j1};const vr=lu,pr=Mi;function uu(e){return e.startsWith("int[")?"int256"+e.slice(3):e==="int"?"int256":e.startsWith("uint[")?"uint256"+e.slice(4):e==="uint"?"uint256":e.startsWith("fixed[")?"fixed128x128"+e.slice(5):e==="fixed"?"fixed128x128":e.startsWith("ufixed[")?"ufixed128x128"+e.slice(6):e==="ufixed"?"ufixed128x128":e}function Vr(e){return parseInt(/^\D+(\d+)$/.exec(e)[1],10)}function pc(e){var t=/^\D+(\d+)x(\d+)$/.exec(e);return[parseInt(t[1],10),parseInt(t[2],10)]}function hu(e){var t=e.match(/(.*)\[(.*?)\]$/);return t?t[2]===""?"dynamic":parseInt(t[2],10):null}function fr(e){var t=typeof e;if(t==="string")return vr.isHexString(e)?new pr(vr.stripHexPrefix(e),16):new pr(e,10);if(t==="number")return new pr(e);if(e.toArray)return e;throw new Error("Argument is not a number")}function Ct(e,t){var r,n,i,s;if(e==="address")return Ct("uint160",fr(t));if(e==="bool")return Ct("uint8",t?1:0);if(e==="string")return Ct("bytes",new Buffer(t,"utf8"));if(W1(e)){if(typeof t.length>"u")throw new Error("Not an array?");if(r=hu(e),r!=="dynamic"&&r!==0&&t.length>r)throw new Error("Elements exceed array size: "+r);i=[],e=e.slice(0,e.lastIndexOf("[")),typeof t=="string"&&(t=JSON.parse(t));for(s in t)i.push(Ct(e,t[s]));if(r==="dynamic"){var c=Ct("uint256",t.length);i.unshift(c)}return Buffer.concat(i)}else{if(e==="bytes")return t=new Buffer(t),i=Buffer.concat([Ct("uint256",t.length),t]),t.length%32!==0&&(i=Buffer.concat([i,vr.zeros(32-t.length%32)])),i;if(e.startsWith("bytes")){if(r=Vr(e),r<1||r>32)throw new Error("Invalid bytes<N> width: "+r);return vr.setLengthRight(t,32)}else if(e.startsWith("uint")){if(r=Vr(e),r%8||r<8||r>256)throw new Error("Invalid uint<N> width: "+r);if(n=fr(t),n.bitLength()>r)throw new Error("Supplied uint exceeds width: "+r+" vs "+n.bitLength());if(n<0)throw new Error("Supplied uint is negative");return n.toArrayLike(Buffer,"be",32)}else if(e.startsWith("int")){if(r=Vr(e),r%8||r<8||r>256)throw new Error("Invalid int<N> width: "+r);if(n=fr(t),n.bitLength()>r)throw new Error("Supplied int exceeds width: "+r+" vs "+n.bitLength());return n.toTwos(256).toArrayLike(Buffer,"be",32)}else if(e.startsWith("ufixed")){if(r=pc(e),n=fr(t),n<0)throw new Error("Supplied ufixed is negative");return Ct("uint256",n.mul(new pr(2).pow(new pr(r[1]))))}else if(e.startsWith("fixed"))return r=pc(e),Ct("int256",fr(t).mul(new pr(2).pow(new pr(r[1]))))}throw new Error("Unsupported or invalid type: "+e)}function H1(e){return e==="string"||e==="bytes"||hu(e)==="dynamic"}function W1(e){return e.lastIndexOf("]")===e.length-1}function q1(e,t){var r=[],n=[],i=32*e.length;for(var s in e){var c=uu(e[s]),o=t[s],u=Ct(c,o);H1(c)?(r.push(Ct("uint256",i)),n.push(u),i+=u.length):r.push(u)}return Buffer.concat(r.concat(n))}function fu(e,t){if(e.length!==t.length)throw new Error("Number of types are not matching the values");for(var r,n,i=[],s=0;s<e.length;s++){var c=uu(e[s]),o=t[s];if(c==="bytes")i.push(o);else if(c==="string")i.push(new Buffer(o,"utf8"));else if(c==="bool")i.push(new Buffer(o?"01":"00","hex"));else if(c==="address")i.push(vr.setLength(o,20));else if(c.startsWith("bytes")){if(r=Vr(c),r<1||r>32)throw new Error("Invalid bytes<N> width: "+r);i.push(vr.setLengthRight(o,r))}else if(c.startsWith("uint")){if(r=Vr(c),r%8||r<8||r>256)throw new Error("Invalid uint<N> width: "+r);if(n=fr(o),n.bitLength()>r)throw new Error("Supplied uint exceeds width: "+r+" vs "+n.bitLength());i.push(n.toArrayLike(Buffer,"be",r/8))}else if(c.startsWith("int")){if(r=Vr(c),r%8||r<8||r>256)throw new Error("Invalid int<N> width: "+r);if(n=fr(o),n.bitLength()>r)throw new Error("Supplied int exceeds width: "+r+" vs "+n.bitLength());i.push(n.toTwos(r).toArrayLike(Buffer,"be",r/8))}else throw new Error("Unsupported or invalid type: "+c)}return Buffer.concat(i)}function V1(e,t){return vr.keccak(fu(e,t))}var z1={rawEncode:q1,solidityPack:fu,soliditySHA3:V1};const St=lu,yn=z1,du={type:"object",properties:{types:{type:"object",additionalProperties:{type:"array",items:{type:"object",properties:{name:{type:"string"},type:{type:"string"}},required:["name","type"]}}},primaryType:{type:"string"},domain:{type:"object"},message:{type:"object"}},required:["types","primaryType","domain","message"]},co={encodeData(e,t,r,n=!0){const i=["bytes32"],s=[this.hashType(e,r)];if(n){const c=(o,u,p)=>{if(r[u]!==void 0)return["bytes32",p==null?"0x0000000000000000000000000000000000000000000000000000000000000000":St.keccak(this.encodeData(u,p,r,n))];if(p===void 0)throw new Error(`missing value for field ${o} of type ${u}`);if(u==="bytes")return["bytes32",St.keccak(p)];if(u==="string")return typeof p=="string"&&(p=Buffer.from(p,"utf8")),["bytes32",St.keccak(p)];if(u.lastIndexOf("]")===u.length-1){const g=u.slice(0,u.lastIndexOf("[")),w=p.map(_=>c(o,g,_));return["bytes32",St.keccak(yn.rawEncode(w.map(([_])=>_),w.map(([,_])=>_)))]}return[u,p]};for(const o of r[e]){const[u,p]=c(o.name,o.type,t[o.name]);i.push(u),s.push(p)}}else for(const c of r[e]){let o=t[c.name];if(o!==void 0)if(c.type==="bytes")i.push("bytes32"),o=St.keccak(o),s.push(o);else if(c.type==="string")i.push("bytes32"),typeof o=="string"&&(o=Buffer.from(o,"utf8")),o=St.keccak(o),s.push(o);else if(r[c.type]!==void 0)i.push("bytes32"),o=St.keccak(this.encodeData(c.type,o,r,n)),s.push(o);else{if(c.type.lastIndexOf("]")===c.type.length-1)throw new Error("Arrays currently unimplemented in encodeData");i.push(c.type),s.push(o)}}return yn.rawEncode(i,s)},encodeType(e,t){let r="",n=this.findTypeDependencies(e,t).filter(i=>i!==e);n=[e].concat(n.sort());for(const i of n){if(!t[i])throw new Error("No type definition specified: "+i);r+=i+"("+t[i].map(({name:c,type:o})=>o+" "+c).join(",")+")"}return r},findTypeDependencies(e,t,r=[]){if(e=e.match(/^\w*/)[0],r.includes(e)||t[e]===void 0)return r;r.push(e);for(const n of t[e])for(const i of this.findTypeDependencies(n.type,t,r))!r.includes(i)&&r.push(i);return r},hashStruct(e,t,r,n=!0){return St.keccak(this.encodeData(e,t,r,n))},hashType(e,t){return St.keccak(this.encodeType(e,t))},sanitizeData(e){const t={};for(const r in du.properties)e[r]&&(t[r]=e[r]);return t.types&&(t.types=Object.assign({EIP712Domain:[]},t.types)),t},hash(e,t=!0){const r=this.sanitizeData(e),n=[Buffer.from("1901","hex")];return n.push(this.hashStruct("EIP712Domain",r.domain,r.types,t)),r.primaryType!=="EIP712Domain"&&n.push(this.hashStruct(r.primaryType,r.message,r.types,t)),St.keccak(Buffer.concat(n))}};var G1={TYPED_MESSAGE_SCHEMA:du,TypedDataUtils:co,hashForSignTypedDataLegacy:function(e){return J1(e.data)},hashForSignTypedData_v3:function(e){return co.hash(e.data,!1)},hashForSignTypedData_v4:function(e){return co.hash(e.data)}};function J1(e){const t=new Error("Expect argument to be non-empty array");if(typeof e!="object"||!e.length)throw t;const r=e.map(function(s){return s.type==="bytes"?St.toBuffer(s.value):s.value}),n=e.map(function(s){return s.type}),i=e.map(function(s){if(!s.name)throw t;return s.type+" "+s.name});return yn.soliditySHA3(["bytes32","bytes32"],[yn.soliditySHA3(new Array(e.length).fill("string"),i),yn.soliditySHA3(n,r)])}var Xr={};Object.defineProperty(Xr,"__esModule",{value:!0});Xr.filterFromParam=Xr.FilterPolyfill=void 0;const Ur=Ke,ot=Q,Z1=5*60*1e3,dr={jsonrpc:"2.0",id:0};class K1{constructor(t){this.logFilters=new Map,this.blockFilters=new Set,this.pendingTransactionFilters=new Set,this.cursors=new Map,this.timeouts=new Map,this.nextFilterId=(0,Ur.IntNumber)(1),this.REQUEST_THROTTLE_INTERVAL=1e3,this.lastFetchTimestamp=new Date(0),this.resolvers=[],this.provider=t}async newFilter(t){const r=pu(t),n=this.makeFilterId(),i=await this.setInitialCursorPosition(n,r.fromBlock);return console.info(`Installing new log filter(${n}):`,r,"initial cursor position:",i),this.logFilters.set(n,r),this.setFilterTimeout(n),(0,ot.hexStringFromIntNumber)(n)}async newBlockFilter(){const t=this.makeFilterId(),r=await this.setInitialCursorPosition(t,"latest");return console.info(`Installing new block filter (${t}) with initial cursor position:`,r),this.blockFilters.add(t),this.setFilterTimeout(t),(0,ot.hexStringFromIntNumber)(t)}async newPendingTransactionFilter(){const t=this.makeFilterId(),r=await this.setInitialCursorPosition(t,"latest");return console.info(`Installing new block filter (${t}) with initial cursor position:`,r),this.pendingTransactionFilters.add(t),this.setFilterTimeout(t),(0,ot.hexStringFromIntNumber)(t)}uninstallFilter(t){const r=(0,ot.intNumberFromHexString)(t);return console.info(`Uninstalling filter (${r})`),this.deleteFilter(r),!0}getFilterChanges(t){const r=(0,ot.intNumberFromHexString)(t);return this.timeouts.has(r)&&this.setFilterTimeout(r),this.logFilters.has(r)?this.getLogFilterChanges(r):this.blockFilters.has(r)?this.getBlockFilterChanges(r):this.pendingTransactionFilters.has(r)?this.getPendingTransactionFilterChanges(r):Promise.resolve(ii())}async getFilterLogs(t){const r=(0,ot.intNumberFromHexString)(t),n=this.logFilters.get(r);return n?this.sendAsyncPromise(Object.assign(Object.assign({},dr),{method:"eth_getLogs",params:[gc(n)]})):ii()}makeFilterId(){return(0,Ur.IntNumber)(++this.nextFilterId)}sendAsyncPromise(t){return new Promise((r,n)=>{this.provider.sendAsync(t,(i,s)=>{if(i)return n(i);if(Array.isArray(s)||s==null)return n(new Error(`unexpected response received: ${JSON.stringify(s)}`));r(s)})})}deleteFilter(t){console.info(`Deleting filter (${t})`),this.logFilters.delete(t),this.blockFilters.delete(t),this.pendingTransactionFilters.delete(t),this.cursors.delete(t),this.timeouts.delete(t)}async getLogFilterChanges(t){const r=this.logFilters.get(t),n=this.cursors.get(t);if(!n||!r)return ii();const i=await this.getCurrentBlockHeight(),s=r.toBlock==="latest"?i:r.toBlock;if(n>i||n>Number(r.toBlock))return si();console.info(`Fetching logs from ${n} to ${s} for filter ${t}`);const c=await this.sendAsyncPromise(Object.assign(Object.assign({},dr),{method:"eth_getLogs",params:[gc(Object.assign(Object.assign({},r),{fromBlock:n,toBlock:s}))]}));if(Array.isArray(c.result)){const o=c.result.map(p=>(0,ot.intNumberFromHexString)(p.blockNumber||"0x0")),u=Math.max(...o);if(u&&u>n){const p=(0,Ur.IntNumber)(u+1);console.info(`Moving cursor position for filter (${t}) from ${n} to ${p}`),this.cursors.set(t,p)}}return c}async getBlockFilterChanges(t){const r=this.cursors.get(t);if(!r)return ii();const n=await this.getCurrentBlockHeight();if(r>n)return si();console.info(`Fetching blocks from ${r} to ${n} for filter (${t})`);const i=(await Promise.all((0,ot.range)(r,n+1).map(c=>this.getBlockHashByNumber((0,Ur.IntNumber)(c))))).filter(c=>!!c),s=(0,Ur.IntNumber)(r+i.length);return console.info(`Moving cursor position for filter (${t}) from ${r} to ${s}`),this.cursors.set(t,s),Object.assign(Object.assign({},dr),{result:i})}async getPendingTransactionFilterChanges(t){return Promise.resolve(si())}async setInitialCursorPosition(t,r){const n=await this.getCurrentBlockHeight(),i=typeof r=="number"&&r>n?r:n;return this.cursors.set(t,i),i}setFilterTimeout(t){const r=this.timeouts.get(t);r&&window.clearTimeout(r);const n=window.setTimeout(()=>{console.info(`Filter (${t}) timed out`),this.deleteFilter(t)},Z1);this.timeouts.set(t,n)}async getCurrentBlockHeight(){const t=new Date;if(t.getTime()-this.lastFetchTimestamp.getTime()>this.REQUEST_THROTTLE_INTERVAL){this.lastFetchTimestamp=t;const r=await this._getCurrentBlockHeight();this.currentBlockHeight=r,this.resolvers.forEach(n=>n(r)),this.resolvers=[]}return this.currentBlockHeight?this.currentBlockHeight:new Promise(r=>this.resolvers.push(r))}async _getCurrentBlockHeight(){const{result:t}=await this.sendAsyncPromise(Object.assign(Object.assign({},dr),{method:"eth_blockNumber",params:[]}));return(0,ot.intNumberFromHexString)((0,ot.ensureHexString)(t))}async getBlockHashByNumber(t){const r=await this.sendAsyncPromise(Object.assign(Object.assign({},dr),{method:"eth_getBlockByNumber",params:[(0,ot.hexStringFromIntNumber)(t),!1]}));return r.result&&typeof r.result.hash=="string"?(0,ot.ensureHexString)(r.result.hash):null}}Xr.FilterPolyfill=K1;function pu(e){return{fromBlock:yc(e.fromBlock),toBlock:yc(e.toBlock),addresses:e.address===void 0?null:Array.isArray(e.address)?e.address:[e.address],topics:e.topics||[]}}Xr.filterFromParam=pu;function gc(e){const t={fromBlock:mc(e.fromBlock),toBlock:mc(e.toBlock),topics:e.topics};return e.addresses!==null&&(t.address=e.addresses),t}function yc(e){if(e===void 0||e==="latest"||e==="pending")return"latest";if(e==="earliest")return(0,Ur.IntNumber)(0);if((0,ot.isHexString)(e))return(0,ot.intNumberFromHexString)(e);throw new Error(`Invalid block option: ${String(e)}`)}function mc(e){return e==="latest"?e:(0,ot.hexStringFromIntNumber)(e)}function ii(){return Object.assign(Object.assign({},dr),{error:{code:-32e3,message:"filter not found"}})}function si(){return Object.assign(Object.assign({},dr),{result:[]})}var ts={},gu={},rs={},Vo=Y1;function Y1(e){e=e||{};var t=e.max||Number.MAX_SAFE_INTEGER,r=typeof e.start<"u"?e.start:Math.floor(Math.random()*t);return function(){return r=r%t,r++}}const vc=(e,t)=>function(){const r=t.promiseModule,n=new Array(arguments.length);for(let i=0;i<arguments.length;i++)n[i]=arguments[i];return new r((i,s)=>{t.errorFirst?n.push(function(c,o){if(t.multiArgs){const u=new Array(arguments.length-1);for(let p=1;p<arguments.length;p++)u[p-1]=arguments[p];c?(u.unshift(c),s(u)):i(u)}else c?s(c):i(o)}):n.push(function(c){if(t.multiArgs){const o=new Array(arguments.length-1);for(let u=0;u<arguments.length;u++)o[u]=arguments[u];i(o)}else i(c)}),e.apply(this,n)})};var Q1=(e,t)=>{t=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:!0,promiseModule:Promise},t);const r=i=>{const s=c=>typeof c=="string"?i===c:c.test(i);return t.include?t.include.some(s):!t.exclude.some(s)};let n;typeof e=="function"?n=function(){return t.excludeMain?e.apply(this,arguments):vc(e,t).apply(this,arguments)}:n=Object.create(Object.getPrototypeOf(e));for(const i in e){const s=e[i];n[i]=typeof s=="function"&&r(i)?vc(s,t):s}return n},Hn={},Wn={};Object.defineProperty(Wn,"__esModule",{value:!0});const X1=Ei;function bc(e,t,r){try{Reflect.apply(e,t,r)}catch(n){setTimeout(()=>{throw n})}}function eg(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}let tg=class extends X1.EventEmitter{emit(t,...r){let n=t==="error";const i=this._events;if(i!==void 0)n=n&&i.error===void 0;else if(!n)return!1;if(n){let c;if(r.length>0&&([c]=r),c instanceof Error)throw c;const o=new Error(`Unhandled error.${c?` (${c.message})`:""}`);throw o.context=c,o}const s=i[t];if(s===void 0)return!1;if(typeof s=="function")bc(s,this,r);else{const c=s.length,o=eg(s);for(let u=0;u<c;u+=1)bc(o[u],this,r)}return!0}};Wn.default=tg;var rg=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Hn,"__esModule",{value:!0});Hn.BaseBlockTracker=void 0;const ng=rg(Wn),ig=1e3,sg=(e,t)=>e+t,wc=["sync","latest"];class og extends ng.default{constructor(t){super(),this._blockResetDuration=t.blockResetDuration||20*ig,this._usePastBlocks=t.usePastBlocks||!1,this._currentBlock=null,this._isRunning=!1,this._onNewListener=this._onNewListener.bind(this),this._onRemoveListener=this._onRemoveListener.bind(this),this._resetCurrentBlock=this._resetCurrentBlock.bind(this),this._setupInternalEvents()}async destroy(){this._cancelBlockResetTimeout(),await this._maybeEnd(),super.removeAllListeners()}isRunning(){return this._isRunning}getCurrentBlock(){return this._currentBlock}async getLatestBlock(){return this._currentBlock?this._currentBlock:await new Promise(r=>this.once("latest",r))}removeAllListeners(t){return t?super.removeAllListeners(t):super.removeAllListeners(),this._setupInternalEvents(),this._onRemoveListener(),this}_setupInternalEvents(){this.removeListener("newListener",this._onNewListener),this.removeListener("removeListener",this._onRemoveListener),this.on("newListener",this._onNewListener),this.on("removeListener",this._onRemoveListener)}_onNewListener(t){wc.includes(t)&&this._maybeStart()}_onRemoveListener(){this._getBlockTrackerEventCount()>0||this._maybeEnd()}async _maybeStart(){this._isRunning||(this._isRunning=!0,this._cancelBlockResetTimeout(),await this._start(),this.emit("_started"))}async _maybeEnd(){this._isRunning&&(this._isRunning=!1,this._setupBlockResetTimeout(),await this._end(),this.emit("_ended"))}_getBlockTrackerEventCount(){return wc.map(t=>this.listenerCount(t)).reduce(sg)}_shouldUseNewBlock(t){const r=this._currentBlock;if(!r)return!0;const n=_c(t),i=_c(r);return this._usePastBlocks&&n<i||n>i}_newPotentialLatest(t){this._shouldUseNewBlock(t)&&this._setCurrentBlock(t)}_setCurrentBlock(t){const r=this._currentBlock;this._currentBlock=t,this.emit("latest",t),this.emit("sync",{oldBlock:r,newBlock:t})}_setupBlockResetTimeout(){this._cancelBlockResetTimeout(),this._blockResetTimeout=setTimeout(this._resetCurrentBlock,this._blockResetDuration),this._blockResetTimeout.unref&&this._blockResetTimeout.unref()}_cancelBlockResetTimeout(){this._blockResetTimeout&&clearTimeout(this._blockResetTimeout)}_resetCurrentBlock(){this._currentBlock=null}}Hn.BaseBlockTracker=og;function _c(e){return Number.parseInt(e,16)}var yu={},mu={},it={};class vu extends TypeError{constructor(t,r){let n;const{message:i,explanation:s,...c}=t,{path:o}=t,u=o.length===0?i:`At path: ${o.join(".")} -- ${i}`;super(s??u),s!=null&&(this.cause=u),Object.assign(this,c),this.name=this.constructor.name,this.failures=()=>n??(n=[t,...r()])}}function ag(e){return wt(e)&&typeof e[Symbol.iterator]=="function"}function wt(e){return typeof e=="object"&&e!=null}function Ec(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;const t=Object.getPrototypeOf(e);return t===null||t===Object.prototype}function Ye(e){return typeof e=="symbol"?e.toString():typeof e=="string"?JSON.stringify(e):`${e}`}function cg(e){const{done:t,value:r}=e.next();return t?void 0:r}function lg(e,t,r,n){if(e===!0)return;e===!1?e={}:typeof e=="string"&&(e={message:e});const{path:i,branch:s}=t,{type:c}=r,{refinement:o,message:u=`Expected a value of type \`${c}\`${o?` with refinement \`${o}\``:""}, but received: \`${Ye(n)}\``}=e;return{value:n,type:c,refinement:o,key:i[i.length-1],path:i,branch:s,...e,message:u}}function*So(e,t,r,n){ag(e)||(e=[e]);for(const i of e){const s=lg(i,t,r,n);s&&(yield s)}}function*zo(e,t,r={}){const{path:n=[],branch:i=[e],coerce:s=!1,mask:c=!1}=r,o={path:n,branch:i};if(s&&(e=t.coercer(e,o),c&&t.type!=="type"&&wt(t.schema)&&wt(e)&&!Array.isArray(e)))for(const p in e)t.schema[p]===void 0&&delete e[p];let u="valid";for(const p of t.validator(e,o))p.explanation=r.message,u="not_valid",yield[p,void 0];for(let[p,g,w]of t.entries(e,o)){const _=zo(g,w,{path:p===void 0?n:[...n,p],branch:p===void 0?i:[...i,g],coerce:s,mask:c,message:r.message});for(const L of _)L[0]?(u=L[0].refinement!=null?"not_refined":"not_valid",yield[L[0],void 0]):s&&(g=L[1],p===void 0?e=g:e instanceof Map?e.set(p,g):e instanceof Set?e.add(g):wt(e)&&(g!==void 0||p in e)&&(e[p]=g))}if(u!=="not_valid")for(const p of t.refiner(e,o))p.explanation=r.message,u="not_refined",yield[p,void 0];u==="valid"&&(yield[void 0,e])}class Ge{constructor(t){const{type:r,schema:n,validator:i,refiner:s,coercer:c=u=>u,entries:o=function*(){}}=t;this.type=r,this.schema=n,this.entries=o,this.coercer=c,i?this.validator=(u,p)=>{const g=i(u,p);return So(g,p,this,u)}:this.validator=()=>[],s?this.refiner=(u,p)=>{const g=s(u,p);return So(g,p,this,u)}:this.refiner=()=>[]}assert(t,r){return bu(t,this,r)}create(t,r){return wu(t,this,r)}is(t){return Go(t,this)}mask(t,r){return _u(t,this,r)}validate(t,r={}){return un(t,this,r)}}function bu(e,t,r){const n=un(e,t,{message:r});if(n[0])throw n[0]}function wu(e,t,r){const n=un(e,t,{coerce:!0,message:r});if(n[0])throw n[0];return n[1]}function _u(e,t,r){const n=un(e,t,{coerce:!0,mask:!0,message:r});if(n[0])throw n[0];return n[1]}function Go(e,t){return!un(e,t)[0]}function un(e,t,r={}){const n=zo(e,t,r),i=cg(n);return i[0]?[new vu(i[0],function*(){for(const c of n)c[0]&&(yield c[0])}),void 0]:[void 0,i[1]]}function ug(...e){const t=e[0].type==="type",r=e.map(i=>i.schema),n=Object.assign({},...r);return t?Vn(n):qn(n)}function lt(e,t){return new Ge({type:e,schema:null,validator:t})}function hg(e,t){return new Ge({...e,refiner:(r,n)=>r===void 0||e.refiner(r,n),validator(r,n){return r===void 0?!0:(t(r,n),e.validator(r,n))}})}function fg(e){return new Ge({type:"dynamic",schema:null,*entries(t,r){yield*e(t,r).entries(t,r)},validator(t,r){return e(t,r).validator(t,r)},coercer(t,r){return e(t,r).coercer(t,r)},refiner(t,r){return e(t,r).refiner(t,r)}})}function dg(e){let t;return new Ge({type:"lazy",schema:null,*entries(r,n){t??(t=e()),yield*t.entries(r,n)},validator(r,n){return t??(t=e()),t.validator(r,n)},coercer(r,n){return t??(t=e()),t.coercer(r,n)},refiner(r,n){return t??(t=e()),t.refiner(r,n)}})}function pg(e,t){const{schema:r}=e,n={...r};for(const i of t)delete n[i];switch(e.type){case"type":return Vn(n);default:return qn(n)}}function gg(e){const t=e instanceof Ge,r=t?{...e.schema}:{...e};for(const n in r)r[n]=Eu(r[n]);return t&&e.type==="type"?Vn(r):qn(r)}function yg(e,t){const{schema:r}=e,n={};for(const i of t)n[i]=r[i];switch(e.type){case"type":return Vn(n);default:return qn(n)}}function mg(e,t){return console.warn("superstruct@0.11 - The `struct` helper has been renamed to `define`."),lt(e,t)}function vg(){return lt("any",()=>!0)}function bg(e){return new Ge({type:"array",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[r,n]of t.entries())yield[r,n,e]},coercer(t){return Array.isArray(t)?t.slice():t},validator(t){return Array.isArray(t)||`Expected an array value, but received: ${Ye(t)}`}})}function wg(){return lt("bigint",e=>typeof e=="bigint")}function _g(){return lt("boolean",e=>typeof e=="boolean")}function Eg(){return lt("date",e=>e instanceof Date&&!isNaN(e.getTime())||`Expected a valid \`Date\` object, but received: ${Ye(e)}`)}function Sg(e){const t={},r=e.map(n=>Ye(n)).join();for(const n of e)t[n]=n;return new Ge({type:"enums",schema:t,validator(n){return e.includes(n)||`Expected one of \`${r}\`, but received: ${Ye(n)}`}})}function Rg(){return lt("func",e=>typeof e=="function"||`Expected a function, but received: ${Ye(e)}`)}function Mg(e){return lt("instance",t=>t instanceof e||`Expected a \`${e.name}\` instance, but received: ${Ye(t)}`)}function Ig(){return lt("integer",e=>typeof e=="number"&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${Ye(e)}`)}function Ag(e){return new Ge({type:"intersection",schema:null,*entries(t,r){for(const n of e)yield*n.entries(t,r)},*validator(t,r){for(const n of e)yield*n.validator(t,r)},*refiner(t,r){for(const n of e)yield*n.refiner(t,r)}})}function xg(e){const t=Ye(e),r=typeof e;return new Ge({type:"literal",schema:r==="string"||r==="number"||r==="boolean"?e:null,validator(n){return n===e||`Expected the literal \`${t}\`, but received: ${Ye(n)}`}})}function kg(e,t){return new Ge({type:"map",schema:null,*entries(r){if(e&&t&&r instanceof Map)for(const[n,i]of r.entries())yield[n,n,e],yield[n,i,t]},coercer(r){return r instanceof Map?new Map(r):r},validator(r){return r instanceof Map||`Expected a \`Map\` object, but received: ${Ye(r)}`}})}function Jo(){return lt("never",()=>!1)}function Cg(e){return new Ge({...e,validator:(t,r)=>t===null||e.validator(t,r),refiner:(t,r)=>t===null||e.refiner(t,r)})}function Tg(){return lt("number",e=>typeof e=="number"&&!isNaN(e)||`Expected a number, but received: ${Ye(e)}`)}function qn(e){const t=e?Object.keys(e):[],r=Jo();return new Ge({type:"object",schema:e||null,*entries(n){if(e&&wt(n)){const i=new Set(Object.keys(n));for(const s of t)i.delete(s),yield[s,n[s],e[s]];for(const s of i)yield[s,n[s],r]}},validator(n){return wt(n)||`Expected an object, but received: ${Ye(n)}`},coercer(n){return wt(n)?{...n}:n}})}function Eu(e){return new Ge({...e,validator:(t,r)=>t===void 0||e.validator(t,r),refiner:(t,r)=>t===void 0||e.refiner(t,r)})}function Bg(e,t){return new Ge({type:"record",schema:null,*entries(r){if(wt(r))for(const n in r){const i=r[n];yield[n,n,e],yield[n,i,t]}},validator(r){return wt(r)||`Expected an object, but received: ${Ye(r)}`}})}function Ng(){return lt("regexp",e=>e instanceof RegExp)}function Pg(e){return new Ge({type:"set",schema:null,*entries(t){if(e&&t instanceof Set)for(const r of t)yield[r,r,e]},coercer(t){return t instanceof Set?new Set(t):t},validator(t){return t instanceof Set||`Expected a \`Set\` object, but received: ${Ye(t)}`}})}function Su(){return lt("string",e=>typeof e=="string"||`Expected a string, but received: ${Ye(e)}`)}function Lg(e){const t=Jo();return new Ge({type:"tuple",schema:null,*entries(r){if(Array.isArray(r)){const n=Math.max(e.length,r.length);for(let i=0;i<n;i++)yield[i,r[i],e[i]||t]}},validator(r){return Array.isArray(r)||`Expected an array, but received: ${Ye(r)}`}})}function Vn(e){const t=Object.keys(e);return new Ge({type:"type",schema:e,*entries(r){if(wt(r))for(const n of t)yield[n,r[n],e[n]]},validator(r){return wt(r)||`Expected an object, but received: ${Ye(r)}`},coercer(r){return wt(r)?{...r}:r}})}function Og(e){const t=e.map(r=>r.type).join(" | ");return new Ge({type:"union",schema:null,coercer(r){for(const n of e){const[i,s]=n.validate(r,{coerce:!0});if(!i)return s}return r},validator(r,n){const i=[];for(const s of e){const[...c]=zo(r,s,n),[o]=c;if(o[0])for(const[u]of c)u&&i.push(u);else return[]}return[`Expected the value to satisfy a union of \`${t}\`, but received: ${Ye(r)}`,...i]}})}function Ru(){return lt("unknown",()=>!0)}function Zo(e,t,r){return new Ge({...e,coercer:(n,i)=>Go(n,t)?e.coercer(r(n,i),i):e.coercer(n,i)})}function $g(e,t,r={}){return Zo(e,Ru(),n=>{const i=typeof t=="function"?t():t;if(n===void 0)return i;if(!r.strict&&Ec(n)&&Ec(i)){const s={...n};let c=!1;for(const o in i)s[o]===void 0&&(s[o]=i[o],c=!0);if(c)return s}return n})}function Fg(e){return Zo(e,Su(),t=>t.trim())}function Dg(e){return Er(e,"empty",t=>{const r=Mu(t);return r===0||`Expected an empty ${e.type} but received one with a size of \`${r}\``})}function Mu(e){return e instanceof Map||e instanceof Set?e.size:e.length}function jg(e,t,r={}){const{exclusive:n}=r;return Er(e,"max",i=>n?i<t:i<=t||`Expected a ${e.type} less than ${n?"":"or equal to "}${t} but received \`${i}\``)}function Ug(e,t,r={}){const{exclusive:n}=r;return Er(e,"min",i=>n?i>t:i>=t||`Expected a ${e.type} greater than ${n?"":"or equal to "}${t} but received \`${i}\``)}function Hg(e){return Er(e,"nonempty",t=>Mu(t)>0||`Expected a nonempty ${e.type} but received an empty one`)}function Wg(e,t){return Er(e,"pattern",r=>t.test(r)||`Expected a ${e.type} matching \`/${t.source}/\` but received "${r}"`)}function qg(e,t,r=t){const n=`Expected a ${e.type}`,i=t===r?`of \`${t}\``:`between \`${t}\` and \`${r}\``;return Er(e,"size",s=>{if(typeof s=="number"||s instanceof Date)return t<=s&&s<=r||`${n} ${i} but received \`${s}\``;if(s instanceof Map||s instanceof Set){const{size:c}=s;return t<=c&&c<=r||`${n} with a size ${i} but received one with a size of \`${c}\``}else{const{length:c}=s;return t<=c&&c<=r||`${n} with a length ${i} but received one with a length of \`${c}\``}})}function Er(e,t,r){return new Ge({...e,*refiner(n,i){yield*e.refiner(n,i);const s=r(n,i),c=So(s,i,e,n);for(const o of c)yield{...o,refinement:t}}})}const Vg=Object.freeze(Object.defineProperty({__proto__:null,Struct:Ge,StructError:vu,any:vg,array:bg,assert:bu,assign:ug,bigint:wg,boolean:_g,coerce:Zo,create:wu,date:Eg,defaulted:$g,define:lt,deprecated:hg,dynamic:fg,empty:Dg,enums:Sg,func:Rg,instance:Mg,integer:Ig,intersection:Ag,is:Go,lazy:dg,literal:xg,map:kg,mask:_u,max:jg,min:Ug,never:Jo,nonempty:Hg,nullable:Cg,number:Tg,object:qn,omit:pg,optional:Eu,partial:gg,pattern:Wg,pick:yg,record:Bg,refine:Er,regexp:Ng,set:Pg,size:qg,string:Su,struct:mg,trimmed:Fg,tuple:Lg,type:Vn,union:Og,unknown:Ru,validate:un},Symbol.toStringTag,{value:"Module"})),Sr=rn(Vg);Object.defineProperty(it,"__esModule",{value:!0});it.assertExhaustive=it.assertStruct=it.assert=it.AssertionError=void 0;const zg=Sr;function Gg(e){return typeof e=="object"&&e!==null&&"message"in e}function Jg(e){var t,r;return typeof((r=(t=e==null?void 0:e.prototype)===null||t===void 0?void 0:t.constructor)===null||r===void 0?void 0:r.name)=="string"}function Zg(e){const t=Gg(e)?e.message:String(e);return t.endsWith(".")?t.slice(0,-1):t}function Iu(e,t){return Jg(e)?new e({message:t}):e({message:t})}class Ko extends Error{constructor(t){super(t.message),this.code="ERR_ASSERTION"}}it.AssertionError=Ko;function Kg(e,t="Assertion failed.",r=Ko){if(!e)throw t instanceof Error?t:Iu(r,t)}it.assert=Kg;function Yg(e,t,r="Assertion failed",n=Ko){try{(0,zg.assert)(e,t)}catch(i){throw Iu(n,`${r}: ${Zg(i)}.`)}}it.assertStruct=Yg;function Qg(e){throw new Error("Invalid branch reached. Should be detected during compilation.")}it.assertExhaustive=Qg;var zn={};Object.defineProperty(zn,"__esModule",{value:!0});zn.base64=void 0;const Xg=Sr,ey=it,ty=(e,t={})=>{var r,n;const i=(r=t.paddingRequired)!==null&&r!==void 0?r:!1,s=(n=t.characterSet)!==null&&n!==void 0?n:"base64";let c;s==="base64"?c=String.raw`[A-Za-z0-9+\/]`:((0,ey.assert)(s==="base64url"),c=String.raw`[-_A-Za-z0-9]`);let o;return i?o=new RegExp(`^(?:${c}{4})*(?:${c}{3}=|${c}{2}==)?$`,"u"):o=new RegExp(`^(?:${c}{4})*(?:${c}{2,3}|${c}{3}=|${c}{2}==)?$`,"u"),(0,Xg.pattern)(e,o)};zn.base64=ty;var be={},Gn={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.remove0x=e.add0x=e.assertIsStrictHexString=e.assertIsHexString=e.isStrictHexString=e.isHexString=e.StrictHexStruct=e.HexStruct=void 0;const t=Sr,r=it;e.HexStruct=(0,t.pattern)((0,t.string)(),/^(?:0x)?[0-9a-f]+$/iu),e.StrictHexStruct=(0,t.pattern)((0,t.string)(),/^0x[0-9a-f]+$/iu);function n(p){return(0,t.is)(p,e.HexStruct)}e.isHexString=n;function i(p){return(0,t.is)(p,e.StrictHexStruct)}e.isStrictHexString=i;function s(p){(0,r.assert)(n(p),"Value must be a hexadecimal string.")}e.assertIsHexString=s;function c(p){(0,r.assert)(i(p),'Value must be a hexadecimal string, starting with "0x".')}e.assertIsStrictHexString=c;function o(p){return p.startsWith("0x")?p:p.startsWith("0X")?`0x${p.substring(2)}`:`0x${p}`}e.add0x=o;function u(p){return p.startsWith("0x")||p.startsWith("0X")?p.substring(2):p}e.remove0x=u})(Gn);Object.defineProperty(be,"__esModule",{value:!0});be.createDataView=be.concatBytes=be.valueToBytes=be.stringToBytes=be.numberToBytes=be.signedBigIntToBytes=be.bigIntToBytes=be.hexToBytes=be.bytesToString=be.bytesToNumber=be.bytesToSignedBigInt=be.bytesToBigInt=be.bytesToHex=be.assertIsBytes=be.isBytes=void 0;const ft=it,Ro=Gn,Sc=48,Rc=58,Mc=87;function ry(){const e=[];return()=>{if(e.length===0)for(let t=0;t<256;t++)e.push(t.toString(16).padStart(2,"0"));return e}}const ny=ry();function Yo(e){return e instanceof Uint8Array}be.isBytes=Yo;function hn(e){(0,ft.assert)(Yo(e),"Value must be a Uint8Array.")}be.assertIsBytes=hn;function Au(e){if(hn(e),e.length===0)return"0x";const t=ny(),r=new Array(e.length);for(let n=0;n<e.length;n++)r[n]=t[e[n]];return(0,Ro.add0x)(r.join(""))}be.bytesToHex=Au;function xu(e){hn(e);const t=Au(e);return BigInt(t)}be.bytesToBigInt=xu;function iy(e){hn(e);let t=BigInt(0);for(const r of e)t=(t<<BigInt(8))+BigInt(r);return BigInt.asIntN(e.length*8,t)}be.bytesToSignedBigInt=iy;function sy(e){hn(e);const t=xu(e);return(0,ft.assert)(t<=BigInt(Number.MAX_SAFE_INTEGER),"Number is not a safe integer. Use `bytesToBigInt` instead."),Number(t)}be.bytesToNumber=sy;function oy(e){return hn(e),new TextDecoder().decode(e)}be.bytesToString=oy;function ns(e){var t;if(((t=e==null?void 0:e.toLowerCase)===null||t===void 0?void 0:t.call(e))==="0x")return new Uint8Array;(0,Ro.assertIsHexString)(e);const r=(0,Ro.remove0x)(e).toLowerCase(),n=r.length%2===0?r:`0${r}`,i=new Uint8Array(n.length/2);for(let s=0;s<i.length;s++){const c=n.charCodeAt(s*2),o=n.charCodeAt(s*2+1),u=c-(c<Rc?Sc:Mc),p=o-(o<Rc?Sc:Mc);i[s]=u*16+p}return i}be.hexToBytes=ns;function ku(e){(0,ft.assert)(typeof e=="bigint","Value must be a bigint."),(0,ft.assert)(e>=BigInt(0),"Value must be a non-negative bigint.");const t=e.toString(16);return ns(t)}be.bigIntToBytes=ku;function ay(e,t){(0,ft.assert)(t>0);const r=e>>BigInt(31);return!((~e&r)+(e&~r)>>BigInt(t*8+-1))}function cy(e,t){(0,ft.assert)(typeof e=="bigint","Value must be a bigint."),(0,ft.assert)(typeof t=="number","Byte length must be a number."),(0,ft.assert)(t>0,"Byte length must be greater than 0."),(0,ft.assert)(ay(e,t),"Byte length is too small to represent the given value.");let r=e;const n=new Uint8Array(t);for(let i=0;i<n.length;i++)n[i]=Number(BigInt.asUintN(8,r)),r>>=BigInt(8);return n.reverse()}be.signedBigIntToBytes=cy;function Cu(e){(0,ft.assert)(typeof e=="number","Value must be a number."),(0,ft.assert)(e>=0,"Value must be a non-negative number."),(0,ft.assert)(Number.isSafeInteger(e),"Value is not a safe integer. Use `bigIntToBytes` instead.");const t=e.toString(16);return ns(t)}be.numberToBytes=Cu;function Tu(e){return(0,ft.assert)(typeof e=="string","Value must be a string."),new TextEncoder().encode(e)}be.stringToBytes=Tu;function Bu(e){if(typeof e=="bigint")return ku(e);if(typeof e=="number")return Cu(e);if(typeof e=="string")return e.startsWith("0x")?ns(e):Tu(e);if(Yo(e))return e;throw new TypeError(`Unsupported value type: "${typeof e}".`)}be.valueToBytes=Bu;function ly(e){const t=new Array(e.length);let r=0;for(let i=0;i<e.length;i++){const s=Bu(e[i]);t[i]=s,r+=s.length}const n=new Uint8Array(r);for(let i=0,s=0;i<t.length;i++)n.set(t[i],s),s+=t[i].length;return n}be.concatBytes=ly;function uy(e){if(typeof Buffer<"u"&&e instanceof Buffer){const t=e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength);return new DataView(t)}return new DataView(e.buffer,e.byteOffset,e.byteLength)}be.createDataView=uy;var is={};Object.defineProperty(is,"__esModule",{value:!0});is.ChecksumStruct=void 0;const Ic=Sr,hy=zn;is.ChecksumStruct=(0,Ic.size)((0,hy.base64)((0,Ic.string)(),{paddingRequired:!0}),44,44);var Lt={};Object.defineProperty(Lt,"__esModule",{value:!0});Lt.createHex=Lt.createBytes=Lt.createBigInt=Lt.createNumber=void 0;const ze=Sr,fy=it,Nu=be,ss=Gn,Pu=(0,ze.union)([(0,ze.number)(),(0,ze.bigint)(),(0,ze.string)(),ss.StrictHexStruct]),dy=(0,ze.coerce)((0,ze.number)(),Pu,Number),py=(0,ze.coerce)((0,ze.bigint)(),Pu,BigInt);(0,ze.union)([ss.StrictHexStruct,(0,ze.instance)(Uint8Array)]);const gy=(0,ze.coerce)((0,ze.instance)(Uint8Array),(0,ze.union)([ss.StrictHexStruct]),Nu.hexToBytes),yy=(0,ze.coerce)(ss.StrictHexStruct,(0,ze.instance)(Uint8Array),Nu.bytesToHex);function my(e){try{const t=(0,ze.create)(e,dy);return(0,fy.assert)(Number.isFinite(t),`Expected a number-like value, got "${e}".`),t}catch(t){throw t instanceof ze.StructError?new Error(`Expected a number-like value, got "${e}".`):t}}Lt.createNumber=my;function vy(e){try{return(0,ze.create)(e,py)}catch(t){throw t instanceof ze.StructError?new Error(`Expected a number-like value, got "${String(t.value)}".`):t}}Lt.createBigInt=vy;function by(e){if(typeof e=="string"&&e.toLowerCase()==="0x")return new Uint8Array;try{return(0,ze.create)(e,gy)}catch(t){throw t instanceof ze.StructError?new Error(`Expected a bytes-like value, got "${String(t.value)}".`):t}}Lt.createBytes=by;function wy(e){if(e instanceof Uint8Array&&e.length===0||typeof e=="string"&&e.toLowerCase()==="0x")return"0x";try{return(0,ze.create)(e,yy)}catch(t){throw t instanceof ze.StructError?new Error(`Expected a bytes-like value, got "${String(t.value)}".`):t}}Lt.createHex=wy;var en={},Lu=ee&&ee.__classPrivateFieldSet||function(e,t,r,n,i){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?i.call(e,r):i?i.value=r:t.set(e,r),r},at=ee&&ee.__classPrivateFieldGet||function(e,t,r,n){if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?n:r==="a"?n.call(e):n?n.value:t.get(e)},Et,At;Object.defineProperty(en,"__esModule",{value:!0});en.FrozenSet=en.FrozenMap=void 0;class Qo{constructor(t){Et.set(this,void 0),Lu(this,Et,new Map(t),"f"),Object.freeze(this)}get size(){return at(this,Et,"f").size}[(Et=new WeakMap,Symbol.iterator)](){return at(this,Et,"f")[Symbol.iterator]()}entries(){return at(this,Et,"f").entries()}forEach(t,r){return at(this,Et,"f").forEach((n,i,s)=>t.call(r,n,i,this))}get(t){return at(this,Et,"f").get(t)}has(t){return at(this,Et,"f").has(t)}keys(){return at(this,Et,"f").keys()}values(){return at(this,Et,"f").values()}toString(){return`FrozenMap(${this.size}) {${this.size>0?` ${[...this.entries()].map(([t,r])=>`${String(t)} => ${String(r)}`).join(", ")} `:""}}`}}en.FrozenMap=Qo;class Xo{constructor(t){At.set(this,void 0),Lu(this,At,new Set(t),"f"),Object.freeze(this)}get size(){return at(this,At,"f").size}[(At=new WeakMap,Symbol.iterator)](){return at(this,At,"f")[Symbol.iterator]()}entries(){return at(this,At,"f").entries()}forEach(t,r){return at(this,At,"f").forEach((n,i,s)=>t.call(r,n,i,this))}has(t){return at(this,At,"f").has(t)}keys(){return at(this,At,"f").keys()}values(){return at(this,At,"f").values()}toString(){return`FrozenSet(${this.size}) {${this.size>0?` ${[...this.values()].map(t=>String(t)).join(", ")} `:""}}`}}en.FrozenSet=Xo;Object.freeze(Qo);Object.freeze(Qo.prototype);Object.freeze(Xo);Object.freeze(Xo.prototype);var Ou={};Object.defineProperty(Ou,"__esModule",{value:!0});var $u={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getJsonRpcIdValidator=e.assertIsJsonRpcError=e.isJsonRpcError=e.assertIsJsonRpcFailure=e.isJsonRpcFailure=e.assertIsJsonRpcSuccess=e.isJsonRpcSuccess=e.assertIsJsonRpcResponse=e.isJsonRpcResponse=e.assertIsPendingJsonRpcResponse=e.isPendingJsonRpcResponse=e.JsonRpcResponseStruct=e.JsonRpcFailureStruct=e.JsonRpcSuccessStruct=e.PendingJsonRpcResponseStruct=e.assertIsJsonRpcRequest=e.isJsonRpcRequest=e.assertIsJsonRpcNotification=e.isJsonRpcNotification=e.JsonRpcNotificationStruct=e.JsonRpcRequestStruct=e.JsonRpcParamsStruct=e.JsonRpcErrorStruct=e.JsonRpcIdStruct=e.JsonRpcVersionStruct=e.jsonrpc2=e.getJsonSize=e.isValidJson=e.JsonStruct=e.UnsafeJsonStruct=void 0;const t=Sr,r=it,n=()=>(0,t.define)("finite number",U=>(0,t.is)(U,(0,t.number)())&&Number.isFinite(U));e.UnsafeJsonStruct=(0,t.union)([(0,t.literal)(null),(0,t.boolean)(),n(),(0,t.string)(),(0,t.array)((0,t.lazy)(()=>e.UnsafeJsonStruct)),(0,t.record)((0,t.string)(),(0,t.lazy)(()=>e.UnsafeJsonStruct))]),e.JsonStruct=(0,t.define)("Json",(U,z)=>{function te(X,Y){const oe=[...Y.validator(X,z)];return oe.length>0?oe:!0}try{const X=te(U,e.UnsafeJsonStruct);return X!==!0?X:te(JSON.parse(JSON.stringify(U)),e.UnsafeJsonStruct)}catch(X){return X instanceof RangeError?"Circular reference detected":!1}});function i(U){return(0,t.is)(U,e.JsonStruct)}e.isValidJson=i;function s(U){(0,r.assertStruct)(U,e.JsonStruct,"Invalid JSON value");const z=JSON.stringify(U);return new TextEncoder().encode(z).byteLength}e.getJsonSize=s,e.jsonrpc2="2.0",e.JsonRpcVersionStruct=(0,t.literal)(e.jsonrpc2),e.JsonRpcIdStruct=(0,t.nullable)((0,t.union)([(0,t.number)(),(0,t.string)()])),e.JsonRpcErrorStruct=(0,t.object)({code:(0,t.integer)(),message:(0,t.string)(),data:(0,t.optional)(e.JsonStruct),stack:(0,t.optional)((0,t.string)())}),e.JsonRpcParamsStruct=(0,t.optional)((0,t.union)([(0,t.record)((0,t.string)(),e.JsonStruct),(0,t.array)(e.JsonStruct)])),e.JsonRpcRequestStruct=(0,t.object)({id:e.JsonRpcIdStruct,jsonrpc:e.JsonRpcVersionStruct,method:(0,t.string)(),params:e.JsonRpcParamsStruct}),e.JsonRpcNotificationStruct=(0,t.omit)(e.JsonRpcRequestStruct,["id"]);function c(U){return(0,t.is)(U,e.JsonRpcNotificationStruct)}e.isJsonRpcNotification=c;function o(U,z){(0,r.assertStruct)(U,e.JsonRpcNotificationStruct,"Invalid JSON-RPC notification",z)}e.assertIsJsonRpcNotification=o;function u(U){return(0,t.is)(U,e.JsonRpcRequestStruct)}e.isJsonRpcRequest=u;function p(U,z){(0,r.assertStruct)(U,e.JsonRpcRequestStruct,"Invalid JSON-RPC request",z)}e.assertIsJsonRpcRequest=p,e.PendingJsonRpcResponseStruct=(0,t.object)({id:e.JsonRpcIdStruct,jsonrpc:e.JsonRpcVersionStruct,result:(0,t.optional)((0,t.unknown)()),error:(0,t.optional)(e.JsonRpcErrorStruct)}),e.JsonRpcSuccessStruct=(0,t.object)({id:e.JsonRpcIdStruct,jsonrpc:e.JsonRpcVersionStruct,result:e.JsonStruct}),e.JsonRpcFailureStruct=(0,t.object)({id:e.JsonRpcIdStruct,jsonrpc:e.JsonRpcVersionStruct,error:e.JsonRpcErrorStruct}),e.JsonRpcResponseStruct=(0,t.union)([e.JsonRpcSuccessStruct,e.JsonRpcFailureStruct]);function g(U){return(0,t.is)(U,e.PendingJsonRpcResponseStruct)}e.isPendingJsonRpcResponse=g;function w(U,z){(0,r.assertStruct)(U,e.PendingJsonRpcResponseStruct,"Invalid pending JSON-RPC response",z)}e.assertIsPendingJsonRpcResponse=w;function _(U){return(0,t.is)(U,e.JsonRpcResponseStruct)}e.isJsonRpcResponse=_;function L(U,z){(0,r.assertStruct)(U,e.JsonRpcResponseStruct,"Invalid JSON-RPC response",z)}e.assertIsJsonRpcResponse=L;function j(U){return(0,t.is)(U,e.JsonRpcSuccessStruct)}e.isJsonRpcSuccess=j;function $(U,z){(0,r.assertStruct)(U,e.JsonRpcSuccessStruct,"Invalid JSON-RPC success response",z)}e.assertIsJsonRpcSuccess=$;function O(U){return(0,t.is)(U,e.JsonRpcFailureStruct)}e.isJsonRpcFailure=O;function A(U,z){(0,r.assertStruct)(U,e.JsonRpcFailureStruct,"Invalid JSON-RPC failure response",z)}e.assertIsJsonRpcFailure=A;function N(U){return(0,t.is)(U,e.JsonRpcErrorStruct)}e.isJsonRpcError=N;function C(U,z){(0,r.assertStruct)(U,e.JsonRpcErrorStruct,"Invalid JSON-RPC error",z)}e.assertIsJsonRpcError=C;function W(U){const{permitEmptyString:z,permitFractions:te,permitNull:X}=Object.assign({permitEmptyString:!0,permitFractions:!1,permitNull:!0},U);return de=>!!(typeof de=="number"&&(te||Number.isInteger(de))||typeof de=="string"&&(z||de.length>0)||X&&de===null)}e.getJsonRpcIdValidator=W})($u);var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0});var tn={},_y=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(tn,"__esModule",{value:!0});tn.createModuleLogger=tn.createProjectLogger=void 0;const Ey=_y(mh),Sy=(0,Ey.default)("metamask");function Ry(e){return Sy.extend(e)}tn.createProjectLogger=Ry;function My(e,t){return e.extend(t)}tn.createModuleLogger=My;var Du={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateNumberSize=e.calculateStringSize=e.isASCII=e.isPlainObject=e.ESCAPE_CHARACTERS_REGEXP=e.JsonSize=e.hasProperty=e.isObject=e.isNullOrUndefined=e.isNonEmptyArray=void 0;function t(p){return Array.isArray(p)&&p.length>0}e.isNonEmptyArray=t;function r(p){return p==null}e.isNullOrUndefined=r;function n(p){return!!p&&typeof p=="object"&&!Array.isArray(p)}e.isObject=n;const i=(p,g)=>Object.hasOwnProperty.call(p,g);e.hasProperty=i,function(p){p[p.Null=4]="Null",p[p.Comma=1]="Comma",p[p.Wrapper=1]="Wrapper",p[p.True=4]="True",p[p.False=5]="False",p[p.Quote=1]="Quote",p[p.Colon=1]="Colon",p[p.Date=24]="Date"}(e.JsonSize||(e.JsonSize={})),e.ESCAPE_CHARACTERS_REGEXP=/"|\\|\n|\r|\t/gu;function s(p){if(typeof p!="object"||p===null)return!1;try{let g=p;for(;Object.getPrototypeOf(g)!==null;)g=Object.getPrototypeOf(g);return Object.getPrototypeOf(p)===g}catch{return!1}}e.isPlainObject=s;function c(p){return p.charCodeAt(0)<=127}e.isASCII=c;function o(p){var g;return p.split("").reduce((_,L)=>c(L)?_+1:_+2,0)+((g=p.match(e.ESCAPE_CHARACTERS_REGEXP))!==null&&g!==void 0?g:[]).length}e.calculateStringSize=o;function u(p){return p.toString().length}e.calculateNumberSize=u})(Du);var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0});Ot.hexToBigInt=Ot.hexToNumber=Ot.bigIntToHex=Ot.numberToHex=void 0;const zr=it,wn=Gn,Iy=e=>((0,zr.assert)(typeof e=="number","Value must be a number."),(0,zr.assert)(e>=0,"Value must be a non-negative number."),(0,zr.assert)(Number.isSafeInteger(e),"Value is not a safe integer. Use `bigIntToHex` instead."),(0,wn.add0x)(e.toString(16)));Ot.numberToHex=Iy;const Ay=e=>((0,zr.assert)(typeof e=="bigint","Value must be a bigint."),(0,zr.assert)(e>=0,"Value must be a non-negative bigint."),(0,wn.add0x)(e.toString(16)));Ot.bigIntToHex=Ay;const xy=e=>{(0,wn.assertIsHexString)(e);const t=parseInt(e,16);return(0,zr.assert)(Number.isSafeInteger(t),"Value is not a safe integer. Use `hexToBigInt` instead."),t};Ot.hexToNumber=xy;const ky=e=>((0,wn.assertIsHexString)(e),BigInt((0,wn.add0x)(e)));Ot.hexToBigInt=ky;var ju={};Object.defineProperty(ju,"__esModule",{value:!0});var Uu={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.timeSince=e.inMilliseconds=e.Duration=void 0,function(s){s[s.Millisecond=1]="Millisecond",s[s.Second=1e3]="Second",s[s.Minute=6e4]="Minute",s[s.Hour=36e5]="Hour",s[s.Day=864e5]="Day",s[s.Week=6048e5]="Week",s[s.Year=31536e6]="Year"}(e.Duration||(e.Duration={}));const t=s=>Number.isInteger(s)&&s>=0,r=(s,c)=>{if(!t(s))throw new Error(`"${c}" must be a non-negative integer. Received: "${s}".`)};function n(s,c){return r(s,"count"),s*c}e.inMilliseconds=n;function i(s){return r(s,"timestamp"),Date.now()-s}e.timeSince=i})(Uu);var Hu={};Object.defineProperty(Hu,"__esModule",{value:!0});var Wu={},Mo={exports:{}};const Cy="2.0.0",qu=256,Ty=Number.MAX_SAFE_INTEGER||9007199254740991,By=16,Ny=qu-6,Py=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var os={MAX_LENGTH:qu,MAX_SAFE_COMPONENT_LENGTH:By,MAX_SAFE_BUILD_LENGTH:Ny,MAX_SAFE_INTEGER:Ty,RELEASE_TYPES:Py,SEMVER_SPEC_VERSION:Cy,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2},lo={};const Ly=typeof process=="object"&&lo&&lo.NODE_DEBUG&&/\bsemver\b/i.test(lo.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var as=Ly;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:i}=os,s=as;t=e.exports={};const c=t.re=[],o=t.safeRe=[],u=t.src=[],p=t.safeSrc=[],g=t.t={};let w=0;const _="[a-zA-Z0-9-]",L=[["\\s",1],["\\d",i],[_,n]],j=O=>{for(const[A,N]of L)O=O.split(`${A}*`).join(`${A}{0,${N}}`).split(`${A}+`).join(`${A}{1,${N}}`);return O},$=(O,A,N)=>{const C=j(A),W=w++;s(O,W,A),g[O]=W,u[W]=A,p[W]=C,c[W]=new RegExp(A,N?"g":void 0),o[W]=new RegExp(C,N?"g":void 0)};$("NUMERICIDENTIFIER","0|[1-9]\\d*"),$("NUMERICIDENTIFIERLOOSE","\\d+"),$("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${_}*`),$("MAINVERSION",`(${u[g.NUMERICIDENTIFIER]})\\.(${u[g.NUMERICIDENTIFIER]})\\.(${u[g.NUMERICIDENTIFIER]})`),$("MAINVERSIONLOOSE",`(${u[g.NUMERICIDENTIFIERLOOSE]})\\.(${u[g.NUMERICIDENTIFIERLOOSE]})\\.(${u[g.NUMERICIDENTIFIERLOOSE]})`),$("PRERELEASEIDENTIFIER",`(?:${u[g.NONNUMERICIDENTIFIER]}|${u[g.NUMERICIDENTIFIER]})`),$("PRERELEASEIDENTIFIERLOOSE",`(?:${u[g.NONNUMERICIDENTIFIER]}|${u[g.NUMERICIDENTIFIERLOOSE]})`),$("PRERELEASE",`(?:-(${u[g.PRERELEASEIDENTIFIER]}(?:\\.${u[g.PRERELEASEIDENTIFIER]})*))`),$("PRERELEASELOOSE",`(?:-?(${u[g.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[g.PRERELEASEIDENTIFIERLOOSE]})*))`),$("BUILDIDENTIFIER",`${_}+`),$("BUILD",`(?:\\+(${u[g.BUILDIDENTIFIER]}(?:\\.${u[g.BUILDIDENTIFIER]})*))`),$("FULLPLAIN",`v?${u[g.MAINVERSION]}${u[g.PRERELEASE]}?${u[g.BUILD]}?`),$("FULL",`^${u[g.FULLPLAIN]}$`),$("LOOSEPLAIN",`[v=\\s]*${u[g.MAINVERSIONLOOSE]}${u[g.PRERELEASELOOSE]}?${u[g.BUILD]}?`),$("LOOSE",`^${u[g.LOOSEPLAIN]}$`),$("GTLT","((?:<|>)?=?)"),$("XRANGEIDENTIFIERLOOSE",`${u[g.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),$("XRANGEIDENTIFIER",`${u[g.NUMERICIDENTIFIER]}|x|X|\\*`),$("XRANGEPLAIN",`[v=\\s]*(${u[g.XRANGEIDENTIFIER]})(?:\\.(${u[g.XRANGEIDENTIFIER]})(?:\\.(${u[g.XRANGEIDENTIFIER]})(?:${u[g.PRERELEASE]})?${u[g.BUILD]}?)?)?`),$("XRANGEPLAINLOOSE",`[v=\\s]*(${u[g.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[g.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[g.XRANGEIDENTIFIERLOOSE]})(?:${u[g.PRERELEASELOOSE]})?${u[g.BUILD]}?)?)?`),$("XRANGE",`^${u[g.GTLT]}\\s*${u[g.XRANGEPLAIN]}$`),$("XRANGELOOSE",`^${u[g.GTLT]}\\s*${u[g.XRANGEPLAINLOOSE]}$`),$("COERCEPLAIN",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?`),$("COERCE",`${u[g.COERCEPLAIN]}(?:$|[^\\d])`),$("COERCEFULL",u[g.COERCEPLAIN]+`(?:${u[g.PRERELEASE]})?(?:${u[g.BUILD]})?(?:$|[^\\d])`),$("COERCERTL",u[g.COERCE],!0),$("COERCERTLFULL",u[g.COERCEFULL],!0),$("LONETILDE","(?:~>?)"),$("TILDETRIM",`(\\s*)${u[g.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",$("TILDE",`^${u[g.LONETILDE]}${u[g.XRANGEPLAIN]}$`),$("TILDELOOSE",`^${u[g.LONETILDE]}${u[g.XRANGEPLAINLOOSE]}$`),$("LONECARET","(?:\\^)"),$("CARETTRIM",`(\\s*)${u[g.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",$("CARET",`^${u[g.LONECARET]}${u[g.XRANGEPLAIN]}$`),$("CARETLOOSE",`^${u[g.LONECARET]}${u[g.XRANGEPLAINLOOSE]}$`),$("COMPARATORLOOSE",`^${u[g.GTLT]}\\s*(${u[g.LOOSEPLAIN]})$|^$`),$("COMPARATOR",`^${u[g.GTLT]}\\s*(${u[g.FULLPLAIN]})$|^$`),$("COMPARATORTRIM",`(\\s*)${u[g.GTLT]}\\s*(${u[g.LOOSEPLAIN]}|${u[g.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",$("HYPHENRANGE",`^\\s*(${u[g.XRANGEPLAIN]})\\s+-\\s+(${u[g.XRANGEPLAIN]})\\s*$`),$("HYPHENRANGELOOSE",`^\\s*(${u[g.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[g.XRANGEPLAINLOOSE]})\\s*$`),$("STAR","(<|>)?=?\\s*\\*"),$("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),$("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(Mo,Mo.exports);var Jn=Mo.exports;const Oy=Object.freeze({loose:!0}),$y=Object.freeze({}),Fy=e=>e?typeof e!="object"?Oy:e:$y;var ea=Fy;const Ac=/^[0-9]+$/,Vu=(e,t)=>{const r=Ac.test(e),n=Ac.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1},Dy=(e,t)=>Vu(t,e);var zu={compareIdentifiers:Vu,rcompareIdentifiers:Dy};const oi=as,{MAX_LENGTH:xc,MAX_SAFE_INTEGER:ai}=os,{safeRe:ci,t:li}=Jn,jy=ea,{compareIdentifiers:Fr}=zu;let Uy=class xt{constructor(t,r){if(r=jy(r),t instanceof xt){if(t.loose===!!r.loose&&t.includePrerelease===!!r.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>xc)throw new TypeError(`version is longer than ${xc} characters`);oi("SemVer",t,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;const n=t.trim().match(r.loose?ci[li.LOOSE]:ci[li.FULL]);if(!n)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>ai||this.major<0)throw new TypeError("Invalid major version");if(this.minor>ai||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>ai||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(i=>{if(/^[0-9]+$/.test(i)){const s=+i;if(s>=0&&s<ai)return s}return i}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(oi("SemVer.compare",this.version,this.options,t),!(t instanceof xt)){if(typeof t=="string"&&t===this.version)return 0;t=new xt(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof xt||(t=new xt(t,this.options)),Fr(this.major,t.major)||Fr(this.minor,t.minor)||Fr(this.patch,t.patch)}comparePre(t){if(t instanceof xt||(t=new xt(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let r=0;do{const n=this.prerelease[r],i=t.prerelease[r];if(oi("prerelease compare",r,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return Fr(n,i)}while(++r)}compareBuild(t){t instanceof xt||(t=new xt(t,this.options));let r=0;do{const n=this.build[r],i=t.build[r];if(oi("build compare",r,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return Fr(n,i)}while(++r)}inc(t,r,n){if(t.startsWith("pre")){if(!r&&n===!1)throw new Error("invalid increment argument: identifier is empty");if(r){const i=`-${r}`.match(this.options.loose?ci[li.PRERELEASELOOSE]:ci[li.PRERELEASE]);if(!i||i[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,n),this.inc("pre",r,n);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,n),this.inc("pre",r,n);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const i=Number(n)?1:0;if(this.prerelease.length===0)this.prerelease=[i];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(r===this.prerelease.join(".")&&n===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(i)}}if(r){let s=[r,i];n===!1&&(s=[r]),Fr(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var Zn=Uy;const kc=Zn,Hy=(e,t,r=!1)=>{if(e instanceof kc)return e;try{return new kc(e,t)}catch(n){if(!r)return null;throw n}};var Wy=Hy;const qy=Wy,Vy=(e,t)=>{const r=qy(e,t);return r?r.version:null};var zy=Vy;const Cc=Zn,Gy=(e,t,r)=>new Cc(e,r).compare(new Cc(t,r));var fn=Gy;const Jy=fn,Zy=(e,t,r)=>Jy(e,t,r)>0;var ta=Zy;const Ky=fn,Yy=(e,t,r)=>Ky(e,t,r)<0;var Gu=Yy;const Qy=fn,Xy=(e,t,r)=>Qy(e,t,r)===0;var em=Xy;const tm=fn,rm=(e,t,r)=>tm(e,t,r)!==0;var nm=rm;const im=fn,sm=(e,t,r)=>im(e,t,r)>=0;var Ju=sm;const om=fn,am=(e,t,r)=>om(e,t,r)<=0;var Zu=am;const cm=em,lm=nm,um=ta,hm=Ju,fm=Gu,dm=Zu,pm=(e,t,r,n)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e===r;case"!==":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e!==r;case"":case"=":case"==":return cm(e,r,n);case"!=":return lm(e,r,n);case">":return um(e,r,n);case">=":return hm(e,r,n);case"<":return fm(e,r,n);case"<=":return dm(e,r,n);default:throw new TypeError(`Invalid operator: ${t}`)}};var gm=pm;const{safeRe:Xb,t:ew}=Jn;class ym{constructor(){this.max=1e3,this.map=new Map}get(t){const r=this.map.get(t);if(r!==void 0)return this.map.delete(t),this.map.set(t,r),r}delete(t){return this.map.delete(t)}set(t,r){if(!this.delete(t)&&r!==void 0){if(this.map.size>=this.max){const i=this.map.keys().next().value;this.delete(i)}this.map.set(t,r)}return this}}var mm=ym,uo,Tc;function Mt(){if(Tc)return uo;Tc=1;const e=/\s+/g;class t{constructor(d,m){if(m=i(m),d instanceof t)return d.loose===!!m.loose&&d.includePrerelease===!!m.includePrerelease?d:new t(d.raw,m);if(d instanceof s)return this.raw=d.value,this.set=[[d]],this.formatted=void 0,this;if(this.options=m,this.loose=!!m.loose,this.includePrerelease=!!m.includePrerelease,this.raw=d.trim().replace(e," "),this.set=this.raw.split("||").map(v=>this.parseRange(v.trim())).filter(v=>v.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const v=this.set[0];if(this.set=this.set.filter(S=>!$(S[0])),this.set.length===0)this.set=[v];else if(this.set.length>1){for(const S of this.set)if(S.length===1&&O(S[0])){this.set=[S];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let d=0;d<this.set.length;d++){d>0&&(this.formatted+="||");const m=this.set[d];for(let v=0;v<m.length;v++)v>0&&(this.formatted+=" "),this.formatted+=m[v].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(d){const v=((this.options.includePrerelease&&L)|(this.options.loose&&j))+":"+d,S=n.get(v);if(S)return S;const x=this.options.loose,B=x?u[p.HYPHENRANGELOOSE]:u[p.HYPHENRANGE];d=d.replace(B,pe(this.options.includePrerelease)),c("hyphen replace",d),d=d.replace(u[p.COMPARATORTRIM],g),c("comparator trim",d),d=d.replace(u[p.TILDETRIM],w),c("tilde trim",d),d=d.replace(u[p.CARETTRIM],_),c("caret trim",d);let b=d.split(" ").map(J=>N(J,this.options)).join(" ").split(/\s+/).map(J=>oe(J,this.options));x&&(b=b.filter(J=>(c("loose invalid filter",J,this.options),!!J.match(u[p.COMPARATORLOOSE])))),c("range list",b);const h=new Map,R=b.map(J=>new s(J,this.options));for(const J of R){if($(J))return[J];h.set(J.value,J)}h.size>1&&h.has("")&&h.delete("");const K=[...h.values()];return n.set(v,K),K}intersects(d,m){if(!(d instanceof t))throw new TypeError("a Range is required");return this.set.some(v=>A(v,m)&&d.set.some(S=>A(S,m)&&v.every(x=>S.every(B=>x.intersects(B,m)))))}test(d){if(!d)return!1;if(typeof d=="string")try{d=new o(d,this.options)}catch{return!1}for(let m=0;m<this.set.length;m++)if(k(this.set[m],d,this.options))return!0;return!1}}uo=t;const r=mm,n=new r,i=ea,s=cs(),c=as,o=Zn,{safeRe:u,t:p,comparatorTrimReplace:g,tildeTrimReplace:w,caretTrimReplace:_}=Jn,{FLAG_INCLUDE_PRERELEASE:L,FLAG_LOOSE:j}=os,$=a=>a.value==="<0.0.0-0",O=a=>a.value==="",A=(a,d)=>{let m=!0;const v=a.slice();let S=v.pop();for(;m&&v.length;)m=v.every(x=>S.intersects(x,d)),S=v.pop();return m},N=(a,d)=>(c("comp",a,d),a=z(a,d),c("caret",a),a=W(a,d),c("tildes",a),a=X(a,d),c("xrange",a),a=de(a,d),c("stars",a),a),C=a=>!a||a.toLowerCase()==="x"||a==="*",W=(a,d)=>a.trim().split(/\s+/).map(m=>U(m,d)).join(" "),U=(a,d)=>{const m=d.loose?u[p.TILDELOOSE]:u[p.TILDE];return a.replace(m,(v,S,x,B,b)=>{c("tilde",a,v,S,x,B,b);let h;return C(S)?h="":C(x)?h=`>=${S}.0.0 <${+S+1}.0.0-0`:C(B)?h=`>=${S}.${x}.0 <${S}.${+x+1}.0-0`:b?(c("replaceTilde pr",b),h=`>=${S}.${x}.${B}-${b} <${S}.${+x+1}.0-0`):h=`>=${S}.${x}.${B} <${S}.${+x+1}.0-0`,c("tilde return",h),h})},z=(a,d)=>a.trim().split(/\s+/).map(m=>te(m,d)).join(" "),te=(a,d)=>{c("caret",a,d);const m=d.loose?u[p.CARETLOOSE]:u[p.CARET],v=d.includePrerelease?"-0":"";return a.replace(m,(S,x,B,b,h)=>{c("caret",a,S,x,B,b,h);let R;return C(x)?R="":C(B)?R=`>=${x}.0.0${v} <${+x+1}.0.0-0`:C(b)?x==="0"?R=`>=${x}.${B}.0${v} <${x}.${+B+1}.0-0`:R=`>=${x}.${B}.0${v} <${+x+1}.0.0-0`:h?(c("replaceCaret pr",h),x==="0"?B==="0"?R=`>=${x}.${B}.${b}-${h} <${x}.${B}.${+b+1}-0`:R=`>=${x}.${B}.${b}-${h} <${x}.${+B+1}.0-0`:R=`>=${x}.${B}.${b}-${h} <${+x+1}.0.0-0`):(c("no pr"),x==="0"?B==="0"?R=`>=${x}.${B}.${b}${v} <${x}.${B}.${+b+1}-0`:R=`>=${x}.${B}.${b}${v} <${x}.${+B+1}.0-0`:R=`>=${x}.${B}.${b} <${+x+1}.0.0-0`),c("caret return",R),R})},X=(a,d)=>(c("replaceXRanges",a,d),a.split(/\s+/).map(m=>Y(m,d)).join(" ")),Y=(a,d)=>{a=a.trim();const m=d.loose?u[p.XRANGELOOSE]:u[p.XRANGE];return a.replace(m,(v,S,x,B,b,h)=>{c("xRange",a,v,S,x,B,b,h);const R=C(x),K=R||C(B),J=K||C(b),T=J;return S==="="&&T&&(S=""),h=d.includePrerelease?"-0":"",R?S===">"||S==="<"?v="<0.0.0-0":v="*":S&&T?(K&&(B=0),b=0,S===">"?(S=">=",K?(x=+x+1,B=0,b=0):(B=+B+1,b=0)):S==="<="&&(S="<",K?x=+x+1:B=+B+1),S==="<"&&(h="-0"),v=`${S+x}.${B}.${b}${h}`):K?v=`>=${x}.0.0${h} <${+x+1}.0.0-0`:J&&(v=`>=${x}.${B}.0${h} <${x}.${+B+1}.0-0`),c("xRange return",v),v})},de=(a,d)=>(c("replaceStars",a,d),a.trim().replace(u[p.STAR],"")),oe=(a,d)=>(c("replaceGTE0",a,d),a.trim().replace(u[d.includePrerelease?p.GTE0PRE:p.GTE0],"")),pe=a=>(d,m,v,S,x,B,b,h,R,K,J,T)=>(C(v)?m="":C(S)?m=`>=${v}.0.0${a?"-0":""}`:C(x)?m=`>=${v}.${S}.0${a?"-0":""}`:B?m=`>=${m}`:m=`>=${m}${a?"-0":""}`,C(R)?h="":C(K)?h=`<${+R+1}.0.0-0`:C(J)?h=`<${R}.${+K+1}.0-0`:T?h=`<=${R}.${K}.${J}-${T}`:a?h=`<${R}.${K}.${+J+1}-0`:h=`<=${h}`,`${m} ${h}`.trim()),k=(a,d,m)=>{for(let v=0;v<a.length;v++)if(!a[v].test(d))return!1;if(d.prerelease.length&&!m.includePrerelease){for(let v=0;v<a.length;v++)if(c(a[v].semver),a[v].semver!==s.ANY&&a[v].semver.prerelease.length>0){const S=a[v].semver;if(S.major===d.major&&S.minor===d.minor&&S.patch===d.patch)return!0}return!1}return!0};return uo}var ho,Bc;function cs(){if(Bc)return ho;Bc=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(g,w){if(w=r(w),g instanceof t){if(g.loose===!!w.loose)return g;g=g.value}g=g.trim().split(/\s+/).join(" "),c("comparator",g,w),this.options=w,this.loose=!!w.loose,this.parse(g),this.semver===e?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(g){const w=this.options.loose?n[i.COMPARATORLOOSE]:n[i.COMPARATOR],_=g.match(w);if(!_)throw new TypeError(`Invalid comparator: ${g}`);this.operator=_[1]!==void 0?_[1]:"",this.operator==="="&&(this.operator=""),_[2]?this.semver=new o(_[2],this.options.loose):this.semver=e}toString(){return this.value}test(g){if(c("Comparator.test",g,this.options.loose),this.semver===e||g===e)return!0;if(typeof g=="string")try{g=new o(g,this.options)}catch{return!1}return s(g,this.operator,this.semver,this.options)}intersects(g,w){if(!(g instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new u(g.value,w).test(this.value):g.operator===""?g.value===""?!0:new u(this.value,w).test(g.semver):(w=r(w),w.includePrerelease&&(this.value==="<0.0.0-0"||g.value==="<0.0.0-0")||!w.includePrerelease&&(this.value.startsWith("<0.0.0")||g.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&g.operator.startsWith(">")||this.operator.startsWith("<")&&g.operator.startsWith("<")||this.semver.version===g.semver.version&&this.operator.includes("=")&&g.operator.includes("=")||s(this.semver,"<",g.semver,w)&&this.operator.startsWith(">")&&g.operator.startsWith("<")||s(this.semver,">",g.semver,w)&&this.operator.startsWith("<")&&g.operator.startsWith(">")))}}ho=t;const r=ea,{safeRe:n,t:i}=Jn,s=gm,c=as,o=Zn,u=Mt();return ho}const vm=Mt(),bm=(e,t,r)=>{try{t=new vm(t,r)}catch{return!1}return t.test(e)};var Ku=bm;Mt();Mt();Mt();Mt();const wm=Mt(),_m=(e,t)=>{try{return new wm(e,t).range||"*"}catch{return null}};var Em=_m;const Sm=Zn,Yu=cs(),{ANY:Rm}=Yu,Mm=Mt(),Im=Ku,Nc=ta,Pc=Gu,Am=Zu,xm=Ju,km=(e,t,r,n)=>{e=new Sm(e,n),t=new Mm(t,n);let i,s,c,o,u;switch(r){case">":i=Nc,s=Am,c=Pc,o=">",u=">=";break;case"<":i=Pc,s=xm,c=Nc,o="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Im(e,t,n))return!1;for(let p=0;p<t.set.length;++p){const g=t.set[p];let w=null,_=null;if(g.forEach(L=>{L.semver===Rm&&(L=new Yu(">=0.0.0")),w=w||L,_=_||L,i(L.semver,w.semver,n)?w=L:c(L.semver,_.semver,n)&&(_=L)}),w.operator===o||w.operator===u||(!_.operator||_.operator===o)&&s(e,_.semver))return!1;if(_.operator===u&&c(e,_.semver))return!1}return!0};var Cm=km;const Tm=Cm,Bm=(e,t,r)=>Tm(e,t,">",r);var Nm=Bm;Mt();Mt();const ra=cs(),{ANY:tw}=ra;new ra(">=0.0.0-0");new ra(">=0.0.0");const fo=Jn,Lc=os,Oc=zu,Pm=zy,Lm=ta;cs();Mt();const Om=Ku,$m=Em,Fm=Nm;var Dm={valid:Pm,gt:Lm,satisfies:Om,validRange:$m,gtr:Fm,re:fo.re,src:fo.src,tokens:fo.t,SEMVER_SPEC_VERSION:Lc.SEMVER_SPEC_VERSION,RELEASE_TYPES:Lc.RELEASE_TYPES,compareIdentifiers:Oc.compareIdentifiers,rcompareIdentifiers:Oc.rcompareIdentifiers};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.satisfiesVersionRange=e.gtRange=e.gtVersion=e.assertIsSemVerRange=e.assertIsSemVerVersion=e.isValidSemVerRange=e.isValidSemVerVersion=e.VersionRangeStruct=e.VersionStruct=void 0;const t=Dm,r=Sr,n=it;e.VersionStruct=(0,r.refine)((0,r.string)(),"Version",w=>(0,t.valid)(w)===null?`Expected SemVer version, got "${w}"`:!0),e.VersionRangeStruct=(0,r.refine)((0,r.string)(),"Version range",w=>(0,t.validRange)(w)===null?`Expected SemVer range, got "${w}"`:!0);function i(w){return(0,r.is)(w,e.VersionStruct)}e.isValidSemVerVersion=i;function s(w){return(0,r.is)(w,e.VersionRangeStruct)}e.isValidSemVerRange=s;function c(w){(0,n.assertStruct)(w,e.VersionStruct)}e.assertIsSemVerVersion=c;function o(w){(0,n.assertStruct)(w,e.VersionRangeStruct)}e.assertIsSemVerRange=o;function u(w,_){return(0,t.gt)(w,_)}e.gtVersion=u;function p(w,_){return(0,t.gtr)(w,_)}e.gtRange=p;function g(w,_){return(0,t.satisfies)(w,_,{includePrerelease:!0})}e.satisfiesVersionRange=g})(Wu);(function(e){var t=ee&&ee.__createBinding||(Object.create?function(n,i,s,c){c===void 0&&(c=s);var o=Object.getOwnPropertyDescriptor(i,s);(!o||("get"in o?!i.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return i[s]}}),Object.defineProperty(n,c,o)}:function(n,i,s,c){c===void 0&&(c=s),n[c]=i[s]}),r=ee&&ee.__exportStar||function(n,i){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s)&&t(i,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(it,e),r(zn,e),r(be,e),r(is,e),r(Lt,e),r(en,e),r(Ou,e),r(Gn,e),r($u,e),r(Fu,e),r(tn,e),r(Du,e),r(Ot,e),r(ju,e),r(Uu,e),r(Hu,e),r(Wu,e)})(mu);(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.createModuleLogger=e.projectLogger=void 0;const t=mu;Object.defineProperty(e,"createModuleLogger",{enumerable:!0,get:function(){return t.createModuleLogger}}),e.projectLogger=(0,t.createProjectLogger)("eth-block-tracker")})(yu);var Qu=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(rs,"__esModule",{value:!0});rs.PollingBlockTracker=void 0;const jm=Qu(Vo),Um=Qu(Q1),Hm=Hn,$c=yu,Fc=(0,$c.createModuleLogger)($c.projectLogger,"polling-block-tracker"),Wm=(0,jm.default)(),qm=1e3;class Vm extends Hm.BaseBlockTracker{constructor(t={}){var r;if(!t.provider)throw new Error("PollingBlockTracker - no provider specified.");super(Object.assign(Object.assign({},t),{blockResetDuration:(r=t.blockResetDuration)!==null&&r!==void 0?r:t.pollingInterval})),this._provider=t.provider,this._pollingInterval=t.pollingInterval||20*qm,this._retryTimeout=t.retryTimeout||this._pollingInterval/10,this._keepEventLoopActive=t.keepEventLoopActive===void 0?!0:t.keepEventLoopActive,this._setSkipCacheFlag=t.setSkipCacheFlag||!1}async checkForLatestBlock(){return await this._updateLatestBlock(),await this.getLatestBlock()}async _start(){this._synchronize()}async _end(){}async _synchronize(){for(var t;this._isRunning;)try{await this._updateLatestBlock();const r=Dc(this._pollingInterval,!this._keepEventLoopActive);this.emit("_waitingForNextIteration"),await r}catch(r){const n=new Error(`PollingBlockTracker - encountered an error while attempting to update latest block:
${(t=r.stack)!==null&&t!==void 0?t:r}`);try{this.emit("error",n)}catch{console.error(n)}const i=Dc(this._retryTimeout,!this._keepEventLoopActive);this.emit("_waitingForNextIteration"),await i}}async _updateLatestBlock(){const t=await this._fetchLatestBlock();this._newPotentialLatest(t)}async _fetchLatestBlock(){const t={jsonrpc:"2.0",id:Wm(),method:"eth_blockNumber",params:[]};this._setSkipCacheFlag&&(t.skipCache=!0),Fc("Making request",t);const r=await(0,Um.default)(n=>this._provider.sendAsync(t,n))();if(Fc("Got response",r),r.error)throw new Error(`PollingBlockTracker - encountered error fetching block:
${r.error.message}`);return r.result}}rs.PollingBlockTracker=Vm;function Dc(e,t){return new Promise(r=>{const n=setTimeout(r,e);n.unref&&t&&n.unref()})}var ls={},zm=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ls,"__esModule",{value:!0});ls.SubscribeBlockTracker=void 0;const Gm=zm(Vo),Jm=Hn,Zm=(0,Gm.default)();class Km extends Jm.BaseBlockTracker{constructor(t={}){if(!t.provider)throw new Error("SubscribeBlockTracker - no provider specified.");super(t),this._provider=t.provider,this._subscriptionId=null}async checkForLatestBlock(){return await this.getLatestBlock()}async _start(){if(this._subscriptionId===void 0||this._subscriptionId===null)try{const t=await this._call("eth_blockNumber");this._subscriptionId=await this._call("eth_subscribe","newHeads"),this._provider.on("data",this._handleSubData.bind(this)),this._newPotentialLatest(t)}catch(t){this.emit("error",t)}}async _end(){if(this._subscriptionId!==null&&this._subscriptionId!==void 0)try{await this._call("eth_unsubscribe",this._subscriptionId),this._subscriptionId=null}catch(t){this.emit("error",t)}}_call(t,...r){return new Promise((n,i)=>{this._provider.sendAsync({id:Zm(),method:t,params:r,jsonrpc:"2.0"},(s,c)=>{s?i(s):n(c.result)})})}_handleSubData(t,r){var n;r.method==="eth_subscription"&&((n=r.params)===null||n===void 0?void 0:n.subscription)===this._subscriptionId&&this._newPotentialLatest(r.params.result.number)}}ls.SubscribeBlockTracker=Km;(function(e){var t=ee&&ee.__createBinding||(Object.create?function(n,i,s,c){c===void 0&&(c=s),Object.defineProperty(n,c,{enumerable:!0,get:function(){return i[s]}})}:function(n,i,s,c){c===void 0&&(c=s),n[c]=i[s]}),r=ee&&ee.__exportStar||function(n,i){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s)&&t(i,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(rs,e),r(ls,e)})(gu);var na={},us={},Kn={};Object.defineProperty(Kn,"__esModule",{value:!0});Kn.getUniqueId=void 0;const Xu=**********;let po=Math.floor(Math.random()*Xu);function Ym(){return po=(po+1)%Xu,po}Kn.getUniqueId=Ym;Object.defineProperty(us,"__esModule",{value:!0});us.createIdRemapMiddleware=void 0;const Qm=Kn;function Xm(){return(e,t,r,n)=>{const i=e.id,s=Qm.getUniqueId();e.id=s,t.id=s,r(c=>{e.id=i,t.id=i,c()})}}us.createIdRemapMiddleware=Xm;var hs={};Object.defineProperty(hs,"__esModule",{value:!0});hs.createAsyncMiddleware=void 0;function ev(e){return async(t,r,n,i)=>{let s;const c=new Promise(g=>{s=g});let o=null,u=!1;const p=async()=>{u=!0,n(g=>{o=g,s()}),await c};try{await e(t,r,p),u?(await c,o(null)):i(null)}catch(g){o?o(g):i(g)}}}hs.createAsyncMiddleware=ev;var fs={};Object.defineProperty(fs,"__esModule",{value:!0});fs.createScaffoldMiddleware=void 0;function tv(e){return(t,r,n,i)=>{const s=e[t.method];return s===void 0?n():typeof s=="function"?s(t,r,n,i):(r.result=s,i())}}fs.createScaffoldMiddleware=tv;var Yn={},ia={};Object.defineProperty(ia,"__esModule",{value:!0});const rv=Ei;function jc(e,t,r){try{Reflect.apply(e,t,r)}catch(n){setTimeout(()=>{throw n})}}function nv(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}let iv=class extends rv.EventEmitter{emit(t,...r){let n=t==="error";const i=this._events;if(i!==void 0)n=n&&i.error===void 0;else if(!n)return!1;if(n){let c;if(r.length>0&&([c]=r),c instanceof Error)throw c;const o=new Error(`Unhandled error.${c?` (${c.message})`:""}`);throw o.context=c,o}const s=i[t];if(s===void 0)return!1;if(typeof s=="function")jc(s,this,r);else{const c=s.length,o=nv(s);for(let u=0;u<c;u+=1)jc(o[u],this,r)}return!0}};ia.default=iv;var eh={},zt={},sv=_n;_n.default=_n;_n.stable=nh;_n.stableStringify=nh;var wi="[...]",th="[Circular]",br=[],gr=[];function rh(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function _n(e,t,r,n){typeof n>"u"&&(n=rh()),Io(e,"",0,[],void 0,0,n);var i;try{gr.length===0?i=JSON.stringify(e,t,r):i=JSON.stringify(e,ih(t),r)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;br.length!==0;){var s=br.pop();s.length===4?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return i}function Gr(e,t,r,n){var i=Object.getOwnPropertyDescriptor(n,r);i.get!==void 0?i.configurable?(Object.defineProperty(n,r,{value:e}),br.push([n,r,t,i])):gr.push([t,r,e]):(n[r]=e,br.push([n,r,t]))}function Io(e,t,r,n,i,s,c){s+=1;var o;if(typeof e=="object"&&e!==null){for(o=0;o<n.length;o++)if(n[o]===e){Gr(th,e,t,i);return}if(typeof c.depthLimit<"u"&&s>c.depthLimit){Gr(wi,e,t,i);return}if(typeof c.edgesLimit<"u"&&r+1>c.edgesLimit){Gr(wi,e,t,i);return}if(n.push(e),Array.isArray(e))for(o=0;o<e.length;o++)Io(e[o],o,o,n,e,s,c);else{var u=Object.keys(e);for(o=0;o<u.length;o++){var p=u[o];Io(e[p],p,o,n,e,s,c)}}n.pop()}}function ov(e,t){return e<t?-1:e>t?1:0}function nh(e,t,r,n){typeof n>"u"&&(n=rh());var i=Ao(e,"",0,[],void 0,0,n)||e,s;try{gr.length===0?s=JSON.stringify(i,t,r):s=JSON.stringify(i,ih(t),r)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;br.length!==0;){var c=br.pop();c.length===4?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return s}function Ao(e,t,r,n,i,s,c){s+=1;var o;if(typeof e=="object"&&e!==null){for(o=0;o<n.length;o++)if(n[o]===e){Gr(th,e,t,i);return}try{if(typeof e.toJSON=="function")return}catch{return}if(typeof c.depthLimit<"u"&&s>c.depthLimit){Gr(wi,e,t,i);return}if(typeof c.edgesLimit<"u"&&r+1>c.edgesLimit){Gr(wi,e,t,i);return}if(n.push(e),Array.isArray(e))for(o=0;o<e.length;o++)Ao(e[o],o,o,n,e,s,c);else{var u={},p=Object.keys(e).sort(ov);for(o=0;o<p.length;o++){var g=p[o];Ao(e[g],g,o,n,e,s,c),u[g]=e[g]}if(typeof i<"u")br.push([i,t,e]),i[t]=u;else return u}n.pop()}}function ih(e){return e=typeof e<"u"?e:function(t,r){return r},function(t,r){if(gr.length>0)for(var n=0;n<gr.length;n++){var i=gr[n];if(i[1]===t&&i[0]===r){r=i[2],gr.splice(n,1);break}}return e.call(this,t,r)}}Object.defineProperty(zt,"__esModule",{value:!0});zt.EthereumProviderError=zt.EthereumRpcError=void 0;const av=sv;class sh extends Error{constructor(t,r,n){if(!Number.isInteger(t))throw new Error('"code" must be an integer.');if(!r||typeof r!="string")throw new Error('"message" must be a nonempty string.');super(r),this.code=t,n!==void 0&&(this.data=n)}serialize(){const t={code:this.code,message:this.message};return this.data!==void 0&&(t.data=this.data),this.stack&&(t.stack=this.stack),t}toString(){return av.default(this.serialize(),uv,2)}}zt.EthereumRpcError=sh;class cv extends sh{constructor(t,r,n){if(!lv(t))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(t,r,n)}}zt.EthereumProviderError=cv;function lv(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}function uv(e,t){if(t!=="[Circular]")return t}var sa={},Gt={};Object.defineProperty(Gt,"__esModule",{value:!0});Gt.errorValues=Gt.errorCodes=void 0;Gt.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}};Gt.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.serializeError=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const t=Gt,r=zt,n=t.errorCodes.rpc.internal,i="Unspecified error message. This is a bug, please report it.",s={code:n,message:c(n)};e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.";function c(_,L=i){if(Number.isInteger(_)){const j=_.toString();if(w(t.errorValues,j))return t.errorValues[j].message;if(p(_))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return L}e.getMessageFromCode=c;function o(_){if(!Number.isInteger(_))return!1;const L=_.toString();return!!(t.errorValues[L]||p(_))}e.isValidCode=o;function u(_,{fallbackError:L=s,shouldIncludeStack:j=!1}={}){var $,O;if(!L||!Number.isInteger(L.code)||typeof L.message!="string")throw new Error("Must provide fallback error with integer number code and string message.");if(_ instanceof r.EthereumRpcError)return _.serialize();const A={};if(_&&typeof _=="object"&&!Array.isArray(_)&&w(_,"code")&&o(_.code)){const C=_;A.code=C.code,C.message&&typeof C.message=="string"?(A.message=C.message,w(C,"data")&&(A.data=C.data)):(A.message=c(A.code),A.data={originalError:g(_)})}else{A.code=L.code;const C=($=_)===null||$===void 0?void 0:$.message;A.message=C&&typeof C=="string"?C:L.message,A.data={originalError:g(_)}}const N=(O=_)===null||O===void 0?void 0:O.stack;return j&&_&&N&&typeof N=="string"&&(A.stack=N),A}e.serializeError=u;function p(_){return _>=-32099&&_<=-32e3}function g(_){return _&&typeof _=="object"&&!Array.isArray(_)?Object.assign({},_):_}function w(_,L){return Object.prototype.hasOwnProperty.call(_,L)}})(sa);var ds={};Object.defineProperty(ds,"__esModule",{value:!0});ds.ethErrors=void 0;const oa=zt,oh=sa,st=Gt;ds.ethErrors={rpc:{parse:e=>gt(st.errorCodes.rpc.parse,e),invalidRequest:e=>gt(st.errorCodes.rpc.invalidRequest,e),invalidParams:e=>gt(st.errorCodes.rpc.invalidParams,e),methodNotFound:e=>gt(st.errorCodes.rpc.methodNotFound,e),internal:e=>gt(st.errorCodes.rpc.internal,e),server:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return gt(t,e)},invalidInput:e=>gt(st.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>gt(st.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>gt(st.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>gt(st.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>gt(st.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>gt(st.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>pn(st.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>pn(st.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>pn(st.errorCodes.provider.unsupportedMethod,e),disconnected:e=>pn(st.errorCodes.provider.disconnected,e),chainDisconnected:e=>pn(st.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:r,data:n}=e;if(!r||typeof r!="string")throw new Error('"message" must be a nonempty string');return new oa.EthereumProviderError(t,r,n)}}};function gt(e,t){const[r,n]=ah(t);return new oa.EthereumRpcError(e,r||oh.getMessageFromCode(e),n)}function pn(e,t){const[r,n]=ah(t);return new oa.EthereumProviderError(e,r||oh.getMessageFromCode(e),n)}function ah(e){if(e){if(typeof e=="string")return[e];if(typeof e=="object"&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&typeof t!="string")throw new Error("Must specify string message.");return[t||void 0,r]}}return[]}(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getMessageFromCode=e.serializeError=e.EthereumProviderError=e.EthereumRpcError=e.ethErrors=e.errorCodes=void 0;const t=zt;Object.defineProperty(e,"EthereumRpcError",{enumerable:!0,get:function(){return t.EthereumRpcError}}),Object.defineProperty(e,"EthereumProviderError",{enumerable:!0,get:function(){return t.EthereumProviderError}});const r=sa;Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return r.serializeError}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return r.getMessageFromCode}});const n=ds;Object.defineProperty(e,"ethErrors",{enumerable:!0,get:function(){return n.ethErrors}});const i=Gt;Object.defineProperty(e,"errorCodes",{enumerable:!0,get:function(){return i.errorCodes}})})(eh);var hv=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Yn,"__esModule",{value:!0});Yn.JsonRpcEngine=void 0;const fv=hv(ia),yt=eh;class $t extends fv.default{constructor(){super(),this._middleware=[]}push(t){this._middleware.push(t)}handle(t,r){if(r&&typeof r!="function")throw new Error('"callback" must be a function if provided.');return Array.isArray(t)?r?this._handleBatch(t,r):this._handleBatch(t):r?this._handle(t,r):this._promiseHandle(t)}asMiddleware(){return async(t,r,n,i)=>{try{const[s,c,o]=await $t._runAllMiddleware(t,r,this._middleware);return c?(await $t._runReturnHandlers(o),i(s)):n(async u=>{try{await $t._runReturnHandlers(o)}catch(p){return u(p)}return u()})}catch(s){return i(s)}}}async _handleBatch(t,r){try{const n=await Promise.all(t.map(this._promiseHandle.bind(this)));return r?r(null,n):n}catch(n){if(r)return r(n);throw n}}_promiseHandle(t){return new Promise(r=>{this._handle(t,(n,i)=>{r(i)})})}async _handle(t,r){if(!t||Array.isArray(t)||typeof t!="object"){const c=new yt.EthereumRpcError(yt.errorCodes.rpc.invalidRequest,`Requests must be plain objects. Received: ${typeof t}`,{request:t});return r(c,{id:void 0,jsonrpc:"2.0",error:c})}if(typeof t.method!="string"){const c=new yt.EthereumRpcError(yt.errorCodes.rpc.invalidRequest,`Must specify a string method. Received: ${typeof t.method}`,{request:t});return r(c,{id:t.id,jsonrpc:"2.0",error:c})}const n=Object.assign({},t),i={id:n.id,jsonrpc:n.jsonrpc};let s=null;try{await this._processRequest(n,i)}catch(c){s=c}return s&&(delete i.result,i.error||(i.error=yt.serializeError(s))),r(s,i)}async _processRequest(t,r){const[n,i,s]=await $t._runAllMiddleware(t,r,this._middleware);if($t._checkForCompletion(t,r,i),await $t._runReturnHandlers(s),n)throw n}static async _runAllMiddleware(t,r,n){const i=[];let s=null,c=!1;for(const o of n)if([s,c]=await $t._runMiddleware(t,r,o,i),c)break;return[s,c,i.reverse()]}static _runMiddleware(t,r,n,i){return new Promise(s=>{const c=u=>{const p=u||r.error;p&&(r.error=yt.serializeError(p)),s([p,!0])},o=u=>{r.error?c(r.error):(u&&(typeof u!="function"&&c(new yt.EthereumRpcError(yt.errorCodes.rpc.internal,`JsonRpcEngine: "next" return handlers must be functions. Received "${typeof u}" for request:
${go(t)}`,{request:t})),i.push(u)),s([null,!1]))};try{n(t,r,o,c)}catch(u){c(u)}})}static async _runReturnHandlers(t){for(const r of t)await new Promise((n,i)=>{r(s=>s?i(s):n())})}static _checkForCompletion(t,r,n){if(!("result"in r)&&!("error"in r))throw new yt.EthereumRpcError(yt.errorCodes.rpc.internal,`JsonRpcEngine: Response has no error or result for request:
${go(t)}`,{request:t});if(!n)throw new yt.EthereumRpcError(yt.errorCodes.rpc.internal,`JsonRpcEngine: Nothing ended request:
${go(t)}`,{request:t})}}Yn.JsonRpcEngine=$t;function go(e){return JSON.stringify(e,null,2)}var ps={};Object.defineProperty(ps,"__esModule",{value:!0});ps.mergeMiddleware=void 0;const dv=Yn;function pv(e){const t=new dv.JsonRpcEngine;return e.forEach(r=>t.push(r)),t.asMiddleware()}ps.mergeMiddleware=pv;(function(e){var t=ee&&ee.__createBinding||(Object.create?function(n,i,s,c){c===void 0&&(c=s),Object.defineProperty(n,c,{enumerable:!0,get:function(){return i[s]}})}:function(n,i,s,c){c===void 0&&(c=s),n[c]=i[s]}),r=ee&&ee.__exportStar||function(n,i){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s)&&t(i,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(us,e),r(hs,e),r(fs,e),r(Kn,e),r(Yn,e),r(ps,e)})(na);var ch={},aa={};const ca=rn(dh);var gs={};Object.defineProperty(gs,"__esModule",{value:!0});var Uc=ca,gv=function(){function e(t){if(this._maxConcurrency=t,this._queue=[],t<=0)throw new Error("semaphore must be initialized to a positive value");this._value=t}return e.prototype.acquire=function(){var t=this,r=this.isLocked(),n=new Promise(function(i){return t._queue.push(i)});return r||this._dispatch(),n},e.prototype.runExclusive=function(t){return Uc.__awaiter(this,void 0,void 0,function(){var r,n,i;return Uc.__generator(this,function(s){switch(s.label){case 0:return[4,this.acquire()];case 1:r=s.sent(),n=r[0],i=r[1],s.label=2;case 2:return s.trys.push([2,,4,5]),[4,t(n)];case 3:return[2,s.sent()];case 4:return i(),[7];case 5:return[2]}})})},e.prototype.isLocked=function(){return this._value<=0},e.prototype.release=function(){if(this._maxConcurrency>1)throw new Error("this method is unavailabel on semaphores with concurrency > 1; use the scoped release returned by acquire instead");if(this._currentReleaser){var t=this._currentReleaser;this._currentReleaser=void 0,t()}},e.prototype._dispatch=function(){var t=this,r=this._queue.shift();if(r){var n=!1;this._currentReleaser=function(){n||(n=!0,t._value++,t._dispatch())},r([this._value--,this._currentReleaser])}},e}();gs.default=gv;Object.defineProperty(aa,"__esModule",{value:!0});var Hc=ca,yv=gs,mv=function(){function e(){this._semaphore=new yv.default(1)}return e.prototype.acquire=function(){return Hc.__awaiter(this,void 0,void 0,function(){var t,r;return Hc.__generator(this,function(n){switch(n.label){case 0:return[4,this._semaphore.acquire()];case 1:return t=n.sent(),r=t[1],[2,r]}})})},e.prototype.runExclusive=function(t){return this._semaphore.runExclusive(function(){return t()})},e.prototype.isLocked=function(){return this._semaphore.isLocked()},e.prototype.release=function(){this._semaphore.release()},e}();aa.default=mv;var ys={};Object.defineProperty(ys,"__esModule",{value:!0});ys.withTimeout=void 0;var ui=ca;function vv(e,t,r){var n=this;return r===void 0&&(r=new Error("timeout")),{acquire:function(){return new Promise(function(i,s){return ui.__awaiter(n,void 0,void 0,function(){var c,o,u;return ui.__generator(this,function(p){switch(p.label){case 0:return c=!1,setTimeout(function(){c=!0,s(r)},t),[4,e.acquire()];case 1:return o=p.sent(),c?(u=Array.isArray(o)?o[1]:o,u()):i(o),[2]}})})})},runExclusive:function(i){return ui.__awaiter(this,void 0,void 0,function(){var s,c;return ui.__generator(this,function(o){switch(o.label){case 0:s=function(){},o.label=1;case 1:return o.trys.push([1,,7,8]),[4,this.acquire()];case 2:return c=o.sent(),Array.isArray(c)?(s=c[1],[4,i(c[0])]):[3,4];case 3:return[2,o.sent()];case 4:return s=c,[4,i()];case 5:return[2,o.sent()];case 6:return[3,8];case 7:return s(),[7];case 8:return[2]}})})},release:function(){e.release()},isLocked:function(){return e.isLocked()}}}ys.withTimeout=vv;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.withTimeout=e.Semaphore=e.Mutex=void 0;var t=aa;Object.defineProperty(e,"Mutex",{enumerable:!0,get:function(){return t.default}});var r=gs;Object.defineProperty(e,"Semaphore",{enumerable:!0,get:function(){return r.default}});var n=ys;Object.defineProperty(e,"withTimeout",{enumerable:!0,get:function(){return n.withTimeout}})})(ch);var bv=_v,wv=Object.prototype.hasOwnProperty;function _v(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var n in r)wv.call(r,n)&&(e[n]=r[n])}return e}const Ev=bv,Sv=Vo();var Rv=ce;function ce(e){const t=this;t.currentProvider=e}ce.prototype.getBalance=Qn(2,"eth_getBalance");ce.prototype.getCode=Qn(2,"eth_getCode");ce.prototype.getTransactionCount=Qn(2,"eth_getTransactionCount");ce.prototype.getStorageAt=Qn(3,"eth_getStorageAt");ce.prototype.call=Qn(2,"eth_call");ce.prototype.protocolVersion=ge("eth_protocolVersion");ce.prototype.syncing=ge("eth_syncing");ce.prototype.coinbase=ge("eth_coinbase");ce.prototype.mining=ge("eth_mining");ce.prototype.hashrate=ge("eth_hashrate");ce.prototype.gasPrice=ge("eth_gasPrice");ce.prototype.accounts=ge("eth_accounts");ce.prototype.blockNumber=ge("eth_blockNumber");ce.prototype.getBlockTransactionCountByHash=ge("eth_getBlockTransactionCountByHash");ce.prototype.getBlockTransactionCountByNumber=ge("eth_getBlockTransactionCountByNumber");ce.prototype.getUncleCountByBlockHash=ge("eth_getUncleCountByBlockHash");ce.prototype.getUncleCountByBlockNumber=ge("eth_getUncleCountByBlockNumber");ce.prototype.sign=ge("eth_sign");ce.prototype.sendTransaction=ge("eth_sendTransaction");ce.prototype.sendRawTransaction=ge("eth_sendRawTransaction");ce.prototype.estimateGas=ge("eth_estimateGas");ce.prototype.getBlockByHash=ge("eth_getBlockByHash");ce.prototype.getBlockByNumber=ge("eth_getBlockByNumber");ce.prototype.getTransactionByHash=ge("eth_getTransactionByHash");ce.prototype.getTransactionByBlockHashAndIndex=ge("eth_getTransactionByBlockHashAndIndex");ce.prototype.getTransactionByBlockNumberAndIndex=ge("eth_getTransactionByBlockNumberAndIndex");ce.prototype.getTransactionReceipt=ge("eth_getTransactionReceipt");ce.prototype.getUncleByBlockHashAndIndex=ge("eth_getUncleByBlockHashAndIndex");ce.prototype.getUncleByBlockNumberAndIndex=ge("eth_getUncleByBlockNumberAndIndex");ce.prototype.getCompilers=ge("eth_getCompilers");ce.prototype.compileLLL=ge("eth_compileLLL");ce.prototype.compileSolidity=ge("eth_compileSolidity");ce.prototype.compileSerpent=ge("eth_compileSerpent");ce.prototype.newFilter=ge("eth_newFilter");ce.prototype.newBlockFilter=ge("eth_newBlockFilter");ce.prototype.newPendingTransactionFilter=ge("eth_newPendingTransactionFilter");ce.prototype.uninstallFilter=ge("eth_uninstallFilter");ce.prototype.getFilterChanges=ge("eth_getFilterChanges");ce.prototype.getFilterLogs=ge("eth_getFilterLogs");ce.prototype.getLogs=ge("eth_getLogs");ce.prototype.getWork=ge("eth_getWork");ce.prototype.submitWork=ge("eth_submitWork");ce.prototype.submitHashrate=ge("eth_submitHashrate");ce.prototype.sendAsync=function(e,t){this.currentProvider.sendAsync(Mv(e),function(n,i){if(!n&&i.error&&(n=new Error("EthQuery - RPC Error - "+i.error.message)),n)return t(n);t(null,i.result)})};function ge(e){return function(){const t=this;var r=[].slice.call(arguments),n=r.pop();t.sendAsync({method:e,params:r},n)}}function Qn(e,t){return function(){const r=this;var n=[].slice.call(arguments),i=n.pop();n.length<e&&n.push("latest"),r.sendAsync({method:t,params:n},i)}}function Mv(e){return Ev({id:Sv(),jsonrpc:"2.0",params:[]},e)}const Wc=(e,t,r,n)=>function(...i){const s=t.promiseModule;return new s((c,o)=>{t.multiArgs?i.push((...p)=>{t.errorFirst?p[0]?o(p):(p.shift(),c(p)):c(p)}):t.errorFirst?i.push((p,g)=>{p?o(p):c(g)}):i.push(c),Reflect.apply(e,this===r?n:this,i)})},qc=new WeakMap;var Iv=(e,t)=>{t={exclude:[/.+(?:Sync|Stream)$/],errorFirst:!0,promiseModule:Promise,...t};const r=typeof e;if(!(e!==null&&(r==="object"||r==="function")))throw new TypeError(`Expected \`input\` to be a \`Function\` or \`Object\`, got \`${e===null?"null":r}\``);const n=(c,o)=>{let u=qc.get(c);if(u||(u={},qc.set(c,u)),o in u)return u[o];const p=j=>typeof j=="string"||typeof o=="symbol"?o===j:j.test(o),g=Reflect.getOwnPropertyDescriptor(c,o),w=g===void 0||g.writable||g.configurable,L=(t.include?t.include.some(p):!t.exclude.some(p))&&w;return u[o]=L,L},i=new WeakMap,s=new Proxy(e,{apply(c,o,u){const p=i.get(c);if(p)return Reflect.apply(p,o,u);const g=t.excludeMain?c:Wc(c,t,s,c);return i.set(c,g),Reflect.apply(g,o,u)},get(c,o){const u=c[o];if(!n(c,o)||u===Function.prototype[o])return u;const p=i.get(u);if(p)return p;if(typeof u=="function"){const g=Wc(u,t,s,c);return i.set(u,g),g}return u}});return s};const Av=Wn.default;let xv=class extends Av{constructor(){super(),this.updates=[]}async initialize(){}async update(){throw new Error("BaseFilter - no update method specified")}addResults(t){this.updates=this.updates.concat(t),t.forEach(r=>this.emit("update",r))}addInitialResults(t){}getChangesAndClear(){const t=this.updates;return this.updates=[],t}};var la=xv;const kv=la;let Cv=class extends kv{constructor(){super(),this.allResults=[]}async update(){throw new Error("BaseFilterWithHistory - no update method specified")}addResults(t){this.allResults=this.allResults.concat(t),super.addResults(t)}addInitialResults(t){this.allResults=this.allResults.concat(t),super.addInitialResults(t)}getAllResults(){return this.allResults}};var Tv=Cv,Xn={minBlockRef:Bv,bnToHex:Pv,blockRefIsNumber:Lv,hexToInt:_i,incrementHexInt:Ov,intToHex:lh,unsafeRandomBytes:$v};function Bv(...e){return Nv(e)[0]}function Nv(e){return e.sort((t,r)=>t==="latest"||r==="earliest"?1:r==="latest"||t==="earliest"?-1:_i(t)-_i(r))}function Pv(e){return"0x"+e.toString(16)}function Lv(e){return e&&!["earliest","latest","pending"].includes(e)}function _i(e){return e==null?e:Number.parseInt(e,16)}function Ov(e){if(e==null)return e;const t=_i(e);return lh(t+1)}function lh(e){if(e==null)return e;let t=e.toString(16);return t.length%2&&(t="0"+t),"0x"+t}function $v(e){let t="0x";for(let r=0;r<e;r++)t+=Vc(),t+=Vc();return t}function Vc(){return Math.floor(Math.random()*16).toString(16)}const Fv=Rv,Dv=Iv,jv=Tv,{bnToHex:sw,hexToInt:hi,incrementHexInt:Uv,minBlockRef:Hv,blockRefIsNumber:Wv}=Xn;let qv=class extends jv{constructor({provider:t,params:r}){super(),this.type="log",this.ethQuery=new Fv(t),this.params=Object.assign({fromBlock:"latest",toBlock:"latest",address:void 0,topics:[]},r),this.params.address&&(Array.isArray(this.params.address)||(this.params.address=[this.params.address]),this.params.address=this.params.address.map(n=>n.toLowerCase()))}async initialize({currentBlock:t}){let r=this.params.fromBlock;["latest","pending"].includes(r)&&(r=t),r==="earliest"&&(r="0x0"),this.params.fromBlock=r;const n=Hv(this.params.toBlock,t),i=Object.assign({},this.params,{toBlock:n}),s=await this._fetchLogs(i);this.addInitialResults(s)}async update({oldBlock:t,newBlock:r}){const n=r;let i;t?i=Uv(t):i=r;const s=Object.assign({},this.params,{fromBlock:i,toBlock:n}),o=(await this._fetchLogs(s)).filter(u=>this.matchLog(u));this.addResults(o)}async _fetchLogs(t){return await Dv(n=>this.ethQuery.getLogs(t,n))()}matchLog(t){if(hi(this.params.fromBlock)>=hi(t.blockNumber)||Wv(this.params.toBlock)&&hi(this.params.toBlock)<=hi(t.blockNumber))return!1;const r=t.address&&t.address.toLowerCase();return this.params.address&&r&&!this.params.address.includes(r)?!1:this.params.topics.every((i,s)=>{let c=t.topics[s];if(!c)return!1;c=c.toLowerCase();let o=Array.isArray(i)?i:[i];return o.includes(null)?!0:(o=o.map(g=>g.toLowerCase()),o.includes(c))})}};var Vv=qv,ua=zv;async function zv({provider:e,fromBlock:t,toBlock:r}){t||(t=r);const n=zc(t),s=zc(r)-n+1,c=Array(s).fill().map((u,p)=>n+p).map(Gv);let o=await Promise.all(c.map(u=>Zv(e,"eth_getBlockByNumber",[u,!1])));return o=o.filter(u=>u!==null),o}function zc(e){return e==null?e:Number.parseInt(e,16)}function Gv(e){return e==null?e:"0x"+e.toString(16)}function Jv(e,t){return new Promise((r,n)=>{e.sendAsync(t,(i,s)=>{i?n(i):s.error?n(s.error):s.result?r(s.result):n(new Error("Result was empty"))})})}async function Zv(e,t,r){for(let n=0;n<3;n++)try{return await Jv(e,{id:1,jsonrpc:"2.0",method:t,params:r})}catch(i){console.error(`provider.sendAsync failed: ${i.stack||i.message||i}`)}return null}const Kv=la,Yv=ua,{incrementHexInt:Qv}=Xn;let Xv=class extends Kv{constructor({provider:t,params:r}){super(),this.type="block",this.provider=t}async update({oldBlock:t,newBlock:r}){const n=r,i=Qv(t),c=(await Yv({provider:this.provider,fromBlock:i,toBlock:n})).map(o=>o.hash);this.addResults(c)}};var eb=Xv;const tb=la,rb=ua,{incrementHexInt:nb}=Xn;let ib=class extends tb{constructor({provider:t}){super(),this.type="tx",this.provider=t}async update({oldBlock:t}){const r=t,n=nb(t),i=await rb({provider:this.provider,fromBlock:n,toBlock:r}),s=[];for(const c of i)s.push(...c.transactions);this.addResults(s)}};var sb=ib;const ob=ch.Mutex,{createAsyncMiddleware:ab,createScaffoldMiddleware:cb}=na,lb=Vv,ub=eb,hb=sb,{intToHex:uh,hexToInt:yo}=Xn;var fb=db;function db({blockTracker:e,provider:t}){let r=0,n={};const i=new ob,s=pb({mutex:i}),c=cb({eth_newFilter:s(mo(u)),eth_newBlockFilter:s(mo(p)),eth_newPendingTransactionFilter:s(mo(g)),eth_uninstallFilter:s(yi(L)),eth_getFilterChanges:s(yi(w)),eth_getFilterLogs:s(yi(_))}),o=async({oldBlock:N,newBlock:C})=>{if(n.length===0)return;const W=await i.acquire();try{await Promise.all(Dr(n).map(async U=>{try{await U.update({oldBlock:N,newBlock:C})}catch(z){console.error(z)}}))}catch(U){console.error(U)}W()};return c.newLogFilter=u,c.newBlockFilter=p,c.newPendingTransactionFilter=g,c.uninstallFilter=L,c.getFilterChanges=w,c.getFilterLogs=_,c.destroy=()=>{O()},c;async function u(N){const C=new lb({provider:t,params:N});return await j(C),C}async function p(){const N=new ub({provider:t});return await j(N),N}async function g(){const N=new hb({provider:t});return await j(N),N}async function w(N){const C=yo(N),W=n[C];if(!W)throw new Error(`No filter for index "${C}"`);return W.getChangesAndClear()}async function _(N){const C=yo(N),W=n[C];if(!W)throw new Error(`No filter for index "${C}"`);let U=[];return W.type==="log"&&(U=W.getAllResults()),U}async function L(N){const C=yo(N),U=!!n[C];return U&&await $(C),U}async function j(N){const C=Dr(n).length,W=await e.getLatestBlock();await N.initialize({currentBlock:W}),r++,n[r]=N,N.id=r,N.idHex=uh(r);const U=Dr(n).length;return A({prevFilterCount:C,newFilterCount:U}),r}async function $(N){const C=Dr(n).length;delete n[N];const W=Dr(n).length;A({prevFilterCount:C,newFilterCount:W})}async function O(){const N=Dr(n).length;n={},A({prevFilterCount:N,newFilterCount:0})}function A({prevFilterCount:N,newFilterCount:C}){if(N===0&&C>0){e.on("sync",o);return}if(N>0&&C===0){e.removeListener("sync",o);return}}}function mo(e){return yi(async(...t)=>{const r=await e(...t);return uh(r.id)})}function yi(e){return ab(async(t,r)=>{const n=await e.apply(null,t.params);r.result=n})}function pb({mutex:e}){return t=>async(r,n,i,s)=>{(await e.acquire())(),t(r,n,i,s)}}function Dr(e,t){const r=[];for(let n in e)r.push(e[n]);return r}const gb=Wn.default,{createAsyncMiddleware:Gc,createScaffoldMiddleware:yb}=na,mb=fb,{unsafeRandomBytes:vb,incrementHexInt:bb}=Xn,wb=ua;var _b=Eb;function Eb({blockTracker:e,provider:t}){const r={},n=mb({blockTracker:e,provider:t});let i=!1;const s=new gb,c=yb({eth_subscribe:Gc(o),eth_unsubscribe:Gc(u)});return c.destroy=g,{events:s,middleware:c};async function o(w,_){if(i)throw new Error("SubscriptionManager - attempting to use after destroying");const L=w.params[0],j=vb(16);let $;switch(L){case"newHeads":$=O({subId:j});break;case"logs":const N=w.params[1],C=await n.newLogFilter(N);$=A({subId:j,filter:C});break;default:throw new Error(`SubscriptionManager - unsupported subscription type "${L}"`)}r[j]=$,_.result=j;return;function O({subId:N}){const C={type:L,destroy:async()=>{e.removeListener("sync",C.update)},update:async({oldBlock:W,newBlock:U})=>{const z=U,te=bb(W);(await wb({provider:t,fromBlock:te,toBlock:z})).map(Sb).filter(de=>de!==null).forEach(de=>{p(N,de)})}};return e.on("sync",C.update),C}function A({subId:N,filter:C}){return C.on("update",U=>p(N,U)),{type:L,destroy:async()=>await n.uninstallFilter(C.idHex)}}}async function u(w,_){if(i)throw new Error("SubscriptionManager - attempting to use after destroying");const L=w.params[0],j=r[L];if(!j){_.result=!1;return}delete r[L],await j.destroy(),_.result=!0}function p(w,_){s.emit("notification",{jsonrpc:"2.0",method:"eth_subscription",params:{subscription:w,result:_}})}function g(){s.removeAllListeners();for(const w in r)r[w].destroy(),delete r[w];i=!0}}function Sb(e){return e==null?null:{hash:e.hash,parentHash:e.parentHash,sha3Uncles:e.sha3Uncles,miner:e.miner,stateRoot:e.stateRoot,transactionsRoot:e.transactionsRoot,receiptsRoot:e.receiptsRoot,logsBloom:e.logsBloom,difficulty:e.difficulty,number:e.number,gasLimit:e.gasLimit,gasUsed:e.gasUsed,nonce:e.nonce,mixHash:e.mixHash,timestamp:e.timestamp,extraData:e.extraData}}Object.defineProperty(ts,"__esModule",{value:!0});ts.SubscriptionManager=void 0;const Rb=gu,Mb=_b,Jc=()=>{};class Ib{constructor(t){const r=new Rb.PollingBlockTracker({provider:t,pollingInterval:15e3,setSkipCacheFlag:!0}),{events:n,middleware:i}=Mb({blockTracker:r,provider:t});this.events=n,this.subscriptionMiddleware=i}async handleRequest(t){const r={};return await this.subscriptionMiddleware(t,r,Jc,Jc),r}destroy(){this.subscriptionMiddleware.destroy()}}ts.SubscriptionManager=Ib;var hh=ee&&ee.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Jr,"__esModule",{value:!0});Jr.CoinbaseWalletProvider=void 0;const Ab=hh(Mi),xb=Zh,me=En,fe=Q,kb=In,Zc=vt,fi=an,ut=nn,vo=hh(G1),gn=on,Cb=Xr,Tb=ts,Kc="DefaultChainId",Yc="DefaultJsonRpcUrl";class Bb extends xb.EventEmitter{constructor(t){var r,n;super(),this._filterPolyfill=new Cb.FilterPolyfill(this),this._subscriptionManager=new Tb.SubscriptionManager(this),this._relay=null,this._addresses=[],this.hasMadeFirstChainChangedEmission=!1,this.setProviderInfo=this.setProviderInfo.bind(this),this.updateProviderInfo=this.updateProviderInfo.bind(this),this.getChainId=this.getChainId.bind(this),this.setAppInfo=this.setAppInfo.bind(this),this.enable=this.enable.bind(this),this.close=this.close.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this.request=this.request.bind(this),this._setAddresses=this._setAddresses.bind(this),this.scanQRCode=this.scanQRCode.bind(this),this.genericRequest=this.genericRequest.bind(this),this._chainIdFromOpts=t.chainId,this._jsonRpcUrlFromOpts=t.jsonRpcUrl,this._overrideIsMetaMask=t.overrideIsMetaMask,this._relayProvider=t.relayProvider,this._storage=t.storage,this._relayEventManager=t.relayEventManager,this.diagnostic=t.diagnosticLogger,this.reloadOnDisconnect=!0,this.isCoinbaseWallet=(r=t.overrideIsCoinbaseWallet)!==null&&r!==void 0?r:!0,this.isCoinbaseBrowser=(n=t.overrideIsCoinbaseBrowser)!==null&&n!==void 0?n:!1,this.qrUrl=t.qrUrl;const i=this.getChainId(),s=(0,fe.prepend0x)(i.toString(16));this.emit("connect",{chainIdStr:s});const c=this._storage.getItem(Zc.LOCAL_STORAGE_ADDRESSES_KEY);if(c){const o=c.split(" ");o[0]!==""&&(this._addresses=o.map(u=>(0,fe.ensureAddressString)(u)),this.emit("accountsChanged",o))}this._subscriptionManager.events.on("notification",o=>{this.emit("message",{type:o.method,data:o.params})}),this._isAuthorized()&&this.initializeRelay(),window.addEventListener("message",o=>{var u;if(!(o.origin!==location.origin||o.source!==window)&&o.data.type==="walletLinkMessage"&&o.data.data.action==="dappChainSwitched"){const p=o.data.data.chainId,g=(u=o.data.data.jsonRpcUrl)!==null&&u!==void 0?u:this.jsonRpcUrl;this.updateProviderInfo(g,Number(p))}})}get selectedAddress(){return this._addresses[0]||void 0}get networkVersion(){return this.getChainId().toString(10)}get chainId(){return(0,fe.prepend0x)(this.getChainId().toString(16))}get isWalletLink(){return!0}get isMetaMask(){return this._overrideIsMetaMask}get host(){return this.jsonRpcUrl}get connected(){return!0}isConnected(){return!0}get jsonRpcUrl(){var t;return(t=this._storage.getItem(Yc))!==null&&t!==void 0?t:this._jsonRpcUrlFromOpts}set jsonRpcUrl(t){this._storage.setItem(Yc,t)}disableReloadOnDisconnect(){this.reloadOnDisconnect=!1}setProviderInfo(t,r){this.isCoinbaseBrowser||(this._chainIdFromOpts=r,this._jsonRpcUrlFromOpts=t),this.updateProviderInfo(this.jsonRpcUrl,this.getChainId())}updateProviderInfo(t,r){this.jsonRpcUrl=t;const n=this.getChainId();this._storage.setItem(Kc,r.toString(10)),((0,fe.ensureIntNumber)(r)!==n||!this.hasMadeFirstChainChangedEmission)&&(this.emit("chainChanged",this.getChainId()),this.hasMadeFirstChainChangedEmission=!0)}async watchAsset(t,r,n,i,s,c){const u=await(await this.initializeRelay()).watchAsset(t,r,n,i,s,c==null?void 0:c.toString()).promise;return(0,ut.isErrorResponse)(u)?!1:!!u.result}async addEthereumChain(t,r,n,i,s,c){var o,u;if((0,fe.ensureIntNumber)(t)===this.getChainId())return!1;const p=await this.initializeRelay(),g=p.inlineAddEthereumChain(t.toString());!this._isAuthorized()&&!g&&await p.requestEthereumAccounts().promise;const w=await p.addEthereumChain(t.toString(),r,s,n,i,c).promise;return(0,ut.isErrorResponse)(w)?!1:(((o=w.result)===null||o===void 0?void 0:o.isApproved)===!0&&this.updateProviderInfo(r[0],t),((u=w.result)===null||u===void 0?void 0:u.isApproved)===!0)}async switchEthereumChain(t){const n=await(await this.initializeRelay()).switchEthereumChain(t.toString(10),this.selectedAddress||void 0).promise;if((0,ut.isErrorResponse)(n)){if(!n.errorCode)return;throw n.errorCode===me.standardErrorCodes.provider.unsupportedChain?me.standardErrors.provider.unsupportedChain():me.standardErrors.provider.custom({message:n.errorMessage,code:n.errorCode})}const i=n.result;i.isApproved&&i.rpcUrl.length>0&&this.updateProviderInfo(i.rpcUrl,t)}setAppInfo(t,r){this.initializeRelay().then(n=>n.setAppInfo(t,r))}async enable(){var t;return(t=this.diagnostic)===null||t===void 0||t.log(gn.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::enable",addresses_length:this._addresses.length,sessionIdHash:this._relay?fi.Session.hash(this._relay.session.id):void 0}),this._isAuthorized()?[...this._addresses]:await this.send("eth_requestAccounts")}async close(){(await this.initializeRelay()).resetAndReload()}send(t,r){try{const n=this._send(t,r);if(n instanceof Promise)return n.catch(i=>{throw(0,me.serializeError)(i,t)})}catch(n){throw(0,me.serializeError)(n,t)}}_send(t,r){if(typeof t=="string"){const i=t,s=Array.isArray(r)?r:r!==void 0?[r]:[],c={jsonrpc:"2.0",id:0,method:i,params:s};return this._sendRequestAsync(c).then(o=>o.result)}if(typeof r=="function"){const i=t,s=r;return this._sendAsync(i,s)}if(Array.isArray(t))return t.map(s=>this._sendRequest(s));const n=t;return this._sendRequest(n)}async sendAsync(t,r){try{return this._sendAsync(t,r).catch(n=>{throw(0,me.serializeError)(n,t)})}catch(n){return Promise.reject((0,me.serializeError)(n,t))}}async _sendAsync(t,r){if(typeof r!="function")throw new Error("callback is required");if(Array.isArray(t)){const i=r;this._sendMultipleRequestsAsync(t).then(s=>i(null,s)).catch(s=>i(s,null));return}const n=r;return this._sendRequestAsync(t).then(i=>n(null,i)).catch(i=>n(i,null))}async request(t){try{return this._request(t).catch(r=>{throw(0,me.serializeError)(r,t.method)})}catch(r){return Promise.reject((0,me.serializeError)(r,t.method))}}async _request(t){if(!t||typeof t!="object"||Array.isArray(t))throw me.standardErrors.rpc.invalidRequest({message:"Expected a single, non-array, object argument.",data:t});const{method:r,params:n}=t;if(typeof r!="string"||r.length===0)throw me.standardErrors.rpc.invalidRequest({message:"'args.method' must be a non-empty string.",data:t});if(n!==void 0&&!Array.isArray(n)&&(typeof n!="object"||n===null))throw me.standardErrors.rpc.invalidRequest({message:"'args.params' must be an object or array if provided.",data:t});const i=n===void 0?[]:n,s=this._relayEventManager.makeRequestId();return(await this._sendRequestAsync({method:r,params:i,jsonrpc:"2.0",id:s})).result}async scanQRCode(t){const n=await(await this.initializeRelay()).scanQRCode((0,fe.ensureRegExpString)(t)).promise;if((0,ut.isErrorResponse)(n))throw(0,me.serializeError)(n.errorMessage,"scanQRCode");if(typeof n.result!="string")throw(0,me.serializeError)("result was not a string","scanQRCode");return n.result}async genericRequest(t,r){const i=await(await this.initializeRelay()).genericRequest(t,r).promise;if((0,ut.isErrorResponse)(i))throw(0,me.serializeError)(i.errorMessage,"generic");if(typeof i.result!="string")throw(0,me.serializeError)("result was not a string","generic");return i.result}async connectAndSignIn(t){var r;(r=this.diagnostic)===null||r===void 0||r.log(gn.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::connectAndSignIn",sessionIdHash:this._relay?fi.Session.hash(this._relay.session.id):void 0});let n;try{const s=await this.initializeRelay();if(!(s instanceof kb.MobileRelay))throw new Error("connectAndSignIn is only supported on mobile");if(n=await s.connectAndSignIn(t).promise,(0,ut.isErrorResponse)(n))throw new Error(n.errorMessage)}catch(s){throw typeof s.message=="string"&&s.message.match(/(denied|rejected)/i)?me.standardErrors.provider.userRejectedRequest("User denied account authorization"):s}if(!n.result)throw new Error("accounts received is empty");const{accounts:i}=n.result;return this._setAddresses(i),this.isCoinbaseBrowser||await this.switchEthereumChain(this.getChainId()),n.result}async selectProvider(t){const n=await(await this.initializeRelay()).selectProvider(t).promise;if((0,ut.isErrorResponse)(n))throw(0,me.serializeError)(n.errorMessage,"selectProvider");if(typeof n.result!="string")throw(0,me.serializeError)("result was not a string","selectProvider");return n.result}supportsSubscriptions(){return!1}subscribe(){throw new Error("Subscriptions are not supported")}unsubscribe(){throw new Error("Subscriptions are not supported")}disconnect(){return!0}_sendRequest(t){const r={jsonrpc:"2.0",id:t.id},{method:n}=t;if(r.result=this._handleSynchronousMethods(t),r.result===void 0)throw new Error(`Coinbase Wallet does not support calling ${n} synchronously without a callback. Please provide a callback parameter to call ${n} asynchronously.`);return r}_setAddresses(t,r){if(!Array.isArray(t))throw new Error("addresses is not an array");const n=t.map(i=>(0,fe.ensureAddressString)(i));JSON.stringify(n)!==JSON.stringify(this._addresses)&&(this._addresses=n,this.emit("accountsChanged",this._addresses),this._storage.setItem(Zc.LOCAL_STORAGE_ADDRESSES_KEY,n.join(" ")))}_sendRequestAsync(t){return new Promise((r,n)=>{try{const i=this._handleSynchronousMethods(t);if(i!==void 0)return r({jsonrpc:"2.0",id:t.id,result:i});const s=this._handleAsynchronousFilterMethods(t);if(s!==void 0){s.then(o=>r(Object.assign(Object.assign({},o),{id:t.id}))).catch(o=>n(o));return}const c=this._handleSubscriptionMethods(t);if(c!==void 0){c.then(o=>r({jsonrpc:"2.0",id:t.id,result:o.result})).catch(o=>n(o));return}}catch(i){return n(i)}this._handleAsynchronousMethods(t).then(i=>i&&r(Object.assign(Object.assign({},i),{id:t.id}))).catch(i=>n(i))})}_sendMultipleRequestsAsync(t){return Promise.all(t.map(r=>this._sendRequestAsync(r)))}_handleSynchronousMethods(t){const{method:r}=t,n=t.params||[];switch(r){case"eth_accounts":return this._eth_accounts();case"eth_coinbase":return this._eth_coinbase();case"eth_uninstallFilter":return this._eth_uninstallFilter(n);case"net_version":return this._net_version();case"eth_chainId":return this._eth_chainId();default:return}}async _handleAsynchronousMethods(t){const{method:r}=t,n=t.params||[];switch(r){case"eth_requestAccounts":return this._eth_requestAccounts();case"eth_sign":return this._eth_sign(n);case"eth_ecRecover":return this._eth_ecRecover(n);case"personal_sign":return this._personal_sign(n);case"personal_ecRecover":return this._personal_ecRecover(n);case"eth_signTransaction":return this._eth_signTransaction(n);case"eth_sendRawTransaction":return this._eth_sendRawTransaction(n);case"eth_sendTransaction":return this._eth_sendTransaction(n);case"eth_signTypedData_v1":return this._eth_signTypedData_v1(n);case"eth_signTypedData_v2":return this._throwUnsupportedMethodError();case"eth_signTypedData_v3":return this._eth_signTypedData_v3(n);case"eth_signTypedData_v4":case"eth_signTypedData":return this._eth_signTypedData_v4(n);case"cbWallet_arbitrary":return this._cbwallet_arbitrary(n);case"wallet_addEthereumChain":return this._wallet_addEthereumChain(n);case"wallet_switchEthereumChain":return this._wallet_switchEthereumChain(n);case"wallet_watchAsset":return this._wallet_watchAsset(n)}return(await this.initializeRelay()).makeEthereumJSONRPCRequest(t,this.jsonRpcUrl).catch(s=>{var c;throw(s.code===me.standardErrorCodes.rpc.methodNotFound||s.code===me.standardErrorCodes.rpc.methodNotSupported)&&((c=this.diagnostic)===null||c===void 0||c.log(gn.EVENTS.METHOD_NOT_IMPLEMENTED,{method:t.method,sessionIdHash:this._relay?fi.Session.hash(this._relay.session.id):void 0})),s})}_handleAsynchronousFilterMethods(t){const{method:r}=t,n=t.params||[];switch(r){case"eth_newFilter":return this._eth_newFilter(n);case"eth_newBlockFilter":return this._eth_newBlockFilter();case"eth_newPendingTransactionFilter":return this._eth_newPendingTransactionFilter();case"eth_getFilterChanges":return this._eth_getFilterChanges(n);case"eth_getFilterLogs":return this._eth_getFilterLogs(n)}}_handleSubscriptionMethods(t){switch(t.method){case"eth_subscribe":case"eth_unsubscribe":return this._subscriptionManager.handleRequest(t)}}_isKnownAddress(t){try{const r=(0,fe.ensureAddressString)(t);return this._addresses.map(i=>(0,fe.ensureAddressString)(i)).includes(r)}catch{}return!1}_ensureKnownAddress(t){var r;if(!this._isKnownAddress(t))throw(r=this.diagnostic)===null||r===void 0||r.log(gn.EVENTS.UNKNOWN_ADDRESS_ENCOUNTERED),new Error("Unknown Ethereum address")}_prepareTransactionParams(t){const r=t.from?(0,fe.ensureAddressString)(t.from):this.selectedAddress;if(!r)throw new Error("Ethereum address is unavailable");this._ensureKnownAddress(r);const n=t.to?(0,fe.ensureAddressString)(t.to):null,i=t.value!=null?(0,fe.ensureBN)(t.value):new Ab.default(0),s=t.data?(0,fe.ensureBuffer)(t.data):Buffer.alloc(0),c=t.nonce!=null?(0,fe.ensureIntNumber)(t.nonce):null,o=t.gasPrice!=null?(0,fe.ensureBN)(t.gasPrice):null,u=t.maxFeePerGas!=null?(0,fe.ensureBN)(t.maxFeePerGas):null,p=t.maxPriorityFeePerGas!=null?(0,fe.ensureBN)(t.maxPriorityFeePerGas):null,g=t.gas!=null?(0,fe.ensureBN)(t.gas):null,w=t.chainId?(0,fe.ensureIntNumber)(t.chainId):this.getChainId();return{fromAddress:r,toAddress:n,weiValue:i,data:s,nonce:c,gasPriceInWei:o,maxFeePerGas:u,maxPriorityFeePerGas:p,gasLimit:g,chainId:w}}_isAuthorized(){return this._addresses.length>0}_requireAuthorization(){if(!this._isAuthorized())throw me.standardErrors.provider.unauthorized({})}_throwUnsupportedMethodError(){throw me.standardErrors.provider.unsupportedMethod({})}async _signEthereumMessage(t,r,n,i){this._ensureKnownAddress(r);try{const c=await(await this.initializeRelay()).signEthereumMessage(t,r,n,i).promise;if((0,ut.isErrorResponse)(c))throw new Error(c.errorMessage);return{jsonrpc:"2.0",id:0,result:c.result}}catch(s){throw typeof s.message=="string"&&s.message.match(/(denied|rejected)/i)?me.standardErrors.provider.userRejectedRequest("User denied message signature"):s}}async _ethereumAddressFromSignedMessage(t,r,n){const s=await(await this.initializeRelay()).ethereumAddressFromSignedMessage(t,r,n).promise;if((0,ut.isErrorResponse)(s))throw new Error(s.errorMessage);return{jsonrpc:"2.0",id:0,result:s.result}}_eth_accounts(){return[...this._addresses]}_eth_coinbase(){return this.selectedAddress||null}_net_version(){return this.getChainId().toString(10)}_eth_chainId(){return(0,fe.hexStringFromIntNumber)(this.getChainId())}getChainId(){const t=this._storage.getItem(Kc);if(!t)return(0,fe.ensureIntNumber)(this._chainIdFromOpts);const r=parseInt(t,10);return(0,fe.ensureIntNumber)(r)}async _eth_requestAccounts(){var t;if((t=this.diagnostic)===null||t===void 0||t.log(gn.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::_eth_requestAccounts",addresses_length:this._addresses.length,sessionIdHash:this._relay?fi.Session.hash(this._relay.session.id):void 0}),this._isAuthorized())return Promise.resolve({jsonrpc:"2.0",id:0,result:this._addresses});let r;try{if(r=await(await this.initializeRelay()).requestEthereumAccounts().promise,(0,ut.isErrorResponse)(r))throw new Error(r.errorMessage)}catch(n){throw typeof n.message=="string"&&n.message.match(/(denied|rejected)/i)?me.standardErrors.provider.userRejectedRequest("User denied account authorization"):n}if(!r.result)throw new Error("accounts received is empty");return this._setAddresses(r.result),this.isCoinbaseBrowser||await this.switchEthereumChain(this.getChainId()),{jsonrpc:"2.0",id:0,result:this._addresses}}_eth_sign(t){this._requireAuthorization();const r=(0,fe.ensureAddressString)(t[0]),n=(0,fe.ensureBuffer)(t[1]);return this._signEthereumMessage(n,r,!1)}_eth_ecRecover(t){const r=(0,fe.ensureBuffer)(t[0]),n=(0,fe.ensureBuffer)(t[1]);return this._ethereumAddressFromSignedMessage(r,n,!1)}_personal_sign(t){this._requireAuthorization();const r=(0,fe.ensureBuffer)(t[0]),n=(0,fe.ensureAddressString)(t[1]);return this._signEthereumMessage(r,n,!0)}_personal_ecRecover(t){const r=(0,fe.ensureBuffer)(t[0]),n=(0,fe.ensureBuffer)(t[1]);return this._ethereumAddressFromSignedMessage(r,n,!0)}async _eth_signTransaction(t){this._requireAuthorization();const r=this._prepareTransactionParams(t[0]||{});try{const i=await(await this.initializeRelay()).signEthereumTransaction(r).promise;if((0,ut.isErrorResponse)(i))throw new Error(i.errorMessage);return{jsonrpc:"2.0",id:0,result:i.result}}catch(n){throw typeof n.message=="string"&&n.message.match(/(denied|rejected)/i)?me.standardErrors.provider.userRejectedRequest("User denied transaction signature"):n}}async _eth_sendRawTransaction(t){const r=(0,fe.ensureBuffer)(t[0]),i=await(await this.initializeRelay()).submitEthereumTransaction(r,this.getChainId()).promise;if((0,ut.isErrorResponse)(i))throw new Error(i.errorMessage);return{jsonrpc:"2.0",id:0,result:i.result}}async _eth_sendTransaction(t){this._requireAuthorization();const r=this._prepareTransactionParams(t[0]||{});try{const i=await(await this.initializeRelay()).signAndSubmitEthereumTransaction(r).promise;if((0,ut.isErrorResponse)(i))throw new Error(i.errorMessage);return{jsonrpc:"2.0",id:0,result:i.result}}catch(n){throw typeof n.message=="string"&&n.message.match(/(denied|rejected)/i)?me.standardErrors.provider.userRejectedRequest("User denied transaction signature"):n}}async _eth_signTypedData_v1(t){this._requireAuthorization();const r=(0,fe.ensureParsedJSONObject)(t[0]),n=(0,fe.ensureAddressString)(t[1]);this._ensureKnownAddress(n);const i=vo.default.hashForSignTypedDataLegacy({data:r}),s=JSON.stringify(r,null,2);return this._signEthereumMessage(i,n,!1,s)}async _eth_signTypedData_v3(t){this._requireAuthorization();const r=(0,fe.ensureAddressString)(t[0]),n=(0,fe.ensureParsedJSONObject)(t[1]);this._ensureKnownAddress(r);const i=vo.default.hashForSignTypedData_v3({data:n}),s=JSON.stringify(n,null,2);return this._signEthereumMessage(i,r,!1,s)}async _eth_signTypedData_v4(t){this._requireAuthorization();const r=(0,fe.ensureAddressString)(t[0]),n=(0,fe.ensureParsedJSONObject)(t[1]);this._ensureKnownAddress(r);const i=vo.default.hashForSignTypedData_v4({data:n}),s=JSON.stringify(n,null,2);return this._signEthereumMessage(i,r,!1,s)}async _cbwallet_arbitrary(t){const r=t[0],n=t[1];if(typeof n!="string")throw new Error("parameter must be a string");if(typeof r!="object"||r===null)throw new Error("parameter must be an object");return{jsonrpc:"2.0",id:0,result:await this.genericRequest(r,n)}}async _wallet_addEthereumChain(t){var r,n,i,s;const c=t[0];if(((r=c.rpcUrls)===null||r===void 0?void 0:r.length)===0)return{jsonrpc:"2.0",id:0,error:{code:2,message:"please pass in at least 1 rpcUrl"}};if(!c.chainName||c.chainName.trim()==="")throw me.standardErrors.rpc.invalidParams("chainName is a required field");if(!c.nativeCurrency)throw me.standardErrors.rpc.invalidParams("nativeCurrency is a required field");const o=parseInt(c.chainId,16);return await this.addEthereumChain(o,(n=c.rpcUrls)!==null&&n!==void 0?n:[],(i=c.blockExplorerUrls)!==null&&i!==void 0?i:[],c.chainName,(s=c.iconUrls)!==null&&s!==void 0?s:[],c.nativeCurrency)?{jsonrpc:"2.0",id:0,result:null}:{jsonrpc:"2.0",id:0,error:{code:2,message:"unable to add ethereum chain"}}}async _wallet_switchEthereumChain(t){const r=t[0];return await this.switchEthereumChain(parseInt(r.chainId,16)),{jsonrpc:"2.0",id:0,result:null}}async _wallet_watchAsset(t){const r=Array.isArray(t)?t[0]:t;if(!r.type)throw me.standardErrors.rpc.invalidParams("Type is required");if((r==null?void 0:r.type)!=="ERC20")throw me.standardErrors.rpc.invalidParams(`Asset of type '${r.type}' is not supported`);if(!(r!=null&&r.options))throw me.standardErrors.rpc.invalidParams("Options are required");if(!(r!=null&&r.options.address))throw me.standardErrors.rpc.invalidParams("Address is required");const n=this.getChainId(),{address:i,symbol:s,image:c,decimals:o}=r.options;return{jsonrpc:"2.0",id:0,result:await this.watchAsset(r.type,i,s,o,c,n)}}_eth_uninstallFilter(t){const r=(0,fe.ensureHexString)(t[0]);return this._filterPolyfill.uninstallFilter(r)}async _eth_newFilter(t){const r=t[0];return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newFilter(r)}}async _eth_newBlockFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newBlockFilter()}}async _eth_newPendingTransactionFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newPendingTransactionFilter()}}_eth_getFilterChanges(t){const r=(0,fe.ensureHexString)(t[0]);return this._filterPolyfill.getFilterChanges(r)}_eth_getFilterLogs(t){const r=(0,fe.ensureHexString)(t[0]);return this._filterPolyfill.getFilterLogs(r)}initializeRelay(){return this._relay?Promise.resolve(this._relay):this._relayProvider().then(t=>(t.setAccountsCallback((r,n)=>this._setAddresses(r,n)),t.setChainCallback((r,n)=>{this.updateProviderInfo(n,parseInt(r,10))}),t.setDappDefaultChainCallback(this._chainIdFromOpts),this._relay=t,t))}}Jr.CoinbaseWalletProvider=Bb;var ms={};Object.defineProperty(ms,"__esModule",{value:!0});ms.RelayEventManager=void 0;const Nb=Q;class Pb{constructor(){this._nextRequestId=0,this.callbacks=new Map}makeRequestId(){this._nextRequestId=(this._nextRequestId+1)%**********;const t=this._nextRequestId,r=(0,Nb.prepend0x)(t.toString(16));return this.callbacks.get(r)&&this.callbacks.delete(r),t}}ms.RelayEventManager=Pb;Object.defineProperty(mn,"__esModule",{value:!0});mn.CoinbaseWalletSDK=void 0;const Lb=Si,Ob=Ri,Qc=Q,$b=Ci,Fb=Jr,Db=In,jb=Un,Ub=ms,Hb=Fn,Wb=An,fh=sn;class vs{constructor(t){var r,n,i;this._appName="",this._appLogoUrl=null,this._relay=null,this._relayEventManager=null;const s=t.linkAPIUrl||Ob.LINK_API_URL;typeof t.overrideIsMetaMask>"u"?this._overrideIsMetaMask=!1:this._overrideIsMetaMask=t.overrideIsMetaMask,this._overrideIsCoinbaseWallet=(r=t.overrideIsCoinbaseWallet)!==null&&r!==void 0?r:!0,this._overrideIsCoinbaseBrowser=(n=t.overrideIsCoinbaseBrowser)!==null&&n!==void 0?n:!1,this._diagnosticLogger=t.diagnosticLogger,this._reloadOnDisconnect=(i=t.reloadOnDisconnect)!==null&&i!==void 0?i:!0;const c=new URL(s),o=`${c.protocol}//${c.host}`;if(this._storage=new $b.ScopedLocalStorage(`-walletlink:${o}`),this._storage.setItem("version",vs.VERSION),this.walletExtension||this.coinbaseBrowser)return;this._relayEventManager=new Ub.RelayEventManager;const u=(0,Qc.isMobileWeb)(),p=t.uiConstructor||(w=>u?new jb.MobileRelayUI(w):new Hb.WalletLinkRelayUI(w)),g={linkAPIUrl:s,version:fh.LIB_VERSION,darkMode:!!t.darkMode,headlessMode:!!t.headlessMode,uiConstructor:p,storage:this._storage,relayEventManager:this._relayEventManager,diagnosticLogger:this._diagnosticLogger,reloadOnDisconnect:this._reloadOnDisconnect,enableMobileWalletLink:t.enableMobileWalletLink};this._relay=u?new Db.MobileRelay(g):new Wb.WalletLinkRelay(g),this.setAppInfo(t.appName,t.appLogoUrl),!t.headlessMode&&this._relay.attachUI()}makeWeb3Provider(t="",r=1){const n=this.walletExtension;if(n)return this.isCipherProvider(n)||n.setProviderInfo(t,r),this._reloadOnDisconnect===!1&&typeof n.disableReloadOnDisconnect=="function"&&n.disableReloadOnDisconnect(),n;const i=this.coinbaseBrowser;if(i)return i;const s=this._relay;if(!s||!this._relayEventManager||!this._storage)throw new Error("Relay not initialized, should never happen");return t||s.setConnectDisabled(!0),new Fb.CoinbaseWalletProvider({relayProvider:()=>Promise.resolve(s),relayEventManager:this._relayEventManager,storage:this._storage,jsonRpcUrl:t,chainId:r,qrUrl:this.getQrUrl(),diagnosticLogger:this._diagnosticLogger,overrideIsMetaMask:this._overrideIsMetaMask,overrideIsCoinbaseWallet:this._overrideIsCoinbaseWallet,overrideIsCoinbaseBrowser:this._overrideIsCoinbaseBrowser})}setAppInfo(t,r){var n;this._appName=t||"DApp",this._appLogoUrl=r||(0,Qc.getFavicon)();const i=this.walletExtension;i?this.isCipherProvider(i)||i.setAppInfo(this._appName,this._appLogoUrl):(n=this._relay)===null||n===void 0||n.setAppInfo(this._appName,this._appLogoUrl)}disconnect(){var t;const r=this===null||this===void 0?void 0:this.walletExtension;r?r.close():(t=this._relay)===null||t===void 0||t.resetAndReload()}getQrUrl(){var t,r;return(r=(t=this._relay)===null||t===void 0?void 0:t.getQRCodeUrl())!==null&&r!==void 0?r:null}getCoinbaseWalletLogo(t,r=240){return(0,Lb.walletLogo)(t,r)}get walletExtension(){var t;return(t=window.coinbaseWalletExtension)!==null&&t!==void 0?t:window.walletLinkExtension}get coinbaseBrowser(){var t,r;try{const n=(t=window.ethereum)!==null&&t!==void 0?t:(r=window.top)===null||r===void 0?void 0:r.ethereum;return n&&"isCoinbaseBrowser"in n&&n.isCoinbaseBrowser?n:void 0}catch{return}}isCipherProvider(t){return typeof t.isCipher=="boolean"&&t.isCipher}}mn.CoinbaseWalletSDK=vs;vs.VERSION=fh.LIB_VERSION;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.CoinbaseWalletProvider=e.CoinbaseWalletSDK=void 0;const t=mn,r=Jr;var n=mn;Object.defineProperty(e,"CoinbaseWalletSDK",{enumerable:!0,get:function(){return n.CoinbaseWalletSDK}});var i=Jr;Object.defineProperty(e,"CoinbaseWalletProvider",{enumerable:!0,get:function(){return i.CoinbaseWalletProvider}}),e.default=t.CoinbaseWalletSDK,typeof window<"u"&&(window.CoinbaseWalletSDK=t.CoinbaseWalletSDK,window.CoinbaseWalletProvider=r.CoinbaseWalletProvider,window.WalletLink=t.CoinbaseWalletSDK,window.WalletLinkProvider=r.CoinbaseWalletProvider)})(Xc);const qb=ph(Xc),lw=Object.freeze(Object.defineProperty({__proto__:null,default:qb},Symbol.toStringTag,{value:"Module"}));export{lw as i};
//# sourceMappingURL=index-CesIlIzc.js.map
