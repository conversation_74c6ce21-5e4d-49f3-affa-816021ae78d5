{"version": 3, "file": "warning-circle-Dn7tmkxe.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const warningCircleSvg = svg `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    d=\"M11 6.67a1 1 0 1 0-2 0v2.66a1 1 0 0 0 2 0V6.67ZM10 14.5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z\"\n  />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M10 1a9 9 0 1 0 0 18 9 9 0 0 0 0-18Zm-7 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=warning-circle.js.map"], "names": ["warningCircleSvg", "svg"], "mappings": "2JACO,MAAMA,EAAmBC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}