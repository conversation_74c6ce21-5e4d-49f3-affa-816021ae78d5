import{k,n as U,l as P,m as E,x as c,y as L,D as _,O as Y,G as W,E as J,R as A,P as Nt,q as M,Q as T,U as fe,I as Hi,W as ge,C as ze,u as si,T as Ne,B as Qt,M as vi,V as yi,b as Pt,L as li,o as Ki}from"./core-C0olQNtY.js";import{n as u,c as C,o as y,r as v,U as st,e as Gi,f as Yi,a as Ji}from"./index-6jImYbgp.js";import"./index-2wea5Wgv.js";import"./events-B2jzgt6q.js";import"./index.es-CAssemqx.js";import"./index-nibyPLVP.js";const Qi=k`
  :host {
    position: relative;
    background-color: var(--wui-color-gray-glass-002);
    display: flex;
    justify-content: center;
    align-items: center;
    width: var(--local-size);
    height: var(--local-size);
    border-radius: inherit;
    border-radius: var(--local-border-radius);
  }

  :host > wui-flex {
    overflow: hidden;
    border-radius: inherit;
    border-radius: var(--local-border-radius);
  }

  :host::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: inherit;
    border: 1px solid var(--wui-color-gray-glass-010);
    pointer-events: none;
  }

  :host([name='Extension'])::after {
    border: 1px solid var(--wui-color-accent-glass-010);
  }

  :host([data-wallet-icon='allWallets']) {
    background-color: var(--wui-all-wallets-bg-100);
  }

  :host([data-wallet-icon='allWallets'])::after {
    border: 1px solid var(--wui-color-accent-glass-010);
  }

  wui-icon[data-parent-size='inherit'] {
    width: 75%;
    height: 75%;
    align-items: center;
  }

  wui-icon[data-parent-size='sm'] {
    width: 18px;
    height: 18px;
  }

  wui-icon[data-parent-size='md'] {
    width: 24px;
    height: 24px;
  }

  wui-icon[data-parent-size='lg'] {
    width: 42px;
    height: 42px;
  }

  wui-icon[data-parent-size='full'] {
    width: 100%;
    height: 100%;
  }

  :host > wui-icon-box {
    position: absolute;
    overflow: hidden;
    right: -1px;
    bottom: -2px;
    z-index: 1;
    border: 2px solid var(--wui-color-bg-150, #1e1f1f);
    padding: 1px;
  }
`;var xt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let lt=class extends E{constructor(){super(...arguments),this.size="md",this.name="",this.installed=!1,this.badgeSize="xs"}render(){let t="xxs";return this.size==="lg"?t="m":this.size==="md"?t="xs":t="xxs",this.style.cssText=`
       --local-border-radius: var(--wui-border-radius-${t});
       --local-size: var(--wui-wallet-image-size-${this.size});
   `,this.walletIcon&&(this.dataset.walletIcon=this.walletIcon),c`
      <wui-flex justifyContent="center" alignItems="center"> ${this.templateVisual()} </wui-flex>
    `}templateVisual(){return this.imageSrc?c`<wui-image src=${this.imageSrc} alt=${this.name}></wui-image>`:this.walletIcon?c`<wui-icon
        data-parent-size="md"
        size="md"
        color="inherit"
        name=${this.walletIcon}
      ></wui-icon>`:c`<wui-icon
      data-parent-size=${this.size}
      size="inherit"
      color="inherit"
      name="walletPlaceholder"
    ></wui-icon>`}};lt.styles=[U,P,Qi];xt([u()],lt.prototype,"size",void 0);xt([u()],lt.prototype,"name",void 0);xt([u()],lt.prototype,"imageSrc",void 0);xt([u()],lt.prototype,"walletIcon",void 0);xt([u({type:Boolean})],lt.prototype,"installed",void 0);xt([u()],lt.prototype,"badgeSize",void 0);lt=xt([C("wui-wallet-image")],lt);const Xi=k`
  :host {
    position: relative;
    border-radius: var(--wui-border-radius-xxs);
    width: 40px;
    height: 40px;
    overflow: hidden;
    background: var(--wui-color-gray-glass-002);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--wui-spacing-4xs);
    padding: 3.75px !important;
  }

  :host::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: inherit;
    border: 1px solid var(--wui-color-gray-glass-010);
    pointer-events: none;
  }

  :host > wui-wallet-image {
    width: 14px;
    height: 14px;
    border-radius: var(--wui-border-radius-5xs);
  }

  :host > wui-flex {
    padding: 2px;
    position: fixed;
    overflow: hidden;
    left: 34px;
    bottom: 8px;
    background: var(--dark-background-150, #1e1f1f);
    border-radius: 50%;
    z-index: 2;
    display: flex;
  }
`;var xi=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};const Se=4;let Xt=class extends E{constructor(){super(...arguments),this.walletImages=[]}render(){const t=this.walletImages.length<Se;return c`${this.walletImages.slice(0,Se).map(({src:i,walletName:r})=>c`
            <wui-wallet-image
              size="inherit"
              imageSrc=${i}
              name=${y(r)}
            ></wui-wallet-image>
          `)}
      ${t?[...Array(Se-this.walletImages.length)].map(()=>c` <wui-wallet-image size="inherit" name=""></wui-wallet-image>`):null}
      <wui-flex>
        <wui-icon-box
          size="xxs"
          iconSize="xxs"
          iconcolor="success-100"
          backgroundcolor="success-100"
          icon="checkmark"
          background="opaque"
        ></wui-icon-box>
      </wui-flex>`}};Xt.styles=[P,Xi];xi([u({type:Array})],Xt.prototype,"walletImages",void 0);Xt=xi([C("wui-all-wallets-image")],Xt);const Zi=k`
  button {
    column-gap: var(--wui-spacing-s);
    padding: 7px var(--wui-spacing-l) 7px var(--wui-spacing-xs);
    width: 100%;
    background-color: var(--wui-color-gray-glass-002);
    border-radius: var(--wui-border-radius-xs);
    color: var(--wui-color-fg-100);
  }

  button > wui-text:nth-child(2) {
    display: flex;
    flex: 1;
  }

  button:disabled {
    background-color: var(--wui-color-gray-glass-015);
    color: var(--wui-color-gray-glass-015);
  }

  button:disabled > wui-tag {
    background-color: var(--wui-color-gray-glass-010);
    color: var(--wui-color-fg-300);
  }

  wui-icon {
    color: var(--wui-color-fg-200) !important;
  }
`;var D=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let N=class extends E{constructor(){super(...arguments),this.walletImages=[],this.imageSrc="",this.name="",this.tabIdx=void 0,this.installed=!1,this.disabled=!1,this.showAllWallets=!1,this.loading=!1,this.loadingSpinnerColor="accent-100"}render(){return c`
      <button ?disabled=${this.disabled} tabindex=${y(this.tabIdx)}>
        ${this.templateAllWallets()} ${this.templateWalletImage()}
        <wui-text variant="paragraph-500" color="inherit">${this.name}</wui-text>
        ${this.templateStatus()}
      </button>
    `}templateAllWallets(){return this.showAllWallets&&this.imageSrc?c` <wui-all-wallets-image .imageeSrc=${this.imageSrc}> </wui-all-wallets-image> `:this.showAllWallets&&this.walletIcon?c` <wui-wallet-image .walletIcon=${this.walletIcon} size="sm"> </wui-wallet-image> `:null}templateWalletImage(){return!this.showAllWallets&&this.imageSrc?c`<wui-wallet-image
        size="sm"
        imageSrc=${this.imageSrc}
        name=${this.name}
        .installed=${this.installed}
      ></wui-wallet-image>`:!this.showAllWallets&&!this.imageSrc?c`<wui-wallet-image size="sm" name=${this.name}></wui-wallet-image>`:null}templateStatus(){return this.loading?c`<wui-loading-spinner
        size="lg"
        color=${this.loadingSpinnerColor}
      ></wui-loading-spinner>`:this.tagLabel&&this.tagVariant?c`<wui-tag variant=${this.tagVariant}>${this.tagLabel}</wui-tag>`:this.icon?c`<wui-icon color="inherit" size="sm" name=${this.icon}></wui-icon>`:null}};N.styles=[P,U,Zi];D([u({type:Array})],N.prototype,"walletImages",void 0);D([u()],N.prototype,"imageSrc",void 0);D([u()],N.prototype,"name",void 0);D([u()],N.prototype,"tagLabel",void 0);D([u()],N.prototype,"tagVariant",void 0);D([u()],N.prototype,"icon",void 0);D([u()],N.prototype,"walletIcon",void 0);D([u()],N.prototype,"tabIdx",void 0);D([u({type:Boolean})],N.prototype,"installed",void 0);D([u({type:Boolean})],N.prototype,"disabled",void 0);D([u({type:Boolean})],N.prototype,"showAllWallets",void 0);D([u({type:Boolean})],N.prototype,"loading",void 0);D([u({type:String})],N.prototype,"loadingSpinnerColor",void 0);N=D([C("wui-list-wallet")],N);var kt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let wt=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.count=_.state.count,this.filteredCount=_.state.filteredWallets.length,this.isFetchingRecommendedWallets=_.state.isFetchingRecommendedWallets,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t),_.subscribeKey("count",t=>this.count=t),_.subscribeKey("filteredWallets",t=>this.filteredCount=t.length),_.subscribeKey("isFetchingRecommendedWallets",t=>this.isFetchingRecommendedWallets=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){const t=this.connectors.find(l=>l.id==="walletConnect"),{allWallets:i}=Y.state;if(!t||i==="HIDE"||i==="ONLY_MOBILE"&&!W.isMobile())return null;const r=_.state.featured.length,n=this.count+r,e=n<10?n:Math.floor(n/10)*10,a=this.filteredCount>0?this.filteredCount:e;let s=`${a}`;return this.filteredCount>0?s=`${this.filteredCount}`:a<n&&(s=`${a}+`),c`
      <wui-list-wallet
        name="All Wallets"
        walletIcon="allWallets"
        showAllWallets
        @click=${this.onAllWallets.bind(this)}
        tagLabel=${s}
        tagVariant="shade"
        data-testid="all-wallets"
        tabIdx=${y(this.tabIdx)}
        .loading=${this.isFetchingRecommendedWallets}
        loadingSpinnerColor=${this.isFetchingRecommendedWallets?"fg-300":"accent-100"}
      ></wui-list-wallet>
    `}onAllWallets(){J.sendEvent({type:"track",event:"CLICK_ALL_WALLETS"}),A.push("AllWallets")}};kt([u()],wt.prototype,"tabIdx",void 0);kt([v()],wt.prototype,"connectors",void 0);kt([v()],wt.prototype,"count",void 0);kt([v()],wt.prototype,"filteredCount",void 0);kt([v()],wt.prototype,"isFetchingRecommendedWallets",void 0);wt=kt([C("w3m-all-wallets-widget")],wt);var Ke=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Zt=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){const t=this.connectors.filter(i=>i.type==="ANNOUNCED");return t!=null&&t.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${t.filter(Nt.showConnector).map(i=>c`
              <wui-list-wallet
                imageSrc=${y(M.getConnectorImage(i))}
                name=${i.name??"Unknown"}
                @click=${()=>this.onConnector(i)}
                tagVariant="success"
                tagLabel="installed"
                data-testid=${`wallet-selector-${i.id}`}
                .installed=${!0}
                tabIdx=${y(this.tabIdx)}
              >
              </wui-list-wallet>
            `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnector(t){t.id==="walletConnect"?W.isMobile()?A.push("AllWallets"):A.push("ConnectingWalletConnect"):A.push("ConnectingExternal",{connector:t})}};Ke([u()],Zt.prototype,"tabIdx",void 0);Ke([v()],Zt.prototype,"connectors",void 0);Zt=Ke([C("w3m-connect-announced-widget")],Zt);var we=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let jt=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.loading=!1,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t)),W.isTelegram()&&W.isIos()&&(this.loading=!T.state.wcUri,this.unsubscribe.push(T.subscribeKey("wcUri",t=>this.loading=!t)))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){const{customWallets:t}=Y.state;if(!(t!=null&&t.length))return this.style.cssText="display: none",null;const i=this.filterOutDuplicateWallets(t);return c`<wui-flex flexDirection="column" gap="xs">
      ${i.map(r=>c`
          <wui-list-wallet
            imageSrc=${y(M.getWalletImage(r))}
            name=${r.name??"Unknown"}
            @click=${()=>this.onConnectWallet(r)}
            data-testid=${`wallet-selector-${r.id}`}
            tabIdx=${y(this.tabIdx)}
            ?loading=${this.loading}
          >
          </wui-list-wallet>
        `)}
    </wui-flex>`}filterOutDuplicateWallets(t){const i=fe.getRecentWallets(),r=this.connectors.map(s=>{var l;return(l=s.info)==null?void 0:l.rdns}).filter(Boolean),n=i.map(s=>s.rdns).filter(Boolean),e=r.concat(n);if(e.includes("io.metamask.mobile")&&W.isMobile()){const s=e.indexOf("io.metamask.mobile");e[s]="io.metamask"}return t.filter(s=>!e.includes(String(s==null?void 0:s.rdns)))}onConnectWallet(t){this.loading||A.push("ConnectingWalletConnect",{wallet:t})}};we([u()],jt.prototype,"tabIdx",void 0);we([v()],jt.prototype,"connectors",void 0);we([v()],jt.prototype,"loading",void 0);jt=we([C("w3m-connect-custom-widget")],jt);var Ge=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let te=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){const r=this.connectors.filter(n=>n.type==="EXTERNAL").filter(Nt.showConnector).filter(n=>n.id!==Hi.CONNECTOR_ID.COINBASE_SDK);return r!=null&&r.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${r.map(n=>c`
            <wui-list-wallet
              imageSrc=${y(M.getConnectorImage(n))}
              .installed=${!0}
              name=${n.name??"Unknown"}
              data-testid=${`wallet-selector-external-${n.id}`}
              @click=${()=>this.onConnector(n)}
              tabIdx=${y(this.tabIdx)}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnector(t){A.push("ConnectingExternal",{connector:t})}};Ge([u()],te.prototype,"tabIdx",void 0);Ge([v()],te.prototype,"connectors",void 0);te=Ge([C("w3m-connect-external-widget")],te);var Ye=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ee=class extends E{constructor(){super(...arguments),this.tabIdx=void 0,this.wallets=[]}render(){return this.wallets.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${this.wallets.map(t=>c`
            <wui-list-wallet
              data-testid=${`wallet-selector-featured-${t.id}`}
              imageSrc=${y(M.getWalletImage(t))}
              name=${t.name??"Unknown"}
              @click=${()=>this.onConnectWallet(t)}
              tabIdx=${y(this.tabIdx)}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnectWallet(t){L.selectWalletConnector(t)}};Ye([u()],ee.prototype,"tabIdx",void 0);Ye([u()],ee.prototype,"wallets",void 0);ee=Ye([C("w3m-connect-featured-widget")],ee);var Je=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ie=class extends E{constructor(){super(...arguments),this.tabIdx=void 0,this.connectors=[]}render(){const t=this.connectors.filter(Nt.showConnector);return t.length===0?(this.style.cssText="display: none",null):c`
      <wui-flex flexDirection="column" gap="xs">
        ${t.map(i=>c`
            <wui-list-wallet
              imageSrc=${y(M.getConnectorImage(i))}
              .installed=${!0}
              name=${i.name??"Unknown"}
              tagVariant="success"
              tagLabel="installed"
              data-testid=${`wallet-selector-${i.id}`}
              @click=${()=>this.onConnector(i)}
              tabIdx=${y(this.tabIdx)}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `}onConnector(t){L.setActiveConnector(t),A.push("ConnectingExternal",{connector:t})}};Je([u()],ie.prototype,"tabIdx",void 0);Je([u()],ie.prototype,"connectors",void 0);ie=Je([C("w3m-connect-injected-widget")],ie);var Qe=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ne=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){const t=this.connectors.filter(i=>i.type==="MULTI_CHAIN"&&i.name!=="WalletConnect");return t!=null&&t.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${t.map(i=>c`
            <wui-list-wallet
              imageSrc=${y(M.getConnectorImage(i))}
              .installed=${!0}
              name=${i.name??"Unknown"}
              tagVariant="shade"
              tagLabel="multichain"
              data-testid=${`wallet-selector-${i.id}`}
              @click=${()=>this.onConnector(i)}
              tabIdx=${y(this.tabIdx)}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnector(t){L.setActiveConnector(t),A.push("ConnectingMultiChain")}};Qe([u()],ne.prototype,"tabIdx",void 0);Qe([v()],ne.prototype,"connectors",void 0);ne=Qe([C("w3m-connect-multi-chain-widget")],ne);var me=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Mt=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.loading=!1,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t)),W.isTelegram()&&W.isIos()&&(this.loading=!T.state.wcUri,this.unsubscribe.push(T.subscribeKey("wcUri",t=>this.loading=!t)))}render(){const i=fe.getRecentWallets().filter(r=>!ge.isExcluded(r)).filter(r=>!this.hasWalletConnector(r)).filter(r=>this.isWalletCompatibleWithCurrentChain(r));return i.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${i.map(r=>c`
            <wui-list-wallet
              imageSrc=${y(M.getWalletImage(r))}
              name=${r.name??"Unknown"}
              @click=${()=>this.onConnectWallet(r)}
              tagLabel="recent"
              tagVariant="shade"
              tabIdx=${y(this.tabIdx)}
              ?loading=${this.loading}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnectWallet(t){this.loading||L.selectWalletConnector(t)}hasWalletConnector(t){return this.connectors.some(i=>i.id===t.id||i.name===t.name)}isWalletCompatibleWithCurrentChain(t){const i=ze.state.activeChain;return i&&t.chains?t.chains.some(r=>{const n=r.split(":")[0];return i===n}):!0}};me([u()],Mt.prototype,"tabIdx",void 0);me([v()],Mt.prototype,"connectors",void 0);me([v()],Mt.prototype,"loading",void 0);Mt=me([C("w3m-connect-recent-widget")],Mt);var be=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Ot=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.wallets=[],this.loading=!1,W.isTelegram()&&W.isIos()&&(this.loading=!T.state.wcUri,this.unsubscribe.push(T.subscribeKey("wcUri",t=>this.loading=!t)))}render(){const{connectors:t}=L.state,{customWallets:i,featuredWalletIds:r}=Y.state,n=fe.getRecentWallets(),e=t.find(I=>I.id==="walletConnect"),s=t.filter(I=>I.type==="INJECTED"||I.type==="ANNOUNCED"||I.type==="MULTI_CHAIN").filter(I=>I.name!=="Browser Wallet");if(!e)return null;if(r||i||!this.wallets.length)return this.style.cssText="display: none",null;const l=s.length+n.length,h=Math.max(0,2-l),d=ge.filterOutDuplicateWallets(this.wallets).slice(0,h);return d.length?c`
      <wui-flex flexDirection="column" gap="xs">
        ${d.map(I=>c`
            <wui-list-wallet
              imageSrc=${y(M.getWalletImage(I))}
              name=${(I==null?void 0:I.name)??"Unknown"}
              @click=${()=>this.onConnectWallet(I)}
              tabIdx=${y(this.tabIdx)}
              ?loading=${this.loading}
            >
            </wui-list-wallet>
          `)}
      </wui-flex>
    `:(this.style.cssText="display: none",null)}onConnectWallet(t){if(this.loading)return;const i=L.getConnector(t.id,t.rdns);i?A.push("ConnectingExternal",{connector:i}):A.push("ConnectingWalletConnect",{wallet:t})}};be([u()],Ot.prototype,"tabIdx",void 0);be([u()],Ot.prototype,"wallets",void 0);be([v()],Ot.prototype,"loading",void 0);Ot=be([C("w3m-connect-recommended-widget")],Ot);var ve=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Ut=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.connectorImages=si.state.connectorImages,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t),si.subscribeKey("connectorImages",t=>this.connectorImages=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){if(W.isMobile())return this.style.cssText="display: none",null;const t=this.connectors.find(r=>r.id==="walletConnect");if(!t)return this.style.cssText="display: none",null;const i=t.imageUrl||this.connectorImages[(t==null?void 0:t.imageId)??""];return c`
      <wui-list-wallet
        imageSrc=${y(i)}
        name=${t.name??"Unknown"}
        @click=${()=>this.onConnector(t)}
        tagLabel="qr code"
        tagVariant="main"
        tabIdx=${y(this.tabIdx)}
        data-testid="wallet-selector-walletconnect"
      >
      </wui-list-wallet>
    `}onConnector(t){L.setActiveConnector(t),A.push("ConnectingWalletConnect")}};ve([u()],Ut.prototype,"tabIdx",void 0);ve([v()],Ut.prototype,"connectors",void 0);ve([v()],Ut.prototype,"connectorImages",void 0);Ut=ve([C("w3m-connect-walletconnect-widget")],Ut);const tn=k`
  :host {
    margin-top: var(--wui-spacing-3xs);
  }
  wui-separator {
    margin: var(--wui-spacing-m) calc(var(--wui-spacing-m) * -1) var(--wui-spacing-xs)
      calc(var(--wui-spacing-m) * -1);
    width: calc(100% + var(--wui-spacing-s) * 2);
  }
`;var Ft=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let mt=class extends E{constructor(){super(),this.unsubscribe=[],this.tabIdx=void 0,this.connectors=L.state.connectors,this.recommended=_.state.recommended,this.featured=_.state.featured,this.unsubscribe.push(L.subscribeKey("connectors",t=>this.connectors=t),_.subscribeKey("recommended",t=>this.recommended=t),_.subscribeKey("featured",t=>this.featured=t))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){return c`
      <wui-flex flexDirection="column" gap="xs"> ${this.connectorListTemplate()} </wui-flex>
    `}connectorListTemplate(){const{custom:t,recent:i,announced:r,injected:n,multiChain:e,recommended:a,featured:s,external:l}=Nt.getConnectorsByType(this.connectors,this.recommended,this.featured);return Nt.getConnectorTypeOrder({custom:t,recent:i,announced:r,injected:n,multiChain:e,recommended:a,featured:s,external:l}).map(d=>{switch(d){case"injected":return c`
            ${e.length?c`<w3m-connect-multi-chain-widget
                  tabIdx=${y(this.tabIdx)}
                ></w3m-connect-multi-chain-widget>`:null}
            ${r.length?c`<w3m-connect-announced-widget
                  tabIdx=${y(this.tabIdx)}
                ></w3m-connect-announced-widget>`:null}
            ${n.length?c`<w3m-connect-injected-widget
                  .connectors=${n}
                  tabIdx=${y(this.tabIdx)}
                ></w3m-connect-injected-widget>`:null}
          `;case"walletConnect":return c`<w3m-connect-walletconnect-widget
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-walletconnect-widget>`;case"recent":return c`<w3m-connect-recent-widget
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-recent-widget>`;case"featured":return c`<w3m-connect-featured-widget
            .wallets=${s}
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-featured-widget>`;case"custom":return c`<w3m-connect-custom-widget
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-custom-widget>`;case"external":return c`<w3m-connect-external-widget
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-external-widget>`;case"recommended":return c`<w3m-connect-recommended-widget
            .wallets=${a}
            tabIdx=${y(this.tabIdx)}
          ></w3m-connect-recommended-widget>`;default:return console.warn(`Unknown connector type: ${d}`),null}})}};mt.styles=tn;Ft([u()],mt.prototype,"tabIdx",void 0);Ft([v()],mt.prototype,"connectors",void 0);Ft([v()],mt.prototype,"recommended",void 0);Ft([v()],mt.prototype,"featured",void 0);mt=Ft([C("w3m-connector-list")],mt);const en=k`
  :host {
    display: inline-flex;
    background-color: var(--wui-color-gray-glass-002);
    border-radius: var(--wui-border-radius-3xl);
    padding: var(--wui-spacing-3xs);
    position: relative;
    height: 36px;
    min-height: 36px;
    overflow: hidden;
  }

  :host::before {
    content: '';
    position: absolute;
    pointer-events: none;
    top: 4px;
    left: 4px;
    display: block;
    width: var(--local-tab-width);
    height: 28px;
    border-radius: var(--wui-border-radius-3xl);
    background-color: var(--wui-color-gray-glass-002);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);
    transform: translateX(calc(var(--local-tab) * var(--local-tab-width)));
    transition: transform var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: background-color, opacity;
  }

  :host([data-type='flex'])::before {
    left: 3px;
    transform: translateX(calc((var(--local-tab) * 34px) + (var(--local-tab) * 4px)));
  }

  :host([data-type='flex']) {
    display: flex;
    padding: 0px 0px 0px 12px;
    gap: 4px;
  }

  :host([data-type='flex']) > button > wui-text {
    position: absolute;
    left: 18px;
    opacity: 0;
  }

  button[data-active='true'] > wui-icon,
  button[data-active='true'] > wui-text {
    color: var(--wui-color-fg-100);
  }

  button[data-active='false'] > wui-icon,
  button[data-active='false'] > wui-text {
    color: var(--wui-color-fg-200);
  }

  button[data-active='true']:disabled,
  button[data-active='false']:disabled {
    background-color: transparent;
    opacity: 0.5;
    cursor: not-allowed;
  }

  button[data-active='true']:disabled > wui-text {
    color: var(--wui-color-fg-200);
  }

  button[data-active='false']:disabled > wui-text {
    color: var(--wui-color-fg-300);
  }

  button > wui-icon,
  button > wui-text {
    pointer-events: none;
    transition: color var(--wui-e ase-out-power-1) var(--wui-duration-md);
    will-change: color;
  }

  button {
    width: var(--local-tab-width);
    transition: background-color var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: background-color;
  }

  :host([data-type='flex']) > button {
    width: 34px;
    position: relative;
    display: flex;
    justify-content: flex-start;
  }

  button:hover:enabled,
  button:active:enabled {
    background-color: transparent !important;
  }

  button:hover:enabled > wui-icon,
  button:active:enabled > wui-icon {
    transition: all var(--wui-ease-out-power-1) var(--wui-duration-lg);
    color: var(--wui-color-fg-125);
  }

  button:hover:enabled > wui-text,
  button:active:enabled > wui-text {
    transition: all var(--wui-ease-out-power-1) var(--wui-duration-lg);
    color: var(--wui-color-fg-125);
  }

  button {
    border-radius: var(--wui-border-radius-3xl);
  }
`;var ft=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let tt=class extends E{constructor(){super(...arguments),this.tabs=[],this.onTabChange=()=>null,this.buttons=[],this.disabled=!1,this.localTabWidth="100px",this.activeTab=0,this.isDense=!1}render(){return this.isDense=this.tabs.length>3,this.style.cssText=`
      --local-tab: ${this.activeTab};
      --local-tab-width: ${this.localTabWidth};
    `,this.dataset.type=this.isDense?"flex":"block",this.tabs.map((t,i)=>{var n;const r=i===this.activeTab;return c`
        <button
          ?disabled=${this.disabled}
          @click=${()=>this.onTabClick(i)}
          data-active=${r}
          data-testid="tab-${(n=t.label)==null?void 0:n.toLowerCase()}"
        >
          ${this.iconTemplate(t)}
          <wui-text variant="small-600" color="inherit"> ${t.label} </wui-text>
        </button>
      `})}firstUpdated(){this.shadowRoot&&this.isDense&&(this.buttons=[...this.shadowRoot.querySelectorAll("button")],setTimeout(()=>{this.animateTabs(0,!0)},0))}iconTemplate(t){return t.icon?c`<wui-icon size="xs" color="inherit" name=${t.icon}></wui-icon>`:null}onTabClick(t){this.buttons&&this.animateTabs(t,!1),this.activeTab=t,this.onTabChange(t)}animateTabs(t,i){const r=this.buttons[this.activeTab],n=this.buttons[t],e=r==null?void 0:r.querySelector("wui-text"),a=n==null?void 0:n.querySelector("wui-text"),s=n==null?void 0:n.getBoundingClientRect(),l=a==null?void 0:a.getBoundingClientRect();r&&e&&!i&&t!==this.activeTab&&(e.animate([{opacity:0}],{duration:50,easing:"ease",fill:"forwards"}),r.animate([{width:"34px"}],{duration:500,easing:"ease",fill:"forwards"})),n&&s&&l&&a&&(t!==this.activeTab||i)&&(this.localTabWidth=`${Math.round(s.width+l.width)+6}px`,n.animate([{width:`${s.width+l.width}px`}],{duration:i?0:500,fill:"forwards",easing:"ease"}),a.animate([{opacity:1}],{duration:i?0:125,delay:i?0:200,fill:"forwards",easing:"ease"}))}};tt.styles=[P,U,en];ft([u({type:Array})],tt.prototype,"tabs",void 0);ft([u()],tt.prototype,"onTabChange",void 0);ft([u({type:Array})],tt.prototype,"buttons",void 0);ft([u({type:Boolean})],tt.prototype,"disabled",void 0);ft([u()],tt.prototype,"localTabWidth",void 0);ft([v()],tt.prototype,"activeTab",void 0);ft([v()],tt.prototype,"isDense",void 0);tt=ft([C("wui-tabs")],tt);var Xe=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let oe=class extends E{constructor(){super(...arguments),this.platformTabs=[],this.unsubscribe=[],this.platforms=[],this.onSelectPlatfrom=void 0}disconnectCallback(){this.unsubscribe.forEach(t=>t())}render(){const t=this.generateTabs();return c`
      <wui-flex justifyContent="center" .padding=${["0","0","l","0"]}>
        <wui-tabs .tabs=${t} .onTabChange=${this.onTabChange.bind(this)}></wui-tabs>
      </wui-flex>
    `}generateTabs(){const t=this.platforms.map(i=>i==="browser"?{label:"Browser",icon:"extension",platform:"browser"}:i==="mobile"?{label:"Mobile",icon:"mobile",platform:"mobile"}:i==="qrcode"?{label:"Mobile",icon:"mobile",platform:"qrcode"}:i==="web"?{label:"Webapp",icon:"browser",platform:"web"}:i==="desktop"?{label:"Desktop",icon:"desktop",platform:"desktop"}:{label:"Browser",icon:"extension",platform:"unsupported"});return this.platformTabs=t.map(({platform:i})=>i),t}onTabChange(t){var r;const i=this.platformTabs[t];i&&((r=this.onSelectPlatfrom)==null||r.call(this,i))}};Xe([u({type:Array})],oe.prototype,"platforms",void 0);Xe([u()],oe.prototype,"onSelectPlatfrom",void 0);oe=Xe([C("w3m-connecting-header")],oe);const nn=k`
  :host {
    width: var(--local-width);
    position: relative;
  }

  button {
    border: none;
    border-radius: var(--local-border-radius);
    width: var(--local-width);
    white-space: nowrap;
  }

  /* -- Sizes --------------------------------------------------- */
  button[data-size='md'] {
    padding: 8.2px var(--wui-spacing-l) 9px var(--wui-spacing-l);
    height: 36px;
  }

  button[data-size='md'][data-icon-left='true'][data-icon-right='false'] {
    padding: 8.2px var(--wui-spacing-l) 9px var(--wui-spacing-s);
  }

  button[data-size='md'][data-icon-right='true'][data-icon-left='false'] {
    padding: 8.2px var(--wui-spacing-s) 9px var(--wui-spacing-l);
  }

  button[data-size='lg'] {
    padding: var(--wui-spacing-m) var(--wui-spacing-2l);
    height: 48px;
  }

  /* -- Variants --------------------------------------------------------- */
  button[data-variant='main'] {
    background-color: var(--wui-color-accent-100);
    color: var(--wui-color-inverse-100);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  button[data-variant='inverse'] {
    background-color: var(--wui-color-inverse-100);
    color: var(--wui-color-inverse-000);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  button[data-variant='accent'] {
    background-color: var(--wui-color-accent-glass-010);
    color: var(--wui-color-accent-100);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);
  }

  button[data-variant='accent-error'] {
    background: var(--wui-color-error-glass-015);
    color: var(--wui-color-error-100);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-error-glass-010);
  }

  button[data-variant='accent-success'] {
    background: var(--wui-color-success-glass-015);
    color: var(--wui-color-success-100);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-success-glass-010);
  }

  button[data-variant='neutral'] {
    background: transparent;
    color: var(--wui-color-fg-100);
    border: none;
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);
  }

  /* -- Focus states --------------------------------------------------- */
  button[data-variant='main']:focus-visible:enabled {
    background-color: var(--wui-color-accent-090);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-accent-100),
      0 0 0 4px var(--wui-color-accent-glass-020);
  }
  button[data-variant='inverse']:focus-visible:enabled {
    background-color: var(--wui-color-inverse-100);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-gray-glass-010),
      0 0 0 4px var(--wui-color-accent-glass-020);
  }
  button[data-variant='accent']:focus-visible:enabled {
    background-color: var(--wui-color-accent-glass-010);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-accent-100),
      0 0 0 4px var(--wui-color-accent-glass-020);
  }
  button[data-variant='accent-error']:focus-visible:enabled {
    background: var(--wui-color-error-glass-015);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-error-100),
      0 0 0 4px var(--wui-color-error-glass-020);
  }
  button[data-variant='accent-success']:focus-visible:enabled {
    background: var(--wui-color-success-glass-015);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-success-100),
      0 0 0 4px var(--wui-color-success-glass-020);
  }
  button[data-variant='neutral']:focus-visible:enabled {
    background: var(--wui-color-gray-glass-005);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-gray-glass-010),
      0 0 0 4px var(--wui-color-gray-glass-002);
  }

  /* -- Hover & Active states ----------------------------------------------------------- */
  @media (hover: hover) and (pointer: fine) {
    button[data-variant='main']:hover:enabled {
      background-color: var(--wui-color-accent-090);
    }

    button[data-variant='main']:active:enabled {
      background-color: var(--wui-color-accent-080);
    }

    button[data-variant='accent']:hover:enabled {
      background-color: var(--wui-color-accent-glass-015);
    }

    button[data-variant='accent']:active:enabled {
      background-color: var(--wui-color-accent-glass-020);
    }

    button[data-variant='accent-error']:hover:enabled {
      background: var(--wui-color-error-glass-020);
      color: var(--wui-color-error-100);
    }

    button[data-variant='accent-error']:active:enabled {
      background: var(--wui-color-error-glass-030);
      color: var(--wui-color-error-100);
    }

    button[data-variant='accent-success']:hover:enabled {
      background: var(--wui-color-success-glass-020);
      color: var(--wui-color-success-100);
    }

    button[data-variant='accent-success']:active:enabled {
      background: var(--wui-color-success-glass-030);
      color: var(--wui-color-success-100);
    }

    button[data-variant='neutral']:hover:enabled {
      background: var(--wui-color-gray-glass-002);
    }

    button[data-variant='neutral']:active:enabled {
      background: var(--wui-color-gray-glass-005);
    }

    button[data-size='lg'][data-icon-left='true'][data-icon-right='false'] {
      padding-left: var(--wui-spacing-m);
    }

    button[data-size='lg'][data-icon-right='true'][data-icon-left='false'] {
      padding-right: var(--wui-spacing-m);
    }
  }

  /* -- Disabled state --------------------------------------------------- */
  button:disabled {
    background-color: var(--wui-color-gray-glass-002);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);
    color: var(--wui-color-gray-glass-020);
    cursor: not-allowed;
  }

  button > wui-text {
    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: opacity;
    opacity: var(--local-opacity-100);
  }

  ::slotted(*) {
    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: opacity;
    opacity: var(--local-opacity-100);
  }

  wui-loading-spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: var(--local-opacity-000);
  }
`;var et=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};const ci={main:"inverse-100",inverse:"inverse-000",accent:"accent-100","accent-error":"error-100","accent-success":"success-100",neutral:"fg-100",disabled:"gray-glass-020"},on={lg:"paragraph-600",md:"small-600"},rn={lg:"md",md:"md"};let q=class extends E{constructor(){super(...arguments),this.size="lg",this.disabled=!1,this.fullWidth=!1,this.loading=!1,this.variant="main",this.hasIconLeft=!1,this.hasIconRight=!1,this.borderRadius="m"}render(){this.style.cssText=`
    --local-width: ${this.fullWidth?"100%":"auto"};
    --local-opacity-100: ${this.loading?0:1};
    --local-opacity-000: ${this.loading?1:0};
    --local-border-radius: var(--wui-border-radius-${this.borderRadius});
    `;const t=this.textVariant??on[this.size];return c`
      <button
        data-variant=${this.variant}
        data-icon-left=${this.hasIconLeft}
        data-icon-right=${this.hasIconRight}
        data-size=${this.size}
        ?disabled=${this.disabled}
      >
        ${this.loadingTemplate()}
        <slot name="iconLeft" @slotchange=${()=>this.handleSlotLeftChange()}></slot>
        <wui-text variant=${t} color="inherit">
          <slot></slot>
        </wui-text>
        <slot name="iconRight" @slotchange=${()=>this.handleSlotRightChange()}></slot>
      </button>
    `}handleSlotLeftChange(){this.hasIconLeft=!0}handleSlotRightChange(){this.hasIconRight=!0}loadingTemplate(){if(this.loading){const t=rn[this.size],i=this.disabled?ci.disabled:ci[this.variant];return c`<wui-loading-spinner color=${i} size=${t}></wui-loading-spinner>`}return c``}};q.styles=[P,U,nn];et([u()],q.prototype,"size",void 0);et([u({type:Boolean})],q.prototype,"disabled",void 0);et([u({type:Boolean})],q.prototype,"fullWidth",void 0);et([u({type:Boolean})],q.prototype,"loading",void 0);et([u()],q.prototype,"variant",void 0);et([u({type:Boolean})],q.prototype,"hasIconLeft",void 0);et([u({type:Boolean})],q.prototype,"hasIconRight",void 0);et([u()],q.prototype,"borderRadius",void 0);et([u()],q.prototype,"textVariant",void 0);q=et([C("wui-button")],q);const an=k`
  button {
    padding: var(--wui-spacing-4xs) var(--wui-spacing-xxs);
    border-radius: var(--wui-border-radius-3xs);
    background-color: transparent;
    color: var(--wui-color-accent-100);
  }

  button:disabled {
    background-color: transparent;
    color: var(--wui-color-gray-glass-015);
  }

  button:hover {
    background-color: var(--wui-color-gray-glass-005);
  }
`;var ye=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let $t=class extends E{constructor(){super(...arguments),this.tabIdx=void 0,this.disabled=!1,this.color="inherit"}render(){return c`
      <button ?disabled=${this.disabled} tabindex=${y(this.tabIdx)}>
        <slot name="iconLeft"></slot>
        <wui-text variant="small-600" color=${this.color}>
          <slot></slot>
        </wui-text>
        <slot name="iconRight"></slot>
      </button>
    `}};$t.styles=[P,U,an];ye([u()],$t.prototype,"tabIdx",void 0);ye([u({type:Boolean})],$t.prototype,"disabled",void 0);ye([u()],$t.prototype,"color",void 0);$t=ye([C("wui-link")],$t);const sn=k`
  :host {
    display: block;
    width: var(--wui-box-size-md);
    height: var(--wui-box-size-md);
  }

  svg {
    width: var(--wui-box-size-md);
    height: var(--wui-box-size-md);
  }

  rect {
    fill: none;
    stroke: var(--wui-color-accent-100);
    stroke-width: 4px;
    stroke-linecap: round;
    animation: dash 1s linear infinite;
  }

  @keyframes dash {
    to {
      stroke-dashoffset: 0px;
    }
  }
`;var Ci=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let re=class extends E{constructor(){super(...arguments),this.radius=36}render(){return this.svgLoaderTemplate()}svgLoaderTemplate(){const t=this.radius>50?50:this.radius,r=36-t,n=116+r,e=245+r,a=360+r*1.75;return c`
      <svg viewBox="0 0 110 110" width="110" height="110">
        <rect
          x="2"
          y="2"
          width="106"
          height="106"
          rx=${t}
          stroke-dasharray="${n} ${e}"
          stroke-dashoffset=${a}
        />
      </svg>
    `}};re.styles=[P,sn];Ci([u({type:Number})],re.prototype,"radius",void 0);re=Ci([C("wui-loading-thumbnail")],re);const ln=k`
  button {
    border: none;
    border-radius: var(--wui-border-radius-3xl);
  }

  button[data-variant='main'] {
    background-color: var(--wui-color-accent-100);
    color: var(--wui-color-inverse-100);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  button[data-variant='accent'] {
    background-color: var(--wui-color-accent-glass-010);
    color: var(--wui-color-accent-100);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);
  }

  button[data-variant='gray'] {
    background-color: transparent;
    color: var(--wui-color-fg-200);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  button[data-variant='shade'] {
    background-color: transparent;
    color: var(--wui-color-accent-100);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  button[data-size='sm'] {
    height: 32px;
    padding: 0 var(--wui-spacing-s);
  }

  button[data-size='md'] {
    height: 40px;
    padding: 0 var(--wui-spacing-l);
  }

  button[data-size='sm'] > wui-image {
    width: 16px;
    height: 16px;
  }

  button[data-size='md'] > wui-image {
    width: 24px;
    height: 24px;
  }

  button[data-size='sm'] > wui-icon {
    width: 12px;
    height: 12px;
  }

  button[data-size='md'] > wui-icon {
    width: 14px;
    height: 14px;
  }

  wui-image {
    border-radius: var(--wui-border-radius-3xl);
    overflow: hidden;
  }

  button.disabled > wui-icon,
  button.disabled > wui-image {
    filter: grayscale(1);
  }

  button[data-variant='main'] > wui-image {
    box-shadow: inset 0 0 0 1px var(--wui-color-accent-090);
  }

  button[data-variant='shade'] > wui-image,
  button[data-variant='gray'] > wui-image {
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);
  }

  @media (hover: hover) and (pointer: fine) {
    button[data-variant='main']:focus-visible {
      background-color: var(--wui-color-accent-090);
    }

    button[data-variant='main']:hover:enabled {
      background-color: var(--wui-color-accent-090);
    }

    button[data-variant='main']:active:enabled {
      background-color: var(--wui-color-accent-080);
    }

    button[data-variant='accent']:hover:enabled {
      background-color: var(--wui-color-accent-glass-015);
    }

    button[data-variant='accent']:active:enabled {
      background-color: var(--wui-color-accent-glass-020);
    }

    button[data-variant='shade']:focus-visible,
    button[data-variant='gray']:focus-visible,
    button[data-variant='shade']:hover,
    button[data-variant='gray']:hover {
      background-color: var(--wui-color-gray-glass-002);
    }

    button[data-variant='gray']:active,
    button[data-variant='shade']:active {
      background-color: var(--wui-color-gray-glass-005);
    }
  }

  button.disabled {
    color: var(--wui-color-gray-glass-020);
    background-color: var(--wui-color-gray-glass-002);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);
    pointer-events: none;
  }
`;var Ct=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ct=class extends E{constructor(){super(...arguments),this.variant="accent",this.imageSrc="",this.disabled=!1,this.icon="externalLink",this.size="md",this.text=""}render(){const t=this.size==="sm"?"small-600":"paragraph-600";return c`
      <button
        class=${this.disabled?"disabled":""}
        data-variant=${this.variant}
        data-size=${this.size}
      >
        ${this.imageSrc?c`<wui-image src=${this.imageSrc}></wui-image>`:null}
        <wui-text variant=${t} color="inherit"> ${this.text} </wui-text>
        <wui-icon name=${this.icon} color="inherit" size="inherit"></wui-icon>
      </button>
    `}};ct.styles=[P,U,ln];Ct([u()],ct.prototype,"variant",void 0);Ct([u()],ct.prototype,"imageSrc",void 0);Ct([u({type:Boolean})],ct.prototype,"disabled",void 0);Ct([u()],ct.prototype,"icon",void 0);Ct([u()],ct.prototype,"size",void 0);Ct([u()],ct.prototype,"text",void 0);ct=Ct([C("wui-chip-button")],ct);const cn=k`
  wui-flex {
    width: 100%;
    background-color: var(--wui-color-gray-glass-002);
    border-radius: var(--wui-border-radius-xs);
  }
`;var xe=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Rt=class extends E{constructor(){super(...arguments),this.disabled=!1,this.label="",this.buttonLabel=""}render(){return c`
      <wui-flex
        justifyContent="space-between"
        alignItems="center"
        .padding=${["1xs","2l","1xs","2l"]}
      >
        <wui-text variant="paragraph-500" color="fg-200">${this.label}</wui-text>
        <wui-chip-button size="sm" variant="shade" text=${this.buttonLabel} icon="chevronRight">
        </wui-chip-button>
      </wui-flex>
    `}};Rt.styles=[P,U,cn];xe([u({type:Boolean})],Rt.prototype,"disabled",void 0);xe([u()],Rt.prototype,"label",void 0);xe([u()],Rt.prototype,"buttonLabel",void 0);Rt=xe([C("wui-cta-button")],Rt);const un=k`
  :host {
    display: block;
    padding: 0 var(--wui-spacing-xl) var(--wui-spacing-xl);
  }
`;var $i=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ae=class extends E{constructor(){super(...arguments),this.wallet=void 0}render(){if(!this.wallet)return this.style.display="none",null;const{name:t,app_store:i,play_store:r,chrome_store:n,homepage:e}=this.wallet,a=W.isMobile(),s=W.isIos(),l=W.isAndroid(),h=[i,r,e,n].filter(Boolean).length>1,d=st.getTruncateString({string:t,charsStart:12,charsEnd:0,truncate:"end"});return h&&!a?c`
        <wui-cta-button
          label=${`Don't have ${d}?`}
          buttonLabel="Get"
          @click=${()=>A.push("Downloads",{wallet:this.wallet})}
        ></wui-cta-button>
      `:!h&&e?c`
        <wui-cta-button
          label=${`Don't have ${d}?`}
          buttonLabel="Get"
          @click=${this.onHomePage.bind(this)}
        ></wui-cta-button>
      `:i&&s?c`
        <wui-cta-button
          label=${`Don't have ${d}?`}
          buttonLabel="Get"
          @click=${this.onAppStore.bind(this)}
        ></wui-cta-button>
      `:r&&l?c`
        <wui-cta-button
          label=${`Don't have ${d}?`}
          buttonLabel="Get"
          @click=${this.onPlayStore.bind(this)}
        ></wui-cta-button>
      `:(this.style.display="none",null)}onAppStore(){var t;(t=this.wallet)!=null&&t.app_store&&W.openHref(this.wallet.app_store,"_blank")}onPlayStore(){var t;(t=this.wallet)!=null&&t.play_store&&W.openHref(this.wallet.play_store,"_blank")}onHomePage(){var t;(t=this.wallet)!=null&&t.homepage&&W.openHref(this.wallet.homepage,"_blank")}};ae.styles=[un];$i([u({type:Object})],ae.prototype,"wallet",void 0);ae=$i([C("w3m-mobile-download-links")],ae);const dn=k`
  @keyframes shake {
    0% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(3px);
    }
    50% {
      transform: translateX(-3px);
    }
    75% {
      transform: translateX(3px);
    }
    100% {
      transform: translateX(0);
    }
  }

  wui-flex:first-child:not(:only-child) {
    position: relative;
  }

  wui-loading-thumbnail {
    position: absolute;
  }

  wui-icon-box {
    position: absolute;
    right: calc(var(--wui-spacing-3xs) * -1);
    bottom: calc(var(--wui-spacing-3xs) * -1);
    opacity: 0;
    transform: scale(0.5);
    transition-property: opacity, transform;
    transition-duration: var(--wui-duration-lg);
    transition-timing-function: var(--wui-ease-out-power-2);
    will-change: opacity, transform;
  }

  wui-text[align='center'] {
    width: 100%;
    padding: 0px var(--wui-spacing-l);
  }

  [data-error='true'] wui-icon-box {
    opacity: 1;
    transform: scale(1);
  }

  [data-error='true'] > wui-flex:first-child {
    animation: shake 250ms cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  }

  [data-retry='false'] wui-link {
    display: none;
  }

  [data-retry='true'] wui-link {
    display: block;
    opacity: 1;
  }
`;var it=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};class j extends E{constructor(){var t,i,r,n,e;super(),this.wallet=(t=A.state.data)==null?void 0:t.wallet,this.connector=(i=A.state.data)==null?void 0:i.connector,this.timeout=void 0,this.secondaryBtnIcon="refresh",this.onConnect=void 0,this.onRender=void 0,this.onAutoConnect=void 0,this.isWalletConnect=!0,this.unsubscribe=[],this.imageSrc=M.getWalletImage(this.wallet)??M.getConnectorImage(this.connector),this.name=((r=this.wallet)==null?void 0:r.name)??((n=this.connector)==null?void 0:n.name)??"Wallet",this.isRetrying=!1,this.uri=T.state.wcUri,this.error=T.state.wcError,this.ready=!1,this.showRetry=!1,this.secondaryBtnLabel="Try again",this.secondaryLabel="Accept connection request in the wallet",this.isLoading=!1,this.isMobile=!1,this.onRetry=void 0,this.unsubscribe.push(T.subscribeKey("wcUri",a=>{var s;this.uri=a,this.isRetrying&&this.onRetry&&(this.isRetrying=!1,(s=this.onConnect)==null||s.call(this))}),T.subscribeKey("wcError",a=>this.error=a)),(W.isTelegram()||W.isSafari())&&W.isIos()&&T.state.wcUri&&((e=this.onConnect)==null||e.call(this))}firstUpdated(){var t;(t=this.onAutoConnect)==null||t.call(this),this.showRetry=!this.onAutoConnect}disconnectedCallback(){this.unsubscribe.forEach(t=>t()),T.setWcError(!1),clearTimeout(this.timeout)}render(){var r;(r=this.onRender)==null||r.call(this),this.onShowRetry();const t=this.error?"Connection can be declined if a previous request is still active":this.secondaryLabel;let i=`Continue in ${this.name}`;return this.error&&(i="Connection declined"),c`
      <wui-flex
        data-error=${y(this.error)}
        data-retry=${this.showRetry}
        flexDirection="column"
        alignItems="center"
        .padding=${["3xl","xl","xl","xl"]}
        gap="xl"
      >
        <wui-flex justifyContent="center" alignItems="center">
          <wui-wallet-image size="lg" imageSrc=${y(this.imageSrc)}></wui-wallet-image>

          ${this.error?null:this.loaderTemplate()}

          <wui-icon-box
            backgroundColor="error-100"
            background="opaque"
            iconColor="error-100"
            icon="close"
            size="sm"
            border
            borderColor="wui-color-bg-125"
          ></wui-icon-box>
        </wui-flex>

        <wui-flex flexDirection="column" alignItems="center" gap="xs">
          <wui-text variant="paragraph-500" color=${this.error?"error-100":"fg-100"}>
            ${i}
          </wui-text>
          <wui-text align="center" variant="small-500" color="fg-200">${t}</wui-text>
        </wui-flex>

        ${this.secondaryBtnLabel?c`
              <wui-button
                variant="accent"
                size="md"
                ?disabled=${this.isRetrying||this.isLoading}
                @click=${this.onTryAgain.bind(this)}
                data-testid="w3m-connecting-widget-secondary-button"
              >
                <wui-icon color="inherit" slot="iconLeft" name=${this.secondaryBtnIcon}></wui-icon>
                ${this.secondaryBtnLabel}
              </wui-button>
            `:null}
      </wui-flex>

      ${this.isWalletConnect?c`
            <wui-flex .padding=${["0","xl","xl","xl"]} justifyContent="center">
              <wui-link @click=${this.onCopyUri} color="fg-200" data-testid="wui-link-copy">
                <wui-icon size="xs" color="fg-200" slot="iconLeft" name="copy"></wui-icon>
                Copy link
              </wui-link>
            </wui-flex>
          `:null}

      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>
    `}onShowRetry(){var t;if(this.error&&!this.showRetry){this.showRetry=!0;const i=(t=this.shadowRoot)==null?void 0:t.querySelector("wui-button");i==null||i.animate([{opacity:0},{opacity:1}],{fill:"forwards",easing:"ease"})}}onTryAgain(){var t,i;T.setWcError(!1),this.onRetry?(this.isRetrying=!0,(t=this.onRetry)==null||t.call(this)):(i=this.onConnect)==null||i.call(this)}loaderTemplate(){const t=Ne.state.themeVariables["--w3m-border-radius-master"],i=t?parseInt(t.replace("px",""),10):4;return c`<wui-loading-thumbnail radius=${i*9}></wui-loading-thumbnail>`}onCopyUri(){try{this.uri&&(W.copyToClopboard(this.uri),Qt.showSuccess("Link copied"))}catch{Qt.showError("Failed to copy")}}}j.styles=dn;it([v()],j.prototype,"isRetrying",void 0);it([v()],j.prototype,"uri",void 0);it([v()],j.prototype,"error",void 0);it([v()],j.prototype,"ready",void 0);it([v()],j.prototype,"showRetry",void 0);it([v()],j.prototype,"secondaryBtnLabel",void 0);it([v()],j.prototype,"secondaryLabel",void 0);it([v()],j.prototype,"isLoading",void 0);it([u({type:Boolean})],j.prototype,"isMobile",void 0);it([u()],j.prototype,"onRetry",void 0);var hn=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ui=class extends j{constructor(){if(super(),!this.wallet)throw new Error("w3m-connecting-wc-browser: No wallet provided");this.onConnect=this.onConnectProxy.bind(this),this.onAutoConnect=this.onConnectProxy.bind(this),J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:this.wallet.name,platform:"browser"}})}async onConnectProxy(){var t;try{this.error=!1;const{connectors:i}=L.state,r=i.find(n=>{var e,a,s;return n.type==="ANNOUNCED"&&((e=n.info)==null?void 0:e.rdns)===((a=this.wallet)==null?void 0:a.rdns)||n.type==="INJECTED"||n.name===((s=this.wallet)==null?void 0:s.name)});if(r)await T.connectExternal(r,r.chain);else throw new Error("w3m-connecting-wc-browser: No connector found");vi.close(),J.sendEvent({type:"track",event:"CONNECT_SUCCESS",properties:{method:"browser",name:((t=this.wallet)==null?void 0:t.name)||"Unknown"}})}catch(i){J.sendEvent({type:"track",event:"CONNECT_ERROR",properties:{message:(i==null?void 0:i.message)??"Unknown"}}),this.error=!0}}};ui=hn([C("w3m-connecting-wc-browser")],ui);var pn=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let di=class extends j{constructor(){if(super(),!this.wallet)throw new Error("w3m-connecting-wc-desktop: No wallet provided");this.onConnect=this.onConnectProxy.bind(this),this.onRender=this.onRenderProxy.bind(this),J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:this.wallet.name,platform:"desktop"}})}onRenderProxy(){var t;!this.ready&&this.uri&&(this.ready=!0,(t=this.onConnect)==null||t.call(this))}onConnectProxy(){var t;if((t=this.wallet)!=null&&t.desktop_link&&this.uri)try{this.error=!1;const{desktop_link:i,name:r}=this.wallet,{redirect:n,href:e}=W.formatNativeUrl(i,this.uri);T.setWcLinking({name:r,href:e}),T.setRecentWallet(this.wallet),W.openHref(n,"_blank")}catch{this.error=!0}}};di=pn([C("w3m-connecting-wc-desktop")],di);var Lt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let bt=class extends j{constructor(){if(super(),this.btnLabelTimeout=void 0,this.redirectDeeplink=void 0,this.redirectUniversalLink=void 0,this.target=void 0,this.preferUniversalLinks=Y.state.experimental_preferUniversalLinks,this.isLoading=!0,this.onConnect=()=>{var t;if((t=this.wallet)!=null&&t.mobile_link&&this.uri)try{this.error=!1;const{mobile_link:i,link_mode:r,name:n}=this.wallet,{redirect:e,redirectUniversalLink:a,href:s}=W.formatNativeUrl(i,this.uri,r);this.redirectDeeplink=e,this.redirectUniversalLink=a,this.target=W.isIframe()?"_top":"_self",T.setWcLinking({name:n,href:s}),T.setRecentWallet(this.wallet),this.preferUniversalLinks&&this.redirectUniversalLink?W.openHref(this.redirectUniversalLink,this.target):W.openHref(this.redirectDeeplink,this.target)}catch(i){J.sendEvent({type:"track",event:"CONNECT_PROXY_ERROR",properties:{message:i instanceof Error?i.message:"Error parsing the deeplink",uri:this.uri,mobile_link:this.wallet.mobile_link,name:this.wallet.name}}),this.error=!0}},!this.wallet)throw new Error("w3m-connecting-wc-mobile: No wallet provided");this.secondaryBtnLabel="Open",this.secondaryLabel=yi.CONNECT_LABELS.MOBILE,this.secondaryBtnIcon="externalLink",this.onHandleURI(),this.unsubscribe.push(T.subscribeKey("wcUri",()=>{this.onHandleURI()})),J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:this.wallet.name,platform:"mobile"}})}disconnectedCallback(){super.disconnectedCallback(),clearTimeout(this.btnLabelTimeout)}onHandleURI(){var t;this.isLoading=!this.uri,!this.ready&&this.uri&&(this.ready=!0,(t=this.onConnect)==null||t.call(this))}onTryAgain(){var t;T.setWcError(!1),(t=this.onConnect)==null||t.call(this)}};Lt([v()],bt.prototype,"redirectDeeplink",void 0);Lt([v()],bt.prototype,"redirectUniversalLink",void 0);Lt([v()],bt.prototype,"target",void 0);Lt([v()],bt.prototype,"preferUniversalLinks",void 0);Lt([v()],bt.prototype,"isLoading",void 0);bt=Lt([C("w3m-connecting-wc-mobile")],bt);var Vt={},fn=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},Ri={},V={};let Ze;const gn=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];V.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};V.getSymbolTotalCodewords=function(t){return gn[t]};V.getBCHDigit=function(o){let t=0;for(;o!==0;)t++,o>>>=1;return t};V.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');Ze=t};V.isKanjiModeEnabled=function(){return typeof Ze<"u"};V.toSJIS=function(t){return Ze(t)};var Ce={};(function(o){o.L={bit:1},o.M={bit:0},o.Q={bit:3},o.H={bit:2};function t(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return o.L;case"m":case"medium":return o.M;case"q":case"quartile":return o.Q;case"h":case"high":return o.H;default:throw new Error("Unknown EC Level: "+i)}}o.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},o.from=function(r,n){if(o.isValid(r))return r;try{return t(r)}catch{return n}}})(Ce);function Ii(){this.buffer=[],this.length=0}Ii.prototype={get:function(o){const t=Math.floor(o/8);return(this.buffer[t]>>>7-o%8&1)===1},put:function(o,t){for(let i=0;i<t;i++)this.putBit((o>>>t-i-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),o&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var wn=Ii;function qt(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}qt.prototype.set=function(o,t,i,r){const n=o*this.size+t;this.data[n]=i,r&&(this.reservedBit[n]=!0)};qt.prototype.get=function(o,t){return this.data[o*this.size+t]};qt.prototype.xor=function(o,t,i){this.data[o*this.size+t]^=i};qt.prototype.isReserved=function(o,t){return this.reservedBit[o*this.size+t]};var mn=qt,Ei={};(function(o){const t=V.getSymbolSize;o.getRowColCoords=function(r){if(r===1)return[];const n=Math.floor(r/7)+2,e=t(r),a=e===145?26:Math.ceil((e-13)/(2*n-2))*2,s=[e-7];for(let l=1;l<n-1;l++)s[l]=s[l-1]-a;return s.push(6),s.reverse()},o.getPositions=function(r){const n=[],e=o.getRowColCoords(r),a=e.length;for(let s=0;s<a;s++)for(let l=0;l<a;l++)s===0&&l===0||s===0&&l===a-1||s===a-1&&l===0||n.push([e[s],e[l]]);return n}})(Ei);var Wi={};const bn=V.getSymbolSize,hi=7;Wi.getPositions=function(t){const i=bn(t);return[[0,0],[i-hi,0],[0,i-hi]]};var Si={};(function(o){o.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};o.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},o.from=function(n){return o.isValid(n)?parseInt(n,10):void 0},o.getPenaltyN1=function(n){const e=n.size;let a=0,s=0,l=0,h=null,d=null;for(let I=0;I<e;I++){s=l=0,h=d=null;for(let x=0;x<e;x++){let b=n.get(I,x);b===h?s++:(s>=5&&(a+=t.N1+(s-5)),h=b,s=1),b=n.get(x,I),b===d?l++:(l>=5&&(a+=t.N1+(l-5)),d=b,l=1)}s>=5&&(a+=t.N1+(s-5)),l>=5&&(a+=t.N1+(l-5))}return a},o.getPenaltyN2=function(n){const e=n.size;let a=0;for(let s=0;s<e-1;s++)for(let l=0;l<e-1;l++){const h=n.get(s,l)+n.get(s,l+1)+n.get(s+1,l)+n.get(s+1,l+1);(h===4||h===0)&&a++}return a*t.N2},o.getPenaltyN3=function(n){const e=n.size;let a=0,s=0,l=0;for(let h=0;h<e;h++){s=l=0;for(let d=0;d<e;d++)s=s<<1&2047|n.get(h,d),d>=10&&(s===1488||s===93)&&a++,l=l<<1&2047|n.get(d,h),d>=10&&(l===1488||l===93)&&a++}return a*t.N3},o.getPenaltyN4=function(n){let e=0;const a=n.data.length;for(let l=0;l<a;l++)e+=n.data[l];return Math.abs(Math.ceil(e*100/a/5)-10)*t.N4};function i(r,n,e){switch(r){case o.Patterns.PATTERN000:return(n+e)%2===0;case o.Patterns.PATTERN001:return n%2===0;case o.Patterns.PATTERN010:return e%3===0;case o.Patterns.PATTERN011:return(n+e)%3===0;case o.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(e/3))%2===0;case o.Patterns.PATTERN101:return n*e%2+n*e%3===0;case o.Patterns.PATTERN110:return(n*e%2+n*e%3)%2===0;case o.Patterns.PATTERN111:return(n*e%3+(n+e)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}o.applyMask=function(n,e){const a=e.size;for(let s=0;s<a;s++)for(let l=0;l<a;l++)e.isReserved(l,s)||e.xor(l,s,i(n,l,s))},o.getBestMask=function(n,e){const a=Object.keys(o.Patterns).length;let s=0,l=1/0;for(let h=0;h<a;h++){e(h),o.applyMask(h,n);const d=o.getPenaltyN1(n)+o.getPenaltyN2(n)+o.getPenaltyN3(n)+o.getPenaltyN4(n);o.applyMask(h,n),d<l&&(l=d,s=h)}return s}})(Si);var $e={};const ht=Ce,Yt=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Jt=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];$e.getBlocksCount=function(t,i){switch(i){case ht.L:return Yt[(t-1)*4+0];case ht.M:return Yt[(t-1)*4+1];case ht.Q:return Yt[(t-1)*4+2];case ht.H:return Yt[(t-1)*4+3];default:return}};$e.getTotalCodewordsCount=function(t,i){switch(i){case ht.L:return Jt[(t-1)*4+0];case ht.M:return Jt[(t-1)*4+1];case ht.Q:return Jt[(t-1)*4+2];case ht.H:return Jt[(t-1)*4+3];default:return}};var Ti={},Re={};const zt=new Uint8Array(512),se=new Uint8Array(256);(function(){let t=1;for(let i=0;i<255;i++)zt[i]=t,se[t]=i,t<<=1,t&256&&(t^=285);for(let i=255;i<512;i++)zt[i]=zt[i-255]})();Re.log=function(t){if(t<1)throw new Error("log("+t+")");return se[t]};Re.exp=function(t){return zt[t]};Re.mul=function(t,i){return t===0||i===0?0:zt[se[t]+se[i]]};(function(o){const t=Re;o.mul=function(r,n){const e=new Uint8Array(r.length+n.length-1);for(let a=0;a<r.length;a++)for(let s=0;s<n.length;s++)e[a+s]^=t.mul(r[a],n[s]);return e},o.mod=function(r,n){let e=new Uint8Array(r);for(;e.length-n.length>=0;){const a=e[0];for(let l=0;l<n.length;l++)e[l]^=t.mul(n[l],a);let s=0;for(;s<e.length&&e[s]===0;)s++;e=e.slice(s)}return e},o.generateECPolynomial=function(r){let n=new Uint8Array([1]);for(let e=0;e<r;e++)n=o.mul(n,new Uint8Array([1,t.exp(e)]));return n}})(Ti);const _i=Ti;function ti(o){this.genPoly=void 0,this.degree=o,this.degree&&this.initialize(this.degree)}ti.prototype.initialize=function(t){this.degree=t,this.genPoly=_i.generateECPolynomial(this.degree)};ti.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const i=new Uint8Array(t.length+this.degree);i.set(t);const r=_i.mod(i,this.genPoly),n=this.degree-r.length;if(n>0){const e=new Uint8Array(this.degree);return e.set(r,n),e}return r};var vn=ti,ki={},gt={},ei={};ei.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var nt={};const Li="[0-9]+",yn="[A-Z $%*+\\-./:]+";let Dt="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Dt=Dt.replace(/u/g,"\\u");const xn="(?:(?![A-Z0-9 $%*+\\-./:]|"+Dt+`)(?:.|[\r
]))+`;nt.KANJI=new RegExp(Dt,"g");nt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");nt.BYTE=new RegExp(xn,"g");nt.NUMERIC=new RegExp(Li,"g");nt.ALPHANUMERIC=new RegExp(yn,"g");const Cn=new RegExp("^"+Dt+"$"),$n=new RegExp("^"+Li+"$"),Rn=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");nt.testKanji=function(t){return Cn.test(t)};nt.testNumeric=function(t){return $n.test(t)};nt.testAlphanumeric=function(t){return Rn.test(t)};(function(o){const t=ei,i=nt;o.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},o.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},o.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},o.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},o.MIXED={bit:-1},o.getCharCountIndicator=function(e,a){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!t.isValid(a))throw new Error("Invalid version: "+a);return a>=1&&a<10?e.ccBits[0]:a<27?e.ccBits[1]:e.ccBits[2]},o.getBestModeForData=function(e){return i.testNumeric(e)?o.NUMERIC:i.testAlphanumeric(e)?o.ALPHANUMERIC:i.testKanji(e)?o.KANJI:o.BYTE},o.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},o.isValid=function(e){return e&&e.bit&&e.ccBits};function r(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return o.NUMERIC;case"alphanumeric":return o.ALPHANUMERIC;case"kanji":return o.KANJI;case"byte":return o.BYTE;default:throw new Error("Unknown mode: "+n)}}o.from=function(e,a){if(o.isValid(e))return e;try{return r(e)}catch{return a}}})(gt);(function(o){const t=V,i=$e,r=Ce,n=gt,e=ei,a=7973,s=t.getBCHDigit(a);function l(x,b,$){for(let m=1;m<=40;m++)if(b<=o.getCapacity(m,$,x))return m}function h(x,b){return n.getCharCountIndicator(x,b)+4}function d(x,b){let $=0;return x.forEach(function(m){const R=h(m.mode,b);$+=R+m.getBitsLength()}),$}function I(x,b){for(let $=1;$<=40;$++)if(d(x,$)<=o.getCapacity($,b,n.MIXED))return $}o.from=function(b,$){return e.isValid(b)?parseInt(b,10):$},o.getCapacity=function(b,$,m){if(!e.isValid(b))throw new Error("Invalid QR Code version");typeof m>"u"&&(m=n.BYTE);const R=t.getSymbolTotalCodewords(b),g=i.getTotalCodewordsCount(b,$),f=(R-g)*8;if(m===n.MIXED)return f;const w=f-h(m,b);switch(m){case n.NUMERIC:return Math.floor(w/10*3);case n.ALPHANUMERIC:return Math.floor(w/11*2);case n.KANJI:return Math.floor(w/13);case n.BYTE:default:return Math.floor(w/8)}},o.getBestVersionForData=function(b,$){let m;const R=r.from($,r.M);if(Array.isArray(b)){if(b.length>1)return I(b,R);if(b.length===0)return 1;m=b[0]}else m=b;return l(m.mode,m.getLength(),R)},o.getEncodedBits=function(b){if(!e.isValid(b)||b<7)throw new Error("Invalid QR Code version");let $=b<<12;for(;t.getBCHDigit($)-s>=0;)$^=a<<t.getBCHDigit($)-s;return b<<12|$}})(ki);var Bi={};const je=V,Ai=1335,In=21522,pi=je.getBCHDigit(Ai);Bi.getEncodedBits=function(t,i){const r=t.bit<<3|i;let n=r<<10;for(;je.getBCHDigit(n)-pi>=0;)n^=Ai<<je.getBCHDigit(n)-pi;return(r<<10|n)^In};var Pi={};const En=gt;function It(o){this.mode=En.NUMERIC,this.data=o.toString()}It.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};It.prototype.getLength=function(){return this.data.length};It.prototype.getBitsLength=function(){return It.getBitsLength(this.data.length)};It.prototype.write=function(t){let i,r,n;for(i=0;i+3<=this.data.length;i+=3)r=this.data.substr(i,3),n=parseInt(r,10),t.put(n,10);const e=this.data.length-i;e>0&&(r=this.data.substr(i),n=parseInt(r,10),t.put(n,e*3+1))};var Wn=It;const Sn=gt,Te=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Et(o){this.mode=Sn.ALPHANUMERIC,this.data=o}Et.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};Et.prototype.getLength=function(){return this.data.length};Et.prototype.getBitsLength=function(){return Et.getBitsLength(this.data.length)};Et.prototype.write=function(t){let i;for(i=0;i+2<=this.data.length;i+=2){let r=Te.indexOf(this.data[i])*45;r+=Te.indexOf(this.data[i+1]),t.put(r,11)}this.data.length%2&&t.put(Te.indexOf(this.data[i]),6)};var Tn=Et,_n=function(t){for(var i=[],r=t.length,n=0;n<r;n++){var e=t.charCodeAt(n);if(e>=55296&&e<=56319&&r>n+1){var a=t.charCodeAt(n+1);a>=56320&&a<=57343&&(e=(e-55296)*1024+a-56320+65536,n+=1)}if(e<128){i.push(e);continue}if(e<2048){i.push(e>>6|192),i.push(e&63|128);continue}if(e<55296||e>=57344&&e<65536){i.push(e>>12|224),i.push(e>>6&63|128),i.push(e&63|128);continue}if(e>=65536&&e<=1114111){i.push(e>>18|240),i.push(e>>12&63|128),i.push(e>>6&63|128),i.push(e&63|128);continue}i.push(239,191,189)}return new Uint8Array(i).buffer};const kn=_n,Ln=gt;function Wt(o){this.mode=Ln.BYTE,typeof o=="string"&&(o=kn(o)),this.data=new Uint8Array(o)}Wt.getBitsLength=function(t){return t*8};Wt.prototype.getLength=function(){return this.data.length};Wt.prototype.getBitsLength=function(){return Wt.getBitsLength(this.data.length)};Wt.prototype.write=function(o){for(let t=0,i=this.data.length;t<i;t++)o.put(this.data[t],8)};var Bn=Wt;const An=gt,Pn=V;function St(o){this.mode=An.KANJI,this.data=o}St.getBitsLength=function(t){return t*13};St.prototype.getLength=function(){return this.data.length};St.prototype.getBitsLength=function(){return St.getBitsLength(this.data.length)};St.prototype.write=function(o){let t;for(t=0;t<this.data.length;t++){let i=Pn.toSJIS(this.data[t]);if(i>=33088&&i<=40956)i-=33088;else if(i>=57408&&i<=60351)i-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);i=(i>>>8&255)*192+(i&255),o.put(i,13)}};var zn=St,zi={exports:{}};(function(o){var t={single_source_shortest_paths:function(i,r,n){var e={},a={};a[r]=0;var s=t.PriorityQueue.make();s.push(r,0);for(var l,h,d,I,x,b,$,m,R;!s.empty();){l=s.pop(),h=l.value,I=l.cost,x=i[h]||{};for(d in x)x.hasOwnProperty(d)&&(b=x[d],$=I+b,m=a[d],R=typeof a[d]>"u",(R||m>$)&&(a[d]=$,s.push(d,$),e[d]=h))}if(typeof n<"u"&&typeof a[n]>"u"){var g=["Could not find a path from ",r," to ",n,"."].join("");throw new Error(g)}return e},extract_shortest_path_from_predecessor_list:function(i,r){for(var n=[],e=r;e;)n.push(e),i[e],e=i[e];return n.reverse(),n},find_path:function(i,r,n){var e=t.single_source_shortest_paths(i,r,n);return t.extract_shortest_path_from_predecessor_list(e,n)},PriorityQueue:{make:function(i){var r=t.PriorityQueue,n={},e;i=i||{};for(e in r)r.hasOwnProperty(e)&&(n[e]=r[e]);return n.queue=[],n.sorter=i.sorter||r.default_sorter,n},default_sorter:function(i,r){return i.cost-r.cost},push:function(i,r){var n={value:i,cost:r};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};o.exports=t})(zi);var Nn=zi.exports;(function(o){const t=gt,i=Wn,r=Tn,n=Bn,e=zn,a=nt,s=V,l=Nn;function h(g){return unescape(encodeURIComponent(g)).length}function d(g,f,w){const p=[];let S;for(;(S=g.exec(w))!==null;)p.push({data:S[0],index:S.index,mode:f,length:S[0].length});return p}function I(g){const f=d(a.NUMERIC,t.NUMERIC,g),w=d(a.ALPHANUMERIC,t.ALPHANUMERIC,g);let p,S;return s.isKanjiModeEnabled()?(p=d(a.BYTE,t.BYTE,g),S=d(a.KANJI,t.KANJI,g)):(p=d(a.BYTE_KANJI,t.BYTE,g),S=[]),f.concat(w,p,S).sort(function(z,G){return z.index-G.index}).map(function(z){return{data:z.data,mode:z.mode,length:z.length}})}function x(g,f){switch(f){case t.NUMERIC:return i.getBitsLength(g);case t.ALPHANUMERIC:return r.getBitsLength(g);case t.KANJI:return e.getBitsLength(g);case t.BYTE:return n.getBitsLength(g)}}function b(g){return g.reduce(function(f,w){const p=f.length-1>=0?f[f.length-1]:null;return p&&p.mode===w.mode?(f[f.length-1].data+=w.data,f):(f.push(w),f)},[])}function $(g){const f=[];for(let w=0;w<g.length;w++){const p=g[w];switch(p.mode){case t.NUMERIC:f.push([p,{data:p.data,mode:t.ALPHANUMERIC,length:p.length},{data:p.data,mode:t.BYTE,length:p.length}]);break;case t.ALPHANUMERIC:f.push([p,{data:p.data,mode:t.BYTE,length:p.length}]);break;case t.KANJI:f.push([p,{data:p.data,mode:t.BYTE,length:h(p.data)}]);break;case t.BYTE:f.push([{data:p.data,mode:t.BYTE,length:h(p.data)}])}}return f}function m(g,f){const w={},p={start:{}};let S=["start"];for(let B=0;B<g.length;B++){const z=g[B],G=[];for(let dt=0;dt<z.length;dt++){const Z=z[dt],At=""+B+dt;G.push(At),w[At]={node:Z,lastCount:0},p[At]={};for(let We=0;We<S.length;We++){const rt=S[We];w[rt]&&w[rt].node.mode===Z.mode?(p[rt][At]=x(w[rt].lastCount+Z.length,Z.mode)-x(w[rt].lastCount,Z.mode),w[rt].lastCount+=Z.length):(w[rt]&&(w[rt].lastCount=Z.length),p[rt][At]=x(Z.length,Z.mode)+4+t.getCharCountIndicator(Z.mode,f))}}S=G}for(let B=0;B<S.length;B++)p[S[B]].end=0;return{map:p,table:w}}function R(g,f){let w;const p=t.getBestModeForData(g);if(w=t.from(f,p),w!==t.BYTE&&w.bit<p.bit)throw new Error('"'+g+'" cannot be encoded with mode '+t.toString(w)+`.
 Suggested mode is: `+t.toString(p));switch(w===t.KANJI&&!s.isKanjiModeEnabled()&&(w=t.BYTE),w){case t.NUMERIC:return new i(g);case t.ALPHANUMERIC:return new r(g);case t.KANJI:return new e(g);case t.BYTE:return new n(g)}}o.fromArray=function(f){return f.reduce(function(w,p){return typeof p=="string"?w.push(R(p,null)):p.data&&w.push(R(p.data,p.mode)),w},[])},o.fromString=function(f,w){const p=I(f,s.isKanjiModeEnabled()),S=$(p),B=m(S,w),z=l.find_path(B.map,"start","end"),G=[];for(let dt=1;dt<z.length-1;dt++)G.push(B.table[z[dt]].node);return o.fromArray(b(G))},o.rawSplit=function(f){return o.fromArray(I(f,s.isKanjiModeEnabled()))}})(Pi);const Ie=V,_e=Ce,jn=wn,Mn=mn,On=Ei,Un=Wi,Me=Si,Oe=$e,Dn=vn,le=ki,Fn=Bi,Vn=gt,ke=Pi;function qn(o,t){const i=o.size,r=Un.getPositions(t);for(let n=0;n<r.length;n++){const e=r[n][0],a=r[n][1];for(let s=-1;s<=7;s++)if(!(e+s<=-1||i<=e+s))for(let l=-1;l<=7;l++)a+l<=-1||i<=a+l||(s>=0&&s<=6&&(l===0||l===6)||l>=0&&l<=6&&(s===0||s===6)||s>=2&&s<=4&&l>=2&&l<=4?o.set(e+s,a+l,!0,!0):o.set(e+s,a+l,!1,!0))}}function Hn(o){const t=o.size;for(let i=8;i<t-8;i++){const r=i%2===0;o.set(i,6,r,!0),o.set(6,i,r,!0)}}function Kn(o,t){const i=On.getPositions(t);for(let r=0;r<i.length;r++){const n=i[r][0],e=i[r][1];for(let a=-2;a<=2;a++)for(let s=-2;s<=2;s++)a===-2||a===2||s===-2||s===2||a===0&&s===0?o.set(n+a,e+s,!0,!0):o.set(n+a,e+s,!1,!0)}}function Gn(o,t){const i=o.size,r=le.getEncodedBits(t);let n,e,a;for(let s=0;s<18;s++)n=Math.floor(s/3),e=s%3+i-8-3,a=(r>>s&1)===1,o.set(n,e,a,!0),o.set(e,n,a,!0)}function Le(o,t,i){const r=o.size,n=Fn.getEncodedBits(t,i);let e,a;for(e=0;e<15;e++)a=(n>>e&1)===1,e<6?o.set(e,8,a,!0):e<8?o.set(e+1,8,a,!0):o.set(r-15+e,8,a,!0),e<8?o.set(8,r-e-1,a,!0):e<9?o.set(8,15-e-1+1,a,!0):o.set(8,15-e-1,a,!0);o.set(r-8,8,1,!0)}function Yn(o,t){const i=o.size;let r=-1,n=i-1,e=7,a=0;for(let s=i-1;s>0;s-=2)for(s===6&&s--;;){for(let l=0;l<2;l++)if(!o.isReserved(n,s-l)){let h=!1;a<t.length&&(h=(t[a]>>>e&1)===1),o.set(n,s-l,h),e--,e===-1&&(a++,e=7)}if(n+=r,n<0||i<=n){n-=r,r=-r;break}}}function Jn(o,t,i){const r=new jn;i.forEach(function(l){r.put(l.mode.bit,4),r.put(l.getLength(),Vn.getCharCountIndicator(l.mode,o)),l.write(r)});const n=Ie.getSymbolTotalCodewords(o),e=Oe.getTotalCodewordsCount(o,t),a=(n-e)*8;for(r.getLengthInBits()+4<=a&&r.put(0,4);r.getLengthInBits()%8!==0;)r.putBit(0);const s=(a-r.getLengthInBits())/8;for(let l=0;l<s;l++)r.put(l%2?17:236,8);return Qn(r,o,t)}function Qn(o,t,i){const r=Ie.getSymbolTotalCodewords(t),n=Oe.getTotalCodewordsCount(t,i),e=r-n,a=Oe.getBlocksCount(t,i),s=r%a,l=a-s,h=Math.floor(r/a),d=Math.floor(e/a),I=d+1,x=h-d,b=new Dn(x);let $=0;const m=new Array(a),R=new Array(a);let g=0;const f=new Uint8Array(o.buffer);for(let z=0;z<a;z++){const G=z<l?d:I;m[z]=f.slice($,$+G),R[z]=b.encode(m[z]),$+=G,g=Math.max(g,G)}const w=new Uint8Array(r);let p=0,S,B;for(S=0;S<g;S++)for(B=0;B<a;B++)S<m[B].length&&(w[p++]=m[B][S]);for(S=0;S<x;S++)for(B=0;B<a;B++)w[p++]=R[B][S];return w}function Xn(o,t,i,r){let n;if(Array.isArray(o))n=ke.fromArray(o);else if(typeof o=="string"){let h=t;if(!h){const d=ke.rawSplit(o);h=le.getBestVersionForData(d,i)}n=ke.fromString(o,h||40)}else throw new Error("Invalid data");const e=le.getBestVersionForData(n,i);if(!e)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=e;else if(t<e)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+e+`.
`);const a=Jn(t,i,n),s=Ie.getSymbolSize(t),l=new Mn(s);return qn(l,t),Hn(l),Kn(l,t),Le(l,i,0),t>=7&&Gn(l,t),Yn(l,a),isNaN(r)&&(r=Me.getBestMask(l,Le.bind(null,l,i))),Me.applyMask(r,l),Le(l,i,r),{modules:l,version:t,errorCorrectionLevel:i,maskPattern:r,segments:n}}Ri.create=function(t,i){if(typeof t>"u"||t==="")throw new Error("No input text");let r=_e.M,n,e;return typeof i<"u"&&(r=_e.from(i.errorCorrectionLevel,_e.M),n=le.from(i.version),e=Me.from(i.maskPattern),i.toSJISFunc&&Ie.setToSJISFunction(i.toSJISFunc)),Xn(t,n,r,e)};var Ni={},ii={};(function(o){function t(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let r=i.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+i);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(e){return[e,e]}))),r.length===6&&r.push("F","F");const n=parseInt(r.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+r.slice(0,6).join("")}}o.getOptions=function(r){r||(r={}),r.color||(r.color={});const n=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,e=r.width&&r.width>=21?r.width:void 0,a=r.scale||4;return{width:e,scale:e?4:a,margin:n,color:{dark:t(r.color.dark||"#000000ff"),light:t(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},o.getScale=function(r,n){return n.width&&n.width>=r+n.margin*2?n.width/(r+n.margin*2):n.scale},o.getImageWidth=function(r,n){const e=o.getScale(r,n);return Math.floor((r+n.margin*2)*e)},o.qrToImageData=function(r,n,e){const a=n.modules.size,s=n.modules.data,l=o.getScale(a,e),h=Math.floor((a+e.margin*2)*l),d=e.margin*l,I=[e.color.light,e.color.dark];for(let x=0;x<h;x++)for(let b=0;b<h;b++){let $=(x*h+b)*4,m=e.color.light;if(x>=d&&b>=d&&x<h-d&&b<h-d){const R=Math.floor((x-d)/l),g=Math.floor((b-d)/l);m=I[s[R*a+g]?1:0]}r[$++]=m.r,r[$++]=m.g,r[$++]=m.b,r[$]=m.a}}})(ii);(function(o){const t=ii;function i(n,e,a){n.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=a,e.width=a,e.style.height=a+"px",e.style.width=a+"px"}function r(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}o.render=function(e,a,s){let l=s,h=a;typeof l>"u"&&(!a||!a.getContext)&&(l=a,a=void 0),a||(h=r()),l=t.getOptions(l);const d=t.getImageWidth(e.modules.size,l),I=h.getContext("2d"),x=I.createImageData(d,d);return t.qrToImageData(x.data,e,l),i(I,h,d),I.putImageData(x,0,0),h},o.renderToDataURL=function(e,a,s){let l=s;typeof l>"u"&&(!a||!a.getContext)&&(l=a,a=void 0),l||(l={});const h=o.render(e,a,l),d=l.type||"image/png",I=l.rendererOpts||{};return h.toDataURL(d,I.quality)}})(Ni);var ji={};const Zn=ii;function fi(o,t){const i=o.a/255,r=t+'="'+o.hex+'"';return i<1?r+" "+t+'-opacity="'+i.toFixed(2).slice(1)+'"':r}function Be(o,t,i){let r=o+t;return typeof i<"u"&&(r+=" "+i),r}function to(o,t,i){let r="",n=0,e=!1,a=0;for(let s=0;s<o.length;s++){const l=Math.floor(s%t),h=Math.floor(s/t);!l&&!e&&(e=!0),o[s]?(a++,s>0&&l>0&&o[s-1]||(r+=e?Be("M",l+i,.5+h+i):Be("m",n,0),n=0,e=!1),l+1<t&&o[s+1]||(r+=Be("h",a),a=0)):n++}return r}ji.render=function(t,i,r){const n=Zn.getOptions(i),e=t.modules.size,a=t.modules.data,s=e+n.margin*2,l=n.color.light.a?"<path "+fi(n.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",h="<path "+fi(n.color.dark,"stroke")+' d="'+to(a,e,n.margin)+'"/>',d='viewBox="0 0 '+s+" "+s+'"',x='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+d+' shape-rendering="crispEdges">'+l+h+`</svg>
`;return typeof r=="function"&&r(null,x),x};const eo=fn,Ue=Ri,Mi=Ni,io=ji;function ni(o,t,i,r,n){const e=[].slice.call(arguments,1),a=e.length,s=typeof e[a-1]=="function";if(!s&&!eo())throw new Error("Callback required as last argument");if(s){if(a<2)throw new Error("Too few arguments provided");a===2?(n=i,i=t,t=r=void 0):a===3&&(t.getContext&&typeof n>"u"?(n=r,r=void 0):(n=r,r=i,i=t,t=void 0))}else{if(a<1)throw new Error("Too few arguments provided");return a===1?(i=t,t=r=void 0):a===2&&!t.getContext&&(r=i,i=t,t=void 0),new Promise(function(l,h){try{const d=Ue.create(i,r);l(o(d,t,r))}catch(d){h(d)}})}try{const l=Ue.create(i,r);n(null,o(l,t,r))}catch(l){n(l)}}Vt.create=Ue.create;Vt.toCanvas=ni.bind(null,Mi.render);Vt.toDataURL=ni.bind(null,Mi.renderToDataURL);Vt.toString=ni.bind(null,function(o,t,i){return io.render(o,i)});const no=.1,gi=2.5,at=7;function Ae(o,t,i){return o===t?!1:(o-t<0?t-o:o-t)<=i+no}function oo(o,t){const i=Array.prototype.slice.call(Vt.create(o,{errorCorrectionLevel:t}).modules.data,0),r=Math.sqrt(i.length);return i.reduce((n,e,a)=>(a%r===0?n.push([e]):n[n.length-1].push(e))&&n,[])}const ro={generate({uri:o,size:t,logoSize:i,dotColor:r="#141414"}){const n="transparent",a=[],s=oo(o,"Q"),l=t/s.length,h=[{x:0,y:0},{x:1,y:0},{x:0,y:1}];h.forEach(({x:m,y:R})=>{const g=(s.length-at)*l*m,f=(s.length-at)*l*R,w=.45;for(let p=0;p<h.length;p+=1){const S=l*(at-p*2);a.push(Pt`
            <rect
              fill=${p===2?r:n}
              width=${p===0?S-5:S}
              rx= ${p===0?(S-5)*w:S*w}
              ry= ${p===0?(S-5)*w:S*w}
              stroke=${r}
              stroke-width=${p===0?5:0}
              height=${p===0?S-5:S}
              x= ${p===0?f+l*p+5/2:f+l*p}
              y= ${p===0?g+l*p+5/2:g+l*p}
            />
          `)}});const d=Math.floor((i+25)/l),I=s.length/2-d/2,x=s.length/2+d/2-1,b=[];s.forEach((m,R)=>{m.forEach((g,f)=>{if(s[R][f]&&!(R<at&&f<at||R>s.length-(at+1)&&f<at||R<at&&f>s.length-(at+1))&&!(R>I&&R<x&&f>I&&f<x)){const w=R*l+l/2,p=f*l+l/2;b.push([w,p])}})});const $={};return b.forEach(([m,R])=>{var g;$[m]?(g=$[m])==null||g.push(R):$[m]=[R]}),Object.entries($).map(([m,R])=>{const g=R.filter(f=>R.every(w=>!Ae(f,w,l)));return[Number(m),g]}).forEach(([m,R])=>{R.forEach(g=>{a.push(Pt`<circle cx=${m} cy=${g} fill=${r} r=${l/gi} />`)})}),Object.entries($).filter(([m,R])=>R.length>1).map(([m,R])=>{const g=R.filter(f=>R.some(w=>Ae(f,w,l)));return[Number(m),g]}).map(([m,R])=>{R.sort((f,w)=>f<w?-1:1);const g=[];for(const f of R){const w=g.find(p=>p.some(S=>Ae(f,S,l)));w?w.push(f):g.push([f])}return[m,g.map(f=>[f[0],f[f.length-1]])]}).forEach(([m,R])=>{R.forEach(([g,f])=>{a.push(Pt`
              <line
                x1=${m}
                x2=${m}
                y1=${g}
                y2=${f}
                stroke=${r}
                stroke-width=${l/(gi/2)}
                stroke-linecap="round"
              />
            `)})}),a}},ao=k`
  :host {
    position: relative;
    user-select: none;
    display: block;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    width: var(--local-size);
  }

  :host([data-theme='dark']) {
    border-radius: clamp(0px, var(--wui-border-radius-l), 40px);
    background-color: var(--wui-color-inverse-100);
    padding: var(--wui-spacing-l);
  }

  :host([data-theme='light']) {
    box-shadow: 0 0 0 1px var(--wui-color-bg-125);
    background-color: var(--wui-color-bg-125);
  }

  :host([data-clear='true']) > wui-icon {
    display: none;
  }

  svg:first-child,
  wui-image,
  wui-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
  }

  wui-image {
    width: 25%;
    height: 25%;
    border-radius: var(--wui-border-radius-xs);
  }

  wui-icon {
    width: 100%;
    height: 100%;
    color: var(--local-icon-color) !important;
    transform: translateY(-50%) translateX(-50%) scale(0.25);
  }
`;var ut=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};const so="#3396ff";let Q=class extends E{constructor(){super(...arguments),this.uri="",this.size=0,this.theme="dark",this.imageSrc=void 0,this.alt=void 0,this.arenaClear=void 0,this.farcaster=void 0}render(){return this.dataset.theme=this.theme,this.dataset.clear=String(this.arenaClear),this.style.cssText=`
     --local-size: ${this.size}px;
     --local-icon-color: ${this.color??so}
    `,c`${this.templateVisual()} ${this.templateSvg()}`}templateSvg(){const t=this.theme==="light"?this.size:this.size-32;return Pt`
      <svg height=${t} width=${t}>
        ${ro.generate({uri:this.uri,size:t,logoSize:this.arenaClear?0:t/4,dotColor:this.color})}
      </svg>
    `}templateVisual(){return this.imageSrc?c`<wui-image src=${this.imageSrc} alt=${this.alt??"logo"}></wui-image>`:this.farcaster?c`<wui-icon
        class="farcaster"
        size="inherit"
        color="inherit"
        name="farcaster"
      ></wui-icon>`:c`<wui-icon size="inherit" color="inherit" name="walletConnect"></wui-icon>`}};Q.styles=[P,ao];ut([u()],Q.prototype,"uri",void 0);ut([u({type:Number})],Q.prototype,"size",void 0);ut([u()],Q.prototype,"theme",void 0);ut([u()],Q.prototype,"imageSrc",void 0);ut([u()],Q.prototype,"alt",void 0);ut([u()],Q.prototype,"color",void 0);ut([u({type:Boolean})],Q.prototype,"arenaClear",void 0);ut([u({type:Boolean})],Q.prototype,"farcaster",void 0);Q=ut([C("wui-qr-code")],Q);const lo=k`
  :host {
    display: block;
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);
    background: linear-gradient(
      120deg,
      var(--wui-color-bg-200) 5%,
      var(--wui-color-bg-200) 48%,
      var(--wui-color-bg-300) 55%,
      var(--wui-color-bg-300) 60%,
      var(--wui-color-bg-300) calc(60% + 10px),
      var(--wui-color-bg-200) calc(60% + 12px),
      var(--wui-color-bg-200) 100%
    );
    background-size: 250%;
    animation: shimmer 3s linear infinite reverse;
  }

  :host([variant='light']) {
    background: linear-gradient(
      120deg,
      var(--wui-color-bg-150) 5%,
      var(--wui-color-bg-150) 48%,
      var(--wui-color-bg-200) 55%,
      var(--wui-color-bg-200) 60%,
      var(--wui-color-bg-200) calc(60% + 10px),
      var(--wui-color-bg-150) calc(60% + 12px),
      var(--wui-color-bg-150) 100%
    );
    background-size: 250%;
  }

  @keyframes shimmer {
    from {
      background-position: -250% 0;
    }
    to {
      background-position: 250% 0;
    }
  }
`;var Ht=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let vt=class extends E{constructor(){super(...arguments),this.width="",this.height="",this.borderRadius="m",this.variant="default"}render(){return this.style.cssText=`
      width: ${this.width};
      height: ${this.height};
      border-radius: ${`clamp(0px,var(--wui-border-radius-${this.borderRadius}), 40px)`};
    `,c`<slot></slot>`}};vt.styles=[lo];Ht([u()],vt.prototype,"width",void 0);Ht([u()],vt.prototype,"height",void 0);Ht([u()],vt.prototype,"borderRadius",void 0);Ht([u()],vt.prototype,"variant",void 0);vt=Ht([C("wui-shimmer")],vt);const co="https://reown.com",uo=k`
  .reown-logo {
    height: var(--wui-spacing-xxl);
  }

  a {
    text-decoration: none;
    cursor: pointer;
  }

  a:hover {
    opacity: 0.9;
  }
`;var ho=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let De=class extends E{render(){return c`
      <a
        data-testid="ux-branding-reown"
        href=${co}
        rel="noreferrer"
        target="_blank"
        style="text-decoration: none;"
      >
        <wui-flex
          justifyContent="center"
          alignItems="center"
          gap="xs"
          .padding=${["0","0","l","0"]}
        >
          <wui-text variant="small-500" color="fg-100"> UX by </wui-text>
          <wui-icon name="reown" size="xxxl" class="reown-logo"></wui-icon>
        </wui-flex>
      </a>
    `}};De.styles=[P,U,uo];De=ho([C("wui-ux-by-reown")],De);const po=k`
  @keyframes fadein {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  wui-shimmer {
    width: 100%;
    aspect-ratio: 1 / 1;
    border-radius: clamp(0px, var(--wui-border-radius-l), 40px) !important;
  }

  wui-qr-code {
    opacity: 0;
    animation-duration: 200ms;
    animation-timing-function: ease;
    animation-name: fadein;
    animation-fill-mode: forwards;
  }
`;var fo=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Fe=class extends j{constructor(){var t;super(),this.forceUpdate=()=>{this.requestUpdate()},window.addEventListener("resize",this.forceUpdate),J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:((t=this.wallet)==null?void 0:t.name)??"WalletConnect",platform:"qrcode"}})}disconnectedCallback(){var t;super.disconnectedCallback(),(t=this.unsubscribe)==null||t.forEach(i=>i()),window.removeEventListener("resize",this.forceUpdate)}render(){return this.onRenderProxy(),c`
      <wui-flex
        flexDirection="column"
        alignItems="center"
        .padding=${["0","xl","xl","xl"]}
        gap="xl"
      >
        <wui-shimmer borderRadius="l" width="100%"> ${this.qrCodeTemplate()} </wui-shimmer>

        <wui-text variant="paragraph-500" color="fg-100">
          Scan this QR Code with your phone
        </wui-text>
        ${this.copyTemplate()}
      </wui-flex>
      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>
    `}onRenderProxy(){!this.ready&&this.uri&&(this.timeout=setTimeout(()=>{this.ready=!0},200))}qrCodeTemplate(){if(!this.uri||!this.ready)return null;const t=this.getBoundingClientRect().width-40,i=this.wallet?this.wallet.name:void 0;return T.setWcLinking(void 0),T.setRecentWallet(this.wallet),c` <wui-qr-code
      size=${t}
      theme=${Ne.state.themeMode}
      uri=${this.uri}
      imageSrc=${y(M.getWalletImage(this.wallet))}
      color=${y(Ne.state.themeVariables["--w3m-qr-color"])}
      alt=${y(i)}
      data-testid="wui-qr-code"
    ></wui-qr-code>`}copyTemplate(){const t=!this.uri||!this.ready;return c`<wui-link
      .disabled=${t}
      @click=${this.onCopyUri}
      color="fg-200"
      data-testid="copy-wc2-uri"
    >
      <wui-icon size="xs" color="fg-200" slot="iconLeft" name="copy"></wui-icon>
      Copy link
    </wui-link>`}};Fe.styles=po;Fe=fo([C("w3m-connecting-wc-qrcode")],Fe);var go=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let wi=class extends E{constructor(){var t;if(super(),this.wallet=(t=A.state.data)==null?void 0:t.wallet,!this.wallet)throw new Error("w3m-connecting-wc-unsupported: No wallet provided");J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:this.wallet.name,platform:"browser"}})}render(){return c`
      <wui-flex
        flexDirection="column"
        alignItems="center"
        .padding=${["3xl","xl","xl","xl"]}
        gap="xl"
      >
        <wui-wallet-image
          size="lg"
          imageSrc=${y(M.getWalletImage(this.wallet))}
        ></wui-wallet-image>

        <wui-text variant="paragraph-500" color="fg-100">Not Detected</wui-text>
      </wui-flex>

      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>
    `}};wi=go([C("w3m-connecting-wc-unsupported")],wi);var Oi=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Ve=class extends j{constructor(){if(super(),this.isLoading=!0,!this.wallet)throw new Error("w3m-connecting-wc-web: No wallet provided");this.onConnect=this.onConnectProxy.bind(this),this.secondaryBtnLabel="Open",this.secondaryLabel=yi.CONNECT_LABELS.MOBILE,this.secondaryBtnIcon="externalLink",this.updateLoadingState(),this.unsubscribe.push(T.subscribeKey("wcUri",()=>{this.updateLoadingState()})),J.sendEvent({type:"track",event:"SELECT_WALLET",properties:{name:this.wallet.name,platform:"web"}})}updateLoadingState(){this.isLoading=!this.uri}onConnectProxy(){var t;if((t=this.wallet)!=null&&t.webapp_link&&this.uri)try{this.error=!1;const{webapp_link:i,name:r}=this.wallet,{redirect:n,href:e}=W.formatUniversalUrl(i,this.uri);T.setWcLinking({name:r,href:e}),T.setRecentWallet(this.wallet),W.openHref(n,"_blank")}catch{this.error=!0}}};Oi([v()],Ve.prototype,"isLoading",void 0);Ve=Oi([C("w3m-connecting-wc-web")],Ve);var Kt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let Tt=class extends E{constructor(){var t;super(),this.wallet=(t=A.state.data)==null?void 0:t.wallet,this.unsubscribe=[],this.platform=void 0,this.platforms=[],this.isSiwxEnabled=!!Y.state.siwx,this.remoteFeatures=Y.state.remoteFeatures,this.determinePlatforms(),this.initializeConnection(),this.unsubscribe.push(Y.subscribeKey("remoteFeatures",i=>this.remoteFeatures=i))}disconnectedCallback(){this.unsubscribe.forEach(t=>t())}render(){return c`
      ${this.headerTemplate()}
      <div>${this.platformTemplate()}</div>
      ${this.reownBrandingTemplate()}
    `}reownBrandingTemplate(){var t;return(t=this.remoteFeatures)!=null&&t.reownBranding?c`<wui-ux-by-reown></wui-ux-by-reown>`:null}async initializeConnection(t=!1){if(!(this.platform==="browser"||Y.state.manualWCControl&&!t))try{const{wcPairingExpiry:i,status:r}=T.state;(t||Y.state.enableEmbedded||W.isPairingExpired(i)||r==="connecting")&&(await T.connectWalletConnect(),this.isSiwxEnabled||vi.close())}catch(i){J.sendEvent({type:"track",event:"CONNECT_ERROR",properties:{message:(i==null?void 0:i.message)??"Unknown"}}),T.setWcError(!0),Qt.showError(i.message??"Connection error"),T.resetWcConnection(),A.goBack()}}determinePlatforms(){if(!this.wallet){this.platforms.push("qrcode"),this.platform="qrcode";return}if(this.platform)return;const{mobile_link:t,desktop_link:i,webapp_link:r,injected:n,rdns:e}=this.wallet,a=n==null?void 0:n.map(({injected_id:$})=>$).filter(Boolean),s=[...e?[e]:a??[]],l=Y.state.isUniversalProvider?!1:s.length,h=t,d=r,I=T.checkInstalled(s),x=l&&I,b=i&&!W.isMobile();x&&!ze.state.noAdapters&&this.platforms.push("browser"),h&&this.platforms.push(W.isMobile()?"mobile":"qrcode"),d&&this.platforms.push("web"),b&&this.platforms.push("desktop"),!x&&l&&!ze.state.noAdapters&&this.platforms.push("unsupported"),this.platform=this.platforms[0]}platformTemplate(){switch(this.platform){case"browser":return c`<w3m-connecting-wc-browser></w3m-connecting-wc-browser>`;case"web":return c`<w3m-connecting-wc-web></w3m-connecting-wc-web>`;case"desktop":return c`
          <w3m-connecting-wc-desktop .onRetry=${()=>this.initializeConnection(!0)}>
          </w3m-connecting-wc-desktop>
        `;case"mobile":return c`
          <w3m-connecting-wc-mobile isMobile .onRetry=${()=>this.initializeConnection(!0)}>
          </w3m-connecting-wc-mobile>
        `;case"qrcode":return c`<w3m-connecting-wc-qrcode></w3m-connecting-wc-qrcode>`;default:return c`<w3m-connecting-wc-unsupported></w3m-connecting-wc-unsupported>`}}headerTemplate(){return this.platforms.length>1?c`
      <w3m-connecting-header
        .platforms=${this.platforms}
        .onSelectPlatfrom=${this.onSelectPlatform.bind(this)}
      >
      </w3m-connecting-header>
    `:null}async onSelectPlatform(t){var r;const i=(r=this.shadowRoot)==null?void 0:r.querySelector("div");i&&(await i.animate([{opacity:1},{opacity:0}],{duration:200,fill:"forwards",easing:"ease"}).finished,this.platform=t,i.animate([{opacity:0},{opacity:1}],{duration:200,fill:"forwards",easing:"ease"}))}};Kt([v()],Tt.prototype,"platform",void 0);Kt([v()],Tt.prototype,"platforms",void 0);Kt([v()],Tt.prototype,"isSiwxEnabled",void 0);Kt([v()],Tt.prototype,"remoteFeatures",void 0);Tt=Kt([C("w3m-connecting-wc-view")],Tt);var Ui=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let qe=class extends E{constructor(){super(...arguments),this.isMobile=W.isMobile()}render(){if(this.isMobile){const{featured:t,recommended:i}=_.state,{customWallets:r}=Y.state,n=fe.getRecentWallets(),e=t.length||i.length||(r==null?void 0:r.length)||n.length;return c`<wui-flex
        flexDirection="column"
        gap="xs"
        .margin=${["3xs","s","s","s"]}
      >
        ${e?c`<w3m-connector-list></w3m-connector-list>`:null}
        <w3m-all-wallets-widget></w3m-all-wallets-widget>
      </wui-flex>`}return c`<wui-flex flexDirection="column" .padding=${["0","0","l","0"]}>
      <w3m-connecting-wc-view></w3m-connecting-wc-view>
      <wui-flex flexDirection="column" .padding=${["0","m","0","m"]}>
        <w3m-all-wallets-widget></w3m-all-wallets-widget> </wui-flex
    ></wui-flex>`}};Ui([v()],qe.prototype,"isMobile",void 0);qe=Ui([C("w3m-connecting-wc-basic-view")],qe);/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const oi=()=>new wo;class wo{}const Pe=new WeakMap,ri=Gi(class extends Yi{render(o){return li}update(o,[t]){var r;const i=t!==this.G;return i&&this.G!==void 0&&this.rt(void 0),(i||this.lt!==this.ct)&&(this.G=t,this.ht=(r=o.options)==null?void 0:r.host,this.rt(this.ct=o.element)),li}rt(o){if(this.isConnected||(o=void 0),typeof this.G=="function"){const t=this.ht??globalThis;let i=Pe.get(t);i===void 0&&(i=new WeakMap,Pe.set(t,i)),i.get(this.G)!==void 0&&this.G.call(this.ht,void 0),i.set(this.G,o),o!==void 0&&this.G.call(this.ht,o)}else this.G.value=o}get lt(){var o,t;return typeof this.G=="function"?(o=Pe.get(this.ht??globalThis))==null?void 0:o.get(this.G):(t=this.G)==null?void 0:t.value}disconnected(){this.lt===this.ct&&this.rt(void 0)}reconnected(){this.rt(this.ct)}}),mo=k`
  :host {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  label {
    position: relative;
    display: inline-block;
    width: 32px;
    height: 22px;
  }

  input {
    width: 0;
    height: 0;
    opacity: 0;
  }

  span {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--wui-color-blue-100);
    border-width: 1px;
    border-style: solid;
    border-color: var(--wui-color-gray-glass-002);
    border-radius: 999px;
    transition:
      background-color var(--wui-ease-inout-power-1) var(--wui-duration-md),
      border-color var(--wui-ease-inout-power-1) var(--wui-duration-md);
    will-change: background-color, border-color;
  }

  span:before {
    position: absolute;
    content: '';
    height: 16px;
    width: 16px;
    left: 3px;
    top: 2px;
    background-color: var(--wui-color-inverse-100);
    transition: transform var(--wui-ease-inout-power-1) var(--wui-duration-lg);
    will-change: transform;
    border-radius: 50%;
  }

  input:checked + span {
    border-color: var(--wui-color-gray-glass-005);
    background-color: var(--wui-color-blue-100);
  }

  input:not(:checked) + span {
    background-color: var(--wui-color-gray-glass-010);
  }

  input:checked + span:before {
    transform: translateX(calc(100% - 7px));
  }
`;var Di=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ce=class extends E{constructor(){super(...arguments),this.inputElementRef=oi(),this.checked=void 0}render(){return c`
      <label>
        <input
          ${ri(this.inputElementRef)}
          type="checkbox"
          ?checked=${y(this.checked)}
          @change=${this.dispatchChangeEvent.bind(this)}
        />
        <span></span>
      </label>
    `}dispatchChangeEvent(){var t;this.dispatchEvent(new CustomEvent("switchChange",{detail:(t=this.inputElementRef.value)==null?void 0:t.checked,bubbles:!0,composed:!0}))}};ce.styles=[P,U,Ki,mo];Di([u({type:Boolean})],ce.prototype,"checked",void 0);ce=Di([C("wui-switch")],ce);const bo=k`
  :host {
    height: 100%;
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: var(--wui-spacing-1xs);
    padding: var(--wui-spacing-xs) var(--wui-spacing-s);
    background-color: var(--wui-color-gray-glass-002);
    border-radius: var(--wui-border-radius-xs);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);
    transition: background-color var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: background-color;
    cursor: pointer;
  }

  wui-switch {
    pointer-events: none;
  }
`;var Fi=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let ue=class extends E{constructor(){super(...arguments),this.checked=void 0}render(){return c`
      <button>
        <wui-icon size="xl" name="walletConnectBrown"></wui-icon>
        <wui-switch ?checked=${y(this.checked)}></wui-switch>
      </button>
    `}};ue.styles=[P,U,bo];Fi([u({type:Boolean})],ue.prototype,"checked",void 0);ue=Fi([C("wui-certified-switch")],ue);const vo=k`
  button {
    background-color: var(--wui-color-fg-300);
    border-radius: var(--wui-border-radius-4xs);
    width: 16px;
    height: 16px;
  }

  button:disabled {
    background-color: var(--wui-color-bg-300);
  }

  wui-icon {
    color: var(--wui-color-bg-200) !important;
  }

  button:focus-visible {
    background-color: var(--wui-color-fg-250);
    border: 1px solid var(--wui-color-accent-100);
  }

  @media (hover: hover) and (pointer: fine) {
    button:hover:enabled {
      background-color: var(--wui-color-fg-250);
    }

    button:active:enabled {
      background-color: var(--wui-color-fg-225);
    }
  }
`;var Vi=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let de=class extends E{constructor(){super(...arguments),this.icon="copy"}render(){return c`
      <button>
        <wui-icon color="inherit" size="xxs" name=${this.icon}></wui-icon>
      </button>
    `}};de.styles=[P,U,vo];Vi([u()],de.prototype,"icon",void 0);de=Vi([C("wui-input-element")],de);const yo=k`
  :host {
    position: relative;
    width: 100%;
    display: inline-block;
    color: var(--wui-color-fg-275);
  }

  input {
    width: 100%;
    border-radius: var(--wui-border-radius-xs);
    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);
    background: var(--wui-color-gray-glass-002);
    font-size: var(--wui-font-size-paragraph);
    letter-spacing: var(--wui-letter-spacing-paragraph);
    color: var(--wui-color-fg-100);
    transition:
      background-color var(--wui-ease-inout-power-1) var(--wui-duration-md),
      border-color var(--wui-ease-inout-power-1) var(--wui-duration-md),
      box-shadow var(--wui-ease-inout-power-1) var(--wui-duration-md);
    will-change: background-color, border-color, box-shadow;
    caret-color: var(--wui-color-accent-100);
  }

  input:disabled {
    cursor: not-allowed;
    border: 1px solid var(--wui-color-gray-glass-010);
  }

  input:disabled::placeholder,
  input:disabled + wui-icon {
    color: var(--wui-color-fg-300);
  }

  input::placeholder {
    color: var(--wui-color-fg-275);
  }

  input:focus:enabled {
    background-color: var(--wui-color-gray-glass-005);
    -webkit-box-shadow:
      inset 0 0 0 1px var(--wui-color-accent-100),
      0px 0px 0px 4px var(--wui-box-shadow-blue);
    -moz-box-shadow:
      inset 0 0 0 1px var(--wui-color-accent-100),
      0px 0px 0px 4px var(--wui-box-shadow-blue);
    box-shadow:
      inset 0 0 0 1px var(--wui-color-accent-100),
      0px 0px 0px 4px var(--wui-box-shadow-blue);
  }

  input:hover:enabled {
    background-color: var(--wui-color-gray-glass-005);
  }

  wui-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  .wui-size-sm {
    padding: 9px var(--wui-spacing-m) 10px var(--wui-spacing-s);
  }

  wui-icon + .wui-size-sm {
    padding: 9px var(--wui-spacing-m) 10px 36px;
  }

  wui-icon[data-input='sm'] {
    left: var(--wui-spacing-s);
  }

  .wui-size-md {
    padding: 15px var(--wui-spacing-m) var(--wui-spacing-l) var(--wui-spacing-m);
  }

  wui-icon + .wui-size-md,
  wui-loading-spinner + .wui-size-md {
    padding: 10.5px var(--wui-spacing-3xl) 10.5px var(--wui-spacing-3xl);
  }

  wui-icon[data-input='md'] {
    left: var(--wui-spacing-l);
  }

  .wui-size-lg {
    padding: var(--wui-spacing-s) var(--wui-spacing-s) var(--wui-spacing-s) var(--wui-spacing-l);
    letter-spacing: var(--wui-letter-spacing-medium-title);
    font-size: var(--wui-font-size-medium-title);
    font-weight: var(--wui-font-weight-light);
    line-height: 130%;
    color: var(--wui-color-fg-100);
    height: 64px;
  }

  .wui-padding-right-xs {
    padding-right: var(--wui-spacing-xs);
  }

  .wui-padding-right-s {
    padding-right: var(--wui-spacing-s);
  }

  .wui-padding-right-m {
    padding-right: var(--wui-spacing-m);
  }

  .wui-padding-right-l {
    padding-right: var(--wui-spacing-l);
  }

  .wui-padding-right-xl {
    padding-right: var(--wui-spacing-xl);
  }

  .wui-padding-right-2xl {
    padding-right: var(--wui-spacing-2xl);
  }

  .wui-padding-right-3xl {
    padding-right: var(--wui-spacing-3xl);
  }

  .wui-padding-right-4xl {
    padding-right: var(--wui-spacing-4xl);
  }

  .wui-padding-right-5xl {
    padding-right: var(--wui-spacing-5xl);
  }

  wui-icon + .wui-size-lg,
  wui-loading-spinner + .wui-size-lg {
    padding-left: 50px;
  }

  wui-icon[data-input='lg'] {
    left: var(--wui-spacing-l);
  }

  .wui-size-mdl {
    padding: 17.25px var(--wui-spacing-m) 17.25px var(--wui-spacing-m);
  }
  wui-icon + .wui-size-mdl,
  wui-loading-spinner + .wui-size-mdl {
    padding: 17.25px var(--wui-spacing-3xl) 17.25px 40px;
  }
  wui-icon[data-input='mdl'] {
    left: var(--wui-spacing-m);
  }

  input:placeholder-shown ~ ::slotted(wui-input-element),
  input:placeholder-shown ~ ::slotted(wui-icon) {
    opacity: 0;
    pointer-events: none;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }

  ::slotted(wui-input-element),
  ::slotted(wui-icon) {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  ::slotted(wui-input-element) {
    right: var(--wui-spacing-m);
  }

  ::slotted(wui-icon) {
    right: 0px;
  }
`;var ot=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let H=class extends E{constructor(){super(...arguments),this.inputElementRef=oi(),this.size="md",this.disabled=!1,this.placeholder="",this.type="text",this.value=""}render(){const t=`wui-padding-right-${this.inputRightPadding}`,r={[`wui-size-${this.size}`]:!0,[t]:!!this.inputRightPadding};return c`${this.templateIcon()}
      <input
        data-testid="wui-input-text"
        ${ri(this.inputElementRef)}
        class=${Ji(r)}
        type=${this.type}
        enterkeyhint=${y(this.enterKeyHint)}
        ?disabled=${this.disabled}
        placeholder=${this.placeholder}
        @input=${this.dispatchInputChangeEvent.bind(this)}
        .value=${this.value||""}
        tabindex=${y(this.tabIdx)}
      />
      <slot></slot>`}templateIcon(){return this.icon?c`<wui-icon
        data-input=${this.size}
        size=${this.size}
        color="inherit"
        name=${this.icon}
      ></wui-icon>`:null}dispatchInputChangeEvent(){var t;this.dispatchEvent(new CustomEvent("inputChange",{detail:(t=this.inputElementRef.value)==null?void 0:t.value,bubbles:!0,composed:!0}))}};H.styles=[P,U,yo];ot([u()],H.prototype,"size",void 0);ot([u()],H.prototype,"icon",void 0);ot([u({type:Boolean})],H.prototype,"disabled",void 0);ot([u()],H.prototype,"placeholder",void 0);ot([u()],H.prototype,"type",void 0);ot([u()],H.prototype,"keyHint",void 0);ot([u()],H.prototype,"value",void 0);ot([u()],H.prototype,"inputRightPadding",void 0);ot([u()],H.prototype,"tabIdx",void 0);H=ot([C("wui-input-text")],H);const xo=k`
  :host {
    position: relative;
    display: inline-block;
    width: 100%;
  }
`;var Co=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let He=class extends E{constructor(){super(...arguments),this.inputComponentRef=oi()}render(){return c`
      <wui-input-text
        ${ri(this.inputComponentRef)}
        placeholder="Search wallet"
        icon="search"
        type="search"
        enterKeyHint="search"
        size="sm"
      >
        <wui-input-element @click=${this.clearValue} icon="close"></wui-input-element>
      </wui-input-text>
    `}clearValue(){const t=this.inputComponentRef.value,i=t==null?void 0:t.inputElementRef.value;i&&(i.value="",i.focus(),i.dispatchEvent(new Event("input")))}};He.styles=[P,xo];He=Co([C("wui-search-bar")],He);const $o=Pt`<svg  viewBox="0 0 48 54" fill="none">
  <path
    d="M43.4605 10.7248L28.0485 1.61089C25.5438 0.129705 22.4562 0.129705 19.9515 1.61088L4.53951 10.7248C2.03626 12.2051 0.5 14.9365 0.5 17.886V36.1139C0.5 39.0635 2.03626 41.7949 4.53951 43.2752L19.9515 52.3891C22.4562 53.8703 25.5438 53.8703 28.0485 52.3891L43.4605 43.2752C45.9637 41.7949 47.5 39.0635 47.5 36.114V17.8861C47.5 14.9365 45.9637 12.2051 43.4605 10.7248Z"
  />
</svg>`,Ro=k`
  :host {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 104px;
    row-gap: var(--wui-spacing-xs);
    padding: var(--wui-spacing-xs) 10px;
    background-color: var(--wui-color-gray-glass-002);
    border-radius: clamp(0px, var(--wui-border-radius-xs), 20px);
    position: relative;
  }

  wui-shimmer[data-type='network'] {
    border: none;
    -webkit-clip-path: var(--wui-path-network);
    clip-path: var(--wui-path-network);
  }

  svg {
    position: absolute;
    width: 48px;
    height: 54px;
    z-index: 1;
  }

  svg > path {
    stroke: var(--wui-color-gray-glass-010);
    stroke-width: 1px;
  }

  @media (max-width: 350px) {
    :host {
      width: 100%;
    }
  }
`;var qi=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let he=class extends E{constructor(){super(...arguments),this.type="wallet"}render(){return c`
      ${this.shimmerTemplate()}
      <wui-shimmer width="56px" height="20px" borderRadius="xs"></wui-shimmer>
    `}shimmerTemplate(){return this.type==="network"?c` <wui-shimmer
          data-type=${this.type}
          width="48px"
          height="54px"
          borderRadius="xs"
        ></wui-shimmer>
        ${$o}`:c`<wui-shimmer width="56px" height="56px" borderRadius="xs"></wui-shimmer>`}};he.styles=[P,U,Ro];qi([u()],he.prototype,"type",void 0);he=qi([C("wui-card-select-loader")],he);const Io=k`
  :host {
    display: grid;
    width: inherit;
    height: inherit;
  }
`;var K=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let O=class extends E{render(){return this.style.cssText=`
      grid-template-rows: ${this.gridTemplateRows};
      grid-template-columns: ${this.gridTemplateColumns};
      justify-items: ${this.justifyItems};
      align-items: ${this.alignItems};
      justify-content: ${this.justifyContent};
      align-content: ${this.alignContent};
      column-gap: ${this.columnGap&&`var(--wui-spacing-${this.columnGap})`};
      row-gap: ${this.rowGap&&`var(--wui-spacing-${this.rowGap})`};
      gap: ${this.gap&&`var(--wui-spacing-${this.gap})`};
      padding-top: ${this.padding&&st.getSpacingStyles(this.padding,0)};
      padding-right: ${this.padding&&st.getSpacingStyles(this.padding,1)};
      padding-bottom: ${this.padding&&st.getSpacingStyles(this.padding,2)};
      padding-left: ${this.padding&&st.getSpacingStyles(this.padding,3)};
      margin-top: ${this.margin&&st.getSpacingStyles(this.margin,0)};
      margin-right: ${this.margin&&st.getSpacingStyles(this.margin,1)};
      margin-bottom: ${this.margin&&st.getSpacingStyles(this.margin,2)};
      margin-left: ${this.margin&&st.getSpacingStyles(this.margin,3)};
    `,c`<slot></slot>`}};O.styles=[P,Io];K([u()],O.prototype,"gridTemplateRows",void 0);K([u()],O.prototype,"gridTemplateColumns",void 0);K([u()],O.prototype,"justifyItems",void 0);K([u()],O.prototype,"alignItems",void 0);K([u()],O.prototype,"justifyContent",void 0);K([u()],O.prototype,"alignContent",void 0);K([u()],O.prototype,"columnGap",void 0);K([u()],O.prototype,"rowGap",void 0);K([u()],O.prototype,"gap",void 0);K([u()],O.prototype,"padding",void 0);K([u()],O.prototype,"margin",void 0);O=K([C("wui-grid")],O);const Eo=k`
  button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 104px;
    row-gap: var(--wui-spacing-xs);
    padding: var(--wui-spacing-s) var(--wui-spacing-0);
    background-color: var(--wui-color-gray-glass-002);
    border-radius: clamp(0px, var(--wui-border-radius-xs), 20px);
    transition:
      color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: background-color, color, border-radius;
    outline: none;
    border: none;
  }

  button > wui-flex > wui-text {
    color: var(--wui-color-fg-100);
    max-width: 86px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    justify-content: center;
  }

  button > wui-flex > wui-text.certified {
    max-width: 66px;
  }

  button:hover:enabled {
    background-color: var(--wui-color-gray-glass-005);
  }

  button:disabled > wui-flex > wui-text {
    color: var(--wui-color-gray-glass-015);
  }

  [data-selected='true'] {
    background-color: var(--wui-color-accent-glass-020);
  }

  @media (hover: hover) and (pointer: fine) {
    [data-selected='true']:hover:enabled {
      background-color: var(--wui-color-accent-glass-015);
    }
  }

  [data-selected='true']:active:enabled {
    background-color: var(--wui-color-accent-glass-010);
  }

  @media (max-width: 350px) {
    button {
      width: 100%;
    }
  }
`;var Gt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let yt=class extends E{constructor(){super(),this.observer=new IntersectionObserver(()=>{}),this.visible=!1,this.imageSrc=void 0,this.imageLoading=!1,this.wallet=void 0,this.observer=new IntersectionObserver(t=>{t.forEach(i=>{i.isIntersecting?(this.visible=!0,this.fetchImageSrc()):this.visible=!1})},{threshold:.01})}firstUpdated(){this.observer.observe(this)}disconnectedCallback(){this.observer.disconnect()}render(){var i,r;const t=((i=this.wallet)==null?void 0:i.badge_type)==="certified";return c`
      <button>
        ${this.imageTemplate()}
        <wui-flex flexDirection="row" alignItems="center" justifyContent="center" gap="3xs">
          <wui-text
            variant="tiny-500"
            color="inherit"
            class=${y(t?"certified":void 0)}
            >${(r=this.wallet)==null?void 0:r.name}</wui-text
          >
          ${t?c`<wui-icon size="sm" name="walletConnectBrown"></wui-icon>`:null}
        </wui-flex>
      </button>
    `}imageTemplate(){var t,i;return!this.visible&&!this.imageSrc||this.imageLoading?this.shimmerTemplate():c`
      <wui-wallet-image
        size="md"
        imageSrc=${y(this.imageSrc)}
        name=${(t=this.wallet)==null?void 0:t.name}
        .installed=${(i=this.wallet)==null?void 0:i.installed}
        badgeSize="sm"
      >
      </wui-wallet-image>
    `}shimmerTemplate(){return c`<wui-shimmer width="56px" height="56px" borderRadius="xs"></wui-shimmer>`}async fetchImageSrc(){this.wallet&&(this.imageSrc=M.getWalletImage(this.wallet),!this.imageSrc&&(this.imageLoading=!0,this.imageSrc=await M.fetchWalletImage(this.wallet.image_id),this.imageLoading=!1))}};yt.styles=Eo;Gt([v()],yt.prototype,"visible",void 0);Gt([v()],yt.prototype,"imageSrc",void 0);Gt([v()],yt.prototype,"imageLoading",void 0);Gt([u()],yt.prototype,"wallet",void 0);yt=Gt([C("w3m-all-wallets-list-item")],yt);const Wo=k`
  wui-grid {
    max-height: clamp(360px, 400px, 80vh);
    overflow: scroll;
    scrollbar-width: none;
    grid-auto-rows: min-content;
    grid-template-columns: repeat(auto-fill, 104px);
  }

  @media (max-width: 350px) {
    wui-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  wui-grid[data-scroll='false'] {
    overflow: hidden;
  }

  wui-grid::-webkit-scrollbar {
    display: none;
  }

  wui-loading-spinner {
    padding-top: var(--wui-spacing-l);
    padding-bottom: var(--wui-spacing-l);
    justify-content: center;
    grid-column: 1 / span 4;
  }
`;var Bt=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};const mi="local-paginator";let pt=class extends E{constructor(){super(),this.unsubscribe=[],this.paginationObserver=void 0,this.loading=!_.state.wallets.length,this.wallets=_.state.wallets,this.recommended=_.state.recommended,this.featured=_.state.featured,this.filteredWallets=_.state.filteredWallets,this.unsubscribe.push(_.subscribeKey("wallets",t=>this.wallets=t),_.subscribeKey("recommended",t=>this.recommended=t),_.subscribeKey("featured",t=>this.featured=t),_.subscribeKey("filteredWallets",t=>this.filteredWallets=t))}firstUpdated(){this.initialFetch(),this.createPaginationObserver()}disconnectedCallback(){var t;this.unsubscribe.forEach(i=>i()),(t=this.paginationObserver)==null||t.disconnect()}render(){return c`
      <wui-grid
        data-scroll=${!this.loading}
        .padding=${["0","s","s","s"]}
        columnGap="xxs"
        rowGap="l"
        justifyContent="space-between"
      >
        ${this.loading?this.shimmerTemplate(16):this.walletsTemplate()}
        ${this.paginationLoaderTemplate()}
      </wui-grid>
    `}async initialFetch(){var i;this.loading=!0;const t=(i=this.shadowRoot)==null?void 0:i.querySelector("wui-grid");t&&(await _.fetchWalletsByPage({page:1}),await t.animate([{opacity:1},{opacity:0}],{duration:200,fill:"forwards",easing:"ease"}).finished,this.loading=!1,t.animate([{opacity:0},{opacity:1}],{duration:200,fill:"forwards",easing:"ease"}))}shimmerTemplate(t,i){return[...Array(t)].map(()=>c`
        <wui-card-select-loader type="wallet" id=${y(i)}></wui-card-select-loader>
      `)}walletsTemplate(){var r;const t=((r=this.filteredWallets)==null?void 0:r.length)>0?W.uniqueBy([...this.featured,...this.recommended,...this.filteredWallets],"id"):W.uniqueBy([...this.featured,...this.recommended,...this.wallets],"id");return ge.markWalletsAsInstalled(t).map(n=>c`
        <w3m-all-wallets-list-item
          @click=${()=>this.onConnectWallet(n)}
          .wallet=${n}
        ></w3m-all-wallets-list-item>
      `)}paginationLoaderTemplate(){const{wallets:t,recommended:i,featured:r,count:n}=_.state,e=window.innerWidth<352?3:4,a=t.length+i.length;let l=Math.ceil(a/e)*e-a+e;return l-=t.length?r.length%e:0,n===0&&r.length>0?null:n===0||[...r,...t,...i].length<n?this.shimmerTemplate(l,mi):null}createPaginationObserver(){var i;const t=(i=this.shadowRoot)==null?void 0:i.querySelector(`#${mi}`);t&&(this.paginationObserver=new IntersectionObserver(([r])=>{if(r!=null&&r.isIntersecting&&!this.loading){const{page:n,count:e,wallets:a}=_.state;a.length<e&&_.fetchWalletsByPage({page:n+1})}}),this.paginationObserver.observe(t))}onConnectWallet(t){L.selectWalletConnector(t)}};pt.styles=Wo;Bt([v()],pt.prototype,"loading",void 0);Bt([v()],pt.prototype,"wallets",void 0);Bt([v()],pt.prototype,"recommended",void 0);Bt([v()],pt.prototype,"featured",void 0);Bt([v()],pt.prototype,"filteredWallets",void 0);pt=Bt([C("w3m-all-wallets-list")],pt);const So=k`
  wui-grid,
  wui-loading-spinner,
  wui-flex {
    height: 360px;
  }

  wui-grid {
    overflow: scroll;
    scrollbar-width: none;
    grid-auto-rows: min-content;
    grid-template-columns: repeat(auto-fill, 104px);
  }

  wui-grid[data-scroll='false'] {
    overflow: hidden;
  }

  wui-grid::-webkit-scrollbar {
    display: none;
  }

  wui-loading-spinner {
    justify-content: center;
    align-items: center;
  }

  @media (max-width: 350px) {
    wui-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
`;var Ee=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let _t=class extends E{constructor(){super(...arguments),this.prevQuery="",this.prevBadge=void 0,this.loading=!0,this.query=""}render(){return this.onSearch(),this.loading?c`<wui-loading-spinner color="accent-100"></wui-loading-spinner>`:this.walletsTemplate()}async onSearch(){(this.query.trim()!==this.prevQuery.trim()||this.badge!==this.prevBadge)&&(this.prevQuery=this.query,this.prevBadge=this.badge,this.loading=!0,await _.searchWallet({search:this.query,badge:this.badge}),this.loading=!1)}walletsTemplate(){const{search:t}=_.state,i=ge.markWalletsAsInstalled(t);return t.length?c`
      <wui-grid
        data-testid="wallet-list"
        .padding=${["0","s","s","s"]}
        rowGap="l"
        columnGap="xs"
        justifyContent="space-between"
      >
        ${i.map(r=>c`
            <w3m-all-wallets-list-item
              @click=${()=>this.onConnectWallet(r)}
              .wallet=${r}
              data-testid="wallet-search-item-${r.id}"
            ></w3m-all-wallets-list-item>
          `)}
      </wui-grid>
    `:c`
        <wui-flex
          data-testid="no-wallet-found"
          justifyContent="center"
          alignItems="center"
          gap="s"
          flexDirection="column"
        >
          <wui-icon-box
            size="lg"
            iconColor="fg-200"
            backgroundColor="fg-300"
            icon="wallet"
            background="transparent"
          ></wui-icon-box>
          <wui-text data-testid="no-wallet-found-text" color="fg-200" variant="paragraph-500">
            No Wallet found
          </wui-text>
        </wui-flex>
      `}onConnectWallet(t){L.selectWalletConnector(t)}};_t.styles=So;Ee([v()],_t.prototype,"loading",void 0);Ee([u()],_t.prototype,"query",void 0);Ee([u()],_t.prototype,"badge",void 0);_t=Ee([C("w3m-all-wallets-search")],_t);var ai=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let pe=class extends E{constructor(){super(...arguments),this.search="",this.onDebouncedSearch=W.debounce(t=>{this.search=t})}render(){const t=this.search.length>=2;return c`
      <wui-flex .padding=${["0","s","s","s"]} gap="xs">
        <wui-search-bar @inputChange=${this.onInputChange.bind(this)}></wui-search-bar>
        <wui-certified-switch
          ?checked=${this.badge}
          @click=${this.onClick.bind(this)}
          data-testid="wui-certified-switch"
        ></wui-certified-switch>
        ${this.qrButtonTemplate()}
      </wui-flex>
      ${t||this.badge?c`<w3m-all-wallets-search
            query=${this.search}
            badge=${y(this.badge)}
          ></w3m-all-wallets-search>`:c`<w3m-all-wallets-list badge=${y(this.badge)}></w3m-all-wallets-list>`}
    `}onInputChange(t){this.onDebouncedSearch(t.detail)}onClick(){if(this.badge==="certified"){this.badge=void 0;return}this.badge="certified",Qt.showSvg("Only WalletConnect certified",{icon:"walletConnectBrown",iconColor:"accent-100"})}qrButtonTemplate(){return W.isMobile()?c`
        <wui-icon-box
          size="lg"
          iconSize="xl"
          iconColor="accent-100"
          backgroundColor="accent-100"
          icon="qrCode"
          background="transparent"
          border
          borderColor="wui-accent-glass-010"
          @click=${this.onWalletConnectQr.bind(this)}
        ></wui-icon-box>
      `:null}onWalletConnectQr(){A.push("ConnectingWalletConnect")}};ai([v()],pe.prototype,"search",void 0);ai([v()],pe.prototype,"badge",void 0);pe=ai([C("w3m-all-wallets-view")],pe);const To=k`
  button {
    column-gap: var(--wui-spacing-s);
    padding: 11px 18px 11px var(--wui-spacing-s);
    width: 100%;
    background-color: var(--wui-color-gray-glass-002);
    border-radius: var(--wui-border-radius-xs);
    color: var(--wui-color-fg-250);
    transition:
      color var(--wui-ease-out-power-1) var(--wui-duration-md),
      background-color var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: color, background-color;
  }

  button[data-iconvariant='square'],
  button[data-iconvariant='square-blue'] {
    padding: 6px 18px 6px 9px;
  }

  button > wui-flex {
    flex: 1;
  }

  button > wui-image {
    width: 32px;
    height: 32px;
    box-shadow: 0 0 0 2px var(--wui-color-gray-glass-005);
    border-radius: var(--wui-border-radius-3xl);
  }

  button > wui-icon {
    width: 36px;
    height: 36px;
    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);
    will-change: opacity;
  }

  button > wui-icon-box[data-variant='blue'] {
    box-shadow: 0 0 0 2px var(--wui-color-accent-glass-005);
  }

  button > wui-icon-box[data-variant='overlay'] {
    box-shadow: 0 0 0 2px var(--wui-color-gray-glass-005);
  }

  button > wui-icon-box[data-variant='square-blue'] {
    border-radius: var(--wui-border-radius-3xs);
    position: relative;
    border: none;
    width: 36px;
    height: 36px;
  }

  button > wui-icon-box[data-variant='square-blue']::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: inherit;
    border: 1px solid var(--wui-color-accent-glass-010);
    pointer-events: none;
  }

  button > wui-icon:last-child {
    width: 14px;
    height: 14px;
  }

  button:disabled {
    color: var(--wui-color-gray-glass-020);
  }

  button[data-loading='true'] > wui-icon {
    opacity: 0;
  }

  wui-loading-spinner {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
  }
`;var X=function(o,t,i,r){var n=arguments.length,e=n<3?t:r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let F=class extends E{constructor(){super(...arguments),this.tabIdx=void 0,this.variant="icon",this.disabled=!1,this.imageSrc=void 0,this.alt=void 0,this.chevron=!1,this.loading=!1}render(){return c`
      <button
        ?disabled=${this.loading?!0:!!this.disabled}
        data-loading=${this.loading}
        data-iconvariant=${y(this.iconVariant)}
        tabindex=${y(this.tabIdx)}
      >
        ${this.loadingTemplate()} ${this.visualTemplate()}
        <wui-flex gap="3xs">
          <slot></slot>
        </wui-flex>
        ${this.chevronTemplate()}
      </button>
    `}visualTemplate(){if(this.variant==="image"&&this.imageSrc)return c`<wui-image src=${this.imageSrc} alt=${this.alt??"list item"}></wui-image>`;if(this.iconVariant==="square"&&this.icon&&this.variant==="icon")return c`<wui-icon name=${this.icon}></wui-icon>`;if(this.variant==="icon"&&this.icon&&this.iconVariant){const t=["blue","square-blue"].includes(this.iconVariant)?"accent-100":"fg-200",i=this.iconVariant==="square-blue"?"mdl":"md",r=this.iconSize?this.iconSize:i;return c`
        <wui-icon-box
          data-variant=${this.iconVariant}
          icon=${this.icon}
          iconSize=${r}
          background="transparent"
          iconColor=${t}
          backgroundColor=${t}
          size=${i}
        ></wui-icon-box>
      `}return null}loadingTemplate(){return this.loading?c`<wui-loading-spinner
        data-testid="wui-list-item-loading-spinner"
        color="fg-300"
      ></wui-loading-spinner>`:c``}chevronTemplate(){return this.chevron?c`<wui-icon size="inherit" color="fg-200" name="chevronRight"></wui-icon>`:null}};F.styles=[P,U,To];X([u()],F.prototype,"icon",void 0);X([u()],F.prototype,"iconSize",void 0);X([u()],F.prototype,"tabIdx",void 0);X([u()],F.prototype,"variant",void 0);X([u()],F.prototype,"iconVariant",void 0);X([u({type:Boolean})],F.prototype,"disabled",void 0);X([u()],F.prototype,"imageSrc",void 0);X([u()],F.prototype,"alt",void 0);X([u({type:Boolean})],F.prototype,"chevron",void 0);X([u({type:Boolean})],F.prototype,"loading",void 0);F=X([C("wui-list-item")],F);var _o=function(o,t,i,r){var n=arguments.length,e=n<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,i):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,t,i,r);else for(var s=o.length-1;s>=0;s--)(a=o[s])&&(e=(n<3?a(e):n>3?a(t,i,e):a(t,i))||e);return n>3&&e&&Object.defineProperty(t,i,e),e};let bi=class extends E{constructor(){var t;super(...arguments),this.wallet=(t=A.state.data)==null?void 0:t.wallet}render(){if(!this.wallet)throw new Error("w3m-downloads-view");return c`
      <wui-flex gap="xs" flexDirection="column" .padding=${["s","s","l","s"]}>
        ${this.chromeTemplate()} ${this.iosTemplate()} ${this.androidTemplate()}
        ${this.homepageTemplate()}
      </wui-flex>
    `}chromeTemplate(){var t;return(t=this.wallet)!=null&&t.chrome_store?c`<wui-list-item
      variant="icon"
      icon="chromeStore"
      iconVariant="square"
      @click=${this.onChromeStore.bind(this)}
      chevron
    >
      <wui-text variant="paragraph-500" color="fg-100">Chrome Extension</wui-text>
    </wui-list-item>`:null}iosTemplate(){var t;return(t=this.wallet)!=null&&t.app_store?c`<wui-list-item
      variant="icon"
      icon="appStore"
      iconVariant="square"
      @click=${this.onAppStore.bind(this)}
      chevron
    >
      <wui-text variant="paragraph-500" color="fg-100">iOS App</wui-text>
    </wui-list-item>`:null}androidTemplate(){var t;return(t=this.wallet)!=null&&t.play_store?c`<wui-list-item
      variant="icon"
      icon="playStore"
      iconVariant="square"
      @click=${this.onPlayStore.bind(this)}
      chevron
    >
      <wui-text variant="paragraph-500" color="fg-100">Android App</wui-text>
    </wui-list-item>`:null}homepageTemplate(){var t;return(t=this.wallet)!=null&&t.homepage?c`
      <wui-list-item
        variant="icon"
        icon="browser"
        iconVariant="square-blue"
        @click=${this.onHomePage.bind(this)}
        chevron
      >
        <wui-text variant="paragraph-500" color="fg-100">Website</wui-text>
      </wui-list-item>
    `:null}onChromeStore(){var t;(t=this.wallet)!=null&&t.chrome_store&&W.openHref(this.wallet.chrome_store,"_blank")}onAppStore(){var t;(t=this.wallet)!=null&&t.app_store&&W.openHref(this.wallet.app_store,"_blank")}onPlayStore(){var t;(t=this.wallet)!=null&&t.play_store&&W.openHref(this.wallet.play_store,"_blank")}onHomePage(){var t;(t=this.wallet)!=null&&t.homepage&&W.openHref(this.wallet.homepage,"_blank")}};bi=_o([C("w3m-downloads-view")],bi);export{pe as W3mAllWalletsView,qe as W3mConnectingWcBasicView,bi as W3mDownloadsView};
//# sourceMappingURL=basic-D4Ate8cw.js.map
