const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/secp256k1-mtaDJVEe.js","assets/index-2wea5Wgv.js","assets/index-CnEfukNX.css","assets/events-B2jzgt6q.js","assets/index.es-CAssemqx.js","assets/index-nibyPLVP.js","assets/basic-D4Ate8cw.js","assets/index-6jImYbgp.js","assets/w3m-modal-CGM7s3iG.js"])))=>i.map(i=>d[i]);
import{m as Kh,T as Vh,U as Gh,E as Jh,w as Yh,n as Xh,_ as _o,o as Yi,t as ka}from"./index-2wea5Wgv.js";import{e as er,N as ua}from"./events-B2jzgt6q.js";import{h as Xi,s as In,i as ha,I as Hr,j as Zh,k as Ot,l as dt,m as D,n as Rr,C as Qh,o as Xl,p as ed,q as td,u as mi,v as So,x as Zl,y as Li,A as Ql,E as rt,z as sd,B as Et,D as Fs,F as Kr,O as da,G as Tt,J as rd,K as pa,L as fa,M as $n,P as xa,Q as id,N as nd,R as Sr,S as eu,T as is,U as Ht,V as us,W as Bt}from"./index.es-CAssemqx.js";import{c as od,a as ad,k as cd,d as ld,s as ud,g as $a}from"./index-nibyPLVP.js";function Ra(t,e={}){const{key:s="fallback",name:r="Fallback",rank:i=!1,shouldThrow:n=hd,retryCount:o,retryDelay:a}=e;return({chain:c,pollingInterval:l=4e3,timeout:u,...h})=>{let d=t,p=()=>{};const w=Kh({key:s,name:r,async request({method:f,params:m}){let y;const b=async(v=0)=>{const C=d[v]({...h,chain:c,retryCount:0,timeout:u});try{const S=await C.request({method:f,params:m});return p({method:f,params:m,response:S,transport:C,status:"success"}),S}catch(S){if(p({error:S,method:f,params:m,transport:C,status:"error"}),n(S)||v===d.length-1||(y??(y=d.slice(v+1).some(I=>{const{include:_,exclude:U}=I({chain:c}).config.methods||{};return _?_.includes(f):U?!U.includes(f):!0})),!y))throw S;return b(v+1)}};return b()},retryCount:o,retryDelay:a,type:"fallback"},{onResponse:f=>p=f,transports:d.map(f=>f({chain:c,retryCount:0}))});if(i){const f=typeof i=="object"?i:{};dd({chain:c,interval:f.interval??l,onTransports:m=>d=m,ping:f.ping,sampleCount:f.sampleCount,timeout:f.timeout,transports:d,weights:f.weights})}return w}}function hd(t){return!!("code"in t&&typeof t.code=="number"&&(t.code===Vh.code||t.code===Gh.code||Jh.nodeMessage.test(t.message)||t.code===5e3))}function dd({chain:t,interval:e=4e3,onTransports:s,ping:r,sampleCount:i=10,timeout:n=1e3,transports:o,weights:a={}}){const{stability:c=.7,latency:l=.3}=a,u=[],h=async()=>{const d=await Promise.all(o.map(async f=>{const m=f({chain:t,retryCount:0,timeout:n}),y=Date.now();let b,v;try{await(r?r({transport:m}):m.request({method:"net_listening"})),v=1}catch{v=0}finally{b=Date.now()}return{latency:b-y,success:v}}));u.push(d),u.length>i&&u.shift();const p=Math.max(...u.map(f=>Math.max(...f.map(({latency:m})=>m)))),w=o.map((f,m)=>{const y=u.map(I=>I[m].latency),v=1-y.reduce((I,_)=>I+_,0)/y.length/p,C=u.map(I=>I[m].success),S=C.reduce((I,_)=>I+_,0)/C.length;return S===0?[0,m]:[l*v+c*S,m]}).sort((f,m)=>m[0]-f[0]);s(w.map(([,f])=>o[f])),await Yh(e),h()};h()}var Ua={};const W={WC_NAME_SUFFIX:".reown.id",WC_NAME_SUFFIX_LEGACY:".wcn.id",BLOCKCHAIN_API_RPC_URL:"https://rpc.walletconnect.org",PULSE_API_URL:"https://pulse.walletconnect.org",W3M_API_URL:"https://api.web3modal.org",CONNECTOR_ID:{WALLET_CONNECT:"walletConnect",INJECTED:"injected",WALLET_STANDARD:"announced",COINBASE:"coinbaseWallet",COINBASE_SDK:"coinbaseWalletSDK",SAFE:"safe",LEDGER:"ledger",OKX:"okx",EIP6963:"eip6963",AUTH:"ID_AUTH"},CONNECTOR_NAMES:{AUTH:"Auth"},AUTH_CONNECTOR_SUPPORTED_CHAINS:["eip155","solana"],LIMITS:{PENDING_TRANSACTIONS:99},CHAIN:{EVM:"eip155",SOLANA:"solana",POLKADOT:"polkadot",BITCOIN:"bip122"},CHAIN_NAME_MAP:{eip155:"EVM Networks",solana:"Solana",polkadot:"Polkadot",bip122:"Bitcoin",cosmos:"Cosmos"},ADAPTER_TYPES:{BITCOIN:"bitcoin",SOLANA:"solana",WAGMI:"wagmi",ETHERS:"ethers",ETHERS5:"ethers5"},USDT_CONTRACT_ADDRESSES:["******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************"],HTTP_STATUS_CODES:{SERVICE_UNAVAILABLE:503,FORBIDDEN:403},UNSUPPORTED_NETWORK_NAME:"Unknown Network",SECURE_SITE_SDK_ORIGIN:(typeof process<"u"&&typeof Ua<"u"?Ua.NEXT_PUBLIC_SECURE_SITE_ORIGIN:void 0)||"https://secure.walletconnect.org"},tu={caipNetworkIdToNumber(t){return t?Number(t.split(":")[1]):void 0},parseEvmChainId(t){return typeof t=="string"?this.caipNetworkIdToNumber(t):t},getNetworksByNamespace(t,e){return(t==null?void 0:t.filter(s=>s.chainNamespace===e))||[]},getFirstNetworkByNamespace(t,e){return this.getNetworksByNamespace(t,e)[0]},getNetworkNameByCaipNetworkId(t,e){var i;if(!e)return;const s=t.find(n=>n.caipNetworkId===e);if(s)return s.name;const[r]=e.split(":");return((i=W.CHAIN_NAME_MAP)==null?void 0:i[r])||void 0}};var pd=20,fd=1,Vs=1e6,Da=1e6,gd=-7,md=21,wd=!1,Mi="[big.js] ",tr=Mi+"Invalid ",Rn=tr+"decimal places",yd=tr+"rounding mode",su=Mi+"Division by zero",ge={},Gt=void 0,bd=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function ru(){function t(e){var s=this;if(!(s instanceof t))return e===Gt?ru():new t(e);if(e instanceof t)s.s=e.s,s.e=e.e,s.c=e.c.slice();else{if(typeof e!="string"){if(t.strict===!0&&typeof e!="bigint")throw TypeError(tr+"value");e=e===0&&1/e<0?"-0":String(e)}vd(s,e)}s.constructor=t}return t.prototype=ge,t.DP=pd,t.RM=fd,t.NE=gd,t.PE=md,t.strict=wd,t.roundDown=0,t.roundHalfUp=1,t.roundHalfEven=2,t.roundUp=3,t}function vd(t,e){var s,r,i;if(!bd.test(e))throw Error(tr+"number");for(t.s=e.charAt(0)=="-"?(e=e.slice(1),-1):1,(s=e.indexOf("."))>-1&&(e=e.replace(".","")),(r=e.search(/e/i))>0?(s<0&&(s=r),s+=+e.slice(r+1),e=e.substring(0,r)):s<0&&(s=e.length),i=e.length,r=0;r<i&&e.charAt(r)=="0";)++r;if(r==i)t.c=[t.e=0];else{for(;i>0&&e.charAt(--i)=="0";);for(t.e=s-r-1,t.c=[],s=0;r<=i;)t.c[s++]=+e.charAt(r++)}return t}function sr(t,e,s,r){var i=t.c;if(s===Gt&&(s=t.constructor.RM),s!==0&&s!==1&&s!==2&&s!==3)throw Error(yd);if(e<1)r=s===3&&(r||!!i[0])||e===0&&(s===1&&i[0]>=5||s===2&&(i[0]>5||i[0]===5&&(r||i[1]!==Gt))),i.length=1,r?(t.e=t.e-e+1,i[0]=1):i[0]=t.e=0;else if(e<i.length){if(r=s===1&&i[e]>=5||s===2&&(i[e]>5||i[e]===5&&(r||i[e+1]!==Gt||i[e-1]&1))||s===3&&(r||!!i[0]),i.length=e,r){for(;++i[--e]>9;)if(i[e]=0,e===0){++t.e,i.unshift(1);break}}for(e=i.length;!i[--e];)i.pop()}return t}function rr(t,e,s){var r=t.e,i=t.c.join(""),n=i.length;if(e)i=i.charAt(0)+(n>1?"."+i.slice(1):"")+(r<0?"e":"e+")+r;else if(r<0){for(;++r;)i="0"+i;i="0."+i}else if(r>0)if(++r>n)for(r-=n;r--;)i+="0";else r<n&&(i=i.slice(0,r)+"."+i.slice(r));else n>1&&(i=i.charAt(0)+"."+i.slice(1));return t.s<0&&s?"-"+i:i}ge.abs=function(){var t=new this.constructor(this);return t.s=1,t};ge.cmp=function(t){var e,s=this,r=s.c,i=(t=new s.constructor(t)).c,n=s.s,o=t.s,a=s.e,c=t.e;if(!r[0]||!i[0])return r[0]?n:i[0]?-o:0;if(n!=o)return n;if(e=n<0,a!=c)return a>c^e?1:-1;for(o=(a=r.length)<(c=i.length)?a:c,n=-1;++n<o;)if(r[n]!=i[n])return r[n]>i[n]^e?1:-1;return a==c?0:a>c^e?1:-1};ge.div=function(t){var e=this,s=e.constructor,r=e.c,i=(t=new s(t)).c,n=e.s==t.s?1:-1,o=s.DP;if(o!==~~o||o<0||o>Vs)throw Error(Rn);if(!i[0])throw Error(su);if(!r[0])return t.s=n,t.c=[t.e=0],t;var a,c,l,u,h,d=i.slice(),p=a=i.length,w=r.length,f=r.slice(0,a),m=f.length,y=t,b=y.c=[],v=0,C=o+(y.e=e.e-t.e)+1;for(y.s=n,n=C<0?0:C,d.unshift(0);m++<a;)f.push(0);do{for(l=0;l<10;l++){if(a!=(m=f.length))u=a>m?1:-1;else for(h=-1,u=0;++h<a;)if(i[h]!=f[h]){u=i[h]>f[h]?1:-1;break}if(u<0){for(c=m==a?i:d;m;){if(f[--m]<c[m]){for(h=m;h&&!f[--h];)f[h]=9;--f[h],f[m]+=10}f[m]-=c[m]}for(;!f[0];)f.shift()}else break}b[v++]=u?l:++l,f[0]&&u?f[m]=r[p]||0:f=[r[p]]}while((p++<w||f[0]!==Gt)&&n--);return!b[0]&&v!=1&&(b.shift(),y.e--,C--),v>C&&sr(y,C,s.RM,f[0]!==Gt),y};ge.eq=function(t){return this.cmp(t)===0};ge.gt=function(t){return this.cmp(t)>0};ge.gte=function(t){return this.cmp(t)>-1};ge.lt=function(t){return this.cmp(t)<0};ge.lte=function(t){return this.cmp(t)<1};ge.minus=ge.sub=function(t){var e,s,r,i,n=this,o=n.constructor,a=n.s,c=(t=new o(t)).s;if(a!=c)return t.s=-c,n.plus(t);var l=n.c.slice(),u=n.e,h=t.c,d=t.e;if(!l[0]||!h[0])return h[0]?t.s=-c:l[0]?t=new o(n):t.s=1,t;if(a=u-d){for((i=a<0)?(a=-a,r=l):(d=u,r=h),r.reverse(),c=a;c--;)r.push(0);r.reverse()}else for(s=((i=l.length<h.length)?l:h).length,a=c=0;c<s;c++)if(l[c]!=h[c]){i=l[c]<h[c];break}if(i&&(r=l,l=h,h=r,t.s=-t.s),(c=(s=h.length)-(e=l.length))>0)for(;c--;)l[e++]=0;for(c=e;s>a;){if(l[--s]<h[s]){for(e=s;e&&!l[--e];)l[e]=9;--l[e],l[s]+=10}l[s]-=h[s]}for(;l[--c]===0;)l.pop();for(;l[0]===0;)l.shift(),--d;return l[0]||(t.s=1,l=[d=0]),t.c=l,t.e=d,t};ge.mod=function(t){var e,s=this,r=s.constructor,i=s.s,n=(t=new r(t)).s;if(!t.c[0])throw Error(su);return s.s=t.s=1,e=t.cmp(s)==1,s.s=i,t.s=n,e?new r(s):(i=r.DP,n=r.RM,r.DP=r.RM=0,s=s.div(t),r.DP=i,r.RM=n,this.minus(s.times(t)))};ge.neg=function(){var t=new this.constructor(this);return t.s=-t.s,t};ge.plus=ge.add=function(t){var e,s,r,i=this,n=i.constructor;if(t=new n(t),i.s!=t.s)return t.s=-t.s,i.minus(t);var o=i.e,a=i.c,c=t.e,l=t.c;if(!a[0]||!l[0])return l[0]||(a[0]?t=new n(i):t.s=i.s),t;if(a=a.slice(),e=o-c){for(e>0?(c=o,r=l):(e=-e,r=a),r.reverse();e--;)r.push(0);r.reverse()}for(a.length-l.length<0&&(r=l,l=a,a=r),e=l.length,s=0;e;a[e]%=10)s=(a[--e]=a[e]+l[e]+s)/10|0;for(s&&(a.unshift(s),++c),e=a.length;a[--e]===0;)a.pop();return t.c=a,t.e=c,t};ge.pow=function(t){var e=this,s=new e.constructor("1"),r=s,i=t<0;if(t!==~~t||t<-Da||t>Da)throw Error(tr+"exponent");for(i&&(t=-t);t&1&&(r=r.times(e)),t>>=1,!!t;)e=e.times(e);return i?s.div(r):r};ge.prec=function(t,e){if(t!==~~t||t<1||t>Vs)throw Error(tr+"precision");return sr(new this.constructor(this),t,e)};ge.round=function(t,e){if(t===Gt)t=0;else if(t!==~~t||t<-Vs||t>Vs)throw Error(Rn);return sr(new this.constructor(this),t+this.e+1,e)};ge.sqrt=function(){var t,e,s,r=this,i=r.constructor,n=r.s,o=r.e,a=new i("0.5");if(!r.c[0])return new i(r);if(n<0)throw Error(Mi+"No square root");n=Math.sqrt(+rr(r,!0,!0)),n===0||n===1/0?(e=r.c.join(""),e.length+o&1||(e+="0"),n=Math.sqrt(e),o=((o+1)/2|0)-(o<0||o&1),t=new i((n==1/0?"5e":(n=n.toExponential()).slice(0,n.indexOf("e")+1))+o)):t=new i(n+""),o=t.e+(i.DP+=4);do s=t,t=a.times(s.plus(r.div(s)));while(s.c.slice(0,o).join("")!==t.c.slice(0,o).join(""));return sr(t,(i.DP-=4)+t.e+1,i.RM)};ge.times=ge.mul=function(t){var e,s=this,r=s.constructor,i=s.c,n=(t=new r(t)).c,o=i.length,a=n.length,c=s.e,l=t.e;if(t.s=s.s==t.s?1:-1,!i[0]||!n[0])return t.c=[t.e=0],t;for(t.e=c+l,o<a&&(e=i,i=n,n=e,l=o,o=a,a=l),e=new Array(l=o+a);l--;)e[l]=0;for(c=a;c--;){for(a=0,l=o+c;l>c;)a=e[l]+n[c]*i[l-c-1]+a,e[l--]=a%10,a=a/10|0;e[l]=a}for(a?++t.e:e.shift(),c=e.length;!e[--c];)e.pop();return t.c=e,t};ge.toExponential=function(t,e){var s=this,r=s.c[0];if(t!==Gt){if(t!==~~t||t<0||t>Vs)throw Error(Rn);for(s=sr(new s.constructor(s),++t,e);s.c.length<t;)s.c.push(0)}return rr(s,!0,!!r)};ge.toFixed=function(t,e){var s=this,r=s.c[0];if(t!==Gt){if(t!==~~t||t<0||t>Vs)throw Error(Rn);for(s=sr(new s.constructor(s),t+s.e+1,e),t=t+s.e+1;s.c.length<t;)s.c.push(0)}return rr(s,!1,!!r)};ge[Symbol.for("nodejs.util.inspect.custom")]=ge.toJSON=ge.toString=function(){var t=this,e=t.constructor;return rr(t,t.e<=e.NE||t.e>=e.PE,!!t.c[0])};ge.toNumber=function(){var t=+rr(this,!0,!0);if(this.constructor.strict===!0&&!this.eq(t.toString()))throw Error(Mi+"Imprecise conversion");return t};ge.toPrecision=function(t,e){var s=this,r=s.constructor,i=s.c[0];if(t!==Gt){if(t!==~~t||t<1||t>Vs)throw Error(tr+"precision");for(s=sr(new r(s),t,e);s.c.length<t;)s.c.push(0)}return rr(s,t<=s.e||s.e<=r.NE||s.e>=r.PE,!!i)};ge.valueOf=function(){var t=this,e=t.constructor;if(e.strict===!0)throw Error(Mi+"valueOf disallowed");return rr(t,t.e<=e.NE||t.e>=e.PE,!0)};var Qr=ru();const Ed={bigNumber(t){return t?new Qr(t):new Qr(0)},multiply(t,e){if(t===void 0||e===void 0)return new Qr(0);const s=new Qr(t),r=new Qr(e);return s.times(r)},formatNumberToLocalString(t,e=2){return t===void 0?"0.00":typeof t=="number"?t.toLocaleString("en-US",{maximumFractionDigits:e,minimumFractionDigits:e}):parseFloat(t).toLocaleString("en-US",{maximumFractionDigits:e,minimumFractionDigits:e})},parseLocalStringToNumber(t){return t===void 0?0:parseFloat(t.replace(/,/gu,""))}},Cd=[{type:"function",name:"transfer",stateMutability:"nonpayable",inputs:[{name:"_to",type:"address"},{name:"_value",type:"uint256"}],outputs:[{name:"",type:"bool"}]},{type:"function",name:"transferFrom",stateMutability:"nonpayable",inputs:[{name:"_from",type:"address"},{name:"_to",type:"address"},{name:"_value",type:"uint256"}],outputs:[{name:"",type:"bool"}]}],Id=[{type:"function",name:"approve",stateMutability:"nonpayable",inputs:[{name:"spender",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]}],Ad=[{type:"function",name:"transfer",stateMutability:"nonpayable",inputs:[{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[]},{type:"function",name:"transferFrom",stateMutability:"nonpayable",inputs:[{name:"sender",type:"address"},{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[{name:"",type:"bool"}]}],Nd={getERC20Abi:t=>W.USDT_CONTRACT_ADDRESSES.includes(t)?Ad:Cd,getSwapAbi:()=>Id},hs={validateCaipAddress(t){var e;if(((e=t.split(":"))==null?void 0:e.length)!==3)throw new Error("Invalid CAIP Address");return t},parseCaipAddress(t){const e=t.split(":");if(e.length!==3)throw new Error(`Invalid CAIP-10 address: ${t}`);const[s,r,i]=e;if(!s||!r||!i)throw new Error(`Invalid CAIP-10 address: ${t}`);return{chainNamespace:s,chainId:r,address:i}},parseCaipNetworkId(t){const e=t.split(":");if(e.length!==2)throw new Error(`Invalid CAIP-2 network id: ${t}`);const[s,r]=e;if(!s||!r)throw new Error(`Invalid CAIP-2 network id: ${t}`);return{chainNamespace:s,chainId:r}}},ee={WALLET_ID:"@appkit/wallet_id",WALLET_NAME:"@appkit/wallet_name",SOLANA_WALLET:"@appkit/solana_wallet",SOLANA_CAIP_CHAIN:"@appkit/solana_caip_chain",ACTIVE_CAIP_NETWORK_ID:"@appkit/active_caip_network_id",CONNECTED_SOCIAL:"@appkit/connected_social",CONNECTED_SOCIAL_USERNAME:"@appkit-wallet/SOCIAL_USERNAME",RECENT_WALLETS:"@appkit/recent_wallets",DEEPLINK_CHOICE:"WALLETCONNECT_DEEPLINK_CHOICE",ACTIVE_NAMESPACE:"@appkit/active_namespace",CONNECTED_NAMESPACES:"@appkit/connected_namespaces",CONNECTION_STATUS:"@appkit/connection_status",SIWX_AUTH_TOKEN:"@appkit/siwx-auth-token",SIWX_NONCE_TOKEN:"@appkit/siwx-nonce-token",TELEGRAM_SOCIAL_PROVIDER:"@appkit/social_provider",NATIVE_BALANCE_CACHE:"@appkit/native_balance_cache",PORTFOLIO_CACHE:"@appkit/portfolio_cache",ENS_CACHE:"@appkit/ens_cache",IDENTITY_CACHE:"@appkit/identity_cache",PREFERRED_ACCOUNT_TYPES:"@appkit/preferred_account_types",CONNECTIONS:"@appkit/connections"};function Hn(t){if(!t)throw new Error("Namespace is required for CONNECTED_CONNECTOR_ID");return`@appkit/${t}:connected_connector_id`}const Z={setItem(t,e){ui()&&e!==void 0&&localStorage.setItem(t,e)},getItem(t){if(ui())return localStorage.getItem(t)||void 0},removeItem(t){ui()&&localStorage.removeItem(t)},clear(){ui()&&localStorage.clear()}};function ui(){return typeof window<"u"&&typeof localStorage<"u"}function Es(t,e){return e==="light"?{"--w3m-accent":(t==null?void 0:t["--w3m-accent"])||"hsla(231, 100%, 70%, 1)","--w3m-background":"#fff"}:{"--w3m-accent":(t==null?void 0:t["--w3m-accent"])||"hsla(230, 100%, 67%, 1)","--w3m-background":"#121313"}}const _d=Symbol(),La=Object.getPrototypeOf,Po=new WeakMap,Sd=t=>t&&(Po.has(t)?Po.get(t):La(t)===Object.prototype||La(t)===Array.prototype),Pd=t=>Sd(t)&&t[_d]||null,Ma=(t,e=!0)=>{Po.set(t,e)},An={},Kn=t=>typeof t=="object"&&t!==null,ws=new WeakMap,hi=new WeakSet,Od=(t=Object.is,e=(l,u)=>new Proxy(l,u),s=l=>Kn(l)&&!hi.has(l)&&(Array.isArray(l)||!(Symbol.iterator in l))&&!(l instanceof WeakMap)&&!(l instanceof WeakSet)&&!(l instanceof Error)&&!(l instanceof Number)&&!(l instanceof Date)&&!(l instanceof String)&&!(l instanceof RegExp)&&!(l instanceof ArrayBuffer),r=l=>{switch(l.status){case"fulfilled":return l.value;case"rejected":throw l.reason;default:throw l}},i=new WeakMap,n=(l,u,h=r)=>{const d=i.get(l);if((d==null?void 0:d[0])===u)return d[1];const p=Array.isArray(l)?[]:Object.create(Object.getPrototypeOf(l));return Ma(p,!0),i.set(l,[u,p]),Reflect.ownKeys(l).forEach(w=>{if(Object.getOwnPropertyDescriptor(p,w))return;const f=Reflect.get(l,w),{enumerable:m}=Reflect.getOwnPropertyDescriptor(l,w),y={value:f,enumerable:m,configurable:!0};if(hi.has(f))Ma(f,!1);else if(f instanceof Promise)delete y.value,y.get=()=>h(f);else if(ws.has(f)){const[b,v]=ws.get(f);y.value=n(b,v(),h)}Object.defineProperty(p,w,y)}),Object.preventExtensions(p)},o=new WeakMap,a=[1,1],c=l=>{if(!Kn(l))throw new Error("object required");const u=o.get(l);if(u)return u;let h=a[0];const d=new Set,p=(E,$=++a[0])=>{h!==$&&(h=$,d.forEach(A=>A(E,$)))};let w=a[1];const f=(E=++a[1])=>(w!==E&&!d.size&&(w=E,y.forEach(([$])=>{const A=$[1](E);A>h&&(h=A)})),h),m=E=>($,A)=>{const L=[...$];L[1]=[E,...L[1]],p(L,A)},y=new Map,b=(E,$)=>{if((An?"production":void 0)!=="production"&&y.has(E))throw new Error("prop listener already exists");if(d.size){const A=$[3](m(E));y.set(E,[$,A])}else y.set(E,[$])},v=E=>{var $;const A=y.get(E);A&&(y.delete(E),($=A[1])==null||$.call(A))},C=E=>(d.add(E),d.size===1&&y.forEach(([A,L],H)=>{if((An?"production":void 0)!=="production"&&L)throw new Error("remove already exists");const N=A[3](m(H));y.set(H,[A,N])}),()=>{d.delete(E),d.size===0&&y.forEach(([A,L],H)=>{L&&(L(),y.set(H,[A]))})}),S=Array.isArray(l)?[]:Object.create(Object.getPrototypeOf(l)),_=e(S,{deleteProperty(E,$){const A=Reflect.get(E,$);v($);const L=Reflect.deleteProperty(E,$);return L&&p(["delete",[$],A]),L},set(E,$,A,L){const H=Reflect.has(E,$),N=Reflect.get(E,$,L);if(H&&(t(N,A)||o.has(A)&&t(N,o.get(A))))return!0;v($),Kn(A)&&(A=Pd(A)||A);let k=A;if(A instanceof Promise)A.then(O=>{A.status="fulfilled",A.value=O,p(["resolve",[$],O])}).catch(O=>{A.status="rejected",A.reason=O,p(["reject",[$],O])});else{!ws.has(A)&&s(A)&&(k=c(A));const O=!hi.has(k)&&ws.get(k);O&&b($,O)}return Reflect.set(E,$,k,L),p(["set",[$],A,N]),!0}});o.set(l,_);const U=[S,f,n,C];return ws.set(_,U),Reflect.ownKeys(l).forEach(E=>{const $=Object.getOwnPropertyDescriptor(l,E);"value"in $&&(_[E]=l[E],delete $.value,delete $.writable),Object.defineProperty(S,E,$)}),_})=>[c,ws,hi,t,e,s,r,i,n,o,a],[Td]=Od();function _e(t={}){return Td(t)}function Qe(t,e,s){const r=ws.get(t);(An?"production":void 0)!=="production"&&!r&&console.warn("Please use proxy object");let i;const n=[],o=r[3];let a=!1;const l=o(u=>{n.push(u),i||(i=Promise.resolve().then(()=>{i=void 0,a&&e(n.splice(0))}))});return a=!0,()=>{a=!1,l()}}function Si(t,e){const s=ws.get(t);(An?"production":void 0)!=="production"&&!s&&console.warn("Please use proxy object");const[r,i,n]=s;return n(r,i(),e)}function Gs(t){return hi.add(t),t}function et(t,e,s,r){let i=t[e];return Qe(t,()=>{const n=t[e];Object.is(i,n)||s(i=n)})}function kd(t){const e=_e({data:Array.from([]),has(s){return this.data.some(r=>r[0]===s)},set(s,r){const i=this.data.find(n=>n[0]===s);return i?i[1]=r:this.data.push([s,r]),this},get(s){var r;return(r=this.data.find(i=>i[0]===s))==null?void 0:r[1]},delete(s){const r=this.data.findIndex(i=>i[0]===s);return r===-1?!1:(this.data.splice(r,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},toJSON(){return new Map(this.data)},forEach(s){this.data.forEach(r=>{s(r[1],r[0],this)})},keys(){return this.data.map(s=>s[0]).values()},values(){return this.data.map(s=>s[1]).values()},entries(){return new Map(this.data).entries()},get[Symbol.toStringTag](){return"Map"},[Symbol.iterator](){return this.entries()}});return Object.defineProperties(e,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(e),e}const iu=[{label:"Coinbase",name:"coinbase",feeRange:"1-2%",url:"",supportedChains:["eip155"]},{label:"Meld.io",name:"meld",feeRange:"1-2%",url:"https://meldcrypto.com",supportedChains:["eip155","solana"]}],xd="WXETMuFUQmqqybHuRkSgxv:25B8LJHSfpG6LVjR2ytU5Cwh7Z4Sch2ocoU",Ee={FOUR_MINUTES_MS:24e4,TEN_SEC_MS:1e4,ONE_SEC_MS:1e3,BALANCE_SUPPORTED_CHAINS:["eip155","solana"],NAMES_SUPPORTED_CHAIN_NAMESPACES:["eip155"],NATIVE_TOKEN_ADDRESS:{eip155:"******************************************",solana:"So11111111111111111111111111111111111111111",polkadot:"0x",bip122:"0x",cosmos:"0x"},CONVERT_SLIPPAGE_TOLERANCE:1,CONNECT_LABELS:{MOBILE:"Open and continue in the wallet app"},SEND_SUPPORTED_NAMESPACES:["eip155","solana"],DEFAULT_REMOTE_FEATURES:{swaps:["1inch"],onramp:["coinbase","meld"],email:!0,socials:["google","x","discord","farcaster","github","apple","facebook"],activity:!0,reownBranding:!0},DEFAULT_REMOTE_FEATURES_DISABLED:{email:!1,socials:!1,swaps:!1,onramp:!1,activity:!1,reownBranding:!1},DEFAULT_FEATURES:{receive:!0,send:!0,emailShowWallets:!0,connectorTypeOrder:["walletConnect","recent","injected","featured","custom","external","recommended"],analytics:!0,allWallets:!0,legalCheckbox:!1,smartSessions:!1,collapseWallets:!1,walletFeaturesOrder:["onramp","swaps","receive","send"],connectMethodsOrder:void 0,pay:!1},DEFAULT_ACCOUNT_TYPES:{bip122:"payment",eip155:"smartAccount",polkadot:"eoa",solana:"eoa"},ADAPTER_TYPES:{UNIVERSAL:"universal"}},F={cacheExpiry:{portfolio:3e4,nativeBalance:3e4,ens:3e5,identity:3e5},isCacheExpired(t,e){return Date.now()-t>e},getActiveNetworkProps(){const t=F.getActiveNamespace(),e=F.getActiveCaipNetworkId(),s=e?e.split(":")[1]:void 0,r=s?isNaN(Number(s))?s:Number(s):void 0;return{namespace:t,caipNetworkId:e,chainId:r}},setWalletConnectDeepLink({name:t,href:e}){try{Z.setItem(ee.DEEPLINK_CHOICE,JSON.stringify({href:e,name:t}))}catch{console.info("Unable to set WalletConnect deep link")}},getWalletConnectDeepLink(){try{const t=Z.getItem(ee.DEEPLINK_CHOICE);if(t)return JSON.parse(t)}catch{console.info("Unable to get WalletConnect deep link")}},deleteWalletConnectDeepLink(){try{Z.removeItem(ee.DEEPLINK_CHOICE)}catch{console.info("Unable to delete WalletConnect deep link")}},setActiveNamespace(t){try{Z.setItem(ee.ACTIVE_NAMESPACE,t)}catch{console.info("Unable to set active namespace")}},setActiveCaipNetworkId(t){try{Z.setItem(ee.ACTIVE_CAIP_NETWORK_ID,t),F.setActiveNamespace(t.split(":")[0])}catch{console.info("Unable to set active caip network id")}},getActiveCaipNetworkId(){try{return Z.getItem(ee.ACTIVE_CAIP_NETWORK_ID)}catch{console.info("Unable to get active caip network id");return}},deleteActiveCaipNetworkId(){try{Z.removeItem(ee.ACTIVE_CAIP_NETWORK_ID)}catch{console.info("Unable to delete active caip network id")}},deleteConnectedConnectorId(t){try{const e=Hn(t);Z.removeItem(e)}catch{console.info("Unable to delete connected connector id")}},setAppKitRecent(t){try{const e=F.getRecentWallets();e.find(r=>r.id===t.id)||(e.unshift(t),e.length>2&&e.pop(),Z.setItem(ee.RECENT_WALLETS,JSON.stringify(e)))}catch{console.info("Unable to set AppKit recent")}},getRecentWallets(){try{const t=Z.getItem(ee.RECENT_WALLETS);return t?JSON.parse(t):[]}catch{console.info("Unable to get AppKit recent")}return[]},setConnectedConnectorId(t,e){try{const s=Hn(t);Z.setItem(s,e)}catch{console.info("Unable to set Connected Connector Id")}},getActiveNamespace(){try{return Z.getItem(ee.ACTIVE_NAMESPACE)}catch{console.info("Unable to get active namespace")}},getConnectedConnectorId(t){if(t)try{const e=Hn(t);return Z.getItem(e)}catch{console.info("Unable to get connected connector id in namespace ",t)}},setConnectedSocialProvider(t){try{Z.setItem(ee.CONNECTED_SOCIAL,t)}catch{console.info("Unable to set connected social provider")}},getConnectedSocialProvider(){try{return Z.getItem(ee.CONNECTED_SOCIAL)}catch{console.info("Unable to get connected social provider")}},deleteConnectedSocialProvider(){try{Z.removeItem(ee.CONNECTED_SOCIAL)}catch{console.info("Unable to delete connected social provider")}},getConnectedSocialUsername(){try{return Z.getItem(ee.CONNECTED_SOCIAL_USERNAME)}catch{console.info("Unable to get connected social username")}},getStoredActiveCaipNetworkId(){var s;const t=Z.getItem(ee.ACTIVE_CAIP_NETWORK_ID);return(s=t==null?void 0:t.split(":"))==null?void 0:s[1]},setConnectionStatus(t){try{Z.setItem(ee.CONNECTION_STATUS,t)}catch{console.info("Unable to set connection status")}},getConnectionStatus(){try{return Z.getItem(ee.CONNECTION_STATUS)}catch{return}},getConnectedNamespaces(){try{const t=Z.getItem(ee.CONNECTED_NAMESPACES);return t!=null&&t.length?t.split(","):[]}catch{return[]}},setConnectedNamespaces(t){try{const e=Array.from(new Set(t));Z.setItem(ee.CONNECTED_NAMESPACES,e.join(","))}catch{console.info("Unable to set namespaces in storage")}},addConnectedNamespace(t){try{const e=F.getConnectedNamespaces();e.includes(t)||(e.push(t),F.setConnectedNamespaces(e))}catch{console.info("Unable to add connected namespace")}},removeConnectedNamespace(t){try{const e=F.getConnectedNamespaces(),s=e.indexOf(t);s>-1&&(e.splice(s,1),F.setConnectedNamespaces(e))}catch{console.info("Unable to remove connected namespace")}},getTelegramSocialProvider(){try{return Z.getItem(ee.TELEGRAM_SOCIAL_PROVIDER)}catch{return console.info("Unable to get telegram social provider"),null}},setTelegramSocialProvider(t){try{Z.setItem(ee.TELEGRAM_SOCIAL_PROVIDER,t)}catch{console.info("Unable to set telegram social provider")}},removeTelegramSocialProvider(){try{Z.removeItem(ee.TELEGRAM_SOCIAL_PROVIDER)}catch{console.info("Unable to remove telegram social provider")}},getBalanceCache(){let t={};try{const e=Z.getItem(ee.PORTFOLIO_CACHE);t=e?JSON.parse(e):{}}catch{console.info("Unable to get balance cache")}return t},removeAddressFromBalanceCache(t){try{const e=F.getBalanceCache();Z.setItem(ee.PORTFOLIO_CACHE,JSON.stringify({...e,[t]:void 0}))}catch{console.info("Unable to remove address from balance cache",t)}},getBalanceCacheForCaipAddress(t){try{const s=F.getBalanceCache()[t];if(s&&!this.isCacheExpired(s.timestamp,this.cacheExpiry.portfolio))return s.balance;F.removeAddressFromBalanceCache(t)}catch{console.info("Unable to get balance cache for address",t)}},updateBalanceCache(t){try{const e=F.getBalanceCache();e[t.caipAddress]=t,Z.setItem(ee.PORTFOLIO_CACHE,JSON.stringify(e))}catch{console.info("Unable to update balance cache",t)}},getNativeBalanceCache(){let t={};try{const e=Z.getItem(ee.NATIVE_BALANCE_CACHE);t=e?JSON.parse(e):{}}catch{console.info("Unable to get balance cache")}return t},removeAddressFromNativeBalanceCache(t){try{const e=F.getBalanceCache();Z.setItem(ee.NATIVE_BALANCE_CACHE,JSON.stringify({...e,[t]:void 0}))}catch{console.info("Unable to remove address from balance cache",t)}},getNativeBalanceCacheForCaipAddress(t){try{const s=F.getNativeBalanceCache()[t];if(s&&!this.isCacheExpired(s.timestamp,this.cacheExpiry.nativeBalance))return s;console.info("Discarding cache for address",t),F.removeAddressFromBalanceCache(t)}catch{console.info("Unable to get balance cache for address",t)}},updateNativeBalanceCache(t){try{const e=F.getNativeBalanceCache();e[t.caipAddress]=t,Z.setItem(ee.NATIVE_BALANCE_CACHE,JSON.stringify(e))}catch{console.info("Unable to update balance cache",t)}},getEnsCache(){let t={};try{const e=Z.getItem(ee.ENS_CACHE);t=e?JSON.parse(e):{}}catch{console.info("Unable to get ens name cache")}return t},getEnsFromCacheForAddress(t){try{const s=F.getEnsCache()[t];if(s&&!this.isCacheExpired(s.timestamp,this.cacheExpiry.ens))return s.ens;F.removeEnsFromCache(t)}catch{console.info("Unable to get ens name from cache",t)}},updateEnsCache(t){try{const e=F.getEnsCache();e[t.address]=t,Z.setItem(ee.ENS_CACHE,JSON.stringify(e))}catch{console.info("Unable to update ens name cache",t)}},removeEnsFromCache(t){try{const e=F.getEnsCache();Z.setItem(ee.ENS_CACHE,JSON.stringify({...e,[t]:void 0}))}catch{console.info("Unable to remove ens name from cache",t)}},getIdentityCache(){let t={};try{const e=Z.getItem(ee.IDENTITY_CACHE);t=e?JSON.parse(e):{}}catch{console.info("Unable to get identity cache")}return t},getIdentityFromCacheForAddress(t){try{const s=F.getIdentityCache()[t];if(s&&!this.isCacheExpired(s.timestamp,this.cacheExpiry.identity))return s.identity;F.removeIdentityFromCache(t)}catch{console.info("Unable to get identity from cache",t)}},updateIdentityCache(t){try{const e=F.getIdentityCache();e[t.address]={identity:t.identity,timestamp:t.timestamp},Z.setItem(ee.IDENTITY_CACHE,JSON.stringify(e))}catch{console.info("Unable to update identity cache",t)}},removeIdentityFromCache(t){try{const e=F.getIdentityCache();Z.setItem(ee.IDENTITY_CACHE,JSON.stringify({...e,[t]:void 0}))}catch{console.info("Unable to remove identity from cache",t)}},clearAddressCache(){try{Z.removeItem(ee.PORTFOLIO_CACHE),Z.removeItem(ee.NATIVE_BALANCE_CACHE),Z.removeItem(ee.ENS_CACHE),Z.removeItem(ee.IDENTITY_CACHE)}catch{console.info("Unable to clear address cache")}},setPreferredAccountTypes(t){try{Z.setItem(ee.PREFERRED_ACCOUNT_TYPES,JSON.stringify(t))}catch{console.info("Unable to set preferred account types",t)}},getPreferredAccountTypes(){try{const t=Z.getItem(ee.PREFERRED_ACCOUNT_TYPES);return t?JSON.parse(t):{}}catch{console.info("Unable to get preferred account types")}return{}},setConnections(t,e){try{const s={...F.getConnections(),[e]:t};Z.setItem(ee.CONNECTIONS,JSON.stringify(s))}catch(s){console.error("Unable to sync connections to storage",s)}},getConnections(){try{const t=Z.getItem(ee.CONNECTIONS);return t?JSON.parse(t):{}}catch(t){return console.error("Unable to get connections from storage",t),{}}}},X={isMobile(){var t;return this.isClient()?!!(typeof(window==null?void 0:window.matchMedia)=="function"&&((t=window==null?void 0:window.matchMedia("(pointer:coarse)"))!=null&&t.matches)||/Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini/u.test(navigator.userAgent)):!1},checkCaipNetwork(t,e=""){return t==null?void 0:t.caipNetworkId.toLocaleLowerCase().includes(e.toLowerCase())},isAndroid(){if(!this.isMobile())return!1;const t=window==null?void 0:window.navigator.userAgent.toLowerCase();return X.isMobile()&&t.includes("android")},isIos(){if(!this.isMobile())return!1;const t=window==null?void 0:window.navigator.userAgent.toLowerCase();return t.includes("iphone")||t.includes("ipad")},isSafari(){return this.isClient()?(window==null?void 0:window.navigator.userAgent.toLowerCase()).includes("safari"):!1},isClient(){return typeof window<"u"},isPairingExpired(t){return t?t-Date.now()<=Ee.TEN_SEC_MS:!0},isAllowedRetry(t,e=Ee.ONE_SEC_MS){return Date.now()-t>=e},copyToClopboard(t){navigator.clipboard.writeText(t)},isIframe(){try{return(window==null?void 0:window.self)!==(window==null?void 0:window.top)}catch{return!1}},isSafeApp(){var t,e;if(X.isClient()&&window.self!==window.top)try{const s=(e=(t=window==null?void 0:window.location)==null?void 0:t.ancestorOrigins)==null?void 0:e[0],r="https://app.safe.global";if(s){const i=new URL(s),n=new URL(r);return i.hostname===n.hostname}}catch{return!1}return!1},getPairingExpiry(){return Date.now()+Ee.FOUR_MINUTES_MS},getNetworkId(t){return t==null?void 0:t.split(":")[1]},getPlainAddress(t){return t==null?void 0:t.split(":")[2]},async wait(t){return new Promise(e=>{setTimeout(e,t)})},debounce(t,e=500){let s;return(...r)=>{function i(){t(...r)}s&&clearTimeout(s),s=setTimeout(i,e)}},isHttpUrl(t){return t.startsWith("http://")||t.startsWith("https://")},formatNativeUrl(t,e,s=null){if(X.isHttpUrl(t))return this.formatUniversalUrl(t,e);let r=t,i=s;r.includes("://")||(r=t.replaceAll("/","").replaceAll(":",""),r=`${r}://`),r.endsWith("/")||(r=`${r}/`),i&&!(i!=null&&i.endsWith("/"))&&(i=`${i}/`),this.isTelegram()&&this.isAndroid()&&(e=encodeURIComponent(e));const n=encodeURIComponent(e);return{redirect:`${r}wc?uri=${n}`,redirectUniversalLink:i?`${i}wc?uri=${n}`:void 0,href:r}},formatUniversalUrl(t,e){if(!X.isHttpUrl(t))return this.formatNativeUrl(t,e);let s=t;s.endsWith("/")||(s=`${s}/`);const r=encodeURIComponent(e);return{redirect:`${s}wc?uri=${r}`,href:s}},getOpenTargetForPlatform(t){return t==="popupWindow"?t:this.isTelegram()?F.getTelegramSocialProvider()?"_top":"_blank":t},openHref(t,e,s){window==null||window.open(t,this.getOpenTargetForPlatform(e),s||"noreferrer noopener")},returnOpenHref(t,e,s){return window==null?void 0:window.open(t,this.getOpenTargetForPlatform(e),s||"noreferrer noopener")},isTelegram(){return typeof window<"u"&&(!!window.TelegramWebviewProxy||!!window.Telegram||!!window.TelegramWebviewProxyProto)},isPWA(){var s,r,i;if(typeof window>"u")return!1;const t=(r=(s=window.matchMedia)==null?void 0:s.call(window,"(display-mode: standalone)"))==null?void 0:r.matches,e=(i=window==null?void 0:window.navigator)==null?void 0:i.standalone;return!!(t||e)},async preloadImage(t){const e=new Promise((s,r)=>{const i=new Image;i.onload=s,i.onerror=r,i.crossOrigin="anonymous",i.src=t});return Promise.race([e,X.wait(2e3)])},formatBalance(t,e){let s="0.000";if(typeof t=="string"){const r=Number(t);if(r){const i=Math.floor(r*1e3)/1e3;i&&(s=i.toString())}}return`${s}${e?` ${e}`:""}`},formatBalance2(t,e){var r;let s;if(t==="0")s="0";else if(typeof t=="string"){const i=Number(t);i&&(s=(r=i.toString().match(/^-?\d+(?:\.\d{0,3})?/u))==null?void 0:r[0])}return{value:s??"0",rest:s==="0"?"000":"",symbol:e}},getApiUrl(){return W.W3M_API_URL},getBlockchainApiUrl(){return W.BLOCKCHAIN_API_RPC_URL},getAnalyticsUrl(){return W.PULSE_API_URL},getUUID(){return crypto!=null&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)})},parseError(t){var e,s;return typeof t=="string"?t:typeof((s=(e=t==null?void 0:t.issues)==null?void 0:e[0])==null?void 0:s.message)=="string"?t.issues[0].message:t instanceof Error?t.message:"Unknown error"},sortRequestedNetworks(t,e=[]){const s={};return e&&t&&(t.forEach((r,i)=>{s[r]=i}),e.sort((r,i)=>{const n=s[r.id],o=s[i.id];return n!==void 0&&o!==void 0?n-o:n!==void 0?-1:o!==void 0?1:0})),e},calculateBalance(t){let e=0;for(const s of t)e+=s.value??0;return e},formatTokenBalance(t){const e=t.toFixed(2),[s,r]=e.split(".");return{dollars:s,pennies:r}},isAddress(t,e="eip155"){switch(e){case"eip155":if(/^(?:0x)?[0-9a-f]{40}$/iu.test(t)){if(/^(?:0x)?[0-9a-f]{40}$/iu.test(t)||/^(?:0x)?[0-9A-F]{40}$/iu.test(t))return!0}else return!1;return!1;case"solana":return/[1-9A-HJ-NP-Za-km-z]{32,44}$/iu.test(t);default:return!1}},uniqueBy(t,e){const s=new Set;return t.filter(r=>{const i=r[e];return s.has(i)?!1:(s.add(i),!0)})},generateSdkVersion(t,e,s){const i=t.length===0?Ee.ADAPTER_TYPES.UNIVERSAL:t.map(n=>n.adapterType).join(",");return`${e}-${i}-${s}`},createAccount(t,e,s,r,i){return{namespace:t,address:e,type:s,publicKey:r,path:i}},isCaipAddress(t){if(typeof t!="string")return!1;const e=t.split(":"),s=e[0];return e.filter(Boolean).length===3&&s in W.CHAIN_NAME_MAP},isMac(){const t=window==null?void 0:window.navigator.userAgent.toLowerCase();return t.includes("macintosh")&&!t.includes("safari")},formatTelegramSocialLoginUrl(t){const e=`--${encodeURIComponent(window==null?void 0:window.location.href)}`,s="state=";if(new URL(t).host==="auth.magic.link"){const i="provider_authorization_url=",n=t.substring(t.indexOf(i)+i.length),o=this.injectIntoUrl(decodeURIComponent(n),s,e);return t.replace(n,encodeURIComponent(o))}return this.injectIntoUrl(t,s,e)},injectIntoUrl(t,e,s){const r=t.indexOf(e);if(r===-1)throw new Error(`${e} parameter not found in the URL: ${t}`);const i=t.indexOf("&",r),n=e.length,o=i!==-1?i:t.length,a=t.substring(0,r+n),c=t.substring(r+n,o),l=t.substring(i),u=c+s;return a+u+l}};async function ei(...t){const e=await fetch(...t);if(!e.ok)throw new Error(`HTTP status code: ${e.status}`,{cause:e});return e}class Bi{constructor({baseUrl:e,clientId:s}){this.baseUrl=e,this.clientId=s}async get({headers:e,signal:s,cache:r,...i}){const n=this.createUrl(i);return(await ei(n,{method:"GET",headers:e,signal:s,cache:r})).json()}async getBlob({headers:e,signal:s,...r}){const i=this.createUrl(r);return(await ei(i,{method:"GET",headers:e,signal:s})).blob()}async post({body:e,headers:s,signal:r,...i}){const n=this.createUrl(i);return(await ei(n,{method:"POST",headers:s,body:e?JSON.stringify(e):void 0,signal:r})).json()}async put({body:e,headers:s,signal:r,...i}){const n=this.createUrl(i);return(await ei(n,{method:"PUT",headers:s,body:e?JSON.stringify(e):void 0,signal:r})).json()}async delete({body:e,headers:s,signal:r,...i}){const n=this.createUrl(i);return(await ei(n,{method:"DELETE",headers:s,body:e?JSON.stringify(e):void 0,signal:r})).json()}createUrl({path:e,params:s}){const r=new URL(e,this.baseUrl);return s&&Object.entries(s).forEach(([i,n])=>{n&&r.searchParams.append(i,n)}),this.clientId&&r.searchParams.append("clientId",this.clientId),r}}const $d={getFeatureValue(t,e){const s=e==null?void 0:e[t];return s===void 0?Ee.DEFAULT_FEATURES[t]:s},filterSocialsByPlatform(t){if(!t||!t.length)return t;if(X.isTelegram()){if(X.isIos())return t.filter(e=>e!=="google");if(X.isMac())return t.filter(e=>e!=="x");if(X.isAndroid())return t.filter(e=>!["facebook","x"].includes(e))}return t}},V=_e({features:Ee.DEFAULT_FEATURES,projectId:"",sdkType:"appkit",sdkVersion:"html-wagmi-undefined",defaultAccountTypes:Ee.DEFAULT_ACCOUNT_TYPES,enableNetworkSwitch:!0,experimental_preferUniversalLinks:!1,remoteFeatures:{}}),T={state:V,subscribeKey(t,e){return et(V,t,e)},setOptions(t){Object.assign(V,t)},setRemoteFeatures(t){var s;if(!t)return;const e={...V.remoteFeatures,...t};V.remoteFeatures=e,(s=V.remoteFeatures)!=null&&s.socials&&(V.remoteFeatures.socials=$d.filterSocialsByPlatform(V.remoteFeatures.socials))},setFeatures(t){if(!t)return;V.features||(V.features=Ee.DEFAULT_FEATURES);const e={...V.features,...t};V.features=e},setProjectId(t){V.projectId=t},setCustomRpcUrls(t){V.customRpcUrls=t},setAllWallets(t){V.allWallets=t},setIncludeWalletIds(t){V.includeWalletIds=t},setExcludeWalletIds(t){V.excludeWalletIds=t},setFeaturedWalletIds(t){V.featuredWalletIds=t},setTokens(t){V.tokens=t},setTermsConditionsUrl(t){V.termsConditionsUrl=t},setPrivacyPolicyUrl(t){V.privacyPolicyUrl=t},setCustomWallets(t){V.customWallets=t},setIsSiweEnabled(t){V.isSiweEnabled=t},setIsUniversalProvider(t){V.isUniversalProvider=t},setSdkVersion(t){V.sdkVersion=t},setMetadata(t){V.metadata=t},setDisableAppend(t){V.disableAppend=t},setEIP6963Enabled(t){V.enableEIP6963=t},setDebug(t){V.debug=t},setEnableWalletConnect(t){V.enableWalletConnect=t},setEnableWalletGuide(t){V.enableWalletGuide=t},setEnableAuthLogger(t){V.enableAuthLogger=t},setEnableWallets(t){V.enableWallets=t},setPreferUniversalLinks(t){V.experimental_preferUniversalLinks=t},setHasMultipleAddresses(t){V.hasMultipleAddresses=t},setSIWX(t){V.siwx=t},setConnectMethodsOrder(t){V.features={...V.features,connectMethodsOrder:t}},setWalletFeaturesOrder(t){V.features={...V.features,walletFeaturesOrder:t}},setSocialsOrder(t){V.remoteFeatures={...V.remoteFeatures,socials:t}},setCollapseWallets(t){V.features={...V.features,collapseWallets:t}},setEnableEmbedded(t){V.enableEmbedded=t},setAllowUnsupportedChain(t){V.allowUnsupportedChain=t},setManualWCControl(t){V.manualWCControl=t},setEnableNetworkSwitch(t){V.enableNetworkSwitch=t},setDefaultAccountTypes(t={}){Object.entries(t).forEach(([e,s])=>{s&&(V.defaultAccountTypes[e]=s)})},setUniversalProviderConfigOverride(t){V.universalProviderConfigOverride=t},getUniversalProviderConfigOverride(){return V.universalProviderConfigOverride},getSnapshot(){return Si(V)}},Rd=Object.freeze({enabled:!0,events:[]}),Ud=new Bi({baseUrl:X.getAnalyticsUrl(),clientId:null}),Dd=5,Ld=60*1e3,ds=_e({...Rd}),Md={state:ds,subscribeKey(t,e){return et(ds,t,e)},async sendError(t,e){if(!ds.enabled)return;const s=Date.now();if(ds.events.filter(n=>{const o=new Date(n.properties.timestamp||"").getTime();return s-o<Ld}).length>=Dd)return;const i={type:"error",event:e,properties:{errorType:t.name,errorMessage:t.message,stackTrace:t.stack,timestamp:new Date().toISOString()}};ds.events.push(i);try{if(typeof window>"u")return;const{projectId:n,sdkType:o,sdkVersion:a}=T.state;await Ud.post({path:"/e",params:{projectId:n,st:o,sv:a||"html-wagmi-4.2.2"},body:{eventId:X.getUUID(),url:window.location.href,domain:window.location.hostname,timestamp:new Date().toISOString(),props:{type:"error",event:e,errorType:t.name,errorMessage:t.message,stackTrace:t.stack}}})}catch{}},enable(){ds.enabled=!0},disable(){ds.enabled=!1},clearEvents(){ds.events=[]}};class Ur extends Error{constructor(e,s,r){super(e),this.name="AppKitError",this.category=s,this.originalError=r,Object.setPrototypeOf(this,Ur.prototype);let i=!1;if(r instanceof Error&&typeof r.stack=="string"&&r.stack){const n=r.stack,o=n.indexOf(`
`);if(o>-1){const a=n.substring(o+1);this.stack=`${this.name}: ${this.message}
${a}`,i=!0}}i||(Error.captureStackTrace?Error.captureStackTrace(this,Ur):this.stack||(this.stack=`${this.name}: ${this.message}`))}}function Ba(t,e){const s=t instanceof Ur?t:new Ur(t instanceof Error?t.message:String(t),e,t);throw Md.sendError(s,s.category),s}function Ct(t,e="INTERNAL_SDK_ERROR"){const s={};return Object.keys(t).forEach(r=>{const i=t[r];if(typeof i=="function"){let n=i;i.constructor.name==="AsyncFunction"?n=async(...o)=>{try{return await i(...o)}catch(a){return Ba(a,e)}}:n=(...o)=>{try{return i(...o)}catch(a){return Ba(a,e)}},s[r]=n}else s[r]=i}),s}const Kt={PHANTOM:{id:"a797aa35c0fadbfc1a53e7f675162ed5226968b44a19ee3d24385c64d1d3c393",url:"https://phantom.app"},SOLFLARE:{id:"1ca0bdd4747578705b1939af023d120677c64fe6ca76add81fda36e350605e79",url:"https://solflare.com"},COINBASE:{id:"fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa",url:"https://go.cb-w.com"}},Bd={handleMobileDeeplinkRedirect(t,e){const s=window.location.href,r=encodeURIComponent(s);if(t===Kt.PHANTOM.id&&!("phantom"in window)){const i=s.startsWith("https")?"https":"http",n=s.split("/")[2],o=encodeURIComponent(`${i}://${n}`);window.location.href=`${Kt.PHANTOM.url}/ul/browse/${r}?ref=${o}`}t===Kt.SOLFLARE.id&&!("solflare"in window)&&(window.location.href=`${Kt.SOLFLARE.url}/ul/v1/browse/${r}?ref=${r}`),e===W.CHAIN.SOLANA&&t===Kt.COINBASE.id&&!("coinbaseSolana"in window)&&(window.location.href=`${Kt.COINBASE.url}/dapp?cb_url=${r}`)}},pt=_e({walletImages:{},networkImages:{},chainImages:{},connectorImages:{},tokenImages:{},currencyImages:{}}),jd={state:pt,subscribeNetworkImages(t){return Qe(pt.networkImages,()=>t(pt.networkImages))},subscribeKey(t,e){return et(pt,t,e)},subscribe(t){return Qe(pt,()=>t(pt))},setWalletImage(t,e){pt.walletImages[t]=e},setNetworkImage(t,e){pt.networkImages[t]=e},setChainImage(t,e){pt.chainImages[t]=e},setConnectorImage(t,e){pt.connectorImages={...pt.connectorImages,[t]:e}},setTokenImage(t,e){pt.tokenImages[t]=e},setCurrencyImage(t,e){pt.currencyImages[t]=e}},Dt=Ct(jd),qd={eip155:"ba0ba0cd-17c6-4806-ad93-f9d174f17900",solana:"a1b58899-f671-4276-6a5e-56ca5bd59700",polkadot:"",bip122:"0b4838db-0161-4ffe-022d-532bf03dba00",cosmos:""},Vn=_e({networkImagePromises:{}}),nu={async fetchWalletImage(t){if(t)return await K._fetchWalletImage(t),this.getWalletImageById(t)},async fetchNetworkImage(t){if(!t)return;const e=this.getNetworkImageById(t);return e||(Vn.networkImagePromises[t]||(Vn.networkImagePromises[t]=K._fetchNetworkImage(t)),await Vn.networkImagePromises[t],this.getNetworkImageById(t))},getWalletImageById(t){if(t)return Dt.state.walletImages[t]},getWalletImage(t){if(t!=null&&t.image_url)return t==null?void 0:t.image_url;if(t!=null&&t.image_id)return Dt.state.walletImages[t.image_id]},getNetworkImage(t){var e,s,r;if((e=t==null?void 0:t.assets)!=null&&e.imageUrl)return(s=t==null?void 0:t.assets)==null?void 0:s.imageUrl;if((r=t==null?void 0:t.assets)!=null&&r.imageId)return Dt.state.networkImages[t.assets.imageId]},getNetworkImageById(t){if(t)return Dt.state.networkImages[t]},getConnectorImage(t){if(t!=null&&t.imageUrl)return t.imageUrl;if(t!=null&&t.imageId)return Dt.state.connectorImages[t.imageId]},getChainImage(t){return Dt.state.networkImages[qd[t]]}},ps=_e({message:"",variant:"info",open:!1}),Fd={state:ps,subscribeKey(t,e){return et(ps,t,e)},open(t,e){const{debug:s}=T.state,{shortMessage:r,longMessage:i}=t;s&&(ps.message=r,ps.variant=e,ps.open=!0),i&&console.error(typeof i=="function"?i():i)},close(){ps.open=!1,ps.message="",ps.variant="info"}},Ms=Ct(Fd),zd=X.getAnalyticsUrl(),Wd=new Bi({baseUrl:zd,clientId:null}),Hd=["MODAL_CREATED"],es=_e({timestamp:Date.now(),reportedErrors:{},data:{type:"track",event:"MODAL_CREATED"}}),Pe={state:es,subscribe(t){return Qe(es,()=>t(es))},getSdkProperties(){const{projectId:t,sdkType:e,sdkVersion:s}=T.state;return{projectId:t,st:e,sv:s||"html-wagmi-4.2.2"}},async _sendAnalyticsEvent(t){try{const e=z.state.address;if(Hd.includes(t.data.event)||typeof window>"u")return;await Wd.post({path:"/e",params:Pe.getSdkProperties(),body:{eventId:X.getUUID(),url:window.location.href,domain:window.location.hostname,timestamp:t.timestamp,props:{...t.data,address:e}}}),es.reportedErrors.FORBIDDEN=!1}catch(e){e instanceof Error&&e.cause instanceof Response&&e.cause.status===W.HTTP_STATUS_CODES.FORBIDDEN&&!es.reportedErrors.FORBIDDEN&&(Ms.open({shortMessage:"Invalid App Configuration",longMessage:`Origin ${ui()?window.origin:"uknown"} not found on Allowlist - update configuration on cloud.reown.com`},"error"),es.reportedErrors.FORBIDDEN=!0)}},sendEvent(t){var e;es.timestamp=Date.now(),es.data=t,(e=T.state.features)!=null&&e.analytics&&Pe._sendAnalyticsEvent(es)}},Kd=X.getApiUrl(),it=new Bi({baseUrl:Kd,clientId:null}),Vd=40,ja=4,Gd=20,te=_e({promises:{},page:1,count:0,featured:[],allFeatured:[],recommended:[],allRecommended:[],wallets:[],filteredWallets:[],search:[],isAnalyticsEnabled:!1,excludedWallets:[],isFetchingRecommendedWallets:!1}),K={state:te,subscribeKey(t,e){return et(te,t,e)},_getSdkProperties(){const{projectId:t,sdkType:e,sdkVersion:s}=T.state;return{projectId:t,st:e||"appkit",sv:s||"html-wagmi-4.2.2"}},_filterOutExtensions(t){return T.state.isUniversalProvider?t.filter(e=>!!(e.mobile_link||e.desktop_link||e.webapp_link)):t},async _fetchWalletImage(t){const e=`${it.baseUrl}/getWalletImage/${t}`,s=await it.getBlob({path:e,params:K._getSdkProperties()});Dt.setWalletImage(t,URL.createObjectURL(s))},async _fetchNetworkImage(t){const e=`${it.baseUrl}/public/getAssetImage/${t}`,s=await it.getBlob({path:e,params:K._getSdkProperties()});Dt.setNetworkImage(t,URL.createObjectURL(s))},async _fetchConnectorImage(t){const e=`${it.baseUrl}/public/getAssetImage/${t}`,s=await it.getBlob({path:e,params:K._getSdkProperties()});Dt.setConnectorImage(t,URL.createObjectURL(s))},async _fetchCurrencyImage(t){const e=`${it.baseUrl}/public/getCurrencyImage/${t}`,s=await it.getBlob({path:e,params:K._getSdkProperties()});Dt.setCurrencyImage(t,URL.createObjectURL(s))},async _fetchTokenImage(t){const e=`${it.baseUrl}/public/getTokenImage/${t}`,s=await it.getBlob({path:e,params:K._getSdkProperties()});Dt.setTokenImage(t,URL.createObjectURL(s))},_filterWalletsByPlatform(t){return X.isMobile()?t==null?void 0:t.filter(s=>s.mobile_link||s.id===Kt.COINBASE.id?!0:g.state.activeChain==="solana"&&(s.id===Kt.SOLFLARE.id||s.id===Kt.PHANTOM.id)):t},async fetchProjectConfig(){return(await it.get({path:"/appkit/v1/config",params:K._getSdkProperties()})).features},async fetchAllowedOrigins(){try{const{allowedOrigins:t}=await it.get({path:"/projects/v1/origins",params:K._getSdkProperties()});return t}catch{return[]}},async fetchNetworkImages(){const t=g.getAllRequestedCaipNetworks(),e=t==null?void 0:t.map(({assets:s})=>s==null?void 0:s.imageId).filter(Boolean).filter(s=>!nu.getNetworkImageById(s));e&&await Promise.allSettled(e.map(s=>K._fetchNetworkImage(s)))},async fetchConnectorImages(){const{connectors:t}=j.state,e=t.map(({imageId:s})=>s).filter(Boolean);await Promise.allSettled(e.map(s=>K._fetchConnectorImage(s)))},async fetchCurrencyImages(t=[]){await Promise.allSettled(t.map(e=>K._fetchCurrencyImage(e)))},async fetchTokenImages(t=[]){await Promise.allSettled(t.map(e=>K._fetchTokenImage(e)))},async fetchWallets(t){var n;const e=t.exclude??[];K._getSdkProperties().sv.startsWith("html-core-")&&e.push(...Object.values(Kt).map(o=>o.id));const r=await it.get({path:"/getWallets",params:{...K._getSdkProperties(),...t,page:String(t.page),entries:String(t.entries),include:(n=t.include)==null?void 0:n.join(","),exclude:e.join(",")}});return{data:K._filterWalletsByPlatform(r==null?void 0:r.data)||[],count:r==null?void 0:r.count}},async fetchFeaturedWallets(){const{featuredWalletIds:t}=T.state;if(t!=null&&t.length){const e={...K._getSdkProperties(),page:1,entries:(t==null?void 0:t.length)??ja,include:t},{data:s}=await K.fetchWallets(e),r=[...s].sort((n,o)=>t.indexOf(n.id)-t.indexOf(o.id)),i=r.map(n=>n.image_id).filter(Boolean);await Promise.allSettled(i.map(n=>K._fetchWalletImage(n))),te.featured=r,te.allFeatured=r}},async fetchRecommendedWallets(){try{te.isFetchingRecommendedWallets=!0;const{includeWalletIds:t,excludeWalletIds:e,featuredWalletIds:s}=T.state,r=[...e??[],...s??[]].filter(Boolean),i=g.getRequestedCaipNetworkIds().join(","),n={page:1,entries:ja,include:t,exclude:r,chains:i},{data:o,count:a}=await K.fetchWallets(n),c=F.getRecentWallets(),l=o.map(h=>h.image_id).filter(Boolean),u=c.map(h=>h.image_id).filter(Boolean);await Promise.allSettled([...l,...u].map(h=>K._fetchWalletImage(h))),te.recommended=o,te.allRecommended=o,te.count=a??0}catch{}finally{te.isFetchingRecommendedWallets=!1}},async fetchWalletsByPage({page:t}){const{includeWalletIds:e,excludeWalletIds:s,featuredWalletIds:r}=T.state,i=g.getRequestedCaipNetworkIds().join(","),n=[...te.recommended.map(({id:u})=>u),...s??[],...r??[]].filter(Boolean),o={page:t,entries:Vd,include:e,exclude:n,chains:i},{data:a,count:c}=await K.fetchWallets(o),l=a.slice(0,Gd).map(u=>u.image_id).filter(Boolean);await Promise.allSettled(l.map(u=>K._fetchWalletImage(u))),te.wallets=X.uniqueBy([...te.wallets,...K._filterOutExtensions(a)],"id").filter(u=>{var h;return(h=u.chains)==null?void 0:h.some(d=>i.includes(d))}),te.count=c>te.count?c:te.count,te.page=t},async initializeExcludedWallets({ids:t}){const e={page:1,entries:t.length,include:t},{data:s}=await K.fetchWallets(e);s&&s.forEach(r=>{te.excludedWallets.push({rdns:r.rdns,name:r.name})})},async searchWallet({search:t,badge:e}){const{includeWalletIds:s,excludeWalletIds:r}=T.state,i=g.getRequestedCaipNetworkIds().join(",");te.search=[];const n={page:1,entries:100,search:t==null?void 0:t.trim(),badge_type:e,include:s,exclude:r,chains:i},{data:o}=await K.fetchWallets(n);Pe.sendEvent({type:"track",event:"SEARCH_WALLET",properties:{badge:e??"",search:t??""}});const a=o.map(c=>c.image_id).filter(Boolean);await Promise.allSettled([...a.map(c=>K._fetchWalletImage(c)),X.wait(300)]),te.search=K._filterOutExtensions(o)},initPromise(t,e){const s=te.promises[t];return s||(te.promises[t]=e())},prefetch({fetchConnectorImages:t=!0,fetchFeaturedWallets:e=!0,fetchRecommendedWallets:s=!0,fetchNetworkImages:r=!0}={}){const i=[t&&K.initPromise("connectorImages",K.fetchConnectorImages),e&&K.initPromise("featuredWallets",K.fetchFeaturedWallets),s&&K.initPromise("recommendedWallets",K.fetchRecommendedWallets),r&&K.initPromise("networkImages",K.fetchNetworkImages)].filter(Boolean);return Promise.allSettled(i)},prefetchAnalyticsConfig(){var t;(t=T.state.features)!=null&&t.analytics&&K.fetchAnalyticsConfig()},async fetchAnalyticsConfig(){try{const{isAnalyticsEnabled:t}=await it.get({path:"/getAnalyticsConfig",params:K._getSdkProperties()});T.setFeatures({analytics:t})}catch{T.setFeatures({analytics:!1})}},filterByNamespaces(t){if(!(t!=null&&t.length)){te.featured=te.allFeatured,te.recommended=te.allRecommended;return}const e=g.getRequestedCaipNetworkIds().join(",");te.featured=te.allFeatured.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))}),te.recommended=te.allRecommended.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))}),te.filteredWallets=te.wallets.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))})},clearFilterByNamespaces(){te.filteredWallets=[]},setFilterByNamespace(t){if(!t){te.featured=te.allFeatured,te.recommended=te.allRecommended;return}const e=g.getRequestedCaipNetworkIds().join(",");te.featured=te.allFeatured.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))}),te.recommended=te.allRecommended.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))}),te.filteredWallets=te.wallets.filter(s=>{var r;return(r=s.chains)==null?void 0:r.some(i=>e.includes(i))})}},me=_e({view:"Connect",history:["Connect"],transactionStack:[]}),Jd={state:me,subscribeKey(t,e){return et(me,t,e)},pushTransactionStack(t){me.transactionStack.push(t)},popTransactionStack(t){const e=me.transactionStack.pop();if(!e)return;const{onSuccess:s,onError:r,onCancel:i}=e;switch(t){case"success":s==null||s();break;case"error":r==null||r(),re.goBack();break;case"cancel":i==null||i(),re.goBack();break}},push(t,e){t!==me.view&&(me.view=t,me.history.push(t),me.data=e)},reset(t,e){me.view=t,me.history=[t],me.data=e},replace(t,e){me.history.at(-1)===t||(me.view=t,me.history[me.history.length-1]=t,me.data=e)},goBack(){var r;const t=g.state.activeCaipAddress,e=re.state.view==="ConnectingFarcaster",s=!t&&e;if(me.history.length>1){me.history.pop();const[i]=me.history.slice(-1);i&&(t&&i==="Connect"?me.view="Account":me.view=i)}else We.close();(r=me.data)!=null&&r.wallet&&(me.data.wallet=void 0),setTimeout(()=>{var i,n,o;if(s){z.setFarcasterUrl(void 0,g.state.activeChain);const a=j.getAuthConnector();(i=a==null?void 0:a.provider)==null||i.reload();const c=Si(T.state);(o=(n=a==null?void 0:a.provider)==null?void 0:n.syncDappData)==null||o.call(n,{metadata:c.metadata,sdkVersion:c.sdkVersion,projectId:c.projectId,sdkType:c.sdkType})}},100)},goBackToIndex(t){if(me.history.length>1){me.history=me.history.slice(0,t+1);const[e]=me.history.slice(-1);e&&(me.view=e)}},goBackOrCloseModal(){re.state.history.length>1?re.goBack():We.close()}},re=Ct(Jd),ts=_e({themeMode:"dark",themeVariables:{},w3mThemeVariables:void 0}),Oo={state:ts,subscribe(t){return Qe(ts,()=>t(ts))},setThemeMode(t){ts.themeMode=t;try{const e=j.getAuthConnector();if(e){const s=Oo.getSnapshot().themeVariables;e.provider.syncTheme({themeMode:t,themeVariables:s,w3mThemeVariables:Es(s,t)})}}catch{console.info("Unable to sync theme to auth connector")}},setThemeVariables(t){ts.themeVariables={...ts.themeVariables,...t};try{const e=j.getAuthConnector();if(e){const s=Oo.getSnapshot().themeVariables;e.provider.syncTheme({themeVariables:s,w3mThemeVariables:Es(ts.themeVariables,ts.themeMode)})}}catch{console.info("Unable to sync theme to auth connector")}},getSnapshot(){return Si(ts)}},mt=Ct(Oo),ou={eip155:void 0,solana:void 0,polkadot:void 0,bip122:void 0,cosmos:void 0},ae=_e({allConnectors:[],connectors:[],activeConnector:void 0,filterByNamespace:void 0,activeConnectorIds:{...ou},filterByNamespaceMap:{eip155:!0,solana:!0,polkadot:!0,bip122:!0,cosmos:!0}}),Yd={state:ae,subscribe(t){return Qe(ae,()=>{t(ae)})},subscribeKey(t,e){return et(ae,t,e)},initialize(t){t.forEach(e=>{const s=F.getConnectedConnectorId(e);s&&j.setConnectorId(s,e)})},setActiveConnector(t){t&&(ae.activeConnector=Gs(t))},setConnectors(t){t.filter(i=>!ae.allConnectors.some(n=>n.id===i.id&&j.getConnectorName(n.name)===j.getConnectorName(i.name)&&n.chain===i.chain)).forEach(i=>{i.type!=="MULTI_CHAIN"&&ae.allConnectors.push(Gs(i))});const s=j.getEnabledNamespaces(),r=j.getEnabledConnectors(s);ae.connectors=j.mergeMultiChainConnectors(r)},filterByNamespaces(t){Object.keys(ae.filterByNamespaceMap).forEach(e=>{ae.filterByNamespaceMap[e]=!1}),t.forEach(e=>{ae.filterByNamespaceMap[e]=!0}),j.updateConnectorsForEnabledNamespaces()},filterByNamespace(t,e){ae.filterByNamespaceMap[t]=e,j.updateConnectorsForEnabledNamespaces()},updateConnectorsForEnabledNamespaces(){const t=j.getEnabledNamespaces(),e=j.getEnabledConnectors(t),s=j.areAllNamespacesEnabled();ae.connectors=j.mergeMultiChainConnectors(e),s?K.clearFilterByNamespaces():K.filterByNamespaces(t)},getEnabledNamespaces(){return Object.entries(ae.filterByNamespaceMap).filter(([t,e])=>e).map(([t])=>t)},getEnabledConnectors(t){return ae.allConnectors.filter(e=>t.includes(e.chain))},areAllNamespacesEnabled(){return Object.values(ae.filterByNamespaceMap).every(t=>t)},mergeMultiChainConnectors(t){const e=j.generateConnectorMapByName(t),s=[];return e.forEach(r=>{const i=r[0],n=(i==null?void 0:i.id)===W.CONNECTOR_ID.AUTH;r.length>1&&i?s.push({name:i.name,imageUrl:i.imageUrl,imageId:i.imageId,connectors:[...r],type:n?"AUTH":"MULTI_CHAIN",chain:"eip155",id:(i==null?void 0:i.id)||""}):i&&s.push(i)}),s},generateConnectorMapByName(t){const e=new Map;return t.forEach(s=>{const{name:r}=s,i=j.getConnectorName(r);if(!i)return;const n=e.get(i)||[];n.find(a=>a.chain===s.chain)||n.push(s),e.set(i,n)}),e},getConnectorName(t){return t&&({"Trust Wallet":"Trust"}[t]||t)},getUniqueConnectorsByName(t){const e=[];return t.forEach(s=>{e.find(r=>r.chain===s.chain)||e.push(s)}),e},addConnector(t){var e,s,r;if(t.id===W.CONNECTOR_ID.AUTH){const i=t,n=Si(T.state),o=mt.getSnapshot().themeMode,a=mt.getSnapshot().themeVariables;(s=(e=i==null?void 0:i.provider)==null?void 0:e.syncDappData)==null||s.call(e,{metadata:n.metadata,sdkVersion:n.sdkVersion,projectId:n.projectId,sdkType:n.sdkType}),(r=i==null?void 0:i.provider)==null||r.syncTheme({themeMode:o,themeVariables:a,w3mThemeVariables:Es(a,o)}),j.setConnectors([t])}else j.setConnectors([t])},getAuthConnector(t){var r;const e=t||g.state.activeChain,s=ae.connectors.find(i=>i.id===W.CONNECTOR_ID.AUTH);if(s)return(r=s==null?void 0:s.connectors)!=null&&r.length?s.connectors.find(n=>n.chain===e):s},getAnnouncedConnectorRdns(){return ae.connectors.filter(t=>t.type==="ANNOUNCED").map(t=>{var e;return(e=t.info)==null?void 0:e.rdns})},getConnectorById(t){return ae.allConnectors.find(e=>e.id===t)},getConnector(t,e){return ae.allConnectors.filter(r=>r.chain===g.state.activeChain).find(r=>{var i;return r.explorerId===t||((i=r.info)==null?void 0:i.rdns)===e})},syncIfAuthConnector(t){var n,o;if(t.id!=="ID_AUTH")return;const e=t,s=Si(T.state),r=mt.getSnapshot().themeMode,i=mt.getSnapshot().themeVariables;(o=(n=e==null?void 0:e.provider)==null?void 0:n.syncDappData)==null||o.call(n,{metadata:s.metadata,sdkVersion:s.sdkVersion,sdkType:s.sdkType,projectId:s.projectId}),e.provider.syncTheme({themeMode:r,themeVariables:i,w3mThemeVariables:Es(i,r)})},getConnectorsByNamespace(t){const e=ae.allConnectors.filter(s=>s.chain===t);return j.mergeMultiChainConnectors(e)},selectWalletConnector(t){const e=j.getConnector(t.id,t.rdns),s=g.state.activeChain;Bd.handleMobileDeeplinkRedirect((e==null?void 0:e.explorerId)||t.id,s),e?re.push("ConnectingExternal",{connector:e}):re.push("ConnectingWalletConnect",{wallet:t})},getConnectors(t){return t?j.getConnectorsByNamespace(t):j.mergeMultiChainConnectors(ae.allConnectors)},setFilterByNamespace(t){ae.filterByNamespace=t,ae.connectors=j.getConnectors(t),K.setFilterByNamespace(t)},setConnectorId(t,e){t&&(ae.activeConnectorIds={...ae.activeConnectorIds,[e]:t},F.setConnectedConnectorId(e,t))},removeConnectorId(t){ae.activeConnectorIds={...ae.activeConnectorIds,[t]:void 0},F.deleteConnectedConnectorId(t)},getConnectorId(t){if(t)return ae.activeConnectorIds[t]},isConnected(t){return t?!!ae.activeConnectorIds[t]:Object.values(ae.activeConnectorIds).some(e=>!!e)},resetConnectorIds(){ae.activeConnectorIds={...ou}}},j=Ct(Yd),wi={ACCOUNT_TYPES:{SMART_ACCOUNT:"smartAccount"}},Ds=Object.freeze({message:"",variant:"success",svg:void 0,open:!1,autoClose:!0}),Ue=_e({...Ds}),Xd={state:Ue,subscribeKey(t,e){return et(Ue,t,e)},showLoading(t,e={}){this._showMessage({message:t,variant:"loading",...e})},showSuccess(t){this._showMessage({message:t,variant:"success"})},showSvg(t,e){this._showMessage({message:t,svg:e})},showError(t){const e=X.parseError(t);this._showMessage({message:e,variant:"error"})},hide(){Ue.message=Ds.message,Ue.variant=Ds.variant,Ue.svg=Ds.svg,Ue.open=Ds.open,Ue.autoClose=Ds.autoClose},_showMessage({message:t,svg:e,variant:s="success",autoClose:r=Ds.autoClose}){Ue.open?(Ue.open=!1,setTimeout(()=>{Ue.message=t,Ue.variant=s,Ue.svg=e,Ue.open=!0,Ue.autoClose=r},150)):(Ue.message=t,Ue.variant=s,Ue.svg=e,Ue.open=!0,Ue.autoClose=r)}},Lt=Xd,Ie=_e({transactions:[],coinbaseTransactions:{},transactionsByYear:{},lastNetworkInView:void 0,loading:!1,empty:!1,next:void 0}),Zd={state:Ie,subscribe(t){return Qe(Ie,()=>t(Ie))},setLastNetworkInView(t){Ie.lastNetworkInView=t},async fetchTransactions(t,e){var s,r;if(!t)throw new Error("Transactions can't be fetched without an accountAddress");Ie.loading=!0;try{const i=await J.fetchTransactions({account:t,cursor:Ie.next,onramp:e,cache:e==="coinbase"?"no-cache":void 0,chainId:(s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId}),n=di.filterSpamTransactions(i.data),o=di.filterByConnectedChain(n),a=[...Ie.transactions,...o];Ie.loading=!1,e==="coinbase"?Ie.coinbaseTransactions=di.groupTransactionsByYearAndMonth(Ie.coinbaseTransactions,i.data):(Ie.transactions=a,Ie.transactionsByYear=di.groupTransactionsByYearAndMonth(Ie.transactionsByYear,o)),Ie.empty=a.length===0,Ie.next=i.next?i.next:void 0}catch{const n=g.state.activeChain;Pe.sendEvent({type:"track",event:"ERROR_FETCH_TRANSACTIONS",properties:{address:t,projectId:T.state.projectId,cursor:Ie.next,isSmartAccount:((r=z.state.preferredAccountTypes)==null?void 0:r[n])===wi.ACCOUNT_TYPES.SMART_ACCOUNT}}),Lt.showError("Failed to fetch transactions"),Ie.loading=!1,Ie.empty=!0,Ie.next=void 0}},groupTransactionsByYearAndMonth(t={},e=[]){const s=t;return e.forEach(r=>{const i=new Date(r.metadata.minedAt).getFullYear(),n=new Date(r.metadata.minedAt).getMonth(),o=s[i]??{},c=(o[n]??[]).filter(l=>l.id!==r.id);s[i]={...o,[n]:[...c,r].sort((l,u)=>new Date(u.metadata.minedAt).getTime()-new Date(l.metadata.minedAt).getTime())}}),s},filterSpamTransactions(t){return t.filter(e=>!e.transfers.every(r=>{var i;return((i=r.nft_info)==null?void 0:i.flags.is_spam)===!0}))},filterByConnectedChain(t){var r;const e=(r=g.state.activeCaipNetwork)==null?void 0:r.caipNetworkId;return t.filter(i=>i.metadata.chain===e)},clearCursor(){Ie.next=void 0},resetTransactions(){Ie.transactions=[],Ie.transactionsByYear={},Ie.lastNetworkInView=void 0,Ie.loading=!1,Ie.empty=!1,Ie.next=void 0}},di=Ct(Zd,"API_ERROR"),ye=_e({connections:new Map,wcError:!1,buffering:!1,status:"disconnected"});let xs;const Qd={state:ye,subscribeKey(t,e){return et(ye,t,e)},_getClient(){return ye._client},setClient(t){ye._client=Gs(t)},async connectWalletConnect(){var t,e,s,r;if(X.isTelegram()||X.isSafari()&&X.isIos()){if(xs){await xs,xs=void 0;return}if(!X.isPairingExpired(ye==null?void 0:ye.wcPairingExpiry)){const i=ye.wcUri;ye.wcUri=i;return}xs=(e=(t=Y._getClient())==null?void 0:t.connectWalletConnect)==null?void 0:e.call(t).catch(()=>{}),Y.state.status="connecting",await xs,xs=void 0,ye.wcPairingExpiry=void 0,Y.state.status="connected"}else await((r=(s=Y._getClient())==null?void 0:s.connectWalletConnect)==null?void 0:r.call(s))},async connectExternal(t,e,s=!0){var r,i;await((i=(r=Y._getClient())==null?void 0:r.connectExternal)==null?void 0:i.call(r,t)),s&&g.setActiveNamespace(e)},async reconnectExternal(t){var s,r;await((r=(s=Y._getClient())==null?void 0:s.reconnectExternal)==null?void 0:r.call(s,t));const e=t.chain||g.state.activeChain;e&&j.setConnectorId(t.id,e)},async setPreferredAccountType(t,e){var r;We.setLoading(!0,g.state.activeChain);const s=j.getAuthConnector();s&&(z.setPreferredAccountType(t,e),await s.provider.setPreferredAccount(t),F.setPreferredAccountTypes(z.state.preferredAccountTypes??{[e]:t}),await Y.reconnectExternal(s),We.setLoading(!1,g.state.activeChain),Pe.sendEvent({type:"track",event:"SET_PREFERRED_ACCOUNT_TYPE",properties:{accountType:t,network:((r=g.state.activeCaipNetwork)==null?void 0:r.caipNetworkId)||""}}))},async signMessage(t){var e;return(e=Y._getClient())==null?void 0:e.signMessage(t)},parseUnits(t,e){var s;return(s=Y._getClient())==null?void 0:s.parseUnits(t,e)},formatUnits(t,e){var s;return(s=Y._getClient())==null?void 0:s.formatUnits(t,e)},async sendTransaction(t){var e;return(e=Y._getClient())==null?void 0:e.sendTransaction(t)},async getCapabilities(t){var e;return(e=Y._getClient())==null?void 0:e.getCapabilities(t)},async grantPermissions(t){var e;return(e=Y._getClient())==null?void 0:e.grantPermissions(t)},async walletGetAssets(t){var e;return((e=Y._getClient())==null?void 0:e.walletGetAssets(t))??{}},async estimateGas(t){var e;return(e=Y._getClient())==null?void 0:e.estimateGas(t)},async writeContract(t){var e;return(e=Y._getClient())==null?void 0:e.writeContract(t)},async getEnsAddress(t){var e;return(e=Y._getClient())==null?void 0:e.getEnsAddress(t)},async getEnsAvatar(t){var e;return(e=Y._getClient())==null?void 0:e.getEnsAvatar(t)},checkInstalled(t){var e,s;return((s=(e=Y._getClient())==null?void 0:e.checkInstalled)==null?void 0:s.call(e,t))||!1},resetWcConnection(){ye.wcUri=void 0,ye.wcPairingExpiry=void 0,ye.wcLinking=void 0,ye.recentWallet=void 0,ye.status="disconnected",di.resetTransactions(),F.deleteWalletConnectDeepLink()},resetUri(){ye.wcUri=void 0,ye.wcPairingExpiry=void 0,xs=void 0},finalizeWcConnection(){var s,r;const{wcLinking:t,recentWallet:e}=Y.state;t&&F.setWalletConnectDeepLink(t),e&&F.setAppKitRecent(e),Pe.sendEvent({type:"track",event:"CONNECT_SUCCESS",properties:{method:t?"mobile":"qrcode",name:((r=(s=re.state.data)==null?void 0:s.wallet)==null?void 0:r.name)||"Unknown"}})},setWcBasic(t){ye.wcBasic=t},setUri(t){ye.wcUri=t,ye.wcPairingExpiry=X.getPairingExpiry()},setWcLinking(t){ye.wcLinking=t},setWcError(t){ye.wcError=t,ye.buffering=!1},setRecentWallet(t){ye.recentWallet=t},setBuffering(t){ye.buffering=t},setStatus(t){ye.status=t},async disconnect(t){var e;try{await((e=Y._getClient())==null?void 0:e.disconnect(t))}catch(s){throw new Ur("Failed to disconnect","INTERNAL_SDK_ERROR",s)}},setConnections(t,e){ye.connections.set(e,t)},switchAccount({connection:t,address:e,namespace:s}){if(j.state.activeConnectorIds[s]===t.connectorId){const n=g.state.activeCaipNetwork;if(n){const o=`${s}:${n.id}:${e}`;z.setCaipAddress(o,s)}else console.warn(`No current network found for namespace "${s}"`)}else{const n=j.getConnector(t.connectorId);n?Y.connectExternal(n,s):console.warn(`No connector found for namespace "${s}"`)}}},Y=Ct(Qd),or=_e({loading:!1,open:!1,selectedNetworkId:void 0,activeChain:void 0,initialized:!1}),Is={state:or,subscribe(t){return Qe(or,()=>t(or))},subscribeOpen(t){return et(or,"open",t)},set(t){Object.assign(or,{...or,...t})}},Gn={createBalance(t,e){const s={name:t.metadata.name||"",symbol:t.metadata.symbol||"",decimals:t.metadata.decimals||0,value:t.metadata.value||0,price:t.metadata.price||0,iconUrl:t.metadata.iconUrl||""};return{name:s.name,symbol:s.symbol,chainId:e,address:t.address==="native"?void 0:this.convertAddressToCAIP10Address(t.address,e),value:s.value,price:s.price,quantity:{decimals:s.decimals.toString(),numeric:this.convertHexToBalance({hex:t.balance,decimals:s.decimals})},iconUrl:s.iconUrl}},convertHexToBalance({hex:t,decimals:e}){return Xh(BigInt(t),e)},convertAddressToCAIP10Address(t,e){return`${e}:${t}`},createCAIP2ChainId(t,e){return`${e}:${parseInt(t,16)}`},getChainIdHexFromCAIP2ChainId(t){const e=t.split(":");if(e.length<2||!e[1])return"0x0";const s=e[1],r=parseInt(s,10);return isNaN(r)?"0x0":`0x${r.toString(16)}`},isWalletGetAssetsResponse(t){return typeof t!="object"||t===null?!1:Object.values(t).every(e=>Array.isArray(e)&&e.every(s=>this.isValidAsset(s)))},isValidAsset(t){return typeof t=="object"&&t!==null&&typeof t.address=="string"&&typeof t.balance=="string"&&(t.type==="ERC20"||t.type==="NATIVE")&&typeof t.metadata=="object"&&t.metadata!==null&&typeof t.metadata.name=="string"&&typeof t.metadata.symbol=="string"&&typeof t.metadata.decimals=="number"&&typeof t.metadata.price=="number"&&typeof t.metadata.iconUrl=="string"}},qa={async getMyTokensWithBalance(t){const e=z.state.address,s=g.state.activeCaipNetwork;if(!e||!s)return[];if(s.chainNamespace==="eip155"){const i=await this.getEIP155Balances(e,s);if(i)return this.filterLowQualityTokens(i)}const r=await J.getBalance(e,s.caipNetworkId,t);return this.filterLowQualityTokens(r.balances)},async getEIP155Balances(t,e){var s,r;try{const i=Gn.getChainIdHexFromCAIP2ChainId(e.caipNetworkId),n=await Y.getCapabilities(t);if(!((r=(s=n==null?void 0:n[i])==null?void 0:s.assetDiscovery)!=null&&r.supported))return null;const o=await Y.walletGetAssets({account:t,chainFilter:[i]});return Gn.isWalletGetAssetsResponse(o)?(o[i]||[]).map(c=>Gn.createBalance(c,e.caipNetworkId)):null}catch{return null}},filterLowQualityTokens(t){return t.filter(e=>e.quantity.decimals!=="0")},mapBalancesToSwapTokens(t){return(t==null?void 0:t.map(e=>({...e,address:e!=null&&e.address?e.address:g.getActiveNetworkTokenAddress(),decimals:parseInt(e.quantity.decimals,10),logoUri:e.iconUrl,eip2612:!1})))||[]}},fe=_e({tokenBalances:[],loading:!1}),ep={state:fe,subscribe(t){return Qe(fe,()=>t(fe))},subscribeKey(t,e){return et(fe,t,e)},setToken(t){t&&(fe.token=Gs(t))},setTokenAmount(t){fe.sendTokenAmount=t},setReceiverAddress(t){fe.receiverAddress=t},setReceiverProfileImageUrl(t){fe.receiverProfileImageUrl=t},setReceiverProfileName(t){fe.receiverProfileName=t},setNetworkBalanceInUsd(t){fe.networkBalanceInUSD=t},setLoading(t){fe.loading=t},async sendToken(){var t;try{switch(he.setLoading(!0),(t=g.state.activeCaipNetwork)==null?void 0:t.chainNamespace){case"eip155":await he.sendEvmToken();return;case"solana":await he.sendSolanaToken();return;default:throw new Error("Unsupported chain")}}finally{he.setLoading(!1)}},async sendEvmToken(){var s,r,i,n;const t=g.state.activeChain,e=(s=z.state.preferredAccountTypes)==null?void 0:s[t];if(!he.state.sendTokenAmount||!he.state.receiverAddress)throw new Error("An amount and receiver address are required");if(!he.state.token)throw new Error("A token is required");(r=he.state.token)!=null&&r.address?(Pe.sendEvent({type:"track",event:"SEND_INITIATED",properties:{isSmartAccount:e===wi.ACCOUNT_TYPES.SMART_ACCOUNT,token:he.state.token.address,amount:he.state.sendTokenAmount,network:((i=g.state.activeCaipNetwork)==null?void 0:i.caipNetworkId)||""}}),await he.sendERC20Token({receiverAddress:he.state.receiverAddress,tokenAddress:he.state.token.address,sendTokenAmount:he.state.sendTokenAmount,decimals:he.state.token.quantity.decimals})):(Pe.sendEvent({type:"track",event:"SEND_INITIATED",properties:{isSmartAccount:e===wi.ACCOUNT_TYPES.SMART_ACCOUNT,token:he.state.token.symbol||"",amount:he.state.sendTokenAmount,network:((n=g.state.activeCaipNetwork)==null?void 0:n.caipNetworkId)||""}}),await he.sendNativeToken({receiverAddress:he.state.receiverAddress,sendTokenAmount:he.state.sendTokenAmount,decimals:he.state.token.quantity.decimals}))},async fetchTokenBalance(t){var n,o;fe.loading=!0;const e=(n=g.state.activeCaipNetwork)==null?void 0:n.caipNetworkId,s=(o=g.state.activeCaipNetwork)==null?void 0:o.chainNamespace,r=g.state.activeCaipAddress,i=r?X.getPlainAddress(r):void 0;if(fe.lastRetry&&!X.isAllowedRetry(fe.lastRetry,30*Ee.ONE_SEC_MS))return fe.loading=!1,[];try{if(i&&e&&s){const a=await qa.getMyTokensWithBalance();return fe.tokenBalances=a,fe.lastRetry=void 0,a}}catch(a){fe.lastRetry=Date.now(),t==null||t(a),Lt.showError("Token Balance Unavailable")}finally{fe.loading=!1}return[]},fetchNetworkBalance(){if(fe.tokenBalances.length===0)return;const t=qa.mapBalancesToSwapTokens(fe.tokenBalances);if(!t)return;const e=t.find(s=>s.address===g.getActiveNetworkTokenAddress());e&&(fe.networkBalanceInUSD=e?Ed.multiply(e.quantity.numeric,e.price).toString():"0")},async sendNativeToken(t){var n,o,a,c;re.pushTransactionStack({});const e=t.receiverAddress,s=z.state.address,r=Y.parseUnits(t.sendTokenAmount.toString(),Number(t.decimals));await Y.sendTransaction({chainNamespace:"eip155",to:e,address:s,data:"0x",value:r??BigInt(0)}),Pe.sendEvent({type:"track",event:"SEND_SUCCESS",properties:{isSmartAccount:((n=z.state.preferredAccountTypes)==null?void 0:n.eip155)===wi.ACCOUNT_TYPES.SMART_ACCOUNT,token:((o=he.state.token)==null?void 0:o.symbol)||"",amount:t.sendTokenAmount,network:((a=g.state.activeCaipNetwork)==null?void 0:a.caipNetworkId)||""}}),(c=Y._getClient())==null||c.updateBalance("eip155"),he.resetSend()},async sendERC20Token(t){re.pushTransactionStack({onSuccess(){re.replace("Account")}});const e=Y.parseUnits(t.sendTokenAmount.toString(),Number(t.decimals));if(z.state.address&&t.sendTokenAmount&&t.receiverAddress&&t.tokenAddress){const s=X.getPlainAddress(t.tokenAddress);await Y.writeContract({fromAddress:z.state.address,tokenAddress:s,args:[t.receiverAddress,e??BigInt(0)],method:"transfer",abi:Nd.getERC20Abi(s),chainNamespace:"eip155"}),he.resetSend()}},async sendSolanaToken(){var t;if(!he.state.sendTokenAmount||!he.state.receiverAddress)throw new Error("An amount and receiver address are required");re.pushTransactionStack({onSuccess(){re.replace("Account")}}),await Y.sendTransaction({chainNamespace:"solana",to:he.state.receiverAddress,value:he.state.sendTokenAmount}),(t=Y._getClient())==null||t.updateBalance("solana"),he.resetSend()},resetSend(){fe.token=void 0,fe.sendTokenAmount=void 0,fe.receiverAddress=void 0,fe.receiverProfileImageUrl=void 0,fe.receiverProfileName=void 0,fe.loading=!1,fe.tokenBalances=[]}},he=Ct(ep),Jn={currentTab:0,tokenBalance:[],smartAccountDeployed:!1,addressLabels:new Map,allAccounts:[],user:void 0},Zi={caipNetwork:void 0,supportsAllNetworks:!0,smartAccountEnabledNetworks:[]},M=_e({chains:kd(),activeCaipAddress:void 0,activeChain:void 0,activeCaipNetwork:void 0,noAdapters:!1,universalAdapter:{networkControllerClient:void 0,connectionControllerClient:void 0},isSwitchingNamespace:!1}),tp={state:M,subscribe(t){return Qe(M,()=>{t(M)})},subscribeKey(t,e){return et(M,t,e)},subscribeChainProp(t,e,s){let r;return Qe(M.chains,()=>{var n;const i=s||M.activeChain;if(i){const o=(n=M.chains.get(i))==null?void 0:n[t];r!==o&&(r=o,e(o))}})},initialize(t,e,s){const{chainId:r,namespace:i}=F.getActiveNetworkProps(),n=e==null?void 0:e.find(u=>u.id.toString()===(r==null?void 0:r.toString())),a=t.find(u=>(u==null?void 0:u.namespace)===i)||(t==null?void 0:t[0]),c=t.map(u=>u.namespace).filter(u=>u!==void 0),l=T.state.enableEmbedded?new Set([...c]):new Set([...(e==null?void 0:e.map(u=>u.chainNamespace))??[]]);((t==null?void 0:t.length)===0||!a)&&(M.noAdapters=!0),M.noAdapters||(M.activeChain=a==null?void 0:a.namespace,M.activeCaipNetwork=n,g.setChainNetworkData(a==null?void 0:a.namespace,{caipNetwork:n}),M.activeChain&&Is.set({activeChain:a==null?void 0:a.namespace})),l.forEach(u=>{const h=e==null?void 0:e.filter(d=>d.chainNamespace===u);g.state.chains.set(u,{namespace:u,networkState:_e({...Zi,caipNetwork:h==null?void 0:h[0]}),accountState:_e(Jn),caipNetworks:h??[],...s}),g.setRequestedCaipNetworks(h??[],u)})},removeAdapter(t){var e,s;if(M.activeChain===t){const r=Array.from(M.chains.entries()).find(([i])=>i!==t);if(r){const i=(s=(e=r[1])==null?void 0:e.caipNetworks)==null?void 0:s[0];i&&g.setActiveCaipNetwork(i)}}M.chains.delete(t)},addAdapter(t,{networkControllerClient:e,connectionControllerClient:s},r){M.chains.set(t.namespace,{namespace:t.namespace,networkState:{...Zi,caipNetwork:r[0]},accountState:Jn,caipNetworks:r,connectionControllerClient:s,networkControllerClient:e}),g.setRequestedCaipNetworks((r==null?void 0:r.filter(i=>i.chainNamespace===t.namespace))??[],t.namespace)},addNetwork(t){var s;const e=M.chains.get(t.chainNamespace);if(e){const r=[...e.caipNetworks||[]];(s=e.caipNetworks)!=null&&s.find(i=>i.id===t.id)||r.push(t),M.chains.set(t.chainNamespace,{...e,caipNetworks:r}),g.setRequestedCaipNetworks(r,t.chainNamespace),j.filterByNamespace(t.chainNamespace,!0)}},removeNetwork(t,e){var r,i,n;const s=M.chains.get(t);if(s){const o=((r=M.activeCaipNetwork)==null?void 0:r.id)===e,a=[...((i=s.caipNetworks)==null?void 0:i.filter(c=>c.id!==e))||[]];o&&((n=s==null?void 0:s.caipNetworks)!=null&&n[0])&&g.setActiveCaipNetwork(s.caipNetworks[0]),M.chains.set(t,{...s,caipNetworks:a}),g.setRequestedCaipNetworks(a||[],t),a.length===0&&j.filterByNamespace(t,!1)}},setAdapterNetworkState(t,e){const s=M.chains.get(t);s&&(s.networkState={...s.networkState||Zi,...e},M.chains.set(t,s))},setChainAccountData(t,e,s=!0){if(!t)throw new Error("Chain is required to update chain account data");const r=M.chains.get(t);if(r){const i={...r.accountState||Jn,...e};M.chains.set(t,{...r,accountState:i}),(M.chains.size===1||M.activeChain===t)&&(e.caipAddress&&(M.activeCaipAddress=e.caipAddress),z.replaceState(i))}},setChainNetworkData(t,e){if(!t)return;const s=M.chains.get(t);if(s){const r={...s.networkState||Zi,...e};M.chains.set(t,{...s,networkState:r})}},setAccountProp(t,e,s,r=!0){g.setChainAccountData(s,{[t]:e},r),t==="status"&&e==="disconnected"&&s&&j.removeConnectorId(s)},setActiveNamespace(t){var r,i;M.activeChain=t;const e=t?M.chains.get(t):void 0,s=(r=e==null?void 0:e.networkState)==null?void 0:r.caipNetwork;s!=null&&s.id&&t&&(M.activeCaipAddress=(i=e==null?void 0:e.accountState)==null?void 0:i.caipAddress,M.activeCaipNetwork=s,g.setChainNetworkData(t,{caipNetwork:s}),F.setActiveCaipNetworkId(s==null?void 0:s.caipNetworkId),Is.set({activeChain:t,selectedNetworkId:s==null?void 0:s.caipNetworkId}))},setActiveCaipNetwork(t){var r,i,n;if(!t)return;M.activeChain!==t.chainNamespace&&g.setIsSwitchingNamespace(!0);const e=M.chains.get(t.chainNamespace);M.activeChain=t.chainNamespace,M.activeCaipNetwork=t,g.setChainNetworkData(t.chainNamespace,{caipNetwork:t}),(r=e==null?void 0:e.accountState)!=null&&r.address?M.activeCaipAddress=`${t.chainNamespace}:${t.id}:${(i=e==null?void 0:e.accountState)==null?void 0:i.address}`:M.activeCaipAddress=void 0,g.setAccountProp("caipAddress",M.activeCaipAddress,t.chainNamespace),e&&z.replaceState(e.accountState),he.resetSend(),Is.set({activeChain:M.activeChain,selectedNetworkId:(n=M.activeCaipNetwork)==null?void 0:n.caipNetworkId}),F.setActiveCaipNetworkId(t.caipNetworkId),!g.checkIfSupportedNetwork(t.chainNamespace)&&T.state.enableNetworkSwitch&&!T.state.allowUnsupportedChain&&!Y.state.wcBasic&&g.showUnsupportedChainUI()},addCaipNetwork(t){var s;if(!t)return;const e=M.chains.get(t.chainNamespace);e&&((s=e==null?void 0:e.caipNetworks)==null||s.push(t))},async switchActiveNamespace(t){var i;if(!t)return;const e=t!==g.state.activeChain,s=(i=g.getNetworkData(t))==null?void 0:i.caipNetwork,r=g.getCaipNetworkByNamespace(t,s==null?void 0:s.id);e&&r&&await g.switchActiveNetwork(r)},async switchActiveNetwork(t){var i;const e=g.state.chains.get(g.state.activeChain),s=!((i=e==null?void 0:e.caipNetworks)!=null&&i.some(n=>{var o;return n.id===((o=M.activeCaipNetwork)==null?void 0:o.id)})),r=g.getNetworkControllerClient(t.chainNamespace);if(r){try{await r.switchCaipNetwork(t),s&&We.close()}catch{re.goBack()}Pe.sendEvent({type:"track",event:"SWITCH_NETWORK",properties:{network:t.caipNetworkId}})}},getNetworkControllerClient(t){const e=t||M.activeChain,s=M.chains.get(e);if(!s)throw new Error("Chain adapter not found");if(!s.networkControllerClient)throw new Error("NetworkController client not set");return s.networkControllerClient},getConnectionControllerClient(t){const e=t||M.activeChain;if(!e)throw new Error("Chain is required to get connection controller client");const s=M.chains.get(e);if(!(s!=null&&s.connectionControllerClient))throw new Error("ConnectionController client not set");return s.connectionControllerClient},getAccountProp(t,e){var i;let s=M.activeChain;if(e&&(s=e),!s)return;const r=(i=M.chains.get(s))==null?void 0:i.accountState;if(r)return r[t]},getNetworkProp(t,e){var r;const s=(r=M.chains.get(e))==null?void 0:r.networkState;if(s)return s[t]},getRequestedCaipNetworks(t){const e=M.chains.get(t),{approvedCaipNetworkIds:s=[],requestedCaipNetworks:r=[]}=(e==null?void 0:e.networkState)||{};return X.sortRequestedNetworks(s,r)},getAllRequestedCaipNetworks(){const t=[];return M.chains.forEach(e=>{const s=g.getRequestedCaipNetworks(e.namespace);t.push(...s)}),t},setRequestedCaipNetworks(t,e){g.setAdapterNetworkState(e,{requestedCaipNetworks:t});const r=g.getAllRequestedCaipNetworks().map(n=>n.chainNamespace),i=Array.from(new Set(r));j.filterByNamespaces(i)},getAllApprovedCaipNetworkIds(){const t=[];return M.chains.forEach(e=>{const s=g.getApprovedCaipNetworkIds(e.namespace);t.push(...s)}),t},getActiveCaipNetwork(){return M.activeCaipNetwork},getActiveCaipAddress(){return M.activeCaipAddress},getApprovedCaipNetworkIds(t){var r;const e=M.chains.get(t);return((r=e==null?void 0:e.networkState)==null?void 0:r.approvedCaipNetworkIds)||[]},async setApprovedCaipNetworksData(t){const e=g.getNetworkControllerClient(),s=await(e==null?void 0:e.getApprovedCaipNetworksData());g.setAdapterNetworkState(t,{approvedCaipNetworkIds:s==null?void 0:s.approvedCaipNetworkIds,supportsAllNetworks:s==null?void 0:s.supportsAllNetworks})},checkIfSupportedNetwork(t,e){const s=e||M.activeCaipNetwork,r=g.getRequestedCaipNetworks(t);return r.length?r==null?void 0:r.some(i=>i.id===(s==null?void 0:s.id)):!0},checkIfSupportedChainId(t){if(!M.activeChain)return!0;const e=g.getRequestedCaipNetworks(M.activeChain);return e==null?void 0:e.some(s=>s.id===t)},setSmartAccountEnabledNetworks(t,e){g.setAdapterNetworkState(e,{smartAccountEnabledNetworks:t})},checkIfSmartAccountEnabled(){var r;const t=tu.caipNetworkIdToNumber((r=M.activeCaipNetwork)==null?void 0:r.caipNetworkId),e=M.activeChain;if(!e||!t)return!1;const s=g.getNetworkProp("smartAccountEnabledNetworks",e);return!!(s!=null&&s.includes(Number(t)))},getActiveNetworkTokenAddress(){var r,i;const t=((r=M.activeCaipNetwork)==null?void 0:r.chainNamespace)||"eip155",e=((i=M.activeCaipNetwork)==null?void 0:i.id)||1,s=Ee.NATIVE_TOKEN_ADDRESS[t];return`${t}:${e}:${s}`},showUnsupportedChainUI(){We.open({view:"UnsupportedChain"})},checkIfNamesSupported(){const t=M.activeCaipNetwork;return!!(t!=null&&t.chainNamespace&&Ee.NAMES_SUPPORTED_CHAIN_NAMESPACES.includes(t.chainNamespace))},resetNetwork(t){g.setAdapterNetworkState(t,{approvedCaipNetworkIds:void 0,supportsAllNetworks:!0,smartAccountEnabledNetworks:[]})},resetAccount(t){const e=t;if(!e)throw new Error("Chain is required to set account prop");M.activeCaipAddress=void 0,g.setChainAccountData(e,{smartAccountDeployed:!1,currentTab:0,caipAddress:void 0,address:void 0,balance:void 0,balanceSymbol:void 0,profileName:void 0,profileImage:void 0,addressExplorerUrl:void 0,tokenBalance:[],connectedWalletInfo:void 0,preferredAccountTypes:void 0,socialProvider:void 0,socialWindow:void 0,farcasterUrl:void 0,allAccounts:[],user:void 0,status:"disconnected"}),j.removeConnectorId(e)},setIsSwitchingNamespace(t){M.isSwitchingNamespace=t},getFirstCaipNetworkSupportsAuthConnector(){var s,r;const t=[];let e;if(M.chains.forEach(i=>{W.AUTH_CONNECTOR_SUPPORTED_CHAINS.find(n=>n===i.namespace)&&i.namespace&&t.push(i.namespace)}),t.length>0){const i=t[0];return e=i?(r=(s=M.chains.get(i))==null?void 0:s.caipNetworks)==null?void 0:r[0]:void 0,e}},getAccountData(t){var e;return t?(e=g.state.chains.get(t))==null?void 0:e.accountState:z.state},getNetworkData(t){var s;const e=t||M.activeChain;if(e)return(s=g.state.chains.get(e))==null?void 0:s.networkState},getCaipNetworkByNamespace(t,e){var i,n,o;if(!t)return;const s=g.state.chains.get(t),r=(i=s==null?void 0:s.caipNetworks)==null?void 0:i.find(a=>a.id===e);return r||((n=s==null?void 0:s.networkState)==null?void 0:n.caipNetwork)||((o=s==null?void 0:s.caipNetworks)==null?void 0:o[0])},getRequestedCaipNetworkIds(){const t=j.state.filterByNamespace;return(t?[M.chains.get(t)]:Array.from(M.chains.values())).flatMap(s=>(s==null?void 0:s.caipNetworks)||[]).map(s=>s.caipNetworkId)},getCaipNetworks(t){return t?g.getRequestedCaipNetworks(t):g.getAllRequestedCaipNetworks()}},g=Ct(tp),sp={purchaseCurrencies:[{id:"2b92315d-eab7-5bef-84fa-089a131333f5",name:"USD Coin",symbol:"USDC",networks:[{name:"ethereum-mainnet",display_name:"Ethereum",chain_id:"1",contract_address:"******************************************"},{name:"polygon-mainnet",display_name:"Polygon",chain_id:"137",contract_address:"******************************************"}]},{id:"2b92315d-eab7-5bef-84fa-089a131333f5",name:"Ether",symbol:"ETH",networks:[{name:"ethereum-mainnet",display_name:"Ethereum",chain_id:"1",contract_address:"******************************************"},{name:"polygon-mainnet",display_name:"Polygon",chain_id:"137",contract_address:"******************************************"}]}],paymentCurrencies:[{id:"USD",payment_method_limits:[{id:"card",min:"10.00",max:"7500.00"},{id:"ach_bank_account",min:"10.00",max:"25000.00"}]},{id:"EUR",payment_method_limits:[{id:"card",min:"10.00",max:"7500.00"},{id:"ach_bank_account",min:"10.00",max:"25000.00"}]}]},au=X.getBlockchainApiUrl(),nt=_e({clientId:null,api:new Bi({baseUrl:au,clientId:null}),supportedChains:{http:[],ws:[]}}),J={state:nt,async get(t){const{st:e,sv:s}=J.getSdkProperties(),r=T.state.projectId,i={...t.params||{},st:e,sv:s,projectId:r};return nt.api.get({...t,params:i})},getSdkProperties(){const{sdkType:t,sdkVersion:e}=T.state;return{st:t||"unknown",sv:e||"unknown"}},async isNetworkSupported(t){if(!t)return!1;try{nt.supportedChains.http.length||await J.getSupportedNetworks()}catch{return!1}return nt.supportedChains.http.includes(t)},async getSupportedNetworks(){try{const t=await J.get({path:"v1/supported-chains"});return nt.supportedChains=t,t}catch{return nt.supportedChains}},async fetchIdentity({address:t,caipNetworkId:e}){if(!await J.isNetworkSupported(e))return{avatar:"",name:""};const r=F.getIdentityFromCacheForAddress(t);if(r)return r;const i=await J.get({path:`/v1/identity/${t}`,params:{sender:g.state.activeCaipAddress?X.getPlainAddress(g.state.activeCaipAddress):void 0}});return F.updateIdentityCache({address:t,identity:i,timestamp:Date.now()}),i},async fetchTransactions({account:t,cursor:e,onramp:s,signal:r,cache:i,chainId:n}){var a;return await J.isNetworkSupported((a=g.state.activeCaipNetwork)==null?void 0:a.caipNetworkId)?J.get({path:`/v1/account/${t}/history`,params:{cursor:e,onramp:s,chainId:n},signal:r,cache:i}):{data:[],next:void 0}},async fetchSwapQuote({amount:t,userAddress:e,from:s,to:r,gasPrice:i}){var o;return await J.isNetworkSupported((o=g.state.activeCaipNetwork)==null?void 0:o.caipNetworkId)?J.get({path:"/v1/convert/quotes",headers:{"Content-Type":"application/json"},params:{amount:t,userAddress:e,from:s,to:r,gasPrice:i}}):{quotes:[]}},async fetchSwapTokens({chainId:t}){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?J.get({path:"/v1/convert/tokens",params:{chainId:t}}):{tokens:[]}},async fetchTokenPrice({addresses:t}){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?nt.api.post({path:"/v1/fungible/price",body:{currency:"usd",addresses:t,projectId:T.state.projectId},headers:{"Content-Type":"application/json"}}):{fungibles:[]}},async fetchSwapAllowance({tokenAddress:t,userAddress:e}){var r;return await J.isNetworkSupported((r=g.state.activeCaipNetwork)==null?void 0:r.caipNetworkId)?J.get({path:"/v1/convert/allowance",params:{tokenAddress:t,userAddress:e},headers:{"Content-Type":"application/json"}}):{allowance:"0"}},async fetchGasPrice({chainId:t}){var i;const{st:e,sv:s}=J.getSdkProperties();if(!await J.isNetworkSupported((i=g.state.activeCaipNetwork)==null?void 0:i.caipNetworkId))throw new Error("Network not supported for Gas Price");return J.get({path:"/v1/convert/gas-price",headers:{"Content-Type":"application/json"},params:{chainId:t,st:e,sv:s}})},async generateSwapCalldata({amount:t,from:e,to:s,userAddress:r,disableEstimate:i}){var o;if(!await J.isNetworkSupported((o=g.state.activeCaipNetwork)==null?void 0:o.caipNetworkId))throw new Error("Network not supported for Swaps");return nt.api.post({path:"/v1/convert/build-transaction",headers:{"Content-Type":"application/json"},body:{amount:t,eip155:{slippage:Ee.CONVERT_SLIPPAGE_TOLERANCE},projectId:T.state.projectId,from:e,to:s,userAddress:r,disableEstimate:i}})},async generateApproveCalldata({from:t,to:e,userAddress:s}){var o;const{st:r,sv:i}=J.getSdkProperties();if(!await J.isNetworkSupported((o=g.state.activeCaipNetwork)==null?void 0:o.caipNetworkId))throw new Error("Network not supported for Swaps");return J.get({path:"/v1/convert/build-approve",headers:{"Content-Type":"application/json"},params:{userAddress:s,from:t,to:e,st:r,sv:i}})},async getBalance(t,e,s){var l;const{st:r,sv:i}=J.getSdkProperties();if(!await J.isNetworkSupported((l=g.state.activeCaipNetwork)==null?void 0:l.caipNetworkId))return Lt.showError("Token Balance Unavailable"),{balances:[]};const o=`${e}:${t}`,a=F.getBalanceCacheForCaipAddress(o);if(a)return a;const c=await J.get({path:`/v1/account/${t}/balance`,params:{currency:"usd",chainId:e,forceUpdate:s,st:r,sv:i}});return F.updateBalanceCache({caipAddress:o,balance:c,timestamp:Date.now()}),c},async lookupEnsName(t){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?J.get({path:`/v1/profile/account/${t}`,params:{apiVersion:"2"}}):{addresses:{},attributes:[]}},async reverseLookupEnsName({address:t}){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?J.get({path:`/v1/profile/reverse/${t}`,params:{sender:z.state.address,apiVersion:"2"}}):[]},async getEnsNameSuggestions(t){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?J.get({path:`/v1/profile/suggestions/${t}`,params:{zone:"reown.id"}}):{suggestions:[]}},async registerEnsName({coinType:t,address:e,message:s,signature:r}){var n;return await J.isNetworkSupported((n=g.state.activeCaipNetwork)==null?void 0:n.caipNetworkId)?nt.api.post({path:"/v1/profile/account",body:{coin_type:t,address:e,message:s,signature:r},headers:{"Content-Type":"application/json"}}):{success:!1}},async generateOnRampURL({destinationWallets:t,partnerUserId:e,defaultNetwork:s,purchaseAmount:r,paymentAmount:i}){var a;return await J.isNetworkSupported((a=g.state.activeCaipNetwork)==null?void 0:a.caipNetworkId)?(await nt.api.post({path:"/v1/generators/onrampurl",params:{projectId:T.state.projectId},body:{destinationWallets:t,defaultNetwork:s,partnerUserId:e,defaultExperience:"buy",presetCryptoAmount:r,presetFiatAmount:i}})).url:""},async getOnrampOptions(){var e;if(!await J.isNetworkSupported((e=g.state.activeCaipNetwork)==null?void 0:e.caipNetworkId))return{paymentCurrencies:[],purchaseCurrencies:[]};try{return await J.get({path:"/v1/onramp/options"})}catch{return sp}},async getOnrampQuote({purchaseCurrency:t,paymentCurrency:e,amount:s,network:r}){var i;try{return await J.isNetworkSupported((i=g.state.activeCaipNetwork)==null?void 0:i.caipNetworkId)?await nt.api.post({path:"/v1/onramp/quote",params:{projectId:T.state.projectId},body:{purchaseCurrency:t,paymentCurrency:e,amount:s,network:r}}):null}catch{return{coinbaseFee:{amount:s,currency:e.id},networkFee:{amount:s,currency:e.id},paymentSubtotal:{amount:s,currency:e.id},paymentTotal:{amount:s,currency:e.id},purchaseAmount:{amount:s,currency:e.id},quoteId:"mocked-quote-id"}}},async getSmartSessions(t){var s;return await J.isNetworkSupported((s=g.state.activeCaipNetwork)==null?void 0:s.caipNetworkId)?J.get({path:`/v1/sessions/${t}`}):[]},async revokeSmartSession(t,e,s){var i;return await J.isNetworkSupported((i=g.state.activeCaipNetwork)==null?void 0:i.caipNetworkId)?nt.api.post({path:`/v1/sessions/${t}/revoke`,params:{projectId:T.state.projectId},body:{pci:e,signature:s}}):{success:!1}},setClientId(t){nt.clientId=t,nt.api=new Bi({baseUrl:au,clientId:t})}},It=_e({currentTab:0,tokenBalance:[],smartAccountDeployed:!1,addressLabels:new Map,allAccounts:[]}),rp={state:It,replaceState(t){t&&Object.assign(It,Gs(t))},subscribe(t){return g.subscribeChainProp("accountState",e=>{if(e)return t(e)})},subscribeKey(t,e,s){let r;return g.subscribeChainProp("accountState",i=>{if(i){const n=i[t];r!==n&&(r=n,e(n))}},s)},setStatus(t,e){g.setAccountProp("status",t,e)},getCaipAddress(t){return g.getAccountProp("caipAddress",t)},setCaipAddress(t,e){const s=t?X.getPlainAddress(t):void 0;e===g.state.activeChain&&(g.state.activeCaipAddress=t),g.setAccountProp("caipAddress",t,e),g.setAccountProp("address",s,e)},setBalance(t,e,s){g.setAccountProp("balance",t,s),g.setAccountProp("balanceSymbol",e,s)},setProfileName(t,e){g.setAccountProp("profileName",t,e)},setProfileImage(t,e){g.setAccountProp("profileImage",t,e)},setUser(t,e){g.setAccountProp("user",t,e)},setAddressExplorerUrl(t,e){g.setAccountProp("addressExplorerUrl",t,e)},setSmartAccountDeployed(t,e){g.setAccountProp("smartAccountDeployed",t,e)},setCurrentTab(t){g.setAccountProp("currentTab",t,g.state.activeChain)},setTokenBalance(t,e){t&&g.setAccountProp("tokenBalance",t,e)},setShouldUpdateToAddress(t,e){g.setAccountProp("shouldUpdateToAddress",t,e)},setAllAccounts(t,e){g.setAccountProp("allAccounts",t,e)},addAddressLabel(t,e,s){const r=g.getAccountProp("addressLabels",s)||new Map;r.set(t,e),g.setAccountProp("addressLabels",r,s)},removeAddressLabel(t,e){const s=g.getAccountProp("addressLabels",e)||new Map;s.delete(t),g.setAccountProp("addressLabels",s,e)},setConnectedWalletInfo(t,e){g.setAccountProp("connectedWalletInfo",t,e,!1)},setPreferredAccountType(t,e){g.setAccountProp("preferredAccountTypes",{...It.preferredAccountTypes,[e]:t},e)},setPreferredAccountTypes(t){It.preferredAccountTypes=t},setSocialProvider(t,e){t&&g.setAccountProp("socialProvider",t,e)},setSocialWindow(t,e){g.setAccountProp("socialWindow",t?Gs(t):void 0,e)},setFarcasterUrl(t,e){g.setAccountProp("farcasterUrl",t,e)},async fetchTokenBalance(t){var n,o;It.balanceLoading=!0;const e=(n=g.state.activeCaipNetwork)==null?void 0:n.caipNetworkId,s=(o=g.state.activeCaipNetwork)==null?void 0:o.chainNamespace,r=g.state.activeCaipAddress,i=r?X.getPlainAddress(r):void 0;if(It.lastRetry&&!X.isAllowedRetry(It.lastRetry,30*Ee.ONE_SEC_MS))return It.balanceLoading=!1,[];try{if(i&&e&&s){const c=(await J.getBalance(i,e)).balances.filter(l=>l.quantity.decimals!=="0");return z.setTokenBalance(c,s),It.lastRetry=void 0,It.balanceLoading=!1,c}}catch(a){It.lastRetry=Date.now(),t==null||t(a),Lt.showError("Token Balance Unavailable")}finally{It.balanceLoading=!1}return[]},resetAccount(t){g.resetAccount(t)}},z=Ct(rp),ip={onSwitchNetwork({network:t,ignoreSwitchConfirmation:e=!1}){const s=g.state.activeCaipNetwork,r=re.state.data;if(t.id===(s==null?void 0:s.id))return;const n=z.getCaipAddress(g.state.activeChain),o=t.chainNamespace!==g.state.activeChain,a=z.getCaipAddress(t.chainNamespace),l=j.getConnectorId(g.state.activeChain)===W.CONNECTOR_ID.AUTH,u=W.AUTH_CONNECTOR_SUPPORTED_CHAINS.find(h=>h===t.chainNamespace);e||l&&u?re.push("SwitchNetwork",{...r,network:t}):n&&o&&!a?re.push("SwitchActiveChain",{switchToChain:t.chainNamespace,navigateTo:"Connect",navigateWithReplace:!0,network:t}):re.push("SwitchNetwork",{...r,network:t})}},ot=_e({loading:!1,loadingNamespaceMap:new Map,open:!1,shake:!1,namespace:void 0}),np={state:ot,subscribe(t){return Qe(ot,()=>t(ot))},subscribeKey(t,e){return et(ot,t,e)},async open(t){var o,a;const e=z.state.status==="connected",s=t==null?void 0:t.namespace,r=g.state.activeChain,i=s&&s!==r,n=(o=g.getAccountData(t==null?void 0:t.namespace))==null?void 0:o.caipAddress;if(Y.state.wcBasic?K.prefetch({fetchNetworkImages:!1,fetchConnectorImages:!1}):await K.prefetch({fetchConnectorImages:!e,fetchFeaturedWallets:!e,fetchRecommendedWallets:!e}),j.setFilterByNamespace(t==null?void 0:t.namespace),We.setLoading(!0,s),s&&i){const c=((a=g.getNetworkData(s))==null?void 0:a.caipNetwork)||g.getRequestedCaipNetworks(s)[0];c&&ip.onSwitchNetwork({network:c,ignoreSwitchConfirmation:!0})}else{const c=g.state.noAdapters;T.state.manualWCControl||c&&!n?X.isMobile()?re.reset("AllWallets"):re.reset("ConnectingWalletConnectBasic"):t!=null&&t.view?re.reset(t.view,t.data):n?re.reset("Account"):re.reset("Connect")}ot.open=!0,Is.set({open:!0}),Pe.sendEvent({type:"track",event:"MODAL_OPEN",properties:{connected:!!n}})},close(){const t=T.state.enableEmbedded,e=!!g.state.activeCaipAddress;ot.open&&Pe.sendEvent({type:"track",event:"MODAL_CLOSE",properties:{connected:e}}),ot.open=!1,re.reset("Connect"),We.clearLoading(),t?e?re.replace("Account"):re.push("Connect"):Is.set({open:!1}),Y.resetUri()},setLoading(t,e){e&&ot.loadingNamespaceMap.set(e,t),ot.loading=t,Is.set({loading:t})},clearLoading(){ot.loadingNamespaceMap.clear(),ot.loading=!1},shake(){ot.shake||(ot.shake=!0,setTimeout(()=>{ot.shake=!1},500))}},We=Ct(np),yi={id:"2b92315d-eab7-5bef-84fa-089a131333f5",name:"USD Coin",symbol:"USDC",networks:[{name:"ethereum-mainnet",display_name:"Ethereum",chain_id:"1",contract_address:"******************************************"},{name:"polygon-mainnet",display_name:"Polygon",chain_id:"137",contract_address:"******************************************"}]},To={id:"USD",payment_method_limits:[{id:"card",min:"10.00",max:"7500.00"},{id:"ach_bank_account",min:"10.00",max:"25000.00"}]},op={providers:iu,selectedProvider:null,error:null,purchaseCurrency:yi,paymentCurrency:To,purchaseCurrencies:[yi],paymentCurrencies:[],quotesLoading:!1},oe=_e(op),ap={state:oe,subscribe(t){return Qe(oe,()=>t(oe))},subscribeKey(t,e){return et(oe,t,e)},setSelectedProvider(t){if(t&&t.name==="meld"){const e=g.state.activeChain===W.CHAIN.SOLANA?"SOL":"USDC",s=z.state.address??"",r=new URL(t.url);r.searchParams.append("publicKey",xd),r.searchParams.append("destinationCurrencyCode",e),r.searchParams.append("walletAddress",s),r.searchParams.append("externalCustomerId",T.state.projectId),oe.selectedProvider={...t,url:r.toString()}}else oe.selectedProvider=t},setOnrampProviders(t){if(Array.isArray(t)&&t.every(e=>typeof e=="string")){const e=t,s=iu.filter(r=>e.includes(r.name));oe.providers=s}else oe.providers=[]},setPurchaseCurrency(t){oe.purchaseCurrency=t},setPaymentCurrency(t){oe.paymentCurrency=t},setPurchaseAmount(t){ko.state.purchaseAmount=t},setPaymentAmount(t){ko.state.paymentAmount=t},async getAvailableCurrencies(){const t=await J.getOnrampOptions();oe.purchaseCurrencies=t.purchaseCurrencies,oe.paymentCurrencies=t.paymentCurrencies,oe.paymentCurrency=t.paymentCurrencies[0]||To,oe.purchaseCurrency=t.purchaseCurrencies[0]||yi,await K.fetchCurrencyImages(t.paymentCurrencies.map(e=>e.id)),await K.fetchTokenImages(t.purchaseCurrencies.map(e=>e.symbol))},async getQuote(){var t,e;oe.quotesLoading=!0;try{const s=await J.getOnrampQuote({purchaseCurrency:oe.purchaseCurrency,paymentCurrency:oe.paymentCurrency,amount:((t=oe.paymentAmount)==null?void 0:t.toString())||"0",network:(e=oe.purchaseCurrency)==null?void 0:e.symbol});return oe.quotesLoading=!1,oe.purchaseAmount=Number(s==null?void 0:s.purchaseAmount.amount),s}catch(s){return oe.error=s.message,oe.quotesLoading=!1,null}finally{oe.quotesLoading=!1}},resetState(){oe.selectedProvider=null,oe.error=null,oe.purchaseCurrency=yi,oe.paymentCurrency=To,oe.purchaseCurrencies=[yi],oe.paymentCurrencies=[],oe.paymentAmount=void 0,oe.purchaseAmount=void 0,oe.quotesLoading=!1}},ko=Ct(ap),Fa=**********,cp={convertEVMChainIdToCoinType(t){if(t>=Fa)throw new Error("Invalid chainId");return(Fa|t)>>>0}},At=_e({suggestions:[],loading:!1}),lp={state:At,subscribe(t){return Qe(At,()=>t(At))},subscribeKey(t,e){return et(At,t,e)},async resolveName(t){var e,s;try{return await J.lookupEnsName(t)}catch(r){const i=r;throw new Error(((s=(e=i==null?void 0:i.reasons)==null?void 0:e[0])==null?void 0:s.description)||"Error resolving name")}},async isNameRegistered(t){try{return await J.lookupEnsName(t),!0}catch{return!1}},async getSuggestions(t){try{At.loading=!0,At.suggestions=[];const e=await J.getEnsNameSuggestions(t);return At.suggestions=e.suggestions.map(s=>({...s,name:s.name}))||[],At.suggestions}catch(e){const s=bi.parseEnsApiError(e,"Error fetching name suggestions");throw new Error(s)}finally{At.loading=!1}},async getNamesForAddress(t){try{if(!g.state.activeCaipNetwork)return[];const s=F.getEnsFromCacheForAddress(t);if(s)return s;const r=await J.reverseLookupEnsName({address:t});return F.updateEnsCache({address:t,ens:r,timestamp:Date.now()}),r}catch(e){const s=bi.parseEnsApiError(e,"Error fetching names for address");throw new Error(s)}},async registerName(t){const e=g.state.activeCaipNetwork;if(!e)throw new Error("Network not found");const s=z.state.address,r=j.getAuthConnector();if(!s||!r)throw new Error("Address or auth connector not found");At.loading=!0;try{const i=JSON.stringify({name:t,attributes:{},timestamp:Math.floor(Date.now()/1e3)});re.pushTransactionStack({onCancel(){re.replace("RegisterAccountName")}});const n=await Y.signMessage(i);At.loading=!1;const o=e.id;if(!o)throw new Error("Network not found");const a=cp.convertEVMChainIdToCoinType(Number(o));await J.registerEnsName({coinType:a,address:s,signature:n,message:i}),z.setProfileName(t,e.chainNamespace),re.replace("RegisterAccountNameSuccess")}catch(i){const n=bi.parseEnsApiError(i,`Error registering name ${t}`);throw re.replace("RegisterAccountName"),new Error(n)}finally{At.loading=!1}},validateName(t){return/^[a-zA-Z0-9-]{4,}$/u.test(t)},parseEnsApiError(t,e){var r,i;const s=t;return((i=(r=s==null?void 0:s.reasons)==null?void 0:r[0])==null?void 0:i.description)||e}},bi=Ct(lp),vi={getSIWX(){return T.state.siwx},async initializeIfEnabled(){var n;const t=T.state.siwx,e=g.getActiveCaipAddress();if(!(t&&e))return;const[s,r,i]=e.split(":");if(g.checkIfSupportedNetwork(s))try{if((await t.getSessions(`${s}:${r}`,i)).length)return;await We.open({view:"SIWXSignMessage"})}catch(o){console.error("SIWXUtil:initializeIfEnabled",o),Pe.sendEvent({type:"track",event:"SIWX_AUTH_ERROR",properties:this.getSIWXEventProperties()}),await((n=Y._getClient())==null?void 0:n.disconnect().catch(console.error)),re.reset("Connect"),Lt.showError("A problem occurred while trying initialize authentication")}},async requestSignMessage(){const t=T.state.siwx,e=X.getPlainAddress(g.getActiveCaipAddress()),s=g.getActiveCaipNetwork(),r=Y._getClient();if(!t)throw new Error("SIWX is not enabled");if(!e)throw new Error("No ActiveCaipAddress found");if(!s)throw new Error("No ActiveCaipNetwork or client found");if(!r)throw new Error("No ConnectionController client found");try{const i=await t.createMessage({chainId:s.caipNetworkId,accountAddress:e}),n=i.toString();j.getConnectorId(s.chainNamespace)===W.CONNECTOR_ID.AUTH&&re.pushTransactionStack({});const a=await r.signMessage(n);await t.addSession({data:i,message:n,signature:a}),We.close(),Pe.sendEvent({type:"track",event:"SIWX_AUTH_SUCCESS",properties:this.getSIWXEventProperties()})}catch(i){const n=this.getSIWXEventProperties();(!We.state.open||re.state.view==="ApproveTransaction")&&await We.open({view:"SIWXSignMessage"}),n.isSmartAccount?Lt.showError("This application might not support Smart Accounts"):Lt.showError("Signature declined"),Pe.sendEvent({type:"track",event:"SIWX_AUTH_ERROR",properties:n}),console.error("SWIXUtil:requestSignMessage",i)}},async cancelSignMessage(){var t;try{const e=this.getSIWX();((t=e==null?void 0:e.getRequired)==null?void 0:t.call(e))?await Y.disconnect():We.close(),re.reset("Connect"),Pe.sendEvent({event:"CLICK_CANCEL_SIWX",type:"track",properties:this.getSIWXEventProperties()})}catch(e){console.error("SIWXUtil:cancelSignMessage",e)}},async getSessions(){const t=T.state.siwx,e=X.getPlainAddress(g.getActiveCaipAddress()),s=g.getActiveCaipNetwork();return t&&e&&s?t.getSessions(s.caipNetworkId,e):[]},async isSIWXCloseDisabled(){var e;const t=this.getSIWX();if(t){const s=re.state.view==="ApproveTransaction",r=re.state.view==="SIWXSignMessage";if(s||r)return((e=t.getRequired)==null?void 0:e.call(t))&&(await this.getSessions()).length===0}return!1},async universalProviderAuthenticate({universalProvider:t,chains:e,methods:s}){var a,c,l;const r=vi.getSIWX(),i=new Set(e.map(u=>u.split(":")[0]));if(!r||i.size!==1||!i.has("eip155"))return!1;const n=await r.createMessage({chainId:((a=g.getActiveCaipNetwork())==null?void 0:a.caipNetworkId)||"",accountAddress:""}),o=await t.authenticate({nonce:n.nonce,domain:n.domain,uri:n.uri,exp:n.expirationTime,iat:n.issuedAt,nbf:n.notBefore,requestId:n.requestId,version:n.version,resources:n.resources,statement:n.statement,chainId:n.chainId,methods:s,chains:[n.chainId,...e.filter(u=>u!==n.chainId)]});if(Lt.showLoading("Authenticating...",{autoClose:!1}),z.setConnectedWalletInfo({...o.session.peer.metadata,name:o.session.peer.metadata.name,icon:(c=o.session.peer.metadata.icons)==null?void 0:c[0],type:"WALLET_CONNECT"},Array.from(i)[0]),(l=o==null?void 0:o.auths)!=null&&l.length){const u=o.auths.map(h=>{const d=t.client.formatAuthMessage({request:h.p,iss:h.p.iss});return{data:{...h.p,accountAddress:h.p.iss.split(":").slice(-1).join(""),chainId:h.p.iss.split(":").slice(2,4).join(":"),uri:h.p.aud,version:h.p.version||n.version,expirationTime:h.p.exp,issuedAt:h.p.iat,notBefore:h.p.nbf},message:d,signature:h.s.s,cacao:h}});try{await r.setSessions(u),Pe.sendEvent({type:"track",event:"SIWX_AUTH_SUCCESS",properties:vi.getSIWXEventProperties()})}catch(h){throw console.error("SIWX:universalProviderAuth - failed to set sessions",h),Pe.sendEvent({type:"track",event:"SIWX_AUTH_ERROR",properties:vi.getSIWXEventProperties()}),await t.disconnect().catch(console.error),h}finally{Lt.hide()}}return!0},getSIWXEventProperties(){var e,s;const t=g.state.activeChain;return{network:((e=g.state.activeCaipNetwork)==null?void 0:e.caipNetworkId)||"",isSmartAccount:((s=z.state.preferredAccountTypes)==null?void 0:s[t])===wi.ACCOUNT_TYPES.SMART_ACCOUNT}},async clearSessions(){const t=this.getSIWX();t&&await t.setSessions([])}};function up(t){return!t||typeof t.then!="function"?Promise.resolve(t):t}function je(t,...e){try{return up(t(...e))}catch(s){return Promise.reject(s)}}function hp(t){const e=typeof t;return t===null||e!=="object"&&e!=="function"}function dp(t){const e=Object.getPrototypeOf(t);return!e||e.isPrototypeOf(Object)}function pn(t){if(hp(t))return String(t);if(dp(t)||Array.isArray(t))return JSON.stringify(t);if(typeof t.toJSON=="function")return pn(t.toJSON());throw new Error("[unstorage] Cannot stringify value!")}const xo="base64:";function pp(t){return typeof t=="string"?t:xo+mp(t)}function fp(t){return typeof t!="string"||!t.startsWith(xo)?t:gp(t.slice(xo.length))}function gp(t){return globalThis.Buffer?Buffer.from(t,"base64"):Uint8Array.from(globalThis.atob(t),e=>e.codePointAt(0))}function mp(t){return globalThis.Buffer?Buffer.from(t).toString("base64"):globalThis.btoa(String.fromCodePoint(...t))}function lt(t){var e;return t&&((e=t.split("?")[0])==null?void 0:e.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,""))||""}function wp(...t){return lt(t.join(":"))}function Qi(t){return t=lt(t),t?t+":":""}function yp(t,e){if(e===void 0)return!0;let s=0,r=t.indexOf(":");for(;r>-1;)s++,r=t.indexOf(":",r+1);return s<=e}function bp(t,e){return e?t.startsWith(e)&&t[t.length-1]!=="$":t[t.length-1]!=="$"}const vp="memory",Ep=()=>{const t=new Map;return{name:vp,getInstance:()=>t,hasItem(e){return t.has(e)},getItem(e){return t.get(e)??null},getItemRaw(e){return t.get(e)??null},setItem(e,s){t.set(e,s)},setItemRaw(e,s){t.set(e,s)},removeItem(e){t.delete(e)},getKeys(){return[...t.keys()]},clear(){t.clear()},dispose(){t.clear()}}};function Cp(t={}){const e={mounts:{"":t.driver||Ep()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},s=l=>{for(const u of e.mountpoints)if(l.startsWith(u))return{base:u,relativeKey:l.slice(u.length),driver:e.mounts[u]};return{base:"",relativeKey:l,driver:e.mounts[""]}},r=(l,u)=>e.mountpoints.filter(h=>h.startsWith(l)||u&&l.startsWith(h)).map(h=>({relativeBase:l.length>h.length?l.slice(h.length):void 0,mountpoint:h,driver:e.mounts[h]})),i=(l,u)=>{if(e.watching){u=lt(u);for(const h of e.watchListeners)h(l,u)}},n=async()=>{if(!e.watching){e.watching=!0;for(const l in e.mounts)e.unwatch[l]=await za(e.mounts[l],i,l)}},o=async()=>{if(e.watching){for(const l in e.unwatch)await e.unwatch[l]();e.unwatch={},e.watching=!1}},a=(l,u,h)=>{const d=new Map,p=w=>{let f=d.get(w.base);return f||(f={driver:w.driver,base:w.base,items:[]},d.set(w.base,f)),f};for(const w of l){const f=typeof w=="string",m=lt(f?w:w.key),y=f?void 0:w.value,b=f||!w.options?u:{...u,...w.options},v=s(m);p(v).items.push({key:m,value:y,relativeKey:v.relativeKey,options:b})}return Promise.all([...d.values()].map(w=>h(w))).then(w=>w.flat())},c={hasItem(l,u={}){l=lt(l);const{relativeKey:h,driver:d}=s(l);return je(d.hasItem,h,u)},getItem(l,u={}){l=lt(l);const{relativeKey:h,driver:d}=s(l);return je(d.getItem,h,u).then(p=>Xi(p))},getItems(l,u={}){return a(l,u,h=>h.driver.getItems?je(h.driver.getItems,h.items.map(d=>({key:d.relativeKey,options:d.options})),u).then(d=>d.map(p=>({key:wp(h.base,p.key),value:Xi(p.value)}))):Promise.all(h.items.map(d=>je(h.driver.getItem,d.relativeKey,d.options).then(p=>({key:d.key,value:Xi(p)})))))},getItemRaw(l,u={}){l=lt(l);const{relativeKey:h,driver:d}=s(l);return d.getItemRaw?je(d.getItemRaw,h,u):je(d.getItem,h,u).then(p=>fp(p))},async setItem(l,u,h={}){if(u===void 0)return c.removeItem(l);l=lt(l);const{relativeKey:d,driver:p}=s(l);p.setItem&&(await je(p.setItem,d,pn(u),h),p.watch||i("update",l))},async setItems(l,u){await a(l,u,async h=>{if(h.driver.setItems)return je(h.driver.setItems,h.items.map(d=>({key:d.relativeKey,value:pn(d.value),options:d.options})),u);h.driver.setItem&&await Promise.all(h.items.map(d=>je(h.driver.setItem,d.relativeKey,pn(d.value),d.options)))})},async setItemRaw(l,u,h={}){if(u===void 0)return c.removeItem(l,h);l=lt(l);const{relativeKey:d,driver:p}=s(l);if(p.setItemRaw)await je(p.setItemRaw,d,u,h);else if(p.setItem)await je(p.setItem,d,pp(u),h);else return;p.watch||i("update",l)},async removeItem(l,u={}){typeof u=="boolean"&&(u={removeMeta:u}),l=lt(l);const{relativeKey:h,driver:d}=s(l);d.removeItem&&(await je(d.removeItem,h,u),(u.removeMeta||u.removeMata)&&await je(d.removeItem,h+"$",u),d.watch||i("remove",l))},async getMeta(l,u={}){typeof u=="boolean"&&(u={nativeOnly:u}),l=lt(l);const{relativeKey:h,driver:d}=s(l),p=Object.create(null);if(d.getMeta&&Object.assign(p,await je(d.getMeta,h,u)),!u.nativeOnly){const w=await je(d.getItem,h+"$",u).then(f=>Xi(f));w&&typeof w=="object"&&(typeof w.atime=="string"&&(w.atime=new Date(w.atime)),typeof w.mtime=="string"&&(w.mtime=new Date(w.mtime)),Object.assign(p,w))}return p},setMeta(l,u,h={}){return this.setItem(l+"$",u,h)},removeMeta(l,u={}){return this.removeItem(l+"$",u)},async getKeys(l,u={}){var m;l=Qi(l);const h=r(l,!0);let d=[];const p=[];let w=!0;for(const y of h){(m=y.driver.flags)!=null&&m.maxDepth||(w=!1);const b=await je(y.driver.getKeys,y.relativeBase,u);for(const v of b){const C=y.mountpoint+lt(v);d.some(S=>C.startsWith(S))||p.push(C)}d=[y.mountpoint,...d.filter(v=>!v.startsWith(y.mountpoint))]}const f=u.maxDepth!==void 0&&!w;return p.filter(y=>(!f||yp(y,u.maxDepth))&&bp(y,l))},async clear(l,u={}){l=Qi(l),await Promise.all(r(l,!1).map(async h=>{if(h.driver.clear)return je(h.driver.clear,h.relativeBase,u);if(h.driver.removeItem){const d=await h.driver.getKeys(h.relativeBase||"",u);return Promise.all(d.map(p=>h.driver.removeItem(p,u)))}}))},async dispose(){await Promise.all(Object.values(e.mounts).map(l=>Wa(l)))},async watch(l){return await n(),e.watchListeners.push(l),async()=>{e.watchListeners=e.watchListeners.filter(u=>u!==l),e.watchListeners.length===0&&await o()}},async unwatch(){e.watchListeners=[],await o()},mount(l,u){if(l=Qi(l),l&&e.mounts[l])throw new Error(`already mounted at ${l}`);return l&&(e.mountpoints.push(l),e.mountpoints.sort((h,d)=>d.length-h.length)),e.mounts[l]=u,e.watching&&Promise.resolve(za(u,i,l)).then(h=>{e.unwatch[l]=h}).catch(console.error),c},async unmount(l,u=!0){var h,d;l=Qi(l),!(!l||!e.mounts[l])&&(e.watching&&l in e.unwatch&&((d=(h=e.unwatch)[l])==null||d.call(h),delete e.unwatch[l]),u&&await Wa(e.mounts[l]),e.mountpoints=e.mountpoints.filter(p=>p!==l),delete e.mounts[l])},getMount(l=""){l=lt(l)+":";const u=s(l);return{driver:u.driver,base:u.base}},getMounts(l="",u={}){return l=lt(l),r(l,u.parents).map(d=>({driver:d.driver,base:d.mountpoint}))},keys:(l,u={})=>c.getKeys(l,u),get:(l,u={})=>c.getItem(l,u),set:(l,u,h={})=>c.setItem(l,u,h),has:(l,u={})=>c.hasItem(l,u),del:(l,u={})=>c.removeItem(l,u),remove:(l,u={})=>c.removeItem(l,u)};return c}function za(t,e,s){return t.watch?t.watch((r,i)=>e(r,s+i)):()=>{}}async function Wa(t){typeof t.dispose=="function"&&await je(t.dispose)}const Ip="idb-keyval";var Ap=(t={})=>{const e=t.base&&t.base.length>0?`${t.base}:`:"",s=i=>e+i;let r;return t.dbName&&t.storeName&&(r=od(t.dbName,t.storeName)),{name:Ip,options:t,async hasItem(i){return!(typeof await $a(s(i),r)>"u")},async getItem(i){return await $a(s(i),r)??null},setItem(i,n){return ud(s(i),n,r)},removeItem(i){return ld(s(i),r)},getKeys(){return cd(r)},clear(){return ad(r)}}};const Np="WALLET_CONNECT_V2_INDEXED_DB",_p="keyvaluestorage";let Sp=class{constructor(){this.indexedDb=Cp({driver:Ap({dbName:Np,storeName:_p})})}async getKeys(){return this.indexedDb.getKeys()}async getEntries(){return(await this.indexedDb.getItems(await this.indexedDb.getKeys())).map(e=>[e.key,e.value])}async getItem(e){const s=await this.indexedDb.getItem(e);if(s!==null)return s}async setItem(e,s){await this.indexedDb.setItem(e,ha(s))}async removeItem(e){await this.indexedDb.removeItem(e)}};var Yn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},fn={exports:{}};(function(){let t;function e(){}t=e,t.prototype.getItem=function(s){return this.hasOwnProperty(s)?String(this[s]):null},t.prototype.setItem=function(s,r){this[s]=String(r)},t.prototype.removeItem=function(s){delete this[s]},t.prototype.clear=function(){const s=this;Object.keys(s).forEach(function(r){s[r]=void 0,delete s[r]})},t.prototype.key=function(s){return s=s||0,Object.keys(this)[s]},t.prototype.__defineGetter__("length",function(){return Object.keys(this).length}),typeof Yn<"u"&&Yn.localStorage?fn.exports=Yn.localStorage:typeof window<"u"&&window.localStorage?fn.exports=window.localStorage:fn.exports=new e})();function Pp(t){var e;return[t[0],In((e=t[1])!=null?e:"")]}let Op=class{constructor(){this.localStorage=fn.exports}async getKeys(){return Object.keys(this.localStorage)}async getEntries(){return Object.entries(this.localStorage).map(Pp)}async getItem(e){const s=this.localStorage.getItem(e);if(s!==null)return In(s)}async setItem(e,s){this.localStorage.setItem(e,ha(s))}async removeItem(e){this.localStorage.removeItem(e)}};const Tp="wc_storage_version",Ha=1,kp=async(t,e,s)=>{const r=Tp,i=await e.getItem(r);if(i&&i>=Ha){s(e);return}const n=await t.getKeys();if(!n.length){s(e);return}const o=[];for(;n.length;){const a=n.shift();if(!a)continue;const c=a.toLowerCase();if(c.includes("wc@")||c.includes("walletconnect")||c.includes("wc_")||c.includes("wallet_connect")){const l=await t.getItem(a);await e.setItem(a,l),o.push(a)}}await e.setItem(r,Ha),s(e),xp(t,o)},xp=async(t,e)=>{e.length&&e.forEach(async s=>{await t.removeItem(s)})};let $p=class{constructor(){this.initialized=!1,this.setInitialized=s=>{this.storage=s,this.initialized=!0};const e=new Op;this.storage=e;try{const s=new Sp;kp(e,s,this.setInitialized)}catch{this.initialized=!0}}async getKeys(){return await this.initialize(),this.storage.getKeys()}async getEntries(){return await this.initialize(),this.storage.getEntries()}async getItem(e){return await this.initialize(),this.storage.getItem(e)}async setItem(e,s){return await this.initialize(),this.storage.setItem(e,s)}async removeItem(e){return await this.initialize(),this.storage.removeItem(e)}async initialize(){this.initialized||await new Promise(e=>{const s=setInterval(()=>{this.initialized&&(clearInterval(s),e())},20)})}};var Rp=Object.defineProperty,Up=(t,e,s)=>e in t?Rp(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ka=(t,e,s)=>Up(t,typeof e!="symbol"?e+"":e,s);let Dp=class extends Hr{constructor(e){super(),this.opts=e,Ka(this,"protocol","wc"),Ka(this,"version",2)}};var Lp=Object.defineProperty,Mp=(t,e,s)=>e in t?Lp(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Bp=(t,e,s)=>Mp(t,e+"",s);let jp=class extends Hr{constructor(e,s){super(),this.core=e,this.logger=s,Bp(this,"records",new Map)}},qp=class{constructor(e,s){this.logger=e,this.core=s}},Fp=class extends Hr{constructor(e,s){super(),this.relayer=e,this.logger=s}},zp=class extends Hr{constructor(e){super()}},Wp=class{constructor(e,s,r,i){this.core=e,this.logger=s,this.name=r}},Hp=class extends Hr{constructor(e,s){super(),this.relayer=e,this.logger=s}},Kp=class extends Hr{constructor(e,s){super(),this.core=e,this.logger=s}},Vp=class{constructor(e,s,r){this.core=e,this.logger=s,this.store=r}},Gp=class{constructor(e,s){this.projectId=e,this.logger=s}},Jp=class{constructor(e,s,r){this.core=e,this.logger=s,this.telemetryEnabled=r}};var Yp=Object.defineProperty,Xp=(t,e,s)=>e in t?Yp(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Va=(t,e,s)=>Xp(t,typeof e!="symbol"?e+"":e,s);let Zp=class{constructor(e){this.opts=e,Va(this,"protocol","wc"),Va(this,"version",2)}},Qp=class{constructor(e){this.client=e}};function Pi(t,{strict:e=!0}={}){return!t||typeof t!="string"?!1:e?/^0x[0-9a-fA-F]*$/.test(t):t.startsWith("0x")}function Ga(t){return Pi(t,{strict:!1})?Math.ceil((t.length-2)/2):t.length}const cu="2.23.2";let ti={getDocsUrl:({docsBaseUrl:t,docsPath:e="",docsSlug:s})=>e?`${t??"https://viem.sh"}${e}${s?`#${s}`:""}`:void 0,version:`viem@${cu}`};class Js extends Error{constructor(e,s={}){var a;const r=(()=>{var c;return s.cause instanceof Js?s.cause.details:(c=s.cause)!=null&&c.message?s.cause.message:s.details})(),i=s.cause instanceof Js&&s.cause.docsPath||s.docsPath,n=(a=ti.getDocsUrl)==null?void 0:a.call(ti,{...s,docsPath:i}),o=[e||"An error occurred.","",...s.metaMessages?[...s.metaMessages,""]:[],...n?[`Docs: ${n}`]:[],...r?[`Details: ${r}`]:[],...ti.version?[`Version: ${ti.version}`]:[]].join(`
`);super(o,s.cause?{cause:s.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metaMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),this.details=r,this.docsPath=i,this.metaMessages=s.metaMessages,this.name=s.name??this.name,this.shortMessage=e,this.version=cu}walk(e){return lu(this,e)}}function lu(t,e){return e!=null&&e(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause!==void 0?lu(t.cause,e):e?null:t}class uu extends Js{constructor({size:e,targetSize:s,type:r}){super(`${r.charAt(0).toUpperCase()}${r.slice(1).toLowerCase()} size (${e}) exceeds padding size (${s}).`,{name:"SizeExceedsPaddingSizeError"})}}function Vr(t,{dir:e,size:s=32}={}){return typeof t=="string"?ef(t,{dir:e,size:s}):tf(t,{dir:e,size:s})}function ef(t,{dir:e,size:s=32}={}){if(s===null)return t;const r=t.replace("0x","");if(r.length>s*2)throw new uu({size:Math.ceil(r.length/2),targetSize:s,type:"hex"});return`0x${r[e==="right"?"padEnd":"padStart"](s*2,"0")}`}function tf(t,{dir:e,size:s=32}={}){if(s===null)return t;if(t.length>s)throw new uu({size:t.length,targetSize:s,type:"bytes"});const r=new Uint8Array(s);for(let i=0;i<s;i++){const n=e==="right";r[n?i:s-i-1]=t[n?i:t.length-i-1]}return r}class sf extends Js{constructor({max:e,min:s,signed:r,size:i,value:n}){super(`Number "${n}" is not in safe ${i?`${i*8}-bit ${r?"signed":"unsigned"} `:""}integer range ${e?`(${s} to ${e})`:`(above ${s})`}`,{name:"IntegerOutOfRangeError"})}}class rf extends Js{constructor({givenSize:e,maxSize:s}){super(`Size cannot exceed ${s} bytes. Given size: ${e} bytes.`,{name:"SizeOverflowError"})}}function Gr(t,{size:e}){if(Ga(t)>e)throw new rf({givenSize:Ga(t),maxSize:e})}function $o(t,e={}){const{signed:s}=e;e.size&&Gr(t,{size:e.size});const r=BigInt(t);if(!s)return r;const i=(t.length-2)/2,n=(1n<<BigInt(i)*8n-1n)-1n;return r<=n?r:r-BigInt(`0x${"f".padStart(i*2,"f")}`)-1n}function nf(t,e={}){return Number($o(t,e))}const of=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Ro(t,e={}){return typeof t=="number"||typeof t=="bigint"?du(t,e):typeof t=="string"?lf(t,e):typeof t=="boolean"?af(t,e):hu(t,e)}function af(t,e={}){const s=`0x${Number(t)}`;return typeof e.size=="number"?(Gr(s,{size:e.size}),Vr(s,{size:e.size})):s}function hu(t,e={}){let s="";for(let i=0;i<t.length;i++)s+=of[t[i]];const r=`0x${s}`;return typeof e.size=="number"?(Gr(r,{size:e.size}),Vr(r,{dir:"right",size:e.size})):r}function du(t,e={}){const{signed:s,size:r}=e,i=BigInt(t);let n;r?s?n=(1n<<BigInt(r)*8n-1n)-1n:n=2n**(BigInt(r)*8n)-1n:typeof t=="number"&&(n=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof n=="bigint"&&s?-n-1n:0;if(n&&i>n||i<o){const c=typeof t=="bigint"?"n":"";throw new sf({max:n?`${n}${c}`:void 0,min:`${o}${c}`,signed:s,size:r,value:`${t}${c}`})}const a=`0x${(s&&i<0?(1n<<BigInt(r*8))+BigInt(i):i).toString(16)}`;return r?Vr(a,{size:r}):a}const cf=new TextEncoder;function lf(t,e={}){const s=cf.encode(t);return hu(s,e)}const uf=new TextEncoder;function hf(t,e={}){return typeof t=="number"||typeof t=="bigint"?pf(t,e):typeof t=="boolean"?df(t,e):Pi(t)?pu(t,e):fu(t,e)}function df(t,e={}){const s=new Uint8Array(1);return s[0]=Number(t),typeof e.size=="number"?(Gr(s,{size:e.size}),Vr(s,{size:e.size})):s}const ss={zero:48,nine:57,A:65,F:70,a:97,f:102};function Ja(t){if(t>=ss.zero&&t<=ss.nine)return t-ss.zero;if(t>=ss.A&&t<=ss.F)return t-(ss.A-10);if(t>=ss.a&&t<=ss.f)return t-(ss.a-10)}function pu(t,e={}){let s=t;e.size&&(Gr(s,{size:e.size}),s=Vr(s,{dir:"right",size:e.size}));let r=s.slice(2);r.length%2&&(r=`0${r}`);const i=r.length/2,n=new Uint8Array(i);for(let o=0,a=0;o<i;o++){const c=Ja(r.charCodeAt(a++)),l=Ja(r.charCodeAt(a++));if(c===void 0||l===void 0)throw new Js(`Invalid byte sequence ("${r[a-2]}${r[a-1]}" in "${r}").`);n[o]=c*16+l}return n}function pf(t,e){const s=du(t,e);return pu(s)}function fu(t,e={}){const s=uf.encode(t);return typeof e.size=="number"?(Gr(s,{size:e.size}),Vr(s,{dir:"right",size:e.size})):s}function Nn(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function ff(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Un(t,...e){if(!ff(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function h1(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Nn(t.outputLen),Nn(t.blockLen)}function Ya(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function gf(t,e){Un(t);const s=e.outputLen;if(t.length<s)throw new Error("digestInto() expects output buffer of length at least "+s)}const en=BigInt(2**32-1),Xa=BigInt(32);function mf(t,e=!1){return e?{h:Number(t&en),l:Number(t>>Xa&en)}:{h:Number(t>>Xa&en)|0,l:Number(t&en)|0}}function wf(t,e=!1){let s=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:n,l:o}=mf(t[i],e);[s[i],r[i]]=[n,o]}return[s,r]}const yf=(t,e,s)=>t<<s|e>>>32-s,bf=(t,e,s)=>e<<s|t>>>32-s,vf=(t,e,s)=>e<<s-32|t>>>64-s,Ef=(t,e,s)=>t<<s-32|e>>>64-s,ar=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Cf(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function d1(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function p1(t,e){return t<<32-e|t>>>e}const Za=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function If(t){return t<<24&**********|t<<8&16711680|t>>>8&65280|t>>>24&255}function Qa(t){for(let e=0;e<t.length;e++)t[e]=If(t[e])}function Af(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function gu(t){return typeof t=="string"&&(t=Af(t)),Un(t),t}function f1(...t){let e=0;for(let r=0;r<t.length;r++){const i=t[r];Un(i),e+=i.length}const s=new Uint8Array(e);for(let r=0,i=0;r<t.length;r++){const n=t[r];s.set(n,i),i+=n.length}return s}class Nf{clone(){return this._cloneInto()}}function _f(t){const e=r=>t().update(gu(r)).digest(),s=t();return e.outputLen=s.outputLen,e.blockLen=s.blockLen,e.create=()=>t(),e}function g1(t=32){if(ar&&typeof ar.getRandomValues=="function")return ar.getRandomValues(new Uint8Array(t));if(ar&&typeof ar.randomBytes=="function")return ar.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const mu=[],wu=[],yu=[],Sf=BigInt(0),si=BigInt(1),Pf=BigInt(2),Of=BigInt(7),Tf=BigInt(256),kf=BigInt(113);for(let t=0,e=si,s=1,r=0;t<24;t++){[s,r]=[r,(2*s+3*r)%5],mu.push(2*(5*r+s)),wu.push((t+1)*(t+2)/2%64);let i=Sf;for(let n=0;n<7;n++)e=(e<<si^(e>>Of)*kf)%Tf,e&Pf&&(i^=si<<(si<<BigInt(n))-si);yu.push(i)}const[xf,$f]=wf(yu,!0),ec=(t,e,s)=>s>32?vf(t,e,s):yf(t,e,s),tc=(t,e,s)=>s>32?Ef(t,e,s):bf(t,e,s);function Rf(t,e=24){const s=new Uint32Array(10);for(let r=24-e;r<24;r++){for(let o=0;o<10;o++)s[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,l=s[c],u=s[c+1],h=ec(l,u,1)^s[a],d=tc(l,u,1)^s[a+1];for(let p=0;p<50;p+=10)t[o+p]^=h,t[o+p+1]^=d}let i=t[2],n=t[3];for(let o=0;o<24;o++){const a=wu[o],c=ec(i,n,a),l=tc(i,n,a),u=mu[o];i=t[u],n=t[u+1],t[u]=c,t[u+1]=l}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)s[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~s[(a+2)%10]&s[(a+4)%10]}t[0]^=xf[r],t[1]^=$f[r]}s.fill(0)}class ga extends Nf{constructor(e,s,r,i=!1,n=24){if(super(),this.blockLen=e,this.suffix=s,this.outputLen=r,this.enableXOF=i,this.rounds=n,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Nn(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=Cf(this.state)}keccak(){Za||Qa(this.state32),Rf(this.state32,this.rounds),Za||Qa(this.state32),this.posOut=0,this.pos=0}update(e){Ya(this);const{blockLen:s,state:r}=this;e=gu(e);const i=e.length;for(let n=0;n<i;){const o=Math.min(s-this.pos,i-n);for(let a=0;a<o;a++)r[this.pos++]^=e[n++];this.pos===s&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:s,pos:r,blockLen:i}=this;e[r]^=s,s&128&&r===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){Ya(this,!1),Un(e),this.finish();const s=this.state,{blockLen:r}=this;for(let i=0,n=e.length;i<n;){this.posOut>=r&&this.keccak();const o=Math.min(r-this.posOut,n-i);e.set(s.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return Nn(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(gf(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:s,suffix:r,outputLen:i,rounds:n,enableXOF:o}=this;return e||(e=new ga(s,r,i,o,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=r,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}}const Uf=(t,e,s)=>_f(()=>new ga(e,t,s)),Df=Uf(1,136,256/8);function bu(t,e){const s=e||"hex",r=Df(Pi(t,{strict:!1})?hf(t):t);return s==="bytes"?r:Ro(r)}class Lf extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const s=super.get(e);return super.has(e)&&s!==void 0&&(this.delete(e),super.set(e,s)),s}set(e,s){if(super.set(e,s),this.maxSize&&this.size>this.maxSize){const r=this.keys().next().value;r&&this.delete(r)}return this}}const Xn=new Lf(8192);function Mf(t,e){if(Xn.has(`${t}.${e}`))return Xn.get(`${t}.${e}`);const s=t.substring(2).toLowerCase(),r=bu(fu(s),"bytes"),i=s.split("");for(let o=0;o<40;o+=2)r[o>>1]>>4>=8&&i[o]&&(i[o]=i[o].toUpperCase()),(r[o>>1]&15)>=8&&i[o+1]&&(i[o+1]=i[o+1].toUpperCase());const n=`0x${i.join("")}`;return Xn.set(`${t}.${e}`,n),n}function Bf(t){const e=bu(`0x${t.substring(4)}`).substring(26);return Mf(`0x${e}`)}async function jf({hash:t,signature:e}){const s=Pi(t)?t:Ro(t),{secp256k1:r}=await _o(async()=>{const{secp256k1:o}=await import("./secp256k1-mtaDJVEe.js");return{secp256k1:o}},__vite__mapDeps([0,1,2,3,4,5]));return`0x${(()=>{if(typeof e=="object"&&"r"in e&&"s"in e){const{r:l,s:u,v:h,yParity:d}=e,p=Number(d??h),w=sc(p);return new r.Signature($o(l),$o(u)).addRecoveryBit(w)}const o=Pi(e)?e:Ro(e),a=nf(`0x${o.slice(130)}`),c=sc(a);return r.Signature.fromCompact(o.substring(2,130)).addRecoveryBit(c)})().recoverPublicKey(s.substring(2)).toHex(!1)}`}function sc(t){if(t===0||t===1)return t;if(t===27)return 0;if(t===28)return 1;throw new Error("Invalid yParityOrV value")}async function qf({hash:t,signature:e}){return Bf(await jf({hash:t,signature:e}))}var Ff={};const zf=":";function kr(t){const[e,s]=t.split(zf);return{namespace:e,reference:s}}function vu(t,e){return t.includes(":")?[t]:e.chains||[]}var Wf=Object.defineProperty,Hf=Object.defineProperties,Kf=Object.getOwnPropertyDescriptors,rc=Object.getOwnPropertySymbols,Vf=Object.prototype.hasOwnProperty,Gf=Object.prototype.propertyIsEnumerable,ic=(t,e,s)=>e in t?Wf(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,nc=(t,e)=>{for(var s in e||(e={}))Vf.call(e,s)&&ic(t,s,e[s]);if(rc)for(var s of rc(e))Gf.call(e,s)&&ic(t,s,e[s]);return t},Jf=(t,e)=>Hf(t,Kf(e));const Yf="ReactNative",bt={reactNative:"react-native",node:"node",browser:"browser",unknown:"unknown"},Xf="js";function _n(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}function Ts(){return!Rr()&&!!Xl()&&navigator.product===Yf}function Zf(){return Ts()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="android"}function Qf(){return Ts()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="ios"}function Jr(){return!_n()&&!!Xl()&&!!Rr()}function ji(){return Ts()?bt.reactNative:_n()?bt.node:Jr()?bt.browser:bt.unknown}function oc(){var t;try{return Ts()&&typeof global<"u"&&typeof(global==null?void 0:global.Application)<"u"?(t=global.Application)==null?void 0:t.applicationId:void 0}catch{return}}function eg(t,e){const s=new URLSearchParams(t);for(const r of Object.keys(e).sort())if(e.hasOwnProperty(r)){const i=e[r];i!==void 0&&s.set(r,i)}return s.toString()}function tg(t){var e,s;const r=Eu();try{return t!=null&&t.url&&r.url&&new URL(t.url).host!==new URL(r.url).host&&(console.warn(`The configured WalletConnect 'metadata.url':${t.url} differs from the actual page url:${r.url}. This is probably unintended and can lead to issues.`),t.url=r.url),(e=t==null?void 0:t.icons)!=null&&e.length&&t.icons.length>0&&(t.icons=t.icons.filter(i=>i!=="")),Jf(nc(nc({},r),t),{url:(t==null?void 0:t.url)||r.url,name:(t==null?void 0:t.name)||r.name,description:(t==null?void 0:t.description)||r.description,icons:(s=t==null?void 0:t.icons)!=null&&s.length&&t.icons.length>0?t.icons:r.icons})}catch(i){return console.warn("Error populating app metadata",i),t||r}}function Eu(){return Zh()||{name:"",description:"",url:"",icons:[""]}}function sg(){if(ji()===bt.reactNative&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"){const{OS:s,Version:r}=global.Platform;return[s,r].join("-")}const t=ed();if(t===null)return"unknown";const e=t.os?t.os.replace(" ","").toLowerCase():"unknown";return t.type==="browser"?[e,t.name,t.version].join("-"):[e,t.version].join("-")}function rg(){var t;const e=ji();return e===bt.browser?[e,((t=td())==null?void 0:t.host)||"unknown"].join(":"):e}function Cu(t,e,s){const r=sg(),i=rg();return[[t,e].join("-"),[Xf,s].join("-"),r,i].join("/")}function ig({protocol:t,version:e,relayUrl:s,sdkVersion:r,auth:i,projectId:n,useOnCloseEvent:o,bundleId:a,packageName:c}){const l=s.split("?"),u=Cu(t,e,r),h={auth:i,ua:u,projectId:n,useOnCloseEvent:o,packageName:c||void 0,bundleId:a||void 0},d=eg(l[1]||"",h);return l[0]+"?"+d}function zs(t,e){return t.filter(s=>e.includes(s)).length===t.length}function Uo(t){return Object.fromEntries(t.entries())}function Do(t){return new Map(Object.entries(t))}function Ls(t=D.FIVE_MINUTES,e){const s=D.toMiliseconds(t||D.FIVE_MINUTES);let r,i,n,o;return{resolve:a=>{n&&r&&(clearTimeout(n),r(a),o=Promise.resolve(a))},reject:a=>{n&&i&&(clearTimeout(n),i(a))},done:()=>new Promise((a,c)=>{if(o)return a(o);n=setTimeout(()=>{const l=new Error(e);o=Promise.reject(l),c(l)},s),r=a,i=c})}}function Cs(t,e,s){return new Promise(async(r,i)=>{const n=setTimeout(()=>i(new Error(s)),e);try{const o=await t;r(o)}catch(o){i(o)}clearTimeout(n)})}function Iu(t,e){if(typeof e=="string"&&e.startsWith(`${t}:`))return e;if(t.toLowerCase()==="topic"){if(typeof e!="string")throw new Error('Value must be "string" for expirer target type: topic');return`topic:${e}`}else if(t.toLowerCase()==="id"){if(typeof e!="number")throw new Error('Value must be "number" for expirer target type: id');return`id:${e}`}throw new Error(`Unknown expirer target type: ${t}`)}function ng(t){return Iu("topic",t)}function og(t){return Iu("id",t)}function Au(t){const[e,s]=t.split(":"),r={id:void 0,topic:void 0};if(e==="topic"&&typeof s=="string")r.topic=s;else if(e==="id"&&Number.isInteger(Number(s)))r.id=Number(s);else throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${s}`);return r}function De(t,e){return D.fromMiliseconds(Date.now()+D.toMiliseconds(t))}function ys(t){return Date.now()>=D.toMiliseconds(t)}function pe(t,e){return`${t}${e?`:${e}`:""}`}function Jt(t=[],e=[]){return[...new Set([...t,...e])]}async function ag({id:t,topic:e,wcDeepLink:s}){var r;try{if(!s)return;const i=typeof s=="string"?JSON.parse(s):s,n=i==null?void 0:i.href;if(typeof n!="string")return;const o=cg(n,t,e),a=ji();if(a===bt.browser){if(!((r=Rr())!=null&&r.hasFocus())){console.warn("Document does not have focus, skipping deeplink.");return}lg(o)}else a===bt.reactNative&&typeof(global==null?void 0:global.Linking)<"u"&&await global.Linking.openURL(o)}catch(i){console.error(i)}}function cg(t,e,s){const r=`requestId=${e}&sessionTopic=${s}`;t.endsWith("/")&&(t=t.slice(0,-1));let i=`${t}`;if(t.startsWith("https://t.me")){const n=t.includes("?")?"&startapp=":"?startapp=";i=`${i}${n}${pg(r,!0)}`}else i=`${i}/wc?${r}`;return i}function lg(t){let e="_self";dg()?e="_top":(hg()||t.startsWith("https://")||t.startsWith("http://"))&&(e="_blank"),window.open(t,e,"noreferrer noopener")}async function ug(t,e){let s="";try{if(Jr()&&(s=localStorage.getItem(e),s))return s;s=await t.getItem(e)}catch(r){console.error(r)}return s}function ac(t,e){if(!t.includes(e))return null;const s=t.split(/([&,?,=])/),r=s.indexOf(e);return s[r+2]}function cc(){return typeof crypto<"u"&&crypto!=null&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)})}function ma(){return typeof process<"u"&&Ff.IS_VITEST==="true"}function hg(){return typeof window<"u"&&(!!window.TelegramWebviewProxy||!!window.Telegram||!!window.TelegramWebviewProxyProto)}function dg(){try{return window.self!==window.top}catch{return!1}}function pg(t,e=!1){const s=Buffer.from(t).toString("base64");return e?s.replace(/[=]/g,""):s}function Nu(t){return Buffer.from(t,"base64").toString("utf-8")}function fg(t){return new Promise(e=>setTimeout(e,t))}function Oi(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function gg(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function qi(t,...e){if(!gg(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function wa(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Oi(t.outputLen),Oi(t.blockLen)}function Dr(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function _u(t,e){qi(t);const s=e.outputLen;if(t.length<s)throw new Error("digestInto() expects output buffer of length at least "+s)}const tn=BigInt(2**32-1),lc=BigInt(32);function mg(t,e=!1){return e?{h:Number(t&tn),l:Number(t>>lc&tn)}:{h:Number(t>>lc&tn)|0,l:Number(t&tn)|0}}function wg(t,e=!1){let s=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:n,l:o}=mg(t[i],e);[s[i],r[i]]=[n,o]}return[s,r]}const yg=(t,e,s)=>t<<s|e>>>32-s,bg=(t,e,s)=>e<<s|t>>>32-s,vg=(t,e,s)=>e<<s-32|t>>>64-s,Eg=(t,e,s)=>t<<s-32|e>>>64-s,cr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function Cg(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function Zn(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function qt(t,e){return t<<32-e|t>>>e}const uc=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Ig(t){return t<<24&**********|t<<8&16711680|t>>>8&65280|t>>>24&255}function hc(t){for(let e=0;e<t.length;e++)t[e]=Ig(t[e])}function Ag(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function Lr(t){return typeof t=="string"&&(t=Ag(t)),qi(t),t}function Ng(...t){let e=0;for(let r=0;r<t.length;r++){const i=t[r];qi(i),e+=i.length}const s=new Uint8Array(e);for(let r=0,i=0;r<t.length;r++){const n=t[r];s.set(n,i),i+=n.length}return s}let ya=class{clone(){return this._cloneInto()}};function Su(t){const e=r=>t().update(Lr(r)).digest(),s=t();return e.outputLen=s.outputLen,e.blockLen=s.blockLen,e.create=()=>t(),e}function Yr(t=32){if(cr&&typeof cr.getRandomValues=="function")return cr.getRandomValues(new Uint8Array(t));if(cr&&typeof cr.randomBytes=="function")return cr.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const Pu=[],Ou=[],Tu=[],_g=BigInt(0),ri=BigInt(1),Sg=BigInt(2),Pg=BigInt(7),Og=BigInt(256),Tg=BigInt(113);for(let t=0,e=ri,s=1,r=0;t<24;t++){[s,r]=[r,(2*s+3*r)%5],Pu.push(2*(5*r+s)),Ou.push((t+1)*(t+2)/2%64);let i=_g;for(let n=0;n<7;n++)e=(e<<ri^(e>>Pg)*Tg)%Og,e&Sg&&(i^=ri<<(ri<<BigInt(n))-ri);Tu.push(i)}const[kg,xg]=wg(Tu,!0),dc=(t,e,s)=>s>32?vg(t,e,s):yg(t,e,s),pc=(t,e,s)=>s>32?Eg(t,e,s):bg(t,e,s);function $g(t,e=24){const s=new Uint32Array(10);for(let r=24-e;r<24;r++){for(let o=0;o<10;o++)s[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,l=s[c],u=s[c+1],h=dc(l,u,1)^s[a],d=pc(l,u,1)^s[a+1];for(let p=0;p<50;p+=10)t[o+p]^=h,t[o+p+1]^=d}let i=t[2],n=t[3];for(let o=0;o<24;o++){const a=Ou[o],c=dc(i,n,a),l=pc(i,n,a),u=Pu[o];i=t[u],n=t[u+1],t[u]=c,t[u+1]=l}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)s[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~s[(a+2)%10]&s[(a+4)%10]}t[0]^=kg[r],t[1]^=xg[r]}s.fill(0)}let Rg=class ku extends ya{constructor(e,s,r,i=!1,n=24){if(super(),this.blockLen=e,this.suffix=s,this.outputLen=r,this.enableXOF=i,this.rounds=n,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Oi(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=Cg(this.state)}keccak(){uc||hc(this.state32),$g(this.state32,this.rounds),uc||hc(this.state32),this.posOut=0,this.pos=0}update(e){Dr(this);const{blockLen:s,state:r}=this;e=Lr(e);const i=e.length;for(let n=0;n<i;){const o=Math.min(s-this.pos,i-n);for(let a=0;a<o;a++)r[this.pos++]^=e[n++];this.pos===s&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:s,pos:r,blockLen:i}=this;e[r]^=s,s&128&&r===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){Dr(this,!1),qi(e),this.finish();const s=this.state,{blockLen:r}=this;for(let i=0,n=e.length;i<n;){this.posOut>=r&&this.keccak();const o=Math.min(r-this.posOut,n-i);e.set(s.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return Oi(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(_u(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:s,suffix:r,outputLen:i,rounds:n,enableXOF:o}=this;return e||(e=new ku(s,r,i,o,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=r,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}};const Ug=(t,e,s)=>Su(()=>new Rg(e,t,s)),Dg=Ug(1,136,256/8),Lg="https://rpc.walletconnect.org/v1";function xu(t){const e=`Ethereum Signed Message:
${t.length}`,s=new TextEncoder().encode(e+t);return"0x"+Buffer.from(Dg(s)).toString("hex")}async function Mg(t,e,s,r,i,n){switch(s.t){case"eip191":return await Bg(t,e,s.s);case"eip1271":return await jg(t,e,s.s,r,i,n);default:throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${s.t}`)}}async function Bg(t,e,s){return(await qf({hash:xu(e),signature:s})).toLowerCase()===t.toLowerCase()}async function jg(t,e,s,r,i,n){const o=kr(r);if(!o.namespace||!o.reference)throw new Error(`isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${r}`);try{const a="0x1626ba7e",c="0000000000000000000000000000000000000000000000000000000000000040",l="0000000000000000000000000000000000000000000000000000000000000041",u=s.substring(2),h=xu(e).substring(2),d=a+h+c+l+u,p=await fetch(`${n||Lg}/?chainId=${r}&projectId=${i}`,{method:"POST",body:JSON.stringify({id:qg(),jsonrpc:"2.0",method:"eth_call",params:[{to:t,data:d},"latest"]})}),{result:w}=await p.json();return w?w.slice(0,a.length).toLowerCase()===a.toLowerCase():!1}catch(a){return console.error("isValidEip1271Signature: ",a),!1}}function qg(){return Date.now()+Math.floor(Math.random()*1e3)}function Fg(t){const e=atob(t),s=new Uint8Array(e.length);for(let o=0;o<e.length;o++)s[o]=e.charCodeAt(o);const r=s[0];if(r===0)throw new Error("No signatures found");const i=1+r*64;if(s.length<i)throw new Error("Transaction data too short for claimed signature count");if(s.length<100)throw new Error("Transaction too short");const n=Buffer.from(t,"base64").slice(1,65);return Zl.encode(n)}var zg=Object.defineProperty,Wg=Object.defineProperties,Hg=Object.getOwnPropertyDescriptors,fc=Object.getOwnPropertySymbols,Kg=Object.prototype.hasOwnProperty,Vg=Object.prototype.propertyIsEnumerable,gc=(t,e,s)=>e in t?zg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Gg=(t,e)=>{for(var s in e||(e={}))Kg.call(e,s)&&gc(t,s,e[s]);if(fc)for(var s of fc(e))Vg.call(e,s)&&gc(t,s,e[s]);return t},Jg=(t,e)=>Wg(t,Hg(e));const Yg="did:pkh:",ba=t=>t==null?void 0:t.split(":"),Xg=t=>{const e=t&&ba(t);if(e)return t.includes(Yg)?e[3]:e[1]},Lo=t=>{const e=t&&ba(t);if(e)return e[2]+":"+e[3]},Sn=t=>{const e=t&&ba(t);if(e)return e.pop()};async function mc(t){const{cacao:e,projectId:s}=t,{s:r,p:i}=e,n=$u(i,i.iss),o=Sn(i.iss);return await Mg(o,n,r,Lo(i.iss),s)}const $u=(t,e)=>{const s=`${t.domain} wants you to sign in with your Ethereum account:`,r=Sn(e);if(!t.aud&&!t.uri)throw new Error("Either `aud` or `uri` is required to construct the message");let i=t.statement||void 0;const n=`URI: ${t.aud||t.uri}`,o=`Version: ${t.version}`,a=`Chain ID: ${Xg(e)}`,c=`Nonce: ${t.nonce}`,l=`Issued At: ${t.iat}`,u=t.exp?`Expiration Time: ${t.exp}`:void 0,h=t.nbf?`Not Before: ${t.nbf}`:void 0,d=t.requestId?`Request ID: ${t.requestId}`:void 0,p=t.resources?`Resources:${t.resources.map(f=>`
- ${f}`).join("")}`:void 0,w=gn(t.resources);if(w){const f=Ti(w);i=om(i,f)}return[s,r,"",i,"",n,o,a,c,l,u,h,d,p].filter(f=>f!=null).join(`
`)};function Zg(t){return Buffer.from(JSON.stringify(t)).toString("base64")}function Qg(t){return JSON.parse(Buffer.from(t,"base64").toString("utf-8"))}function Ys(t){if(!t)throw new Error("No recap provided, value is undefined");if(!t.att)throw new Error("No `att` property found");const e=Object.keys(t.att);if(!(e!=null&&e.length))throw new Error("No resources found in `att` property");e.forEach(s=>{const r=t.att[s];if(Array.isArray(r))throw new Error(`Resource must be an object: ${s}`);if(typeof r!="object")throw new Error(`Resource must be an object: ${s}`);if(!Object.keys(r).length)throw new Error(`Resource object is empty: ${s}`);Object.keys(r).forEach(i=>{const n=r[i];if(!Array.isArray(n))throw new Error(`Ability limits ${i} must be an array of objects, found: ${n}`);if(!n.length)throw new Error(`Value of ${i} is empty array, must be an array with objects`);n.forEach(o=>{if(typeof o!="object")throw new Error(`Ability limits (${i}) must be an array of objects, found: ${o}`)})})})}function em(t,e,s,r={}){return s==null||s.sort((i,n)=>i.localeCompare(n)),{att:{[t]:tm(e,s,r)}}}function tm(t,e,s={}){e=e==null?void 0:e.sort((i,n)=>i.localeCompare(n));const r=e.map(i=>({[`${t}/${i}`]:[s]}));return Object.assign({},...r)}function Ru(t){return Ys(t),`urn:recap:${Zg(t).replace(/=/g,"")}`}function Ti(t){const e=Qg(t.replace("urn:recap:",""));return Ys(e),e}function sm(t,e,s){const r=em(t,e,s);return Ru(r)}function rm(t){return t&&t.includes("urn:recap:")}function im(t,e){const s=Ti(t),r=Ti(e),i=nm(s,r);return Ru(i)}function nm(t,e){Ys(t),Ys(e);const s=Object.keys(t.att).concat(Object.keys(e.att)).sort((i,n)=>i.localeCompare(n)),r={att:{}};return s.forEach(i=>{var n,o;Object.keys(((n=t.att)==null?void 0:n[i])||{}).concat(Object.keys(((o=e.att)==null?void 0:o[i])||{})).sort((a,c)=>a.localeCompare(c)).forEach(a=>{var c,l;r.att[i]=Jg(Gg({},r.att[i]),{[a]:((c=t.att[i])==null?void 0:c[a])||((l=e.att[i])==null?void 0:l[a])})})}),r}function om(t="",e){Ys(e);const s="I further authorize the stated URI to perform the following actions on my behalf: ";if(t.includes(s))return t;const r=[];let i=0;Object.keys(e.att).forEach(a=>{const c=Object.keys(e.att[a]).map(h=>({ability:h.split("/")[0],action:h.split("/")[1]}));c.sort((h,d)=>h.action.localeCompare(d.action));const l={};c.forEach(h=>{l[h.ability]||(l[h.ability]=[]),l[h.ability].push(h.action)});const u=Object.keys(l).map(h=>(i++,`(${i}) '${h}': '${l[h].join("', '")}' for '${a}'.`));r.push(u.join(", ").replace(".,","."))});const n=r.join(" "),o=`${s}${n}`;return`${t?t+" ":""}${o}`}function wc(t){var e;const s=Ti(t);Ys(s);const r=(e=s.att)==null?void 0:e.eip155;return r?Object.keys(r).map(i=>i.split("/")[1]):[]}function yc(t){const e=Ti(t);Ys(e);const s=[];return Object.values(e.att).forEach(r=>{Object.values(r).forEach(i=>{var n;(n=i==null?void 0:i[0])!=null&&n.chains&&s.push(i[0].chains)})}),[...new Set(s.flat())]}function gn(t){if(!t)return;const e=t==null?void 0:t[t.length-1];return rm(e)?e:void 0}function Qn(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function Uu(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function yt(t,...e){if(!Uu(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function bc(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function am(t,e){yt(t);const s=e.outputLen;if(t.length<s)throw new Error("digestInto() expects output buffer of length at least "+s)}function vc(t){if(typeof t!="boolean")throw new Error(`boolean expected, not ${t}`)}const As=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4)),cm=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),lm=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!lm)throw new Error("Non little-endian hardware is not supported");function um(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}function Mo(t){if(typeof t=="string")t=um(t);else if(Uu(t))t=Bo(t);else throw new Error("Uint8Array expected, got "+typeof t);return t}function hm(t,e){if(e==null||typeof e!="object")throw new Error("options must be defined");return Object.assign(t,e)}function dm(t,e){if(t.length!==e.length)return!1;let s=0;for(let r=0;r<t.length;r++)s|=t[r]^e[r];return s===0}const pm=(t,e)=>{function s(r,...i){if(yt(r),t.nonceLength!==void 0){const l=i[0];if(!l)throw new Error("nonce / iv required");t.varSizeNonce?yt(l):yt(l,t.nonceLength)}const n=t.tagLength;n&&i[1]!==void 0&&yt(i[1]);const o=e(r,...i),a=(l,u)=>{if(u!==void 0){if(l!==2)throw new Error("cipher output not supported");yt(u)}};let c=!1;return{encrypt(l,u){if(c)throw new Error("cannot encrypt() twice with same key + nonce");return c=!0,yt(l),a(o.encrypt.length,u),o.encrypt(l,u)},decrypt(l,u){if(yt(l),n&&l.length<n)throw new Error("invalid ciphertext length: smaller than tagLength="+n);return a(o.decrypt.length,u),o.decrypt(l,u)}}}return Object.assign(s,t),s};function Ec(t,e,s=!0){if(e===void 0)return new Uint8Array(t);if(e.length!==t)throw new Error("invalid output length, expected "+t+", got: "+e.length);if(s&&!fm(e))throw new Error("invalid output, must be aligned");return e}function Cc(t,e,s,r){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,s,r);const i=BigInt(32),n=BigInt(4294967295),o=Number(s>>i&n),a=Number(s&n);t.setUint32(e+4,o,r),t.setUint32(e+0,a,r)}function fm(t){return t.byteOffset%4===0}function Bo(t){return Uint8Array.from(t)}function Mr(...t){for(let e=0;e<t.length;e++)t[e].fill(0)}const Du=t=>Uint8Array.from(t.split("").map(e=>e.charCodeAt(0))),gm=Du("expand 16-byte k"),mm=Du("expand 32-byte k"),wm=As(gm),ym=As(mm);function ce(t,e){return t<<e|t>>>32-e}function jo(t){return t.byteOffset%4===0}const sn=64,bm=16,Lu=2**32-1,Ic=new Uint32Array;function vm(t,e,s,r,i,n,o,a){const c=i.length,l=new Uint8Array(sn),u=As(l),h=jo(i)&&jo(n),d=h?As(i):Ic,p=h?As(n):Ic;for(let w=0;w<c;o++){if(t(e,s,r,u,o,a),o>=Lu)throw new Error("arx: counter overflow");const f=Math.min(sn,c-w);if(h&&f===sn){const m=w/4;if(w%4!==0)throw new Error("arx: invalid block position");for(let y=0,b;y<bm;y++)b=m+y,p[b]=d[b]^u[y];w+=sn;continue}for(let m=0,y;m<f;m++)y=w+m,n[y]=i[y]^l[m];w+=f}}function Em(t,e){const{allowShortKeys:s,extendNonceFn:r,counterLength:i,counterRight:n,rounds:o}=hm({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},e);if(typeof t!="function")throw new Error("core must be a function");return Qn(i),Qn(o),vc(n),vc(s),(a,c,l,u,h=0)=>{yt(a),yt(c),yt(l);const d=l.length;if(u===void 0&&(u=new Uint8Array(d)),yt(u),Qn(h),h<0||h>=Lu)throw new Error("arx: counter overflow");if(u.length<d)throw new Error(`arx: output (${u.length}) is shorter than data (${d})`);const p=[];let w=a.length,f,m;if(w===32)p.push(f=Bo(a)),m=ym;else if(w===16&&s)f=new Uint8Array(32),f.set(a),f.set(a,16),m=wm,p.push(f);else throw new Error(`arx: invalid 32-byte key, got length=${w}`);jo(c)||p.push(c=Bo(c));const y=As(f);if(r){if(c.length!==24)throw new Error("arx: extended nonce must be 24 bytes");r(m,y,As(c.subarray(0,16)),y),c=c.subarray(16)}const b=16-i;if(b!==c.length)throw new Error(`arx: nonce must be ${b} or 16 bytes`);if(b!==12){const C=new Uint8Array(12);C.set(c,n?0:12-c.length),c=C,p.push(c)}const v=As(c);return vm(t,m,y,v,l,u,h,o),Mr(...p),u}}const Ke=(t,e)=>t[e++]&255|(t[e++]&255)<<8;class Cm{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,e=Mo(e),yt(e,32);const s=Ke(e,0),r=Ke(e,2),i=Ke(e,4),n=Ke(e,6),o=Ke(e,8),a=Ke(e,10),c=Ke(e,12),l=Ke(e,14);this.r[0]=s&8191,this.r[1]=(s>>>13|r<<3)&8191,this.r[2]=(r>>>10|i<<6)&7939,this.r[3]=(i>>>7|n<<9)&8191,this.r[4]=(n>>>4|o<<12)&255,this.r[5]=o>>>1&8190,this.r[6]=(o>>>14|a<<2)&8191,this.r[7]=(a>>>11|c<<5)&8065,this.r[8]=(c>>>8|l<<8)&8191,this.r[9]=l>>>5&127;for(let u=0;u<8;u++)this.pad[u]=Ke(e,16+2*u)}process(e,s,r=!1){const i=r?0:2048,{h:n,r:o}=this,a=o[0],c=o[1],l=o[2],u=o[3],h=o[4],d=o[5],p=o[6],w=o[7],f=o[8],m=o[9],y=Ke(e,s+0),b=Ke(e,s+2),v=Ke(e,s+4),C=Ke(e,s+6),S=Ke(e,s+8),I=Ke(e,s+10),_=Ke(e,s+12),U=Ke(e,s+14);let E=n[0]+(y&8191),$=n[1]+((y>>>13|b<<3)&8191),A=n[2]+((b>>>10|v<<6)&8191),L=n[3]+((v>>>7|C<<9)&8191),H=n[4]+((C>>>4|S<<12)&8191),N=n[5]+(S>>>1&8191),k=n[6]+((S>>>14|I<<2)&8191),O=n[7]+((I>>>11|_<<5)&8191),B=n[8]+((_>>>8|U<<8)&8191),q=n[9]+(U>>>5|i),x=0,G=x+E*a+$*(5*m)+A*(5*f)+L*(5*w)+H*(5*p);x=G>>>13,G&=8191,G+=N*(5*d)+k*(5*h)+O*(5*u)+B*(5*l)+q*(5*c),x+=G>>>13,G&=8191;let Q=x+E*c+$*a+A*(5*m)+L*(5*f)+H*(5*w);x=Q>>>13,Q&=8191,Q+=N*(5*p)+k*(5*d)+O*(5*h)+B*(5*u)+q*(5*l),x+=Q>>>13,Q&=8191;let se=x+E*l+$*c+A*a+L*(5*m)+H*(5*f);x=se>>>13,se&=8191,se+=N*(5*w)+k*(5*p)+O*(5*d)+B*(5*h)+q*(5*u),x+=se>>>13,se&=8191;let Ce=x+E*u+$*l+A*c+L*a+H*(5*m);x=Ce>>>13,Ce&=8191,Ce+=N*(5*f)+k*(5*w)+O*(5*p)+B*(5*d)+q*(5*h),x+=Ce>>>13,Ce&=8191;let de=x+E*h+$*u+A*l+L*c+H*a;x=de>>>13,de&=8191,de+=N*(5*m)+k*(5*f)+O*(5*w)+B*(5*p)+q*(5*d),x+=de>>>13,de&=8191;let Te=x+E*d+$*h+A*u+L*l+H*c;x=Te>>>13,Te&=8191,Te+=N*a+k*(5*m)+O*(5*f)+B*(5*w)+q*(5*p),x+=Te>>>13,Te&=8191;let Le=x+E*p+$*d+A*h+L*u+H*l;x=Le>>>13,Le&=8191,Le+=N*c+k*a+O*(5*m)+B*(5*f)+q*(5*w),x+=Le>>>13,Le&=8191;let Xe=x+E*w+$*p+A*d+L*h+H*u;x=Xe>>>13,Xe&=8191,Xe+=N*l+k*c+O*a+B*(5*m)+q*(5*f),x+=Xe>>>13,Xe&=8191;let $e=x+E*f+$*w+A*p+L*d+H*h;x=$e>>>13,$e&=8191,$e+=N*u+k*l+O*c+B*a+q*(5*m),x+=$e>>>13,$e&=8191;let Re=x+E*m+$*f+A*w+L*p+H*d;x=Re>>>13,Re&=8191,Re+=N*h+k*u+O*l+B*c+q*a,x+=Re>>>13,Re&=8191,x=(x<<2)+x|0,x=x+G|0,G=x&8191,x=x>>>13,Q+=x,n[0]=G,n[1]=Q,n[2]=se,n[3]=Ce,n[4]=de,n[5]=Te,n[6]=Le,n[7]=Xe,n[8]=$e,n[9]=Re}finalize(){const{h:e,pad:s}=this,r=new Uint16Array(10);let i=e[1]>>>13;e[1]&=8191;for(let a=2;a<10;a++)e[a]+=i,i=e[a]>>>13,e[a]&=8191;e[0]+=i*5,i=e[0]>>>13,e[0]&=8191,e[1]+=i,i=e[1]>>>13,e[1]&=8191,e[2]+=i,r[0]=e[0]+5,i=r[0]>>>13,r[0]&=8191;for(let a=1;a<10;a++)r[a]=e[a]+i,i=r[a]>>>13,r[a]&=8191;r[9]-=8192;let n=(i^1)-1;for(let a=0;a<10;a++)r[a]&=n;n=~n;for(let a=0;a<10;a++)e[a]=e[a]&n|r[a];e[0]=(e[0]|e[1]<<13)&65535,e[1]=(e[1]>>>3|e[2]<<10)&65535,e[2]=(e[2]>>>6|e[3]<<7)&65535,e[3]=(e[3]>>>9|e[4]<<4)&65535,e[4]=(e[4]>>>12|e[5]<<1|e[6]<<14)&65535,e[5]=(e[6]>>>2|e[7]<<11)&65535,e[6]=(e[7]>>>5|e[8]<<8)&65535,e[7]=(e[8]>>>8|e[9]<<5)&65535;let o=e[0]+s[0];e[0]=o&65535;for(let a=1;a<8;a++)o=(e[a]+s[a]|0)+(o>>>16)|0,e[a]=o&65535;Mr(r)}update(e){bc(this);const{buffer:s,blockLen:r}=this;e=Mo(e);const i=e.length;for(let n=0;n<i;){const o=Math.min(r-this.pos,i-n);if(o===r){for(;r<=i-n;n+=r)this.process(e,n);continue}s.set(e.subarray(n,n+o),this.pos),this.pos+=o,n+=o,this.pos===r&&(this.process(s,0,!1),this.pos=0)}return this}destroy(){Mr(this.h,this.r,this.buffer,this.pad)}digestInto(e){bc(this),am(e,this),this.finished=!0;const{buffer:s,h:r}=this;let{pos:i}=this;if(i){for(s[i++]=1;i<16;i++)s[i]=0;this.process(s,0,!0)}this.finalize();let n=0;for(let o=0;o<8;o++)e[n++]=r[o]>>>0,e[n++]=r[o]>>>8;return e}digest(){const{buffer:e,outputLen:s}=this;this.digestInto(e);const r=e.slice(0,s);return this.destroy(),r}}function Im(t){const e=(r,i)=>t(i).update(Mo(r)).digest(),s=t(new Uint8Array(32));return e.outputLen=s.outputLen,e.blockLen=s.blockLen,e.create=r=>t(r),e}const Am=Im(t=>new Cm(t));function Nm(t,e,s,r,i,n=20){let o=t[0],a=t[1],c=t[2],l=t[3],u=e[0],h=e[1],d=e[2],p=e[3],w=e[4],f=e[5],m=e[6],y=e[7],b=i,v=s[0],C=s[1],S=s[2],I=o,_=a,U=c,E=l,$=u,A=h,L=d,H=p,N=w,k=f,O=m,B=y,q=b,x=v,G=C,Q=S;for(let Ce=0;Ce<n;Ce+=2)I=I+$|0,q=ce(q^I,16),N=N+q|0,$=ce($^N,12),I=I+$|0,q=ce(q^I,8),N=N+q|0,$=ce($^N,7),_=_+A|0,x=ce(x^_,16),k=k+x|0,A=ce(A^k,12),_=_+A|0,x=ce(x^_,8),k=k+x|0,A=ce(A^k,7),U=U+L|0,G=ce(G^U,16),O=O+G|0,L=ce(L^O,12),U=U+L|0,G=ce(G^U,8),O=O+G|0,L=ce(L^O,7),E=E+H|0,Q=ce(Q^E,16),B=B+Q|0,H=ce(H^B,12),E=E+H|0,Q=ce(Q^E,8),B=B+Q|0,H=ce(H^B,7),I=I+A|0,Q=ce(Q^I,16),O=O+Q|0,A=ce(A^O,12),I=I+A|0,Q=ce(Q^I,8),O=O+Q|0,A=ce(A^O,7),_=_+L|0,q=ce(q^_,16),B=B+q|0,L=ce(L^B,12),_=_+L|0,q=ce(q^_,8),B=B+q|0,L=ce(L^B,7),U=U+H|0,x=ce(x^U,16),N=N+x|0,H=ce(H^N,12),U=U+H|0,x=ce(x^U,8),N=N+x|0,H=ce(H^N,7),E=E+$|0,G=ce(G^E,16),k=k+G|0,$=ce($^k,12),E=E+$|0,G=ce(G^E,8),k=k+G|0,$=ce($^k,7);let se=0;r[se++]=o+I|0,r[se++]=a+_|0,r[se++]=c+U|0,r[se++]=l+E|0,r[se++]=u+$|0,r[se++]=h+A|0,r[se++]=d+L|0,r[se++]=p+H|0,r[se++]=w+N|0,r[se++]=f+k|0,r[se++]=m+O|0,r[se++]=y+B|0,r[se++]=b+q|0,r[se++]=v+x|0,r[se++]=C+G|0,r[se++]=S+Q|0}const _m=Em(Nm,{counterRight:!1,counterLength:4,allowShortKeys:!1}),Sm=new Uint8Array(16),Ac=(t,e)=>{t.update(e);const s=e.length%16;s&&t.update(Sm.subarray(s))},Pm=new Uint8Array(32);function Nc(t,e,s,r,i){const n=t(e,s,Pm),o=Am.create(n);i&&Ac(o,i),Ac(o,r);const a=new Uint8Array(16),c=cm(a);Cc(c,0,BigInt(i?i.length:0),!0),Cc(c,8,BigInt(r.length),!0),o.update(a);const l=o.digest();return Mr(n,a),l}const Om=t=>(e,s,r)=>({encrypt(i,n){const o=i.length;n=Ec(o+16,n,!1),n.set(i);const a=n.subarray(0,-16);t(e,s,a,a,1);const c=Nc(t,e,s,a,r);return n.set(c,o),Mr(c),n},decrypt(i,n){n=Ec(i.length-16,n,!1);const o=i.subarray(0,-16),a=i.subarray(-16),c=Nc(t,e,s,o,r);if(!dm(a,c))throw new Error("invalid tag");return n.set(i.subarray(0,-16)),t(e,s,n,n,1),Mr(c),n}}),Mu=pm({blockSize:64,nonceLength:12,tagLength:16},Om(_m));let Bu=class extends ya{constructor(e,s){super(),this.finished=!1,this.destroyed=!1,wa(e);const r=Lr(s);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,n=new Uint8Array(i);n.set(r.length>i?e.create().update(r).digest():r);for(let o=0;o<n.length;o++)n[o]^=54;this.iHash.update(n),this.oHash=e.create();for(let o=0;o<n.length;o++)n[o]^=106;this.oHash.update(n),n.fill(0)}update(e){return Dr(this),this.iHash.update(e),this}digestInto(e){Dr(this),qi(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:s,iHash:r,finished:i,destroyed:n,blockLen:o,outputLen:a}=this;return e=e,e.finished=i,e.destroyed=n,e.blockLen=o,e.outputLen=a,e.oHash=s._cloneInto(e.oHash),e.iHash=r._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}};const Dn=(t,e,s)=>new Bu(t,e).update(s).digest();Dn.create=(t,e)=>new Bu(t,e);function Tm(t,e,s){return wa(t),s===void 0&&(s=new Uint8Array(t.outputLen)),Dn(t,Lr(s),Lr(e))}const eo=new Uint8Array([0]),_c=new Uint8Array;function km(t,e,s,r=32){if(wa(t),Oi(r),r>255*t.outputLen)throw new Error("Length should be <= 255*HashLen");const i=Math.ceil(r/t.outputLen);s===void 0&&(s=_c);const n=new Uint8Array(i*t.outputLen),o=Dn.create(t,e),a=o._cloneInto(),c=new Uint8Array(o.outputLen);for(let l=0;l<i;l++)eo[0]=l+1,a.update(l===0?_c:c).update(s).update(eo).digestInto(c),n.set(c,t.outputLen*l),o._cloneInto(a);return o.destroy(),a.destroy(),c.fill(0),eo.fill(0),n.slice(0,r)}const xm=(t,e,s,r,i)=>km(t,Tm(t,e,s),r,i);function $m(t,e,s,r){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,s,r);const i=BigInt(32),n=BigInt(4294967295),o=Number(s>>i&n),a=Number(s&n),c=r?4:0,l=r?0:4;t.setUint32(e+c,o,r),t.setUint32(e+l,a,r)}function Rm(t,e,s){return t&e^~t&s}function Um(t,e,s){return t&e^t&s^e&s}let Dm=class extends ya{constructor(e,s,r,i){super(),this.blockLen=e,this.outputLen=s,this.padOffset=r,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Zn(this.buffer)}update(e){Dr(this);const{view:s,buffer:r,blockLen:i}=this;e=Lr(e);const n=e.length;for(let o=0;o<n;){const a=Math.min(i-this.pos,n-o);if(a===i){const c=Zn(e);for(;i<=n-o;o+=i)this.process(c,o);continue}r.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(s,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Dr(this),_u(e,this),this.finished=!0;const{buffer:s,view:r,blockLen:i,isLE:n}=this;let{pos:o}=this;s[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(r,0),o=0);for(let h=o;h<i;h++)s[h]=0;$m(r,i-8,BigInt(this.length*8),n),this.process(r,0);const a=Zn(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const l=c/4,u=this.get();if(l>u.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<l;h++)a.setUint32(4*h,u[h],n)}digest(){const{buffer:e,outputLen:s}=this;this.digestInto(e);const r=e.slice(0,s);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:s,buffer:r,length:i,finished:n,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=n,e.destroyed=o,i%s&&e.buffer.set(r),e}};const Lm=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),fs=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),gs=new Uint32Array(64);class Mm extends Dm{constructor(){super(64,32,8,!1),this.A=fs[0]|0,this.B=fs[1]|0,this.C=fs[2]|0,this.D=fs[3]|0,this.E=fs[4]|0,this.F=fs[5]|0,this.G=fs[6]|0,this.H=fs[7]|0}get(){const{A:e,B:s,C:r,D:i,E:n,F:o,G:a,H:c}=this;return[e,s,r,i,n,o,a,c]}set(e,s,r,i,n,o,a,c){this.A=e|0,this.B=s|0,this.C=r|0,this.D=i|0,this.E=n|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,s){for(let h=0;h<16;h++,s+=4)gs[h]=e.getUint32(s,!1);for(let h=16;h<64;h++){const d=gs[h-15],p=gs[h-2],w=qt(d,7)^qt(d,18)^d>>>3,f=qt(p,17)^qt(p,19)^p>>>10;gs[h]=f+gs[h-7]+w+gs[h-16]|0}let{A:r,B:i,C:n,D:o,E:a,F:c,G:l,H:u}=this;for(let h=0;h<64;h++){const d=qt(a,6)^qt(a,11)^qt(a,25),p=u+d+Rm(a,c,l)+Lm[h]+gs[h]|0,w=(qt(r,2)^qt(r,13)^qt(r,22))+Um(r,i,n)|0;u=l,l=c,c=a,a=o+p|0,o=n,n=i,i=r,r=p+w|0}r=r+this.A|0,i=i+this.B|0,n=n+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,l=l+this.G|0,u=u+this.H|0,this.set(r,i,n,o,a,c,l,u)}roundClean(){gs.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Fi=Su(()=>new Mm);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Ln=BigInt(0),Mn=BigInt(1),Bm=BigInt(2);function Xs(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function zi(t){if(!Xs(t))throw new Error("Uint8Array expected")}function Br(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const jm=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function jr(t){zi(t);let e="";for(let s=0;s<t.length;s++)e+=jm[t[s]];return e}function Pr(t){const e=t.toString(16);return e.length&1?"0"+e:e}function va(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?Ln:BigInt("0x"+t)}const rs={_0:48,_9:57,A:65,F:70,a:97,f:102};function Sc(t){if(t>=rs._0&&t<=rs._9)return t-rs._0;if(t>=rs.A&&t<=rs.F)return t-(rs.A-10);if(t>=rs.a&&t<=rs.f)return t-(rs.a-10)}function qr(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,s=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(s);for(let i=0,n=0;i<s;i++,n+=2){const o=Sc(t.charCodeAt(n)),a=Sc(t.charCodeAt(n+1));if(o===void 0||a===void 0){const c=t[n]+t[n+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+n)}r[i]=o*16+a}return r}function Hs(t){return va(jr(t))}function ki(t){return zi(t),va(jr(Uint8Array.from(t).reverse()))}function Fr(t,e){return qr(t.toString(16).padStart(e*2,"0"))}function Bn(t,e){return Fr(t,e).reverse()}function qm(t){return qr(Pr(t))}function wt(t,e,s){let r;if(typeof e=="string")try{r=qr(e)}catch(n){throw new Error(t+" must be hex string or Uint8Array, cause: "+n)}else if(Xs(e))r=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=r.length;if(typeof s=="number"&&i!==s)throw new Error(t+" of length "+s+" expected, got "+i);return r}function xi(...t){let e=0;for(let r=0;r<t.length;r++){const i=t[r];zi(i),e+=i.length}const s=new Uint8Array(e);for(let r=0,i=0;r<t.length;r++){const n=t[r];s.set(n,i),i+=n.length}return s}function Fm(t,e){if(t.length!==e.length)return!1;let s=0;for(let r=0;r<t.length;r++)s|=t[r]^e[r];return s===0}function zm(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const to=t=>typeof t=="bigint"&&Ln<=t;function jn(t,e,s){return to(t)&&to(e)&&to(s)&&e<=t&&t<s}function cs(t,e,s,r){if(!jn(e,s,r))throw new Error("expected valid "+t+": "+s+" <= n < "+r+", got "+e)}function ju(t){let e;for(e=0;t>Ln;t>>=Mn,e+=1);return e}function Wm(t,e){return t>>BigInt(e)&Mn}function Hm(t,e,s){return t|(s?Mn:Ln)<<BigInt(e)}const Ea=t=>(Bm<<BigInt(t-1))-Mn,so=t=>new Uint8Array(t),Pc=t=>Uint8Array.from(t);function qu(t,e,s){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof s!="function")throw new Error("hmacFn must be a function");let r=so(t),i=so(t),n=0;const o=()=>{r.fill(1),i.fill(0),n=0},a=(...u)=>s(i,r,...u),c=(u=so())=>{i=a(Pc([0]),u),r=a(),u.length!==0&&(i=a(Pc([1]),u),r=a())},l=()=>{if(n++>=1e3)throw new Error("drbg: tried 1000 values");let u=0;const h=[];for(;u<e;){r=a();const d=r.slice();h.push(d),u+=r.length}return xi(...h)};return(u,h)=>{o(),c(u);let d;for(;!(d=h(l()));)c();return o(),d}}const Km={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Xs(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function Xr(t,e,s={}){const r=(i,n,o)=>{const a=Km[n];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+n+", got "+c)};for(const[i,n]of Object.entries(e))r(i,n,!1);for(const[i,n]of Object.entries(s))r(i,n,!0);return t}const Vm=()=>{throw new Error("not implemented")};function qo(t){const e=new WeakMap;return(s,...r)=>{const i=e.get(s);if(i!==void 0)return i;const n=t(s,...r);return e.set(s,n),n}}var Gm=Object.freeze({__proto__:null,isBytes:Xs,abytes:zi,abool:Br,bytesToHex:jr,numberToHexUnpadded:Pr,hexToNumber:va,hexToBytes:qr,bytesToNumberBE:Hs,bytesToNumberLE:ki,numberToBytesBE:Fr,numberToBytesLE:Bn,numberToVarBytesBE:qm,ensureBytes:wt,concatBytes:xi,equalBytes:Fm,utf8ToBytes:zm,inRange:jn,aInRange:cs,bitLen:ju,bitGet:Wm,bitSet:Hm,bitMask:Ea,createHmacDrbg:qu,validateObject:Xr,notImplemented:Vm,memoized:qo});const He=BigInt(0),Oe=BigInt(1),Bs=BigInt(2),Jm=BigInt(3),Fo=BigInt(4),Oc=BigInt(5),Tc=BigInt(8);function ht(t,e){const s=t%e;return s>=He?s:e+s}function Fu(t,e,s){if(e<He)throw new Error("invalid exponent, negatives unsupported");if(s<=He)throw new Error("invalid modulus");if(s===Oe)return He;let r=Oe;for(;e>He;)e&Oe&&(r=r*t%s),t=t*t%s,e>>=Oe;return r}function Ut(t,e,s){let r=t;for(;e-- >He;)r*=r,r%=s;return r}function zo(t,e){if(t===He)throw new Error("invert: expected non-zero number");if(e<=He)throw new Error("invert: expected positive modulus, got "+e);let s=ht(t,e),r=e,i=He,n=Oe;for(;s!==He;){const o=r/s,a=r%s,c=i-n*o;r=s,s=a,i=n,n=c}if(r!==Oe)throw new Error("invert: does not exist");return ht(i,e)}function Ym(t){const e=(t-Oe)/Bs;let s,r,i;for(s=t-Oe,r=0;s%Bs===He;s/=Bs,r++);for(i=Bs;i<t&&Fu(i,e,t)!==t-Oe;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(r===1){const o=(t+Oe)/Fo;return function(a,c){const l=a.pow(c,o);if(!a.eql(a.sqr(l),c))throw new Error("Cannot find square root");return l}}const n=(s+Oe)/Bs;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=r,l=o.pow(o.mul(o.ONE,i),s),u=o.pow(a,n),h=o.pow(a,s);for(;!o.eql(h,o.ONE);){if(o.eql(h,o.ZERO))return o.ZERO;let d=1;for(let w=o.sqr(h);d<c&&!o.eql(w,o.ONE);d++)w=o.sqr(w);const p=o.pow(l,Oe<<BigInt(c-d-1));l=o.sqr(p),u=o.mul(u,p),h=o.mul(h,l),c=d}return u}}function Xm(t){if(t%Fo===Jm){const e=(t+Oe)/Fo;return function(s,r){const i=s.pow(r,e);if(!s.eql(s.sqr(i),r))throw new Error("Cannot find square root");return i}}if(t%Tc===Oc){const e=(t-Oc)/Tc;return function(s,r){const i=s.mul(r,Bs),n=s.pow(i,e),o=s.mul(r,n),a=s.mul(s.mul(o,Bs),n),c=s.mul(o,s.sub(a,s.ONE));if(!s.eql(s.sqr(c),r))throw new Error("Cannot find square root");return c}}return Ym(t)}const Zm=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Qm(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},s=Zm.reduce((r,i)=>(r[i]="function",r),e);return Xr(t,s)}function ew(t,e,s){if(s<He)throw new Error("invalid exponent, negatives unsupported");if(s===He)return t.ONE;if(s===Oe)return e;let r=t.ONE,i=e;for(;s>He;)s&Oe&&(r=t.mul(r,i)),i=t.sqr(i),s>>=Oe;return r}function tw(t,e){const s=new Array(e.length),r=e.reduce((n,o,a)=>t.is0(o)?n:(s[a]=n,t.mul(n,o)),t.ONE),i=t.inv(r);return e.reduceRight((n,o,a)=>t.is0(o)?n:(s[a]=t.mul(n,s[a]),t.mul(n,o)),i),s}function zu(t,e){const s=e!==void 0?e:t.toString(2).length,r=Math.ceil(s/8);return{nBitLength:s,nByteLength:r}}function Wu(t,e,s=!1,r={}){if(t<=He)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:n}=zu(t,e);if(n>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:s,BITS:i,BYTES:n,MASK:Ea(i),ZERO:He,ONE:Oe,create:c=>ht(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return He<=c&&c<t},is0:c=>c===He,isOdd:c=>(c&Oe)===Oe,neg:c=>ht(-c,t),eql:(c,l)=>c===l,sqr:c=>ht(c*c,t),add:(c,l)=>ht(c+l,t),sub:(c,l)=>ht(c-l,t),mul:(c,l)=>ht(c*l,t),pow:(c,l)=>ew(a,c,l),div:(c,l)=>ht(c*zo(l,t),t),sqrN:c=>c*c,addN:(c,l)=>c+l,subN:(c,l)=>c-l,mulN:(c,l)=>c*l,inv:c=>zo(c,t),sqrt:r.sqrt||(c=>(o||(o=Xm(t)),o(a,c))),invertBatch:c=>tw(a,c),cmov:(c,l,u)=>u?l:c,toBytes:c=>s?Bn(c,n):Fr(c,n),fromBytes:c=>{if(c.length!==n)throw new Error("Field.fromBytes: expected "+n+" bytes, got "+c.length);return s?ki(c):Hs(c)}});return Object.freeze(a)}function Hu(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function Ku(t){const e=Hu(t);return e+Math.ceil(e/2)}function sw(t,e,s=!1){const r=t.length,i=Hu(e),n=Ku(e);if(r<16||r<n||r>1024)throw new Error("expected "+n+"-1024 bytes of input, got "+r);const o=s?ki(t):Hs(t),a=ht(o,e-Oe)+Oe;return s?Bn(a,i):Fr(a,i)}const kc=BigInt(0),rn=BigInt(1);function ro(t,e){const s=e.negate();return t?s:e}function Vu(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function io(t,e){Vu(t,e);const s=Math.ceil(e/t)+1,r=2**(t-1);return{windows:s,windowSize:r}}function rw(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((s,r)=>{if(!(s instanceof e))throw new Error("invalid point at index "+r)})}function iw(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((s,r)=>{if(!e.isValid(s))throw new Error("invalid scalar at index "+r)})}const no=new WeakMap,Gu=new WeakMap;function oo(t){return Gu.get(t)||1}function nw(t,e){return{constTimeNegate:ro,hasPrecomputes(s){return oo(s)!==1},unsafeLadder(s,r,i=t.ZERO){let n=s;for(;r>kc;)r&rn&&(i=i.add(n)),n=n.double(),r>>=rn;return i},precomputeWindow(s,r){const{windows:i,windowSize:n}=io(r,e),o=[];let a=s,c=a;for(let l=0;l<i;l++){c=a,o.push(c);for(let u=1;u<n;u++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(s,r,i){const{windows:n,windowSize:o}=io(s,e);let a=t.ZERO,c=t.BASE;const l=BigInt(2**s-1),u=2**s,h=BigInt(s);for(let d=0;d<n;d++){const p=d*o;let w=Number(i&l);i>>=h,w>o&&(w-=u,i+=rn);const f=p,m=p+Math.abs(w)-1,y=d%2!==0,b=w<0;w===0?c=c.add(ro(y,r[f])):a=a.add(ro(b,r[m]))}return{p:a,f:c}},wNAFUnsafe(s,r,i,n=t.ZERO){const{windows:o,windowSize:a}=io(s,e),c=BigInt(2**s-1),l=2**s,u=BigInt(s);for(let h=0;h<o;h++){const d=h*a;if(i===kc)break;let p=Number(i&c);if(i>>=u,p>a&&(p-=l,i+=rn),p===0)continue;let w=r[d+Math.abs(p)-1];p<0&&(w=w.negate()),n=n.add(w)}return n},getPrecomputes(s,r,i){let n=no.get(r);return n||(n=this.precomputeWindow(r,s),s!==1&&no.set(r,i(n))),n},wNAFCached(s,r,i){const n=oo(s);return this.wNAF(n,this.getPrecomputes(n,s,i),r)},wNAFCachedUnsafe(s,r,i,n){const o=oo(s);return o===1?this.unsafeLadder(s,r,n):this.wNAFUnsafe(o,this.getPrecomputes(o,s,i),r,n)},setWindowSize(s,r){Vu(r,e),Gu.set(s,r),no.delete(s)}}}function ow(t,e,s,r){if(rw(s,t),iw(r,e),s.length!==r.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,n=ju(BigInt(s.length)),o=n>12?n-3:n>4?n-2:n?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),l=Math.floor((e.BITS-1)/o)*o;let u=i;for(let h=l;h>=0;h-=o){c.fill(i);for(let p=0;p<r.length;p++){const w=r[p],f=Number(w>>BigInt(h)&BigInt(a));c[f]=c[f].add(s[p])}let d=i;for(let p=c.length-1,w=i;p>0;p--)w=w.add(c[p]),d=d.add(w);if(u=u.add(d),h!==0)for(let p=0;p<o;p++)u=u.double()}return u}function Ju(t){return Qm(t.Fp),Xr(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...zu(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}BigInt(0),BigInt(1),BigInt(2),BigInt(8);const lr=BigInt(0),ao=BigInt(1);function aw(t){return Xr(t,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze({...t})}function cw(t){const e=aw(t),{P:s}=e,r=b=>ht(b,s),i=e.montgomeryBits,n=Math.ceil(i/8),o=e.nByteLength,a=e.adjustScalarBytes||(b=>b),c=e.powPminus2||(b=>Fu(b,s-BigInt(2),s));function l(b,v,C){const S=r(b*(v-C));return v=r(v-S),C=r(C+S),[v,C]}const u=(e.a-BigInt(2))/BigInt(4);function h(b,v){cs("u",b,lr,s),cs("scalar",v,lr,s);const C=v,S=b;let I=ao,_=lr,U=b,E=ao,$=lr,A;for(let H=BigInt(i-1);H>=lr;H--){const N=C>>H&ao;$^=N,A=l($,I,U),I=A[0],U=A[1],A=l($,_,E),_=A[0],E=A[1],$=N;const k=I+_,O=r(k*k),B=I-_,q=r(B*B),x=O-q,G=U+E,Q=U-E,se=r(Q*k),Ce=r(G*B),de=se+Ce,Te=se-Ce;U=r(de*de),E=r(S*r(Te*Te)),I=r(O*q),_=r(x*(O+r(u*x)))}A=l($,I,U),I=A[0],U=A[1],A=l($,_,E),_=A[0],E=A[1];const L=c(_);return r(I*L)}function d(b){return Bn(r(b),n)}function p(b){const v=wt("u coordinate",b,n);return o===32&&(v[31]&=127),ki(v)}function w(b){const v=wt("scalar",b),C=v.length;if(C!==n&&C!==o){let S=""+n+" or "+o;throw new Error("invalid scalar, expected "+S+" bytes, got "+C)}return ki(a(v))}function f(b,v){const C=p(v),S=w(b),I=h(C,S);if(I===lr)throw new Error("invalid private or public key received");return d(I)}const m=d(e.Gu);function y(b){return f(b,m)}return{scalarMult:f,scalarMultBase:y,getSharedSecret:(b,v)=>f(b,v),getPublicKey:b=>y(b),utils:{randomPrivateKey:()=>e.randomBytes(e.nByteLength)},GuBytes:m}}const Wo=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");BigInt(0);const lw=BigInt(1),xc=BigInt(2),uw=BigInt(3),hw=BigInt(5);BigInt(8);function dw(t){const e=BigInt(10),s=BigInt(20),r=BigInt(40),i=BigInt(80),n=Wo,o=t*t%n*t%n,a=Ut(o,xc,n)*o%n,c=Ut(a,lw,n)*t%n,l=Ut(c,hw,n)*c%n,u=Ut(l,e,n)*l%n,h=Ut(u,s,n)*u%n,d=Ut(h,r,n)*h%n,p=Ut(d,i,n)*d%n,w=Ut(p,i,n)*d%n,f=Ut(w,e,n)*l%n;return{pow_p_5_8:Ut(f,xc,n)*t%n,b2:o}}function pw(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}const Ho=cw({P:Wo,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:t=>{const e=Wo,{pow_p_5_8:s,b2:r}=dw(t);return ht(Ut(s,uw,e)*r,e)},adjustScalarBytes:pw,randomBytes:Yr});function $c(t){t.lowS!==void 0&&Br("lowS",t.lowS),t.prehash!==void 0&&Br("prehash",t.prehash)}function fw(t){const e=Ju(t);Xr(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:s,Fp:r,a:i}=e;if(s){if(!r.eql(i,r.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof s!="object"||typeof s.beta!="bigint"||typeof s.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...e})}const{bytesToNumberBE:gw,hexToBytes:mw}=Gm;class ww extends Error{constructor(e=""){super(e)}}const os={Err:ww,_tlv:{encode:(t,e)=>{const{Err:s}=os;if(t<0||t>256)throw new s("tlv.encode: wrong tag");if(e.length&1)throw new s("tlv.encode: unpadded data");const r=e.length/2,i=Pr(r);if(i.length/2&128)throw new s("tlv.encode: long form length too big");const n=r>127?Pr(i.length/2|128):"";return Pr(t)+n+i+e},decode(t,e){const{Err:s}=os;let r=0;if(t<0||t>256)throw new s("tlv.encode: wrong tag");if(e.length<2||e[r++]!==t)throw new s("tlv.decode: wrong tlv");const i=e[r++],n=!!(i&128);let o=0;if(!n)o=i;else{const c=i&127;if(!c)throw new s("tlv.decode(long): indefinite length not supported");if(c>4)throw new s("tlv.decode(long): byte length is too big");const l=e.subarray(r,r+c);if(l.length!==c)throw new s("tlv.decode: length bytes not complete");if(l[0]===0)throw new s("tlv.decode(long): zero leftmost byte");for(const u of l)o=o<<8|u;if(r+=c,o<128)throw new s("tlv.decode(long): not minimal encoding")}const a=e.subarray(r,r+o);if(a.length!==o)throw new s("tlv.decode: wrong value length");return{v:a,l:e.subarray(r+o)}}},_int:{encode(t){const{Err:e}=os;if(t<as)throw new e("integer: negative integers are not allowed");let s=Pr(t);if(Number.parseInt(s[0],16)&8&&(s="00"+s),s.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return s},decode(t){const{Err:e}=os;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return gw(t)}},toSig(t){const{Err:e,_int:s,_tlv:r}=os,i=typeof t=="string"?mw(t):t;zi(i);const{v:n,l:o}=r.decode(48,i);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=r.decode(2,n),{v:l,l:u}=r.decode(2,c);if(u.length)throw new e("invalid signature: left bytes after parsing");return{r:s.decode(a),s:s.decode(l)}},hexFromSig(t){const{_tlv:e,_int:s}=os,r=e.encode(2,s.encode(t.r)),i=e.encode(2,s.encode(t.s)),n=r+i;return e.encode(48,n)}},as=BigInt(0),qe=BigInt(1);BigInt(2);const Rc=BigInt(3);BigInt(4);function yw(t){const e=fw(t),{Fp:s}=e,r=Wu(e.n,e.nBitLength),i=e.toBytes||((f,m,y)=>{const b=m.toAffine();return xi(Uint8Array.from([4]),s.toBytes(b.x),s.toBytes(b.y))}),n=e.fromBytes||(f=>{const m=f.subarray(1),y=s.fromBytes(m.subarray(0,s.BYTES)),b=s.fromBytes(m.subarray(s.BYTES,2*s.BYTES));return{x:y,y:b}});function o(f){const{a:m,b:y}=e,b=s.sqr(f),v=s.mul(b,f);return s.add(s.add(v,s.mul(f,m)),y)}if(!s.eql(s.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(f){return jn(f,qe,e.n)}function c(f){const{allowedPrivateKeyLengths:m,nByteLength:y,wrapPrivateKey:b,n:v}=e;if(m&&typeof f!="bigint"){if(Xs(f)&&(f=jr(f)),typeof f!="string"||!m.includes(f.length))throw new Error("invalid private key");f=f.padStart(y*2,"0")}let C;try{C=typeof f=="bigint"?f:Hs(wt("private key",f,y))}catch{throw new Error("invalid private key, expected hex or "+y+" bytes, got "+typeof f)}return b&&(C=ht(C,v)),cs("private key",C,qe,v),C}function l(f){if(!(f instanceof d))throw new Error("ProjectivePoint expected")}const u=qo((f,m)=>{const{px:y,py:b,pz:v}=f;if(s.eql(v,s.ONE))return{x:y,y:b};const C=f.is0();m==null&&(m=C?s.ONE:s.inv(v));const S=s.mul(y,m),I=s.mul(b,m),_=s.mul(v,m);if(C)return{x:s.ZERO,y:s.ZERO};if(!s.eql(_,s.ONE))throw new Error("invZ was invalid");return{x:S,y:I}}),h=qo(f=>{if(f.is0()){if(e.allowInfinityPoint&&!s.is0(f.py))return;throw new Error("bad point: ZERO")}const{x:m,y}=f.toAffine();if(!s.isValid(m)||!s.isValid(y))throw new Error("bad point: x or y not FE");const b=s.sqr(y),v=o(m);if(!s.eql(b,v))throw new Error("bad point: equation left != right");if(!f.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(m,y,b){if(this.px=m,this.py=y,this.pz=b,m==null||!s.isValid(m))throw new Error("x required");if(y==null||!s.isValid(y))throw new Error("y required");if(b==null||!s.isValid(b))throw new Error("z required");Object.freeze(this)}static fromAffine(m){const{x:y,y:b}=m||{};if(!m||!s.isValid(y)||!s.isValid(b))throw new Error("invalid affine point");if(m instanceof d)throw new Error("projective point not allowed");const v=C=>s.eql(C,s.ZERO);return v(y)&&v(b)?d.ZERO:new d(y,b,s.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(m){const y=s.invertBatch(m.map(b=>b.pz));return m.map((b,v)=>b.toAffine(y[v])).map(d.fromAffine)}static fromHex(m){const y=d.fromAffine(n(wt("pointHex",m)));return y.assertValidity(),y}static fromPrivateKey(m){return d.BASE.multiply(c(m))}static msm(m,y){return ow(d,r,m,y)}_setWindowSize(m){w.setWindowSize(this,m)}assertValidity(){h(this)}hasEvenY(){const{y:m}=this.toAffine();if(s.isOdd)return!s.isOdd(m);throw new Error("Field doesn't support isOdd")}equals(m){l(m);const{px:y,py:b,pz:v}=this,{px:C,py:S,pz:I}=m,_=s.eql(s.mul(y,I),s.mul(C,v)),U=s.eql(s.mul(b,I),s.mul(S,v));return _&&U}negate(){return new d(this.px,s.neg(this.py),this.pz)}double(){const{a:m,b:y}=e,b=s.mul(y,Rc),{px:v,py:C,pz:S}=this;let I=s.ZERO,_=s.ZERO,U=s.ZERO,E=s.mul(v,v),$=s.mul(C,C),A=s.mul(S,S),L=s.mul(v,C);return L=s.add(L,L),U=s.mul(v,S),U=s.add(U,U),I=s.mul(m,U),_=s.mul(b,A),_=s.add(I,_),I=s.sub($,_),_=s.add($,_),_=s.mul(I,_),I=s.mul(L,I),U=s.mul(b,U),A=s.mul(m,A),L=s.sub(E,A),L=s.mul(m,L),L=s.add(L,U),U=s.add(E,E),E=s.add(U,E),E=s.add(E,A),E=s.mul(E,L),_=s.add(_,E),A=s.mul(C,S),A=s.add(A,A),E=s.mul(A,L),I=s.sub(I,E),U=s.mul(A,$),U=s.add(U,U),U=s.add(U,U),new d(I,_,U)}add(m){l(m);const{px:y,py:b,pz:v}=this,{px:C,py:S,pz:I}=m;let _=s.ZERO,U=s.ZERO,E=s.ZERO;const $=e.a,A=s.mul(e.b,Rc);let L=s.mul(y,C),H=s.mul(b,S),N=s.mul(v,I),k=s.add(y,b),O=s.add(C,S);k=s.mul(k,O),O=s.add(L,H),k=s.sub(k,O),O=s.add(y,v);let B=s.add(C,I);return O=s.mul(O,B),B=s.add(L,N),O=s.sub(O,B),B=s.add(b,v),_=s.add(S,I),B=s.mul(B,_),_=s.add(H,N),B=s.sub(B,_),E=s.mul($,O),_=s.mul(A,N),E=s.add(_,E),_=s.sub(H,E),E=s.add(H,E),U=s.mul(_,E),H=s.add(L,L),H=s.add(H,L),N=s.mul($,N),O=s.mul(A,O),H=s.add(H,N),N=s.sub(L,N),N=s.mul($,N),O=s.add(O,N),L=s.mul(H,O),U=s.add(U,L),L=s.mul(B,O),_=s.mul(k,_),_=s.sub(_,L),L=s.mul(k,H),E=s.mul(B,E),E=s.add(E,L),new d(_,U,E)}subtract(m){return this.add(m.negate())}is0(){return this.equals(d.ZERO)}wNAF(m){return w.wNAFCached(this,m,d.normalizeZ)}multiplyUnsafe(m){const{endo:y,n:b}=e;cs("scalar",m,as,b);const v=d.ZERO;if(m===as)return v;if(this.is0()||m===qe)return this;if(!y||w.hasPrecomputes(this))return w.wNAFCachedUnsafe(this,m,d.normalizeZ);let{k1neg:C,k1:S,k2neg:I,k2:_}=y.splitScalar(m),U=v,E=v,$=this;for(;S>as||_>as;)S&qe&&(U=U.add($)),_&qe&&(E=E.add($)),$=$.double(),S>>=qe,_>>=qe;return C&&(U=U.negate()),I&&(E=E.negate()),E=new d(s.mul(E.px,y.beta),E.py,E.pz),U.add(E)}multiply(m){const{endo:y,n:b}=e;cs("scalar",m,qe,b);let v,C;if(y){const{k1neg:S,k1:I,k2neg:_,k2:U}=y.splitScalar(m);let{p:E,f:$}=this.wNAF(I),{p:A,f:L}=this.wNAF(U);E=w.constTimeNegate(S,E),A=w.constTimeNegate(_,A),A=new d(s.mul(A.px,y.beta),A.py,A.pz),v=E.add(A),C=$.add(L)}else{const{p:S,f:I}=this.wNAF(m);v=S,C=I}return d.normalizeZ([v,C])[0]}multiplyAndAddUnsafe(m,y,b){const v=d.BASE,C=(I,_)=>_===as||_===qe||!I.equals(v)?I.multiplyUnsafe(_):I.multiply(_),S=C(this,y).add(C(m,b));return S.is0()?void 0:S}toAffine(m){return u(this,m)}isTorsionFree(){const{h:m,isTorsionFree:y}=e;if(m===qe)return!0;if(y)return y(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:m,clearCofactor:y}=e;return m===qe?this:y?y(d,this):this.multiplyUnsafe(e.h)}toRawBytes(m=!0){return Br("isCompressed",m),this.assertValidity(),i(d,this,m)}toHex(m=!0){return Br("isCompressed",m),jr(this.toRawBytes(m))}}d.BASE=new d(e.Gx,e.Gy,s.ONE),d.ZERO=new d(s.ZERO,s.ONE,s.ZERO);const p=e.nBitLength,w=nw(d,e.endo?Math.ceil(p/2):p);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function bw(t){const e=Ju(t);return Xr(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function vw(t){const e=bw(t),{Fp:s,n:r}=e,i=s.BYTES+1,n=2*s.BYTES+1;function o(N){return ht(N,r)}function a(N){return zo(N,r)}const{ProjectivePoint:c,normPrivateKeyToScalar:l,weierstrassEquation:u,isWithinCurveOrder:h}=yw({...e,toBytes(N,k,O){const B=k.toAffine(),q=s.toBytes(B.x),x=xi;return Br("isCompressed",O),O?x(Uint8Array.from([k.hasEvenY()?2:3]),q):x(Uint8Array.from([4]),q,s.toBytes(B.y))},fromBytes(N){const k=N.length,O=N[0],B=N.subarray(1);if(k===i&&(O===2||O===3)){const q=Hs(B);if(!jn(q,qe,s.ORDER))throw new Error("Point is not on curve");const x=u(q);let G;try{G=s.sqrt(x)}catch(se){const Ce=se instanceof Error?": "+se.message:"";throw new Error("Point is not on curve"+Ce)}const Q=(G&qe)===qe;return(O&1)===1!==Q&&(G=s.neg(G)),{x:q,y:G}}else if(k===n&&O===4){const q=s.fromBytes(B.subarray(0,s.BYTES)),x=s.fromBytes(B.subarray(s.BYTES,2*s.BYTES));return{x:q,y:x}}else{const q=i,x=n;throw new Error("invalid Point, expected length of "+q+", or uncompressed "+x+", got "+k)}}}),d=N=>jr(Fr(N,e.nByteLength));function p(N){const k=r>>qe;return N>k}function w(N){return p(N)?o(-N):N}const f=(N,k,O)=>Hs(N.slice(k,O));class m{constructor(k,O,B){this.r=k,this.s=O,this.recovery=B,this.assertValidity()}static fromCompact(k){const O=e.nByteLength;return k=wt("compactSignature",k,O*2),new m(f(k,0,O),f(k,O,2*O))}static fromDER(k){const{r:O,s:B}=os.toSig(wt("DER",k));return new m(O,B)}assertValidity(){cs("r",this.r,qe,r),cs("s",this.s,qe,r)}addRecoveryBit(k){return new m(this.r,this.s,k)}recoverPublicKey(k){const{r:O,s:B,recovery:q}=this,x=I(wt("msgHash",k));if(q==null||![0,1,2,3].includes(q))throw new Error("recovery id invalid");const G=q===2||q===3?O+e.n:O;if(G>=s.ORDER)throw new Error("recovery id 2 or 3 invalid");const Q=q&1?"03":"02",se=c.fromHex(Q+d(G)),Ce=a(G),de=o(-x*Ce),Te=o(B*Ce),Le=c.BASE.multiplyAndAddUnsafe(se,de,Te);if(!Le)throw new Error("point at infinify");return Le.assertValidity(),Le}hasHighS(){return p(this.s)}normalizeS(){return this.hasHighS()?new m(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return qr(this.toDERHex())}toDERHex(){return os.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return qr(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const y={isValidPrivateKey(N){try{return l(N),!0}catch{return!1}},normPrivateKeyToScalar:l,randomPrivateKey:()=>{const N=Ku(e.n);return sw(e.randomBytes(N),e.n)},precompute(N=8,k=c.BASE){return k._setWindowSize(N),k.multiply(BigInt(3)),k}};function b(N,k=!0){return c.fromPrivateKey(N).toRawBytes(k)}function v(N){const k=Xs(N),O=typeof N=="string",B=(k||O)&&N.length;return k?B===i||B===n:O?B===2*i||B===2*n:N instanceof c}function C(N,k,O=!0){if(v(N))throw new Error("first arg must be private key");if(!v(k))throw new Error("second arg must be public key");return c.fromHex(k).multiply(l(N)).toRawBytes(O)}const S=e.bits2int||function(N){if(N.length>8192)throw new Error("input is too large");const k=Hs(N),O=N.length*8-e.nBitLength;return O>0?k>>BigInt(O):k},I=e.bits2int_modN||function(N){return o(S(N))},_=Ea(e.nBitLength);function U(N){return cs("num < 2^"+e.nBitLength,N,as,_),Fr(N,e.nByteLength)}function E(N,k,O=$){if(["recovered","canonical"].some($e=>$e in O))throw new Error("sign() legacy options not supported");const{hash:B,randomBytes:q}=e;let{lowS:x,prehash:G,extraEntropy:Q}=O;x==null&&(x=!0),N=wt("msgHash",N),$c(O),G&&(N=wt("prehashed msgHash",B(N)));const se=I(N),Ce=l(k),de=[U(Ce),U(se)];if(Q!=null&&Q!==!1){const $e=Q===!0?q(s.BYTES):Q;de.push(wt("extraEntropy",$e))}const Te=xi(...de),Le=se;function Xe($e){const Re=S($e);if(!h(Re))return;const ks=a(Re),Xt=c.BASE.multiply(Re).toAffine(),jt=o(Xt.x);if(jt===as)return;const Zt=o(ks*o(Le+jt*Ce));if(Zt===as)return;let Qt=(Xt.x===jt?0:2)|Number(Xt.y&qe),Ji=Zt;return x&&p(Zt)&&(Ji=w(Zt),Qt^=1),new m(jt,Ji,Qt)}return{seed:Te,k2sig:Xe}}const $={lowS:e.lowS,prehash:!1},A={lowS:e.lowS,prehash:!1};function L(N,k,O=$){const{seed:B,k2sig:q}=E(N,k,O),x=e;return qu(x.hash.outputLen,x.nByteLength,x.hmac)(B,q)}c.BASE._setWindowSize(8);function H(N,k,O,B=A){var Zt;const q=N;k=wt("msgHash",k),O=wt("publicKey",O);const{lowS:x,prehash:G,format:Q}=B;if($c(B),"strict"in B)throw new Error("options.strict was renamed to lowS");if(Q!==void 0&&Q!=="compact"&&Q!=="der")throw new Error("format must be compact or der");const se=typeof q=="string"||Xs(q),Ce=!se&&!Q&&typeof q=="object"&&q!==null&&typeof q.r=="bigint"&&typeof q.s=="bigint";if(!se&&!Ce)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let de,Te;try{if(Ce&&(de=new m(q.r,q.s)),se){try{Q!=="compact"&&(de=m.fromDER(q))}catch(Qt){if(!(Qt instanceof os.Err))throw Qt}!de&&Q!=="der"&&(de=m.fromCompact(q))}Te=c.fromHex(O)}catch{return!1}if(!de||x&&de.hasHighS())return!1;G&&(k=e.hash(k));const{r:Le,s:Xe}=de,$e=I(k),Re=a(Xe),ks=o($e*Re),Xt=o(Le*Re),jt=(Zt=c.BASE.multiplyAndAddUnsafe(Te,ks,Xt))==null?void 0:Zt.toAffine();return jt?o(jt.x)===Le:!1}return{CURVE:e,getPublicKey:b,getSharedSecret:C,sign:L,verify:H,ProjectivePoint:c,Signature:m,utils:y}}function Ew(t){return{hash:t,hmac:(e,...s)=>Dn(t,e,Ng(...s)),randomBytes:Yr}}function Cw(t,e){const s=r=>vw({...t,...Ew(r)});return{...s(e),create:s}}const Yu=Wu(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff")),Iw=Yu.create(BigInt("-3")),Aw=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"),Nw=Cw({a:Iw,b:Aw,Fp:Yu,n:BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"),Gx:BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"),Gy:BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"),h:BigInt(1),lowS:!1},Fi),Xu="base10",st="base16",Mt="base64pad",bs="base64url",Wi="utf8",Zu=0,ls=1,Hi=2,_w=0,Uc=1,Ei=12,Ca=32;function Sw(){const t=Ho.utils.randomPrivateKey(),e=Ho.getPublicKey(t);return{privateKey:dt(t,st),publicKey:dt(e,st)}}function Ko(){const t=Yr(Ca);return dt(t,st)}function Pw(t,e){const s=Ho.getSharedSecret(Ot(t,st),Ot(e,st)),r=xm(Fi,s,void 0,void 0,Ca);return dt(r,st)}function mn(t){const e=Fi(Ot(t,st));return dt(e,st)}function Vt(t){const e=Fi(Ot(t,Wi));return dt(e,st)}function Qu(t){return Ot(`${t}`,Xu)}function Zs(t){return Number(dt(t,Xu))}function eh(t){return t.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function th(t){const e=t.replace(/-/g,"+").replace(/_/g,"/"),s=(4-e.length%4)%4;return e+"=".repeat(s)}function Ow(t){const e=Qu(typeof t.type<"u"?t.type:Zu);if(Zs(e)===ls&&typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");const s=typeof t.senderPublicKey<"u"?Ot(t.senderPublicKey,st):void 0,r=typeof t.iv<"u"?Ot(t.iv,st):Yr(Ei),i=Ot(t.symKey,st),n=Mu(i,r).encrypt(Ot(t.message,Wi)),o=sh({type:e,sealed:n,iv:r,senderPublicKey:s});return t.encoding===bs?eh(o):o}function Tw(t){const e=Ot(t.symKey,st),{sealed:s,iv:r}=$i({encoded:t.encoded,encoding:t.encoding}),i=Mu(e,r).decrypt(s);if(i===null)throw new Error("Failed to decrypt");return dt(i,Wi)}function kw(t,e){const s=Qu(Hi),r=Yr(Ei),i=Ot(t,Wi),n=sh({type:s,sealed:i,iv:r});return e===bs?eh(n):n}function xw(t,e){const{sealed:s}=$i({encoded:t,encoding:e});return dt(s,Wi)}function sh(t){if(Zs(t.type)===Hi)return dt(mi([t.type,t.sealed]),Mt);if(Zs(t.type)===ls){if(typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");return dt(mi([t.type,t.senderPublicKey,t.iv,t.sealed]),Mt)}return dt(mi([t.type,t.iv,t.sealed]),Mt)}function $i(t){const e=(t.encoding||Mt)===bs?th(t.encoded):t.encoded,s=Ot(e,Mt),r=s.slice(_w,Uc),i=Uc;if(Zs(r)===ls){const c=i+Ca,l=c+Ei,u=s.slice(i,c),h=s.slice(c,l),d=s.slice(l);return{type:r,sealed:d,iv:h,senderPublicKey:u}}if(Zs(r)===Hi){const c=s.slice(i),l=Yr(Ei);return{type:r,sealed:c,iv:l}}const n=i+Ei,o=s.slice(i,n),a=s.slice(n);return{type:r,sealed:a,iv:o}}function $w(t,e){const s=$i({encoded:t,encoding:e==null?void 0:e.encoding});return rh({type:Zs(s.type),senderPublicKey:typeof s.senderPublicKey<"u"?dt(s.senderPublicKey,st):void 0,receiverPublicKey:e==null?void 0:e.receiverPublicKey})}function rh(t){const e=(t==null?void 0:t.type)||Zu;if(e===ls){if(typeof(t==null?void 0:t.senderPublicKey)>"u")throw new Error("missing sender public key");if(typeof(t==null?void 0:t.receiverPublicKey)>"u")throw new Error("missing receiver public key")}return{type:e,senderPublicKey:t==null?void 0:t.senderPublicKey,receiverPublicKey:t==null?void 0:t.receiverPublicKey}}function Dc(t){return t.type===ls&&typeof t.senderPublicKey=="string"&&typeof t.receiverPublicKey=="string"}function Lc(t){return t.type===Hi}function Rw(t){const e=Buffer.from(t.x,"base64"),s=Buffer.from(t.y,"base64");return mi([new Uint8Array([4]),e,s])}function Uw(t,e){const[s,r,i]=t.split("."),n=Buffer.from(th(i),"base64");if(n.length!==64)throw new Error("Invalid signature length");const o=n.slice(0,32),a=n.slice(32,64),c=`${s}.${r}`,l=Fi(c),u=Rw(e);if(!Nw.verify(mi([o,a]),l,u))throw new Error("Invalid signature");return So(t).payload}const Dw="irn";function Pn(t){return(t==null?void 0:t.relay)||{protocol:Dw}}function pi(t){const e=Qh[t];if(typeof e>"u")throw new Error(`Relay Protocol not supported: ${t}`);return e}function Lw(t,e="-"){const s={},r="relay"+e;return Object.keys(t).forEach(i=>{if(i.startsWith(r)){const n=i.replace(r,""),o=t[i];s[n]=o}}),s}function Mc(t){if(!t.includes("wc:")){const l=Nu(t);l!=null&&l.includes("wc:")&&(t=l)}t=t.includes("wc://")?t.replace("wc://",""):t,t=t.includes("wc:")?t.replace("wc:",""):t;const e=t.indexOf(":"),s=t.indexOf("?")!==-1?t.indexOf("?"):void 0,r=t.substring(0,e),i=t.substring(e+1,s).split("@"),n=typeof s<"u"?t.substring(s):"",o=new URLSearchParams(n),a={};o.forEach((l,u)=>{a[u]=l});const c=typeof a.methods=="string"?a.methods.split(","):void 0;return{protocol:r,topic:Mw(i[0]),version:parseInt(i[1],10),symKey:a.symKey,relay:Lw(a),methods:c,expiryTimestamp:a.expiryTimestamp?parseInt(a.expiryTimestamp,10):void 0}}function Mw(t){return t.startsWith("//")?t.substring(2):t}function Bw(t,e="-"){const s="relay",r={};return Object.keys(t).forEach(i=>{const n=i,o=s+e+n;t[n]&&(r[o]=t[n])}),r}function Bc(t){const e=new URLSearchParams,s=Bw(t.relay);Object.keys(s).sort().forEach(i=>{e.set(i,s[i])}),e.set("symKey",t.symKey),t.expiryTimestamp&&e.set("expiryTimestamp",t.expiryTimestamp.toString()),t.methods&&e.set("methods",t.methods.join(","));const r=e.toString();return`${t.protocol}:${t.topic}@${t.version}?${r}`}function nn(t,e,s){return`${t}?wc_ev=${s}&topic=${e}`}var jw=Object.defineProperty,qw=Object.defineProperties,Fw=Object.getOwnPropertyDescriptors,jc=Object.getOwnPropertySymbols,zw=Object.prototype.hasOwnProperty,Ww=Object.prototype.propertyIsEnumerable,qc=(t,e,s)=>e in t?jw(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Hw=(t,e)=>{for(var s in e||(e={}))zw.call(e,s)&&qc(t,s,e[s]);if(jc)for(var s of jc(e))Ww.call(e,s)&&qc(t,s,e[s]);return t},Kw=(t,e)=>qw(t,Fw(e));function Zr(t){const e=[];return t.forEach(s=>{const[r,i]=s.split(":");e.push(`${r}:${i}`)}),e}function Vw(t){const e=[];return Object.values(t).forEach(s=>{e.push(...Zr(s.accounts))}),e}function Gw(t,e){const s=[];return Object.values(t).forEach(r=>{Zr(r.accounts).includes(e)&&s.push(...r.methods)}),s}function Jw(t,e){const s=[];return Object.values(t).forEach(r=>{Zr(r.accounts).includes(e)&&s.push(...r.events)}),s}function qn(t){return t.includes(":")}function Or(t){return qn(t)?t.split(":")[0]:t}function Fc(t){var e,s,r;const i={};if(!_s(t))return i;for(const[n,o]of Object.entries(t)){const a=qn(n)?[n]:o.chains,c=o.methods||[],l=o.events||[],u=Or(n);i[u]=Kw(Hw({},i[u]),{chains:Jt(a,(e=i[u])==null?void 0:e.chains),methods:Jt(c,(s=i[u])==null?void 0:s.methods),events:Jt(l,(r=i[u])==null?void 0:r.events)})}return i}function Yw(t){const e={};return t==null||t.forEach(s=>{var r;const[i,n]=s.split(":");e[i]||(e[i]={accounts:[],chains:[],events:[],methods:[]}),e[i].accounts.push(s),(r=e[i].chains)==null||r.push(`${i}:${n}`)}),e}function zc(t,e){e=e.map(r=>r.replace("did:pkh:",""));const s=Yw(e);for(const[r,i]of Object.entries(s))i.methods?i.methods=Jt(i.methods,t):i.methods=t,i.events=["chainChanged","accountsChanged"];return s}function Xw(t,e){var s,r,i,n,o,a;const c=Fc(t),l=Fc(e),u={},h=Object.keys(c).concat(Object.keys(l));for(const d of h)u[d]={chains:Jt((s=c[d])==null?void 0:s.chains,(r=l[d])==null?void 0:r.chains),methods:Jt((i=c[d])==null?void 0:i.methods,(n=l[d])==null?void 0:n.methods),events:Jt((o=c[d])==null?void 0:o.events,(a=l[d])==null?void 0:a.events)};return u}const Zw={INVALID_METHOD:{message:"Invalid method.",code:1001},INVALID_EVENT:{message:"Invalid event.",code:1002},INVALID_UPDATE_REQUEST:{message:"Invalid update request.",code:1003},INVALID_EXTEND_REQUEST:{message:"Invalid extend request.",code:1004},INVALID_SESSION_SETTLE_REQUEST:{message:"Invalid session settle request.",code:1005},UNAUTHORIZED_METHOD:{message:"Unauthorized method.",code:3001},UNAUTHORIZED_EVENT:{message:"Unauthorized event.",code:3002},UNAUTHORIZED_UPDATE_REQUEST:{message:"Unauthorized update request.",code:3003},UNAUTHORIZED_EXTEND_REQUEST:{message:"Unauthorized extend request.",code:3004},USER_REJECTED:{message:"User rejected.",code:5e3},USER_REJECTED_CHAINS:{message:"User rejected chains.",code:5001},USER_REJECTED_METHODS:{message:"User rejected methods.",code:5002},USER_REJECTED_EVENTS:{message:"User rejected events.",code:5003},UNSUPPORTED_CHAINS:{message:"Unsupported chains.",code:5100},UNSUPPORTED_METHODS:{message:"Unsupported methods.",code:5101},UNSUPPORTED_EVENTS:{message:"Unsupported events.",code:5102},UNSUPPORTED_ACCOUNTS:{message:"Unsupported accounts.",code:5103},UNSUPPORTED_NAMESPACE_KEY:{message:"Unsupported namespace key.",code:5104},USER_DISCONNECTED:{message:"User disconnected.",code:6e3},SESSION_SETTLEMENT_FAILED:{message:"Session settlement failed.",code:7e3},WC_METHOD_UNSUPPORTED:{message:"Unsupported wc_ method.",code:10001}},Qw={NOT_INITIALIZED:{message:"Not initialized.",code:1},NO_MATCHING_KEY:{message:"No matching key.",code:2},RESTORE_WILL_OVERRIDE:{message:"Restore will override.",code:3},RESUBSCRIBED:{message:"Resubscribed.",code:4},MISSING_OR_INVALID:{message:"Missing or invalid.",code:5},EXPIRED:{message:"Expired.",code:6},UNKNOWN_TYPE:{message:"Unknown type.",code:7},MISMATCHED_TOPIC:{message:"Mismatched topic.",code:8},NON_CONFORMING_NAMESPACES:{message:"Non conforming namespaces.",code:9}};function R(t,e){const{message:s,code:r}=Qw[t];return{message:e?`${s} ${e}`:s,code:r}}function we(t,e){const{message:s,code:r}=Zw[t];return{message:e?`${s} ${e}`:s,code:r}}function Ns(t,e){return!!Array.isArray(t)}function _s(t){return Object.getPrototypeOf(t)===Object.prototype&&Object.keys(t).length}function Je(t){return typeof t>"u"}function xe(t,e){return e&&Je(t)?!0:typeof t=="string"&&!!t.trim().length}function Ia(t,e){return e&&Je(t)?!0:typeof t=="number"&&!isNaN(t)}function ey(t,e){const{requiredNamespaces:s}=e,r=Object.keys(t.namespaces),i=Object.keys(s);let n=!0;return zs(i,r)?(r.forEach(o=>{const{accounts:a,methods:c,events:l}=t.namespaces[o],u=Zr(a),h=s[o];(!zs(vu(o,h),u)||!zs(h.methods,c)||!zs(h.events,l))&&(n=!1)}),n):!1}function On(t){return xe(t,!1)&&t.includes(":")?t.split(":").length===2:!1}function ty(t){if(xe(t,!1)&&t.includes(":")){const e=t.split(":");if(e.length===3){const s=e[0]+":"+e[1];return!!e[2]&&On(s)}}return!1}function sy(t){function e(s){try{return typeof new URL(s)<"u"}catch{return!1}}try{if(xe(t,!1)){if(e(t))return!0;const s=Nu(t);return e(s)}}catch{}return!1}function ry(t){var e;return(e=t==null?void 0:t.proposer)==null?void 0:e.publicKey}function iy(t){return t==null?void 0:t.topic}function ny(t,e){let s=null;return xe(t==null?void 0:t.publicKey,!1)||(s=R("MISSING_OR_INVALID",`${e} controller public key should be a string`)),s}function Wc(t){let e=!0;return Ns(t)?t.length&&(e=t.every(s=>xe(s,!1))):e=!1,e}function oy(t,e,s){let r=null;return Ns(e)&&e.length?e.forEach(i=>{r||On(i)||(r=we("UNSUPPORTED_CHAINS",`${s}, chain ${i} should be a string and conform to "namespace:chainId" format`))}):On(t)||(r=we("UNSUPPORTED_CHAINS",`${s}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)),r}function ay(t,e,s){let r=null;return Object.entries(t).forEach(([i,n])=>{if(r)return;const o=oy(i,vu(i,n),`${e} ${s}`);o&&(r=o)}),r}function cy(t,e){let s=null;return Ns(t)?t.forEach(r=>{s||ty(r)||(s=we("UNSUPPORTED_ACCOUNTS",`${e}, account ${r} should be a string and conform to "namespace:chainId:address" format`))}):s=we("UNSUPPORTED_ACCOUNTS",`${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`),s}function ly(t,e){let s=null;return Object.values(t).forEach(r=>{if(s)return;const i=cy(r==null?void 0:r.accounts,`${e} namespace`);i&&(s=i)}),s}function uy(t,e){let s=null;return Wc(t==null?void 0:t.methods)?Wc(t==null?void 0:t.events)||(s=we("UNSUPPORTED_EVENTS",`${e}, events should be an array of strings or empty array for no events`)):s=we("UNSUPPORTED_METHODS",`${e}, methods should be an array of strings or empty array for no methods`),s}function ih(t,e){let s=null;return Object.values(t).forEach(r=>{if(s)return;const i=uy(r,`${e}, namespace`);i&&(s=i)}),s}function hy(t,e,s){let r=null;if(t&&_s(t)){const i=ih(t,e);i&&(r=i);const n=ay(t,e,s);n&&(r=n)}else r=R("MISSING_OR_INVALID",`${e}, ${s} should be an object with data`);return r}function co(t,e){let s=null;if(t&&_s(t)){const r=ih(t,e);r&&(s=r);const i=ly(t,e);i&&(s=i)}else s=R("MISSING_OR_INVALID",`${e}, namespaces should be an object with data`);return s}function nh(t){return xe(t.protocol,!0)}function dy(t,e){let s=!1;return t?t&&Ns(t)&&t.length&&t.forEach(r=>{s=nh(r)}):s=!0,s}function py(t){return typeof t=="number"}function ut(t){return typeof t<"u"&&typeof t!==null}function fy(t){return!(!t||typeof t!="object"||!t.code||!Ia(t.code,!1)||!t.message||!xe(t.message,!1))}function gy(t){return!(Je(t)||!xe(t.method,!1))}function my(t){return!(Je(t)||Je(t.result)&&Je(t.error)||!Ia(t.id,!1)||!xe(t.jsonrpc,!1))}function wy(t){return!(Je(t)||!xe(t.name,!1))}function Hc(t,e){return!(!On(e)||!Vw(t).includes(e))}function yy(t,e,s){return xe(s,!1)?Gw(t,e).includes(s):!1}function by(t,e,s){return xe(s,!1)?Jw(t,e).includes(s):!1}function Kc(t,e,s){let r=null;const i=vy(t),n=Ey(e),o=Object.keys(i),a=Object.keys(n),c=Vc(Object.keys(t)),l=Vc(Object.keys(e)),u=c.filter(h=>!l.includes(h));return u.length&&(r=R("NON_CONFORMING_NAMESPACES",`${s} namespaces keys don't satisfy requiredNamespaces.
      Required: ${u.toString()}
      Received: ${Object.keys(e).toString()}`)),zs(o,a)||(r=R("NON_CONFORMING_NAMESPACES",`${s} namespaces chains don't satisfy required namespaces.
      Required: ${o.toString()}
      Approved: ${a.toString()}`)),Object.keys(e).forEach(h=>{if(!h.includes(":")||r)return;const d=Zr(e[h].accounts);d.includes(h)||(r=R("NON_CONFORMING_NAMESPACES",`${s} namespaces accounts don't satisfy namespace accounts for ${h}
        Required: ${h}
        Approved: ${d.toString()}`))}),o.forEach(h=>{r||(zs(i[h].methods,n[h].methods)?zs(i[h].events,n[h].events)||(r=R("NON_CONFORMING_NAMESPACES",`${s} namespaces events don't satisfy namespace events for ${h}`)):r=R("NON_CONFORMING_NAMESPACES",`${s} namespaces methods don't satisfy namespace methods for ${h}`))}),r}function vy(t){const e={};return Object.keys(t).forEach(s=>{var r;s.includes(":")?e[s]=t[s]:(r=t[s].chains)==null||r.forEach(i=>{e[i]={methods:t[s].methods,events:t[s].events}})}),e}function Vc(t){return[...new Set(t.map(e=>e.includes(":")?e.split(":")[0]:e))]}function Ey(t){const e={};return Object.keys(t).forEach(s=>{if(s.includes(":"))e[s]=t[s];else{const r=Zr(t[s].accounts);r==null||r.forEach(i=>{e[i]={accounts:t[s].accounts.filter(n=>n.includes(`${i}:`)),methods:t[s].methods,events:t[s].events}})}}),e}function Cy(t,e){return Ia(t,!1)&&t<=e.max&&t>=e.min}function Gc(){const t=ji();return new Promise(e=>{switch(t){case bt.browser:e(Iy());break;case bt.reactNative:e(Ay());break;case bt.node:e(Ny());break;default:e(!0)}})}function Iy(){return Jr()&&(navigator==null?void 0:navigator.onLine)}async function Ay(){if(Ts()&&typeof global<"u"&&global!=null&&global.NetInfo){const t=await(global==null?void 0:global.NetInfo.fetch());return t==null?void 0:t.isConnected}return!0}function Ny(){return!0}function _y(t){switch(ji()){case bt.browser:Sy(t);break;case bt.reactNative:Py(t);break}}function Sy(t){!Ts()&&Jr()&&(window.addEventListener("online",()=>t(!0)),window.addEventListener("offline",()=>t(!1)))}function Py(t){Ts()&&typeof global<"u"&&global!=null&&global.NetInfo&&(global==null||global.NetInfo.addEventListener(e=>t(e==null?void 0:e.isConnected)))}function Oy(){var t;return Jr()&&Rr()?((t=Rr())==null?void 0:t.visibilityState)==="visible":!0}const lo={};class ii{static get(e){return lo[e]}static set(e,s){lo[e]=s}static delete(e){delete lo[e]}}var Ty={};const oh="wc",ah=2,Vo="core",Yt=`${oh}@2:${Vo}:`,ky={logger:"error"},xy={database:":memory:"},$y="crypto",Jc="client_ed25519_seed",Ry=D.ONE_DAY,Uy="keychain",Dy="0.3",Ly="messages",My="0.3",Yc=D.SIX_HOURS,By="publisher",ch="irn",jy="error",lh="wss://relay.walletconnect.org",qy="relayer",Fe={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},Fy="_subscription",Nt={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},zy=.1,Go="2.21.0",Ne={link_mode:"link_mode",relay:"relay"},wn={inbound:"inbound",outbound:"outbound"},Wy="0.3",Hy="WALLETCONNECT_CLIENT_ID",Xc="WALLETCONNECT_LINK_MODE_APPS",gt={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},Ky="subscription",Vy="0.3",Gy="pairing",Jy="0.3",ni={wc_pairingDelete:{req:{ttl:D.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:D.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:D.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:D.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:D.ONE_DAY,prompt:!1,tag:0},res:{ttl:D.ONE_DAY,prompt:!1,tag:0}}},js={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},xt={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},Yy="history",Xy="0.3",Zy="expirer",Pt={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},Qy="0.3",eb="verify-api",tb="https://verify.walletconnect.com",uh="https://verify.walletconnect.org",Ci=uh,sb=`${Ci}/v3`,rb=[tb,uh],ib="echo",nb="https://echo.walletconnect.com",Wt={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},ns={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},$t={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},$s={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},Rs={authenticated_session_approve_started:"authenticated_session_approve_started",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve"},oi={no_internet_connection:"no_internet_connection",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},ob=.1,ab="event-client",cb=86400,lb="https://pulse.walletconnect.org/batch";function ub(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var s=new Uint8Array(256),r=0;r<s.length;r++)s[r]=255;for(var i=0;i<t.length;i++){var n=t.charAt(i),o=n.charCodeAt(0);if(s[o]!==255)throw new TypeError(n+" is ambiguous");s[o]=i}var a=t.length,c=t.charAt(0),l=Math.log(a)/Math.log(256),u=Math.log(256)/Math.log(a);function h(w){if(w instanceof Uint8Array||(ArrayBuffer.isView(w)?w=new Uint8Array(w.buffer,w.byteOffset,w.byteLength):Array.isArray(w)&&(w=Uint8Array.from(w))),!(w instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(w.length===0)return"";for(var f=0,m=0,y=0,b=w.length;y!==b&&w[y]===0;)y++,f++;for(var v=(b-y)*u+1>>>0,C=new Uint8Array(v);y!==b;){for(var S=w[y],I=0,_=v-1;(S!==0||I<m)&&_!==-1;_--,I++)S+=256*C[_]>>>0,C[_]=S%a>>>0,S=S/a>>>0;if(S!==0)throw new Error("Non-zero carry");m=I,y++}for(var U=v-m;U!==v&&C[U]===0;)U++;for(var E=c.repeat(f);U<v;++U)E+=t.charAt(C[U]);return E}function d(w){if(typeof w!="string")throw new TypeError("Expected String");if(w.length===0)return new Uint8Array;var f=0;if(w[f]!==" "){for(var m=0,y=0;w[f]===c;)m++,f++;for(var b=(w.length-f)*l+1>>>0,v=new Uint8Array(b);w[f];){var C=s[w.charCodeAt(f)];if(C===255)return;for(var S=0,I=b-1;(C!==0||S<y)&&I!==-1;I--,S++)C+=a*v[I]>>>0,v[I]=C%256>>>0,C=C/256>>>0;if(C!==0)throw new Error("Non-zero carry");y=S,f++}if(w[f]!==" "){for(var _=b-y;_!==b&&v[_]===0;)_++;for(var U=new Uint8Array(m+(b-_)),E=m;_!==b;)U[E++]=v[_++];return U}}}function p(w){var f=d(w);if(f)return f;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:p}}var hb=ub,db=hb;const hh=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},pb=t=>new TextEncoder().encode(t),fb=t=>new TextDecoder().decode(t);class gb{constructor(e,s,r){this.name=e,this.prefix=s,this.baseEncode=r}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class mb{constructor(e,s,r){if(this.name=e,this.prefix=s,s.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=s.codePointAt(0),this.baseDecode=r}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return dh(this,e)}}class wb{constructor(e){this.decoders=e}or(e){return dh(this,e)}decode(e){const s=e[0],r=this.decoders[s];if(r)return r.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const dh=(t,e)=>new wb({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class yb{constructor(e,s,r,i){this.name=e,this.prefix=s,this.baseEncode=r,this.baseDecode=i,this.encoder=new gb(e,s,r),this.decoder=new mb(e,s,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Fn=({name:t,prefix:e,encode:s,decode:r})=>new yb(t,e,s,r),Ki=({prefix:t,name:e,alphabet:s})=>{const{encode:r,decode:i}=db(s,e);return Fn({prefix:t,name:e,encode:r,decode:n=>hh(i(n))})},bb=(t,e,s,r)=>{const i={};for(let u=0;u<e.length;++u)i[e[u]]=u;let n=t.length;for(;t[n-1]==="=";)--n;const o=new Uint8Array(n*s/8|0);let a=0,c=0,l=0;for(let u=0;u<n;++u){const h=i[t[u]];if(h===void 0)throw new SyntaxError(`Non-${r} character`);c=c<<s|h,a+=s,a>=8&&(a-=8,o[l++]=255&c>>a)}if(a>=s||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},vb=(t,e,s)=>{const r=e[e.length-1]==="=",i=(1<<s)-1;let n="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>s;)o-=s,n+=e[i&a>>o];if(o&&(n+=e[i&a<<s-o]),r)for(;n.length*s&7;)n+="=";return n},Ye=({name:t,prefix:e,bitsPerChar:s,alphabet:r})=>Fn({prefix:e,name:t,encode(i){return vb(i,r,s)},decode(i){return bb(i,r,s,t)}}),Eb=Fn({prefix:"\0",name:"identity",encode:t=>fb(t),decode:t=>pb(t)});var Cb=Object.freeze({__proto__:null,identity:Eb});const Ib=Ye({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Ab=Object.freeze({__proto__:null,base2:Ib});const Nb=Ye({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var _b=Object.freeze({__proto__:null,base8:Nb});const Sb=Ki({prefix:"9",name:"base10",alphabet:"0123456789"});var Pb=Object.freeze({__proto__:null,base10:Sb});const Ob=Ye({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Tb=Ye({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var kb=Object.freeze({__proto__:null,base16:Ob,base16upper:Tb});const xb=Ye({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),$b=Ye({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Rb=Ye({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Ub=Ye({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Db=Ye({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Lb=Ye({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Mb=Ye({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),Bb=Ye({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),jb=Ye({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var qb=Object.freeze({__proto__:null,base32:xb,base32upper:$b,base32pad:Rb,base32padupper:Ub,base32hex:Db,base32hexupper:Lb,base32hexpad:Mb,base32hexpadupper:Bb,base32z:jb});const Fb=Ki({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),zb=Ki({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var Wb=Object.freeze({__proto__:null,base36:Fb,base36upper:zb});const Hb=Ki({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Kb=Ki({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var Vb=Object.freeze({__proto__:null,base58btc:Hb,base58flickr:Kb});const Gb=Ye({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),Jb=Ye({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Yb=Ye({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Xb=Ye({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Zb=Object.freeze({__proto__:null,base64:Gb,base64pad:Jb,base64url:Yb,base64urlpad:Xb});const ph=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),Qb=ph.reduce((t,e,s)=>(t[s]=e,t),[]),ev=ph.reduce((t,e,s)=>(t[e.codePointAt(0)]=s,t),[]);function tv(t){return t.reduce((e,s)=>(e+=Qb[s],e),"")}function sv(t){const e=[];for(const s of t){const r=ev[s.codePointAt(0)];if(r===void 0)throw new Error(`Non-base256emoji character: ${s}`);e.push(r)}return new Uint8Array(e)}const rv=Fn({prefix:"🚀",name:"base256emoji",encode:tv,decode:sv});var iv=Object.freeze({__proto__:null,base256emoji:rv}),nv=fh,Zc=128,ov=-128,av=Math.pow(2,31);function fh(t,e,s){e=e||[],s=s||0;for(var r=s;t>=av;)e[s++]=t&255|Zc,t/=128;for(;t&ov;)e[s++]=t&255|Zc,t>>>=7;return e[s]=t|0,fh.bytes=s-r+1,e}var cv=Jo,lv=128,Qc=127;function Jo(t,r){var s=0,r=r||0,i=0,n=r,o,a=t.length;do{if(n>=a)throw Jo.bytes=0,new RangeError("Could not decode varint");o=t[n++],s+=i<28?(o&Qc)<<i:(o&Qc)*Math.pow(2,i),i+=7}while(o>=lv);return Jo.bytes=n-r,s}var uv=Math.pow(2,7),hv=Math.pow(2,14),dv=Math.pow(2,21),pv=Math.pow(2,28),fv=Math.pow(2,35),gv=Math.pow(2,42),mv=Math.pow(2,49),wv=Math.pow(2,56),yv=Math.pow(2,63),bv=function(t){return t<uv?1:t<hv?2:t<dv?3:t<pv?4:t<fv?5:t<gv?6:t<mv?7:t<wv?8:t<yv?9:10},vv={encode:nv,decode:cv,encodingLength:bv},gh=vv;const el=(t,e,s=0)=>(gh.encode(t,e,s),e),tl=t=>gh.encodingLength(t),Yo=(t,e)=>{const s=e.byteLength,r=tl(t),i=r+tl(s),n=new Uint8Array(i+s);return el(t,n,0),el(s,n,r),n.set(e,i),new Ev(t,s,e,n)};class Ev{constructor(e,s,r,i){this.code=e,this.size=s,this.digest=r,this.bytes=i}}const mh=({name:t,code:e,encode:s})=>new Cv(t,e,s);class Cv{constructor(e,s,r){this.name=e,this.code=s,this.encode=r}digest(e){if(e instanceof Uint8Array){const s=this.encode(e);return s instanceof Uint8Array?Yo(this.code,s):s.then(r=>Yo(this.code,r))}else throw Error("Unknown type, must be binary type")}}const wh=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),Iv=mh({name:"sha2-256",code:18,encode:wh("SHA-256")}),Av=mh({name:"sha2-512",code:19,encode:wh("SHA-512")});var Nv=Object.freeze({__proto__:null,sha256:Iv,sha512:Av});const yh=0,_v="identity",bh=hh,Sv=t=>Yo(yh,bh(t)),Pv={code:yh,name:_v,encode:bh,digest:Sv};var Ov=Object.freeze({__proto__:null,identity:Pv});new TextEncoder,new TextDecoder;const sl={...Cb,...Ab,..._b,...Pb,...kb,...qb,...Wb,...Vb,...Zb,...iv};({...Nv,...Ov});function Tv(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function vh(t,e,s,r){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:s},decoder:{decode:r}}}const rl=vh("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),uo=vh("ascii","a",t=>{let e="a";for(let s=0;s<t.length;s++)e+=String.fromCharCode(t[s]);return e},t=>{t=t.substring(1);const e=Tv(t.length);for(let s=0;s<t.length;s++)e[s]=t.charCodeAt(s);return e}),kv={utf8:rl,"utf-8":rl,hex:sl.base16,latin1:uo,ascii:uo,binary:uo,...sl};function xv(t,e="utf8"){const s=kv[e];if(!s)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):s.decoder.decode(`${s.prefix}${t}`)}var $v=Object.defineProperty,Rv=(t,e,s)=>e in t?$v(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ft=(t,e,s)=>Rv(t,typeof e!="symbol"?e+"":e,s);class Uv{constructor(e,s){this.core=e,this.logger=s,Ft(this,"keychain",new Map),Ft(this,"name",Uy),Ft(this,"version",Dy),Ft(this,"initialized",!1),Ft(this,"storagePrefix",Yt),Ft(this,"init",async()=>{if(!this.initialized){const r=await this.getKeyChain();typeof r<"u"&&(this.keychain=r),this.initialized=!0}}),Ft(this,"has",r=>(this.isInitialized(),this.keychain.has(r))),Ft(this,"set",async(r,i)=>{this.isInitialized(),this.keychain.set(r,i),await this.persist()}),Ft(this,"get",r=>{this.isInitialized();const i=this.keychain.get(r);if(typeof i>"u"){const{message:n}=R("NO_MATCHING_KEY",`${this.name}: ${r}`);throw new Error(n)}return i}),Ft(this,"del",async r=>{this.isInitialized(),this.keychain.delete(r),await this.persist()}),this.core=e,this.logger=rt(s,this.name)}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,Uo(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Do(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Dv=Object.defineProperty,Lv=(t,e,s)=>e in t?Dv(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ve=(t,e,s)=>Lv(t,typeof e!="symbol"?e+"":e,s);class Mv{constructor(e,s,r){this.core=e,this.logger=s,Ve(this,"name",$y),Ve(this,"keychain"),Ve(this,"randomSessionIdentifier",Ko()),Ve(this,"initialized",!1),Ve(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),Ve(this,"hasKeys",i=>(this.isInitialized(),this.keychain.has(i))),Ve(this,"getClientId",async()=>{this.isInitialized();const i=await this.getClientSeed(),n=xa(i);return id(n.publicKey)}),Ve(this,"generateKeyPair",()=>{this.isInitialized();const i=Sw();return this.setPrivateKey(i.publicKey,i.privateKey)}),Ve(this,"signJWT",async i=>{this.isInitialized();const n=await this.getClientSeed(),o=xa(n),a=this.randomSessionIdentifier;return await nd(a,i,Ry,o)}),Ve(this,"generateSharedKey",(i,n,o)=>{this.isInitialized();const a=this.getPrivateKey(i),c=Pw(a,n);return this.setSymKey(c,o)}),Ve(this,"setSymKey",async(i,n)=>{this.isInitialized();const o=n||mn(i);return await this.keychain.set(o,i),o}),Ve(this,"deleteKeyPair",async i=>{this.isInitialized(),await this.keychain.del(i)}),Ve(this,"deleteSymKey",async i=>{this.isInitialized(),await this.keychain.del(i)}),Ve(this,"encode",async(i,n,o)=>{this.isInitialized();const a=rh(o),c=ha(n);if(Lc(a))return kw(c,o==null?void 0:o.encoding);if(Dc(a)){const d=a.senderPublicKey,p=a.receiverPublicKey;i=await this.generateSharedKey(d,p)}const l=this.getSymKey(i),{type:u,senderPublicKey:h}=a;return Ow({type:u,symKey:l,message:c,senderPublicKey:h,encoding:o==null?void 0:o.encoding})}),Ve(this,"decode",async(i,n,o)=>{this.isInitialized();const a=$w(n,o);if(Lc(a)){const c=xw(n,o==null?void 0:o.encoding);return In(c)}if(Dc(a)){const c=a.receiverPublicKey,l=a.senderPublicKey;i=await this.generateSharedKey(c,l)}try{const c=this.getSymKey(i),l=Tw({symKey:c,encoded:n,encoding:o==null?void 0:o.encoding});return In(l)}catch(c){this.logger.error(`Failed to decode message from topic: '${i}', clientId: '${await this.getClientId()}'`),this.logger.error(c)}}),Ve(this,"getPayloadType",(i,n=Mt)=>{const o=$i({encoded:i,encoding:n});return Zs(o.type)}),Ve(this,"getPayloadSenderPublicKey",(i,n=Mt)=>{const o=$i({encoded:i,encoding:n});return o.senderPublicKey?dt(o.senderPublicKey,st):void 0}),this.core=e,this.logger=rt(s,this.name),this.keychain=r||new Uv(this.core,this.logger)}get context(){return Et(this.logger)}async setPrivateKey(e,s){return await this.keychain.set(e,s),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(Jc)}catch{e=Ko(),await this.keychain.set(Jc,e)}return xv(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Bv=Object.defineProperty,jv=Object.defineProperties,qv=Object.getOwnPropertyDescriptors,il=Object.getOwnPropertySymbols,Fv=Object.prototype.hasOwnProperty,zv=Object.prototype.propertyIsEnumerable,Xo=(t,e,s)=>e in t?Bv(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Wv=(t,e)=>{for(var s in e||(e={}))Fv.call(e,s)&&Xo(t,s,e[s]);if(il)for(var s of il(e))zv.call(e,s)&&Xo(t,s,e[s]);return t},Hv=(t,e)=>jv(t,qv(e)),ft=(t,e,s)=>Xo(t,typeof e!="symbol"?e+"":e,s);class Kv extends qp{constructor(e,s){super(e,s),this.logger=e,this.core=s,ft(this,"messages",new Map),ft(this,"messagesWithoutClientAck",new Map),ft(this,"name",Ly),ft(this,"version",My),ft(this,"initialized",!1),ft(this,"storagePrefix",Yt),ft(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const r=await this.getRelayerMessages();typeof r<"u"&&(this.messages=r);const i=await this.getRelayerMessagesWithoutClientAck();typeof i<"u"&&(this.messagesWithoutClientAck=i),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(r){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(r)}finally{this.initialized=!0}}}),ft(this,"set",async(r,i,n)=>{this.isInitialized();const o=Vt(i);let a=this.messages.get(r);if(typeof a>"u"&&(a={}),typeof a[o]<"u")return o;if(a[o]=i,this.messages.set(r,a),n===wn.inbound){const c=this.messagesWithoutClientAck.get(r)||{};this.messagesWithoutClientAck.set(r,Hv(Wv({},c),{[o]:i}))}return await this.persist(),o}),ft(this,"get",r=>{this.isInitialized();let i=this.messages.get(r);return typeof i>"u"&&(i={}),i}),ft(this,"getWithoutAck",r=>{this.isInitialized();const i={};for(const n of r){const o=this.messagesWithoutClientAck.get(n)||{};i[n]=Object.values(o)}return i}),ft(this,"has",(r,i)=>{this.isInitialized();const n=this.get(r),o=Vt(i);return typeof n[o]<"u"}),ft(this,"ack",async(r,i)=>{this.isInitialized();const n=this.messagesWithoutClientAck.get(r);if(typeof n>"u")return;const o=Vt(i);delete n[o],Object.keys(n).length===0?this.messagesWithoutClientAck.delete(r):this.messagesWithoutClientAck.set(r,n),await this.persist()}),ft(this,"del",async r=>{this.isInitialized(),this.messages.delete(r),this.messagesWithoutClientAck.delete(r),await this.persist()}),this.logger=rt(e,this.name),this.core=s}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get storageKeyWithoutClientAck(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name+"_withoutClientAck"}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,Uo(e))}async setRelayerMessagesWithoutClientAck(e){await this.core.storage.setItem(this.storageKeyWithoutClientAck,Uo(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Do(e):void 0}async getRelayerMessagesWithoutClientAck(){const e=await this.core.storage.getItem(this.storageKeyWithoutClientAck);return typeof e<"u"?Do(e):void 0}async persist(){await this.setRelayerMessages(this.messages),await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck)}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Vv=Object.defineProperty,Gv=Object.defineProperties,Jv=Object.getOwnPropertyDescriptors,nl=Object.getOwnPropertySymbols,Yv=Object.prototype.hasOwnProperty,Xv=Object.prototype.propertyIsEnumerable,Zo=(t,e,s)=>e in t?Vv(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,on=(t,e)=>{for(var s in e||(e={}))Yv.call(e,s)&&Zo(t,s,e[s]);if(nl)for(var s of nl(e))Xv.call(e,s)&&Zo(t,s,e[s]);return t},ho=(t,e)=>Gv(t,Jv(e)),Rt=(t,e,s)=>Zo(t,typeof e!="symbol"?e+"":e,s);class Zv extends Fp{constructor(e,s){super(e,s),this.relayer=e,this.logger=s,Rt(this,"events",new er.EventEmitter),Rt(this,"name",By),Rt(this,"queue",new Map),Rt(this,"publishTimeout",D.toMiliseconds(D.ONE_MINUTE)),Rt(this,"initialPublishTimeout",D.toMiliseconds(D.ONE_SECOND*15)),Rt(this,"needsTransportRestart",!1),Rt(this,"publish",async(r,i,n)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:r,message:i,opts:n}});const a=(n==null?void 0:n.ttl)||Yc,c=Pn(n),l=(n==null?void 0:n.prompt)||!1,u=(n==null?void 0:n.tag)||0,h=(n==null?void 0:n.id)||Sr().toString(),d={topic:r,message:i,opts:{ttl:a,relay:c,prompt:l,tag:u,id:h,attestation:n==null?void 0:n.attestation,tvf:n==null?void 0:n.tvf}},p=`Failed to publish payload, please try again. id:${h} tag:${u}`;try{const w=new Promise(async f=>{const m=({id:b})=>{d.opts.id===b&&(this.removeRequestFromQueue(b),this.relayer.events.removeListener(Fe.publish,m),f(d))};this.relayer.events.on(Fe.publish,m);const y=Cs(new Promise((b,v)=>{this.rpcPublish({topic:r,message:i,ttl:a,prompt:l,tag:u,id:h,attestation:n==null?void 0:n.attestation,tvf:n==null?void 0:n.tvf}).then(b).catch(C=>{this.logger.warn(C,C==null?void 0:C.message),v(C)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${h} tag:${u}`);try{await y,this.events.removeListener(Fe.publish,m)}catch(b){this.queue.set(h,ho(on({},d),{attempt:1})),this.logger.warn(b,b==null?void 0:b.message)}});this.logger.trace({type:"method",method:"publish",params:{id:h,topic:r,message:i,opts:n}}),await Cs(w,this.publishTimeout,p)}catch(w){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(w),(o=n==null?void 0:n.internal)!=null&&o.throwOnFailedPublish)throw w}finally{this.queue.delete(h)}}),Rt(this,"on",(r,i)=>{this.events.on(r,i)}),Rt(this,"once",(r,i)=>{this.events.once(r,i)}),Rt(this,"off",(r,i)=>{this.events.off(r,i)}),Rt(this,"removeListener",(r,i)=>{this.events.removeListener(r,i)}),this.relayer=e,this.logger=rt(s,this.name),this.registerEventListeners()}get context(){return Et(this.logger)}async rpcPublish(e){var s,r,i,n;const{topic:o,message:a,ttl:c=Yc,prompt:l,tag:u,id:h,attestation:d,tvf:p}=e,w={method:pi(Pn().protocol).publish,params:on({topic:o,message:a,ttl:c,prompt:l,tag:u,attestation:d},p),id:h};Je((s=w.params)==null?void 0:s.prompt)&&((r=w.params)==null||delete r.prompt),Je((i=w.params)==null?void 0:i.tag)&&((n=w.params)==null||delete n.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:w});const f=await this.relayer.request(w);return this.relayer.events.emit(Fe.publish,e),this.logger.debug("Successfully Published Payload"),f}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,s)=>{const r=e.attempt+1;this.queue.set(s,ho(on({},e),{attempt:r}));const{topic:i,message:n,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${r}`),await this.rpcPublish(ho(on({},e),{topic:i,message:n,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(Kr.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(Fe.connection_stalled);return}this.checkQueue()}),this.relayer.on(Fe.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var Qv=Object.defineProperty,e0=(t,e,s)=>e in t?Qv(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ur=(t,e,s)=>e0(t,typeof e!="symbol"?e+"":e,s);class t0{constructor(){ur(this,"map",new Map),ur(this,"set",(e,s)=>{const r=this.get(e);this.exists(e,s)||this.map.set(e,[...r,s])}),ur(this,"get",e=>this.map.get(e)||[]),ur(this,"exists",(e,s)=>this.get(e).includes(s)),ur(this,"delete",(e,s)=>{if(typeof s>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const r=this.get(e);if(!this.exists(e,s))return;const i=r.filter(n=>n!==s);if(!i.length){this.map.delete(e);return}this.map.set(e,i)}),ur(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var s0=Object.defineProperty,r0=Object.defineProperties,i0=Object.getOwnPropertyDescriptors,ol=Object.getOwnPropertySymbols,n0=Object.prototype.hasOwnProperty,o0=Object.prototype.propertyIsEnumerable,Qo=(t,e,s)=>e in t?s0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ai=(t,e)=>{for(var s in e||(e={}))n0.call(e,s)&&Qo(t,s,e[s]);if(ol)for(var s of ol(e))o0.call(e,s)&&Qo(t,s,e[s]);return t},po=(t,e)=>r0(t,i0(e)),be=(t,e,s)=>Qo(t,typeof e!="symbol"?e+"":e,s);class a0 extends Hp{constructor(e,s){super(e,s),this.relayer=e,this.logger=s,be(this,"subscriptions",new Map),be(this,"topicMap",new t0),be(this,"events",new er.EventEmitter),be(this,"name",Ky),be(this,"version",Vy),be(this,"pending",new Map),be(this,"cached",[]),be(this,"initialized",!1),be(this,"storagePrefix",Yt),be(this,"subscribeTimeout",D.toMiliseconds(D.ONE_MINUTE)),be(this,"initialSubscribeTimeout",D.toMiliseconds(D.ONE_SECOND*15)),be(this,"clientId"),be(this,"batchSubscribeTopicsLimit",500),be(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),be(this,"subscribe",async(r,i)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:r,opts:i}});try{const n=Pn(i),o={topic:r,relay:n,transportType:i==null?void 0:i.transportType};this.pending.set(r,o);const a=await this.rpcSubscribe(r,n,i);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:r,opts:i}})),a}catch(n){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(n),n}}),be(this,"unsubscribe",async(r,i)=>{this.isInitialized(),typeof(i==null?void 0:i.id)<"u"?await this.unsubscribeById(r,i.id,i):await this.unsubscribeByTopic(r,i)}),be(this,"isSubscribed",r=>new Promise(i=>{i(this.topicMap.topics.includes(r))})),be(this,"isKnownTopic",r=>new Promise(i=>{i(this.topicMap.topics.includes(r)||this.pending.has(r)||this.cached.some(n=>n.topic===r))})),be(this,"on",(r,i)=>{this.events.on(r,i)}),be(this,"once",(r,i)=>{this.events.once(r,i)}),be(this,"off",(r,i)=>{this.events.off(r,i)}),be(this,"removeListener",(r,i)=>{this.events.removeListener(r,i)}),be(this,"start",async()=>{await this.onConnect()}),be(this,"stop",async()=>{await this.onDisconnect()}),be(this,"restart",async()=>{await this.restore(),await this.onRestart()}),be(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const r=[];this.pending.forEach(i=>{r.push(i)}),await this.batchSubscribe(r)}),be(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(Kr.pulse,async()=>{await this.checkPending()}),this.events.on(gt.created,async r=>{const i=gt.created;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:r}),await this.persist()}),this.events.on(gt.deleted,async r=>{const i=gt.deleted;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:r}),await this.persist()})}),this.relayer=e,this.logger=rt(s,this.name),this.clientId=""}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,s){let r=!1;try{r=this.getSubscription(e).topic===s}catch{}return r}reset(){this.cached=[],this.initialized=!0}onDisable(){this.values.length>0&&(this.cached=this.values),this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,s){const r=this.topicMap.get(e);await Promise.all(r.map(async i=>await this.unsubscribeById(e,i,s)))}async unsubscribeById(e,s,r){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:s,opts:r}});try{const i=Pn(r);await this.restartToComplete({topic:e,id:s,relay:i}),await this.rpcUnsubscribe(e,s,i);const n=we("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,s,n),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:s,opts:r}})}catch(i){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(i),i}}async rpcSubscribe(e,s,r){var i;(!r||(r==null?void 0:r.transportType)===Ne.relay)&&await this.restartToComplete({topic:e,id:e,relay:s});const n={method:pi(s.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});const o=(i=r==null?void 0:r.internal)==null?void 0:i.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if((r==null?void 0:r.transportType)===Ne.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(n).catch(u=>this.logger.warn(u))},D.toMiliseconds(D.ONE_SECOND)),a;const c=new Promise(async u=>{const h=d=>{d.topic===e&&(this.events.removeListener(gt.created,h),u(d.id))};this.events.on(gt.created,h);try{const d=await Cs(new Promise((p,w)=>{this.relayer.request(n).catch(f=>{this.logger.warn(f,f==null?void 0:f.message),w(f)}).then(p)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener(gt.created,h),u(d)}catch{}}),l=await Cs(c,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!l&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return l?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(Fe.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const s=e[0].relay,r={method:pi(s.protocol).batchSubscribe,params:{topics:e.map(i=>i.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:r});try{await await Cs(new Promise(i=>{this.relayer.request(r).catch(n=>this.logger.warn(n)).then(i)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(Fe.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const s=e[0].relay,r={method:pi(s.protocol).batchFetchMessages,params:{topics:e.map(n=>n.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:r});let i;try{i=await await Cs(new Promise((n,o)=>{this.relayer.request(r).catch(a=>{this.logger.warn(a),o(a)}).then(n)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(Fe.connection_stalled)}return i}rpcUnsubscribe(e,s,r){const i={method:pi(r.protocol).unsubscribe,params:{topic:e,id:s}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(e,s){this.setSubscription(e,po(ai({},s),{id:e})),this.pending.delete(s.topic)}onBatchSubscribe(e){e.length&&e.forEach(s=>{this.setSubscription(s.id,ai({},s)),this.pending.delete(s.topic)})}async onUnsubscribe(e,s,r){this.events.removeAllListeners(s),this.hasSubscription(s,e)&&this.deleteSubscription(s,r),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,s){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:s}),this.addSubscription(e,s)}addSubscription(e,s){this.subscriptions.set(e,ai({},s)),this.topicMap.set(s.topic,e),this.events.emit(gt.created,s)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const s=this.subscriptions.get(e);if(!s){const{message:r}=R("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(r)}return s}deleteSubscription(e,s){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:s});const r=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(r.topic,e),this.events.emit(gt.deleted,po(ai({},r),{reason:s}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit(gt.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],s=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let r=0;r<s;r++){const i=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(i)}}this.events.emit(gt.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:s}=R("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(s),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(s)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async s=>po(ai({},s),{id:await this.getSubscriptionId(s.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const s=await this.rpcBatchFetchMessages(e);s&&s.messages&&(await fg(D.toMiliseconds(D.ONE_SECOND)),await this.relayer.handleBatchMessageEvents(s.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return Vt(e+await this.getClientId())}}var c0=Object.defineProperty,al=Object.getOwnPropertySymbols,l0=Object.prototype.hasOwnProperty,u0=Object.prototype.propertyIsEnumerable,ea=(t,e,s)=>e in t?c0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,cl=(t,e)=>{for(var s in e||(e={}))l0.call(e,s)&&ea(t,s,e[s]);if(al)for(var s of al(e))u0.call(e,s)&&ea(t,s,e[s]);return t},le=(t,e,s)=>ea(t,typeof e!="symbol"?e+"":e,s);class h0 extends zp{constructor(e){super(e),le(this,"protocol","wc"),le(this,"version",2),le(this,"core"),le(this,"logger"),le(this,"events",new er.EventEmitter),le(this,"provider"),le(this,"messages"),le(this,"subscriber"),le(this,"publisher"),le(this,"name",qy),le(this,"transportExplicitlyClosed",!1),le(this,"initialized",!1),le(this,"connectionAttemptInProgress",!1),le(this,"relayUrl"),le(this,"projectId"),le(this,"packageName"),le(this,"bundleId"),le(this,"hasExperiencedNetworkDisruption",!1),le(this,"pingTimeout"),le(this,"heartBeatTimeout",D.toMiliseconds(D.THIRTY_SECONDS+D.FIVE_SECONDS)),le(this,"reconnectTimeout"),le(this,"connectPromise"),le(this,"reconnectInProgress",!1),le(this,"requestsInFlight",[]),le(this,"connectTimeout",D.toMiliseconds(D.ONE_SECOND*15)),le(this,"request",async s=>{var r,i;this.logger.debug("Publishing Request Payload");const n=s.id||Sr().toString();await this.toEstablishConnection();try{this.logger.trace({id:n,method:s.method,topic:(r=s.params)==null?void 0:r.topic},"relayer.request - publishing...");const o=`${n}:${((i=s.params)==null?void 0:i.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(s);return this.requestsInFlight=this.requestsInFlight.filter(c=>c!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${n}`),o}}),le(this,"resetPingTimeout",()=>{_n()&&(clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var s,r,i,n;try{this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(n=(i=(r=(s=this.provider)==null?void 0:s.connection)==null?void 0:r.socket)==null?void 0:i.terminate)==null||n.call(i)}catch(o){this.logger.warn(o,o==null?void 0:o.message)}},this.heartBeatTimeout))}),le(this,"onPayloadHandler",s=>{this.onProviderPayload(s),this.resetPingTimeout()}),le(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected 🛜"),this.startPingTimeout(),this.events.emit(Fe.connect)}),le(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected 🛑"),this.requestsInFlight=[],this.onProviderDisconnect()}),le(this,"onProviderErrorHandler",s=>{this.logger.fatal(`Fatal socket error: ${s.message}`),this.events.emit(Fe.error,s),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),le(this,"registerProviderListeners",()=>{this.provider.on(Nt.payload,this.onPayloadHandler),this.provider.on(Nt.connect,this.onConnectHandler),this.provider.on(Nt.disconnect,this.onDisconnectHandler),this.provider.on(Nt.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?rt(e.logger,this.name):da(Li({level:e.logger||jy})),this.messages=new Kv(this.logger,e.core),this.subscriber=new a0(this,this.logger),this.publisher=new Zv(this,this.logger),this.relayUrl=(e==null?void 0:e.relayUrl)||lh,this.projectId=e.projectId,Zf()?this.packageName=oc():Qf()&&(this.bundleId=oc()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e==null?void 0:e.message)}}get context(){return Et(this.logger)}get connected(){var e,s,r;return((r=(s=(e=this.provider)==null?void 0:e.connection)==null?void 0:s.socket)==null?void 0:r.readyState)===1||!1}get connecting(){var e,s,r;return((r=(s=(e=this.provider)==null?void 0:e.connection)==null?void 0:s.socket)==null?void 0:r.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,s,r){this.isInitialized(),await this.publisher.publish(e,s,r),await this.recordMessageEvent({topic:e,message:s,publishedAt:Date.now(),transportType:Ne.relay},wn.outbound)}async subscribe(e,s){var r,i,n;this.isInitialized(),(!(s!=null&&s.transportType)||(s==null?void 0:s.transportType)==="relay")&&await this.toEstablishConnection();const o=typeof((r=s==null?void 0:s.internal)==null?void 0:r.throwOnFailedPublish)>"u"?!0:(i=s==null?void 0:s.internal)==null?void 0:i.throwOnFailedPublish;let a=((n=this.subscriber.topicMap.get(e))==null?void 0:n[0])||"",c;const l=u=>{u.topic===e&&(this.subscriber.off(gt.created,l),c())};return await Promise.all([new Promise(u=>{c=u,this.subscriber.on(gt.created,l)}),new Promise(async(u,h)=>{a=await this.subscriber.subscribe(e,cl({internal:{throwOnFailedPublish:o}},s)).catch(d=>{o&&h(d)})||a,u()})]),a}async unsubscribe(e,s){this.isInitialized(),await this.subscriber.unsubscribe(e,s)}on(e,s){this.events.on(e,s)}once(e,s){this.events.once(e,s)}off(e,s){this.events.off(e,s)}removeListener(e,s){this.events.removeListener(e,s)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await Cs(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(s,r)=>{await this.connect(e).then(s).catch(r).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await Gc())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if((e==null?void 0:e.length)===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const s=e.sort((r,i)=>r.publishedAt-i.publishedAt);this.logger.debug(`Batch of ${s.length} message events sorted`);for(const r of s)try{await this.onMessageEvent(r)}catch(i){this.logger.warn(i,"Error while processing batch message event: "+(i==null?void 0:i.message))}this.logger.trace(`Batch of ${s.length} message events processed`)}async onLinkMessageEvent(e,s){const{topic:r}=e;if(!s.sessionExists){const i=De(D.FIVE_MINUTES),n={topic:r,expiry:i,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(r,n)}this.events.emit(Fe.message,e),await this.recordMessageEvent(e,wn.inbound)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let s=1;for(;s<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${s}...`),await this.createProvider(),await new Promise(async(r,i)=>{const n=()=>{i(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(Nt.disconnect,n),await Cs(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{i(o)}).finally(()=>{this.provider.off(Nt.disconnect,n),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const c=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(Nt.disconnect,c),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(Nt.disconnect,c)})}),this.hasExperiencedNetworkDisruption=!1,r()})}catch(r){await this.subscriber.stop();const i=r;this.logger.warn({},i.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${s}`);break}await new Promise(r=>setTimeout(r,D.toMiliseconds(s*1))),s++}}startPingTimeout(){var e,s,r,i,n;if(_n())try{(s=(e=this.provider)==null?void 0:e.connection)!=null&&s.socket&&((n=(i=(r=this.provider)==null?void 0:r.connection)==null?void 0:i.socket)==null||n.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o==null?void 0:o.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new Tt(new rd(ig({sdkVersion:Go,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e,s){const{topic:r,message:i}=e;await this.messages.set(r,i,s)}async shouldIgnoreMessageEvent(e){const{topic:s,message:r}=e;if(!r||r.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${r}`),!0;if(!await this.subscriber.isKnownTopic(s))return this.logger.warn(`Ignoring message for unknown topic ${s}`),!0;const i=this.messages.has(s,r);return i&&this.logger.warn(`Ignoring duplicate message: ${r}`),i}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),pa(e)){if(!e.method.endsWith(Fy))return;const s=e.params,{topic:r,message:i,publishedAt:n,attestation:o}=s.data,a={topic:r,message:i,publishedAt:n,transportType:Ne.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(cl({type:"event",event:s.id},a)),this.events.emit(s.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else fa(e)&&this.events.emit(Fe.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(await this.recordMessageEvent(e,wn.inbound),this.events.emit(Fe.message,e))}async acknowledgePayload(e){const s=$n(e.id,!0);await this.provider.connection.send(s)}unregisterProviderListeners(){this.provider.off(Nt.payload,this.onPayloadHandler),this.provider.off(Nt.connect,this.onConnectHandler),this.provider.off(Nt.disconnect,this.onDisconnectHandler),this.provider.off(Nt.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await Gc();_y(async s=>{e!==s&&(e=s,s?await this.transportOpen().catch(r=>this.logger.error(r,r==null?void 0:r.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))}),this.core.heartbeat.on(Kr.pulse,async()=>{if(!this.transportExplicitlyClosed&&!this.connected&&Oy())try{await this.confirmOnlineStateOrThrow(),await this.transportOpen()}catch(s){this.logger.warn(s,s==null?void 0:s.message)}})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(Fe.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e==null?void 0:e.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},D.toMiliseconds(zy)))))}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){if(await this.confirmOnlineStateOrThrow(),!this.connected){if(this.connectPromise){await this.connectPromise;return}await this.connect()}}}function d0(){}function ll(t){if(!t||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e===null||e===Object.prototype||Object.getPrototypeOf(e)===null?Object.prototype.toString.call(t)==="[object Object]":!1}function ul(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function hl(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const p0="[object RegExp]",f0="[object String]",g0="[object Number]",m0="[object Boolean]",dl="[object Arguments]",w0="[object Symbol]",y0="[object Date]",b0="[object Map]",v0="[object Set]",E0="[object Array]",C0="[object Function]",I0="[object ArrayBuffer]",fo="[object Object]",A0="[object Error]",N0="[object DataView]",_0="[object Uint8Array]",S0="[object Uint8ClampedArray]",P0="[object Uint16Array]",O0="[object Uint32Array]",T0="[object BigUint64Array]",k0="[object Int8Array]",x0="[object Int16Array]",$0="[object Int32Array]",R0="[object BigInt64Array]",U0="[object Float32Array]",D0="[object Float64Array]";function L0(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}function M0(t,e,s){return fi(t,e,void 0,void 0,void 0,void 0,s)}function fi(t,e,s,r,i,n,o){const a=o(t,e,s,r,i,n);if(a!==void 0)return a;if(typeof t==typeof e)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return t===e;case"number":return t===e||Object.is(t,e);case"function":return t===e;case"object":return Ii(t,e,n,o)}return Ii(t,e,n,o)}function Ii(t,e,s,r){if(Object.is(t,e))return!0;let i=hl(t),n=hl(e);if(i===dl&&(i=fo),n===dl&&(n=fo),i!==n)return!1;switch(i){case f0:return t.toString()===e.toString();case g0:{const c=t.valueOf(),l=e.valueOf();return L0(c,l)}case m0:case y0:case w0:return Object.is(t.valueOf(),e.valueOf());case p0:return t.source===e.source&&t.flags===e.flags;case C0:return t===e}s=s??new Map;const o=s.get(t),a=s.get(e);if(o!=null&&a!=null)return o===e;s.set(t,e),s.set(e,t);try{switch(i){case b0:{if(t.size!==e.size)return!1;for(const[c,l]of t.entries())if(!e.has(c)||!fi(l,e.get(c),c,t,e,s,r))return!1;return!0}case v0:{if(t.size!==e.size)return!1;const c=Array.from(t.values()),l=Array.from(e.values());for(let u=0;u<c.length;u++){const h=c[u],d=l.findIndex(p=>fi(h,p,void 0,t,e,s,r));if(d===-1)return!1;l.splice(d,1)}return!0}case E0:case _0:case S0:case P0:case O0:case T0:case k0:case x0:case $0:case R0:case U0:case D0:{if(typeof Buffer<"u"&&Buffer.isBuffer(t)!==Buffer.isBuffer(e)||t.length!==e.length)return!1;for(let c=0;c<t.length;c++)if(!fi(t[c],e[c],c,t,e,s,r))return!1;return!0}case I0:return t.byteLength!==e.byteLength?!1:Ii(new Uint8Array(t),new Uint8Array(e),s,r);case N0:return t.byteLength!==e.byteLength||t.byteOffset!==e.byteOffset?!1:Ii(new Uint8Array(t),new Uint8Array(e),s,r);case A0:return t.name===e.name&&t.message===e.message;case fo:{if(!(Ii(t.constructor,e.constructor,s,r)||ll(t)&&ll(e)))return!1;const c=[...Object.keys(t),...ul(t)],l=[...Object.keys(e),...ul(e)];if(c.length!==l.length)return!1;for(let u=0;u<c.length;u++){const h=c[u],d=t[h];if(!Object.hasOwn(e,h))return!1;const p=e[h];if(!fi(d,p,h,t,e,s,r))return!1}return!0}default:return!1}}finally{s.delete(t),s.delete(e)}}function B0(t,e){return M0(t,e,d0)}var j0=Object.defineProperty,pl=Object.getOwnPropertySymbols,q0=Object.prototype.hasOwnProperty,F0=Object.prototype.propertyIsEnumerable,ta=(t,e,s)=>e in t?j0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,fl=(t,e)=>{for(var s in e||(e={}))q0.call(e,s)&&ta(t,s,e[s]);if(pl)for(var s of pl(e))F0.call(e,s)&&ta(t,s,e[s]);return t},at=(t,e,s)=>ta(t,typeof e!="symbol"?e+"":e,s);class ir extends Wp{constructor(e,s,r,i=Yt,n=void 0){super(e,s,r,i),this.core=e,this.logger=s,this.name=r,at(this,"map",new Map),at(this,"version",Wy),at(this,"cached",[]),at(this,"initialized",!1),at(this,"getKey"),at(this,"storagePrefix",Yt),at(this,"recentlyDeleted",[]),at(this,"recentlyDeletedLimit",200),at(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!Je(o)?this.map.set(this.getKey(o),o):ry(o)?this.map.set(o.id,o):iy(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),at(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),at(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),at(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(c=>B0(a[c],o[c]))):this.values)),at(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const c=fl(fl({},this.getData(o)),a);this.map.set(o,c),await this.persist()}),at(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=rt(s,this.name),this.storagePrefix=i,this.getKey=n}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const s=this.map.get(e);if(!s){if(this.recentlyDeleted.includes(e)){const{message:i}=R("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}const{message:r}=R("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(r),new Error(r)}return s}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:s}=R("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(s),new Error(s)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var z0=Object.defineProperty,W0=(t,e,s)=>e in t?z0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ie=(t,e,s)=>W0(t,typeof e!="symbol"?e+"":e,s);class H0{constructor(e,s){this.core=e,this.logger=s,ie(this,"name",Gy),ie(this,"version",Jy),ie(this,"events",new ua),ie(this,"pairings"),ie(this,"initialized",!1),ie(this,"storagePrefix",Yt),ie(this,"ignoredPayloadTypes",[ls]),ie(this,"registeredMethods",[]),ie(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),ie(this,"register",({methods:r})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...r])]}),ie(this,"create",async r=>{this.isInitialized();const i=Ko(),n=await this.core.crypto.setSymKey(i),o=De(D.FIVE_MINUTES),a={protocol:ch},c={topic:n,expiry:o,relay:a,active:!1,methods:r==null?void 0:r.methods},l=Bc({protocol:this.core.protocol,version:this.core.version,topic:n,symKey:i,relay:a,expiryTimestamp:o,methods:r==null?void 0:r.methods});return this.events.emit(js.create,c),this.core.expirer.set(n,o),await this.pairings.set(n,c),await this.core.relayer.subscribe(n,{transportType:r==null?void 0:r.transportType}),{topic:n,uri:l}}),ie(this,"pair",async r=>{this.isInitialized();const i=this.core.eventClient.createEvent({properties:{topic:r==null?void 0:r.uri,trace:[Wt.pairing_started]}});this.isValidPair(r,i);const{topic:n,symKey:o,relay:a,expiryTimestamp:c,methods:l}=Mc(r.uri);i.props.properties.topic=n,i.addTrace(Wt.pairing_uri_validation_success),i.addTrace(Wt.pairing_uri_not_expired);let u;if(this.pairings.keys.includes(n)){if(u=this.pairings.get(n),i.addTrace(Wt.existing_pairing),u.active)throw i.setError(ns.active_pairing_already_exists),new Error(`Pairing already exists: ${n}. Please try again with a new connection URI.`);i.addTrace(Wt.pairing_not_expired)}const h=c||De(D.FIVE_MINUTES),d={topic:n,relay:a,expiry:h,active:!1,methods:l};this.core.expirer.set(n,h),await this.pairings.set(n,d),i.addTrace(Wt.store_new_pairing),r.activatePairing&&await this.activate({topic:n}),this.events.emit(js.create,d),i.addTrace(Wt.emit_inactive_pairing),this.core.crypto.keychain.has(n)||await this.core.crypto.setSymKey(o,n),i.addTrace(Wt.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{i.setError(ns.no_internet_connection)}try{await this.core.relayer.subscribe(n,{relay:a})}catch(p){throw i.setError(ns.subscribe_pairing_topic_failure),p}return i.addTrace(Wt.subscribe_pairing_topic_success),d}),ie(this,"activate",async({topic:r})=>{this.isInitialized();const i=De(D.FIVE_MINUTES);this.core.expirer.set(r,i),await this.pairings.update(r,{active:!0,expiry:i})}),ie(this,"ping",async r=>{this.isInitialized(),await this.isValidPing(r),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:i}=r;if(this.pairings.keys.includes(i)){const n=await this.sendRequest(i,"wc_pairingPing",{}),{done:o,resolve:a,reject:c}=Ls();this.events.once(pe("pairing_ping",n),({error:l})=>{l?c(l):a()}),await o()}}),ie(this,"updateExpiry",async({topic:r,expiry:i})=>{this.isInitialized(),await this.pairings.update(r,{expiry:i})}),ie(this,"updateMetadata",async({topic:r,metadata:i})=>{this.isInitialized(),await this.pairings.update(r,{peerMetadata:i})}),ie(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),ie(this,"disconnect",async r=>{this.isInitialized(),await this.isValidDisconnect(r);const{topic:i}=r;this.pairings.keys.includes(i)&&(await this.sendRequest(i,"wc_pairingDelete",we("USER_DISCONNECTED")),await this.deletePairing(i))}),ie(this,"formatUriFromPairing",r=>{this.isInitialized();const{topic:i,relay:n,expiry:o,methods:a}=r,c=this.core.crypto.keychain.get(i);return Bc({protocol:this.core.protocol,version:this.core.version,topic:i,symKey:c,relay:n,expiryTimestamp:o,methods:a})}),ie(this,"sendRequest",async(r,i,n)=>{const o=Fs(i,n),a=await this.core.crypto.encode(r,o),c=ni[i].req;return this.core.history.set(r,o),this.core.relayer.publish(r,a,c),o.id}),ie(this,"sendResult",async(r,i,n)=>{const o=$n(r,n),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,r)).request.method,l=ni[c].res;await this.core.relayer.publish(i,a,l),await this.core.history.resolve(o)}),ie(this,"sendError",async(r,i,n)=>{const o=eu(r,n),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,r)).request.method,l=ni[c]?ni[c].res:ni.unregistered_method.res;await this.core.relayer.publish(i,a,l),await this.core.history.resolve(o)}),ie(this,"deletePairing",async(r,i)=>{await this.core.relayer.unsubscribe(r),await Promise.all([this.pairings.delete(r,we("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(r),i?Promise.resolve():this.core.expirer.del(r)])}),ie(this,"cleanup",async()=>{const r=this.pairings.getAll().filter(i=>ys(i.expiry));await Promise.all(r.map(i=>this.deletePairing(i.topic)))}),ie(this,"onRelayEventRequest",async r=>{const{topic:i,payload:n}=r;switch(n.method){case"wc_pairingPing":return await this.onPairingPingRequest(i,n);case"wc_pairingDelete":return await this.onPairingDeleteRequest(i,n);default:return await this.onUnknownRpcMethodRequest(i,n)}}),ie(this,"onRelayEventResponse",async r=>{const{topic:i,payload:n}=r,o=(await this.core.history.get(i,n.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(i,n);default:return this.onUnknownRpcMethodResponse(o)}}),ie(this,"onPairingPingRequest",async(r,i)=>{const{id:n}=i;try{this.isValidPing({topic:r}),await this.sendResult(n,r,!0),this.events.emit(js.ping,{id:n,topic:r})}catch(o){await this.sendError(n,r,o),this.logger.error(o)}}),ie(this,"onPairingPingResponse",(r,i)=>{const{id:n}=i;setTimeout(()=>{is(i)?this.events.emit(pe("pairing_ping",n),{}):Ht(i)&&this.events.emit(pe("pairing_ping",n),{error:i.error})},500)}),ie(this,"onPairingDeleteRequest",async(r,i)=>{const{id:n}=i;try{this.isValidDisconnect({topic:r}),await this.deletePairing(r),this.events.emit(js.delete,{id:n,topic:r})}catch(o){await this.sendError(n,r,o),this.logger.error(o)}}),ie(this,"onUnknownRpcMethodRequest",async(r,i)=>{const{id:n,method:o}=i;try{if(this.registeredMethods.includes(o))return;const a=we("WC_METHOD_UNSUPPORTED",o);await this.sendError(n,r,a),this.logger.error(a)}catch(a){await this.sendError(n,r,a),this.logger.error(a)}}),ie(this,"onUnknownRpcMethodResponse",r=>{this.registeredMethods.includes(r)||this.logger.error(we("WC_METHOD_UNSUPPORTED",r))}),ie(this,"isValidPair",(r,i)=>{var n;if(!ut(r)){const{message:a}=R("MISSING_OR_INVALID",`pair() params: ${r}`);throw i.setError(ns.malformed_pairing_uri),new Error(a)}if(!sy(r.uri)){const{message:a}=R("MISSING_OR_INVALID",`pair() uri: ${r.uri}`);throw i.setError(ns.malformed_pairing_uri),new Error(a)}const o=Mc(r==null?void 0:r.uri);if(!((n=o==null?void 0:o.relay)!=null&&n.protocol)){const{message:a}=R("MISSING_OR_INVALID","pair() uri#relay-protocol");throw i.setError(ns.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=R("MISSING_OR_INVALID","pair() uri#symKey");throw i.setError(ns.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&D.toMiliseconds(o==null?void 0:o.expiryTimestamp)<Date.now()){i.setError(ns.pairing_expired);const{message:a}=R("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),ie(this,"isValidPing",async r=>{if(!ut(r)){const{message:n}=R("MISSING_OR_INVALID",`ping() params: ${r}`);throw new Error(n)}const{topic:i}=r;await this.isValidPairingTopic(i)}),ie(this,"isValidDisconnect",async r=>{if(!ut(r)){const{message:n}=R("MISSING_OR_INVALID",`disconnect() params: ${r}`);throw new Error(n)}const{topic:i}=r;await this.isValidPairingTopic(i)}),ie(this,"isValidPairingTopic",async r=>{if(!xe(r,!1)){const{message:i}=R("MISSING_OR_INVALID",`pairing topic should be a string: ${r}`);throw new Error(i)}if(!this.pairings.keys.includes(r)){const{message:i}=R("NO_MATCHING_KEY",`pairing topic doesn't exist: ${r}`);throw new Error(i)}if(ys(this.pairings.get(r).expiry)){await this.deletePairing(r);const{message:i}=R("EXPIRED",`pairing topic: ${r}`);throw new Error(i)}}),this.core=e,this.logger=rt(s,this.name),this.pairings=new ir(this.core,this.logger,this.name,this.storagePrefix)}get context(){return Et(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(Fe.message,async e=>{const{topic:s,message:r,transportType:i}=e;if(this.pairings.keys.includes(s)&&i!==Ne.link_mode&&!this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(r)))try{const n=await this.core.crypto.decode(s,r);pa(n)?(this.core.history.set(s,n),await this.onRelayEventRequest({topic:s,payload:n})):fa(n)&&(await this.core.history.resolve(n),await this.onRelayEventResponse({topic:s,payload:n}),this.core.history.delete(s,n.id)),await this.core.relayer.messages.ack(s,r)}catch(n){this.logger.error(n)}})}registerExpirerEvents(){this.core.expirer.on(Pt.expired,async e=>{const{topic:s}=Au(e.target);s&&this.pairings.keys.includes(s)&&(await this.deletePairing(s,!0),this.events.emit(js.expire,{topic:s}))})}}var K0=Object.defineProperty,V0=(t,e,s)=>e in t?K0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ge=(t,e,s)=>V0(t,typeof e!="symbol"?e+"":e,s);class G0 extends jp{constructor(e,s){super(e,s),this.core=e,this.logger=s,Ge(this,"records",new Map),Ge(this,"events",new er.EventEmitter),Ge(this,"name",Yy),Ge(this,"version",Xy),Ge(this,"cached",[]),Ge(this,"initialized",!1),Ge(this,"storagePrefix",Yt),Ge(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(r=>this.records.set(r.id,r)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),Ge(this,"set",(r,i,n)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:r,request:i,chainId:n}),this.records.has(i.id))return;const o={id:i.id,topic:r,request:{method:i.method,params:i.params||null},chainId:n,expiry:De(D.THIRTY_DAYS)};this.records.set(o.id,o),this.persist(),this.events.emit(xt.created,o)}),Ge(this,"resolve",async r=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:r}),!this.records.has(r.id))return;const i=await this.getRecord(r.id);typeof i.response>"u"&&(i.response=Ht(r)?{error:r.error}:{result:r.result},this.records.set(i.id,i),this.persist(),this.events.emit(xt.updated,i))}),Ge(this,"get",async(r,i)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:r,id:i}),await this.getRecord(i))),Ge(this,"delete",(r,i)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:i}),this.values.forEach(n=>{if(n.topic===r){if(typeof i<"u"&&n.id!==i)return;this.records.delete(n.id),this.events.emit(xt.deleted,n)}}),this.persist()}),Ge(this,"exists",async(r,i)=>(this.isInitialized(),this.records.has(i)?(await this.getRecord(i)).topic===r:!1)),Ge(this,"on",(r,i)=>{this.events.on(r,i)}),Ge(this,"once",(r,i)=>{this.events.once(r,i)}),Ge(this,"off",(r,i)=>{this.events.off(r,i)}),Ge(this,"removeListener",(r,i)=>{this.events.removeListener(r,i)}),this.logger=rt(s,this.name)}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(s=>{if(typeof s.response<"u")return;const r={topic:s.topic,request:Fs(s.request.method,s.request.params,s.id),chainId:s.chainId};return e.push(r)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const s=this.records.get(e);if(!s){const{message:r}=R("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(r)}return s}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(xt.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:s}=R("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(s),new Error(s)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(xt.created,e=>{const s=xt.created;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,record:e})}),this.events.on(xt.updated,e=>{const s=xt.updated;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,record:e})}),this.events.on(xt.deleted,e=>{const s=xt.deleted;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,record:e})}),this.core.heartbeat.on(Kr.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(s=>{D.toMiliseconds(s.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${s.id}`),this.records.delete(s.id),this.events.emit(xt.deleted,s,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var J0=Object.defineProperty,Y0=(t,e,s)=>e in t?J0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ze=(t,e,s)=>Y0(t,typeof e!="symbol"?e+"":e,s);class X0 extends Kp{constructor(e,s){super(e,s),this.core=e,this.logger=s,Ze(this,"expirations",new Map),Ze(this,"events",new er.EventEmitter),Ze(this,"name",Zy),Ze(this,"version",Qy),Ze(this,"cached",[]),Ze(this,"initialized",!1),Ze(this,"storagePrefix",Yt),Ze(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(r=>this.expirations.set(r.target,r)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),Ze(this,"has",r=>{try{const i=this.formatTarget(r);return typeof this.getExpiration(i)<"u"}catch{return!1}}),Ze(this,"set",(r,i)=>{this.isInitialized();const n=this.formatTarget(r),o={target:n,expiry:i};this.expirations.set(n,o),this.checkExpiry(n,o),this.events.emit(Pt.created,{target:n,expiration:o})}),Ze(this,"get",r=>{this.isInitialized();const i=this.formatTarget(r);return this.getExpiration(i)}),Ze(this,"del",r=>{if(this.isInitialized(),this.has(r)){const i=this.formatTarget(r),n=this.getExpiration(i);this.expirations.delete(i),this.events.emit(Pt.deleted,{target:i,expiration:n})}}),Ze(this,"on",(r,i)=>{this.events.on(r,i)}),Ze(this,"once",(r,i)=>{this.events.once(r,i)}),Ze(this,"off",(r,i)=>{this.events.off(r,i)}),Ze(this,"removeListener",(r,i)=>{this.events.removeListener(r,i)}),this.logger=rt(s,this.name)}get context(){return Et(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return ng(e);if(typeof e=="number")return og(e);const{message:s}=R("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(s)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(Pt.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:s}=R("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(s),new Error(s)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const s=this.expirations.get(e);if(!s){const{message:r}=R("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(r),new Error(r)}return s}checkExpiry(e,s){const{expiry:r}=s;D.toMiliseconds(r)-Date.now()<=0&&this.expire(e,s)}expire(e,s){this.expirations.delete(e),this.events.emit(Pt.expired,{target:e,expiration:s})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,s)=>this.checkExpiry(s,e))}registerEventListeners(){this.core.heartbeat.on(Kr.pulse,()=>this.checkExpirations()),this.events.on(Pt.created,e=>{const s=Pt.created;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,data:e}),this.persist()}),this.events.on(Pt.expired,e=>{const s=Pt.expired;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,data:e}),this.persist()}),this.events.on(Pt.deleted,e=>{const s=Pt.deleted;this.logger.info(`Emitting ${s}`),this.logger.debug({type:"event",event:s,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Z0=Object.defineProperty,Q0=(t,e,s)=>e in t?Z0(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ke=(t,e,s)=>Q0(t,typeof e!="symbol"?e+"":e,s);class eE extends Vp{constructor(e,s,r){super(e,s,r),this.core=e,this.logger=s,this.store=r,ke(this,"name",eb),ke(this,"abortController"),ke(this,"isDevEnv"),ke(this,"verifyUrlV3",sb),ke(this,"storagePrefix",Yt),ke(this,"version",ah),ke(this,"publicKey"),ke(this,"fetchPromise"),ke(this,"init",async()=>{var i;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&D.toMiliseconds((i=this.publicKey)==null?void 0:i.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),ke(this,"register",async i=>{if(!Jr()||this.isDevEnv)return;const n=window.location.origin,{id:o,decryptedId:a}=i,c=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${n}&id=${o}&decryptedId=${a}`;try{const l=Rr(),u=this.startAbortTimer(D.ONE_SECOND*5),h=await new Promise((d,p)=>{const w=()=>{window.removeEventListener("message",m),l.body.removeChild(f),p("attestation aborted")};this.abortController.signal.addEventListener("abort",w);const f=l.createElement("iframe");f.src=c,f.style.display="none",f.addEventListener("error",w,{signal:this.abortController.signal});const m=y=>{if(y.data&&typeof y.data=="string")try{const b=JSON.parse(y.data);if(b.type==="verify_attestation"){if(So(b.attestation).payload.id!==o)return;clearInterval(u),l.body.removeChild(f),this.abortController.signal.removeEventListener("abort",w),window.removeEventListener("message",m),d(b.attestation===null?"":b.attestation)}}catch(b){this.logger.warn(b)}};l.body.appendChild(f),window.addEventListener("message",m,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",h),h}catch(l){this.logger.warn(l)}return""}),ke(this,"resolve",async i=>{if(this.isDevEnv)return"";const{attestationId:n,hash:o,encryptedId:a}=i;if(n===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(n){if(So(n).payload.id!==a)return;const l=await this.isValidJwtAttestation(n);if(l){if(!l.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return l}}if(!o)return;const c=this.getVerifyUrl(i==null?void 0:i.verifyUrl);return this.fetchAttestation(o,c)}),ke(this,"fetchAttestation",async(i,n)=>{this.logger.debug(`resolving attestation: ${i} from url: ${n}`);const o=this.startAbortTimer(D.ONE_SECOND*5),a=await fetch(`${n}/attestation/${i}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),ke(this,"getVerifyUrl",i=>{let n=i||Ci;return rb.includes(n)||(this.logger.info(`verify url: ${n}, not included in trusted list, assigning default: ${Ci}`),n=Ci),n}),ke(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const i=this.startAbortTimer(D.FIVE_SECONDS),n=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(i),await n.json()}catch(i){this.logger.warn(i)}}),ke(this,"persistPublicKey",async i=>{this.logger.debug("persisting public key to local storage",i),await this.store.setItem(this.storeKey,i),this.publicKey=i}),ke(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),ke(this,"isValidJwtAttestation",async i=>{const n=await this.getPublicKey();try{if(n)return this.validateAttestation(i,n)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(i,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),ke(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),ke(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async n=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),n(o))});const i=await this.fetchPromise;return this.fetchPromise=void 0,i}),ke(this,"validateAttestation",(i,n)=>{const o=Uw(i,n.publicKey),a={hasExpired:D.toMiliseconds(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=rt(s,this.name),this.abortController=new AbortController,this.isDevEnv=ma(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return Et(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),D.toMiliseconds(e))}}var tE=Object.defineProperty,sE=(t,e,s)=>e in t?tE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,gl=(t,e,s)=>sE(t,typeof e!="symbol"?e+"":e,s);class rE extends Gp{constructor(e,s){super(e,s),this.projectId=e,this.logger=s,gl(this,"context",ib),gl(this,"registerDeviceToken",async r=>{const{clientId:i,token:n,notificationType:o,enableEncrypted:a=!1}=r,c=`${nb}/${this.projectId}/clients`;await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:i,type:o,token:n,always_raw:a})})}),this.logger=rt(s,this.context)}}var iE=Object.defineProperty,ml=Object.getOwnPropertySymbols,nE=Object.prototype.hasOwnProperty,oE=Object.prototype.propertyIsEnumerable,sa=(t,e,s)=>e in t?iE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ci=(t,e)=>{for(var s in e||(e={}))nE.call(e,s)&&sa(t,s,e[s]);if(ml)for(var s of ml(e))oE.call(e,s)&&sa(t,s,e[s]);return t},Me=(t,e,s)=>sa(t,typeof e!="symbol"?e+"":e,s);class aE extends Jp{constructor(e,s,r=!0){super(e,s,r),this.core=e,this.logger=s,Me(this,"context",ab),Me(this,"storagePrefix",Yt),Me(this,"storageVersion",ob),Me(this,"events",new Map),Me(this,"shouldPersist",!1),Me(this,"init",async()=>{if(!ma())try{const i={eventId:cc(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:Cu(this.core.relayer.protocol,this.core.relayer.version,Go)}}};await this.sendEvent([i])}catch(i){this.logger.warn(i)}}),Me(this,"createEvent",i=>{const{event:n="ERROR",type:o="",properties:{topic:a,trace:c}}=i,l=cc(),u=this.core.projectId||"",h=Date.now(),d=ci({eventId:l,timestamp:h,props:{event:n,type:o,properties:{topic:a,trace:c}},bundleId:u,domain:this.getAppDomain()},this.setMethods(l));return this.telemetryEnabled&&(this.events.set(l,d),this.shouldPersist=!0),d}),Me(this,"getEvent",i=>{const{eventId:n,topic:o}=i;if(n)return this.events.get(n);const a=Array.from(this.events.values()).find(c=>c.props.properties.topic===o);if(a)return ci(ci({},a),this.setMethods(a.eventId))}),Me(this,"deleteEvent",i=>{const{eventId:n}=i;this.events.delete(n),this.shouldPersist=!0}),Me(this,"setEventListeners",()=>{this.core.heartbeat.on(Kr.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(i=>{D.fromMiliseconds(Date.now())-D.fromMiliseconds(i.timestamp)>cb&&(this.events.delete(i.eventId),this.shouldPersist=!0)})})}),Me(this,"setMethods",i=>({addTrace:n=>this.addTrace(i,n),setError:n=>this.setError(i,n)})),Me(this,"addTrace",(i,n)=>{const o=this.events.get(i);o&&(o.props.properties.trace.push(n),this.events.set(i,o),this.shouldPersist=!0)}),Me(this,"setError",(i,n)=>{const o=this.events.get(i);o&&(o.props.type=n,o.timestamp=Date.now(),this.events.set(i,o),this.shouldPersist=!0)}),Me(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),Me(this,"restore",async()=>{try{const i=await this.core.storage.getItem(this.storageKey)||[];if(!i.length)return;i.forEach(n=>{this.events.set(n.eventId,ci(ci({},n),this.setMethods(n.eventId)))})}catch(i){this.logger.warn(i)}}),Me(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const i=[];for(const[n,o]of this.events)o.props.type&&i.push(o);if(i.length!==0)try{if((await this.sendEvent(i)).ok)for(const n of i)this.events.delete(n.eventId),this.shouldPersist=!0}catch(n){this.logger.warn(n)}}),Me(this,"sendEvent",async i=>{const n=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${lb}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${Go}${n}`,{method:"POST",body:JSON.stringify(i)})}),Me(this,"getAppDomain",()=>Eu().url),this.logger=rt(s,this.context),this.telemetryEnabled=r,r?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var cE=Object.defineProperty,wl=Object.getOwnPropertySymbols,lE=Object.prototype.hasOwnProperty,uE=Object.prototype.propertyIsEnumerable,ra=(t,e,s)=>e in t?cE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,yl=(t,e)=>{for(var s in e||(e={}))lE.call(e,s)&&ra(t,s,e[s]);if(wl)for(var s of wl(e))uE.call(e,s)&&ra(t,s,e[s]);return t},Ae=(t,e,s)=>ra(t,typeof e!="symbol"?e+"":e,s);let hE=class Eh extends Dp{constructor(e){var s;super(e),Ae(this,"protocol",oh),Ae(this,"version",ah),Ae(this,"name",Vo),Ae(this,"relayUrl"),Ae(this,"projectId"),Ae(this,"customStoragePrefix"),Ae(this,"events",new er.EventEmitter),Ae(this,"logger"),Ae(this,"heartbeat"),Ae(this,"relayer"),Ae(this,"crypto"),Ae(this,"storage"),Ae(this,"history"),Ae(this,"expirer"),Ae(this,"pairing"),Ae(this,"verify"),Ae(this,"echoClient"),Ae(this,"linkModeSupportedApps"),Ae(this,"eventClient"),Ae(this,"initialized",!1),Ae(this,"logChunkController"),Ae(this,"on",(a,c)=>this.events.on(a,c)),Ae(this,"once",(a,c)=>this.events.once(a,c)),Ae(this,"off",(a,c)=>this.events.off(a,c)),Ae(this,"removeListener",(a,c)=>this.events.removeListener(a,c)),Ae(this,"dispatchEnvelope",({topic:a,message:c,sessionExists:l})=>{if(!a||!c)return;const u={topic:a,message:c,publishedAt:Date.now(),transportType:Ne.link_mode};this.relayer.onLinkMessageEvent(u,{sessionExists:l})});const r=this.getGlobalCore(e==null?void 0:e.customStoragePrefix);if(r)try{return this.customStoragePrefix=r.customStoragePrefix,this.logger=r.logger,this.heartbeat=r.heartbeat,this.crypto=r.crypto,this.history=r.history,this.expirer=r.expirer,this.storage=r.storage,this.relayer=r.relayer,this.pairing=r.pairing,this.verify=r.verify,this.echoClient=r.echoClient,this.linkModeSupportedApps=r.linkModeSupportedApps,this.eventClient=r.eventClient,this.initialized=r.initialized,this.logChunkController=r.logChunkController,r}catch(a){console.warn("Failed to copy global core",a)}this.projectId=e==null?void 0:e.projectId,this.relayUrl=(e==null?void 0:e.relayUrl)||lh,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const i=Li({level:typeof(e==null?void 0:e.logger)=="string"&&e.logger?e.logger:ky.logger,name:Vo}),{logger:n,chunkLoggerController:o}=Ql({opts:i,maxSizeInBytes:e==null?void 0:e.maxLogBlobSizeInBytes,loggerOverride:e==null?void 0:e.logger});this.logChunkController=o,(s=this.logChunkController)!=null&&s.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var a,c;(a=this.logChunkController)!=null&&a.downloadLogsBlobInBrowser&&((c=this.logChunkController)==null||c.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=rt(n,this.name),this.heartbeat=new sd,this.crypto=new Mv(this,this.logger,e==null?void 0:e.keychain),this.history=new G0(this,this.logger),this.expirer=new X0(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new $p(yl(yl({},xy),e==null?void 0:e.storageOptions)),this.relayer=new h0({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new H0(this,this.logger),this.verify=new eE(this,this.logger,this.storage),this.echoClient=new rE(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new aE(this,this.logger,e==null?void 0:e.telemetryEnabled),this.setGlobalCore(this)}static async init(e){const s=new Eh(e);await s.initialize();const r=await s.crypto.getClientId();return await s.storage.setItem(Hy,r),s}get context(){return Et(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(Xc,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(Xc)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}getGlobalCore(e=""){try{if(this.isGlobalCoreDisabled())return;const s=`_walletConnectCore_${e}`,r=`${s}_count`;return globalThis[r]=(globalThis[r]||0)+1,globalThis[r]>1&&console.warn(`WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[r]} times.`),globalThis[s]}catch(s){console.warn("Failed to get global WalletConnect core",s);return}}setGlobalCore(e){var s;try{if(this.isGlobalCoreDisabled())return;const r=`_walletConnectCore_${((s=e.opts)==null?void 0:s.customStoragePrefix)||""}`;globalThis[r]=e}catch(r){console.warn("Failed to set global WalletConnect core",r)}}isGlobalCoreDisabled(){try{return typeof process<"u"&&Ty.DISABLE_GLOBAL_CORE==="true"}catch{return!0}}};const dE=hE,Ch="wc",Ih=2,Ah="client",Aa=`${Ch}@${Ih}:${Ah}:`,go={name:Ah,logger:"error"},bl="WALLETCONNECT_DEEPLINK_CHOICE",pE="proposal",vl="Proposal expired",fE="session",hr=D.SEVEN_DAYS,gE="engine",Be={wc_sessionPropose:{req:{ttl:D.FIVE_MINUTES,prompt:!0,tag:1100},res:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1101},reject:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1120},autoReject:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1121}},wc_sessionSettle:{req:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1102},res:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1103}},wc_sessionUpdate:{req:{ttl:D.ONE_DAY,prompt:!1,tag:1104},res:{ttl:D.ONE_DAY,prompt:!1,tag:1105}},wc_sessionExtend:{req:{ttl:D.ONE_DAY,prompt:!1,tag:1106},res:{ttl:D.ONE_DAY,prompt:!1,tag:1107}},wc_sessionRequest:{req:{ttl:D.FIVE_MINUTES,prompt:!0,tag:1108},res:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1109}},wc_sessionEvent:{req:{ttl:D.FIVE_MINUTES,prompt:!0,tag:1110},res:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1111}},wc_sessionDelete:{req:{ttl:D.ONE_DAY,prompt:!1,tag:1112},res:{ttl:D.ONE_DAY,prompt:!1,tag:1113}},wc_sessionPing:{req:{ttl:D.ONE_DAY,prompt:!1,tag:1114},res:{ttl:D.ONE_DAY,prompt:!1,tag:1115}},wc_sessionAuthenticate:{req:{ttl:D.ONE_HOUR,prompt:!0,tag:1116},res:{ttl:D.ONE_HOUR,prompt:!1,tag:1117},reject:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1118},autoReject:{ttl:D.FIVE_MINUTES,prompt:!1,tag:1119}}},mo={min:D.FIVE_MINUTES,max:D.SEVEN_DAYS},zt={idle:"IDLE",active:"ACTIVE"},El={eth_sendTransaction:{key:""},eth_sendRawTransaction:{key:""},wallet_sendCalls:{key:""},solana_signTransaction:{key:"signature"},solana_signAllTransactions:{key:"transactions"},solana_signAndSendTransaction:{key:"signature"}},mE="request",wE=["wc_sessionPropose","wc_sessionRequest","wc_authRequest","wc_sessionAuthenticate"],yE="wc",bE="auth",vE="authKeys",EE="pairingTopics",CE="requests",zn=`${yE}@${1.5}:${bE}:`,yn=`${zn}:PUB_KEY`;var IE=Object.defineProperty,AE=Object.defineProperties,NE=Object.getOwnPropertyDescriptors,Cl=Object.getOwnPropertySymbols,_E=Object.prototype.hasOwnProperty,SE=Object.prototype.propertyIsEnumerable,ia=(t,e,s)=>e in t?IE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ve=(t,e)=>{for(var s in e||(e={}))_E.call(e,s)&&ia(t,s,e[s]);if(Cl)for(var s of Cl(e))SE.call(e,s)&&ia(t,s,e[s]);return t},tt=(t,e)=>AE(t,NE(e)),P=(t,e,s)=>ia(t,typeof e!="symbol"?e+"":e,s);class PE extends Qp{constructor(e){super(e),P(this,"name",gE),P(this,"events",new ua),P(this,"initialized",!1),P(this,"requestQueue",{state:zt.idle,queue:[]}),P(this,"sessionRequestQueue",{state:zt.idle,queue:[]}),P(this,"requestQueueDelay",D.ONE_SECOND),P(this,"expectedPairingMethodMap",new Map),P(this,"recentlyDeletedMap",new Map),P(this,"recentlyDeletedLimit",200),P(this,"relayMessageCache",[]),P(this,"pendingSessions",new Map),P(this,"init",async()=>{this.initialized||(await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.registerPairingEvents(),await this.registerLinkModeListeners(),this.client.core.pairing.register({methods:Object.keys(Be)}),this.initialized=!0,setTimeout(async()=>{await this.processPendingMessageEvents(),this.sessionRequestQueue.queue=this.getPendingSessionRequests(),this.processSessionRequestQueue()},D.toMiliseconds(this.requestQueueDelay)))}),P(this,"connect",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();const r=tt(ve({},s),{requiredNamespaces:s.requiredNamespaces||{},optionalNamespaces:s.optionalNamespaces||{}});await this.isValidConnect(r),r.optionalNamespaces=Xw(r.requiredNamespaces,r.optionalNamespaces),r.requiredNamespaces={};const{pairingTopic:i,requiredNamespaces:n,optionalNamespaces:o,sessionProperties:a,scopedProperties:c,relays:l}=r;let u=i,h,d=!1;try{if(u){const I=this.client.core.pairing.pairings.get(u);this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."),d=I.active}}catch(I){throw this.client.logger.error(`connect() -> pairing.get(${u}) failed`),I}if(!u||!d){const{topic:I,uri:_}=await this.client.core.pairing.create();u=I,h=_}if(!u){const{message:I}=R("NO_MATCHING_KEY",`connect() pairing topic: ${u}`);throw new Error(I)}const p=await this.client.core.crypto.generateKeyPair(),w=Be.wc_sessionPropose.req.ttl||D.FIVE_MINUTES,f=De(w),m=tt(ve(ve({requiredNamespaces:n,optionalNamespaces:o,relays:l??[{protocol:ch}],proposer:{publicKey:p,metadata:this.client.metadata},expiryTimestamp:f,pairingTopic:u},a&&{sessionProperties:a}),c&&{scopedProperties:c}),{id:us()}),y=pe("session_connect",m.id),{reject:b,resolve:v,done:C}=Ls(w,vl),S=({id:I})=>{I===m.id&&(this.client.events.off("proposal_expire",S),this.pendingSessions.delete(m.id),this.events.emit(y,{error:{message:vl,code:0}}))};return this.client.events.on("proposal_expire",S),this.events.once(y,({error:I,session:_})=>{this.client.events.off("proposal_expire",S),I?b(I):_&&v(_)}),await this.sendRequest({topic:u,method:"wc_sessionPropose",params:m,throwOnFailedPublish:!0,clientRpcId:m.id}),await this.setProposal(m.id,m),{uri:h,approval:C}}),P(this,"pair",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{return await this.client.core.pairing.pair(s)}catch(r){throw this.client.logger.error("pair() failed"),r}}),P(this,"approve",async s=>{var r,i,n;const o=this.client.core.eventClient.createEvent({properties:{topic:(r=s==null?void 0:s.id)==null?void 0:r.toString(),trace:[$t.session_approve_started]}});try{this.isInitialized(),await this.confirmOnlineStateOrThrow()}catch(E){throw o.setError($s.no_internet_connection),E}try{await this.isValidProposalId(s==null?void 0:s.id)}catch(E){throw this.client.logger.error(`approve() -> proposal.get(${s==null?void 0:s.id}) failed`),o.setError($s.proposal_not_found),E}try{await this.isValidApprove(s)}catch(E){throw this.client.logger.error("approve() -> isValidApprove() failed"),o.setError($s.session_approve_namespace_validation_failure),E}const{id:a,relayProtocol:c,namespaces:l,sessionProperties:u,scopedProperties:h,sessionConfig:d}=s,p=this.client.proposal.get(a);this.client.core.eventClient.deleteEvent({eventId:o.eventId});const{pairingTopic:w,proposer:f,requiredNamespaces:m,optionalNamespaces:y}=p;let b=(i=this.client.core.eventClient)==null?void 0:i.getEvent({topic:w});b||(b=(n=this.client.core.eventClient)==null?void 0:n.createEvent({type:$t.session_approve_started,properties:{topic:w,trace:[$t.session_approve_started,$t.session_namespaces_validation_success]}}));const v=await this.client.core.crypto.generateKeyPair(),C=f.publicKey,S=await this.client.core.crypto.generateSharedKey(v,C),I=ve(ve(ve({relay:{protocol:c??"irn"},namespaces:l,controller:{publicKey:v,metadata:this.client.metadata},expiry:De(hr)},u&&{sessionProperties:u}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),_=Ne.relay;b.addTrace($t.subscribing_session_topic);try{await this.client.core.relayer.subscribe(S,{transportType:_})}catch(E){throw b.setError($s.subscribe_session_topic_failure),E}b.addTrace($t.subscribe_session_topic_success);const U=tt(ve({},I),{topic:S,requiredNamespaces:m,optionalNamespaces:y,pairingTopic:w,acknowledged:!1,self:I.controller,peer:{publicKey:f.publicKey,metadata:f.metadata},controller:v,transportType:Ne.relay});await this.client.session.set(S,U),b.addTrace($t.store_session);try{b.addTrace($t.publishing_session_settle),await this.sendRequest({topic:S,method:"wc_sessionSettle",params:I,throwOnFailedPublish:!0}).catch(E=>{throw b==null||b.setError($s.session_settle_publish_failure),E}),b.addTrace($t.session_settle_publish_success),b.addTrace($t.publishing_session_approve),await this.sendResult({id:a,topic:w,result:{relay:{protocol:c??"irn"},responderPublicKey:v},throwOnFailedPublish:!0}).catch(E=>{throw b==null||b.setError($s.session_approve_publish_failure),E}),b.addTrace($t.session_approve_publish_success)}catch(E){throw this.client.logger.error(E),this.client.session.delete(S,we("USER_DISCONNECTED")),await this.client.core.relayer.unsubscribe(S),E}return this.client.core.eventClient.deleteEvent({eventId:b.eventId}),await this.client.core.pairing.updateMetadata({topic:w,metadata:f.metadata}),await this.client.proposal.delete(a,we("USER_DISCONNECTED")),await this.client.core.pairing.activate({topic:w}),await this.setExpiry(S,De(hr)),{topic:S,acknowledged:()=>Promise.resolve(this.client.session.get(S))}}),P(this,"reject",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidReject(s)}catch(o){throw this.client.logger.error("reject() -> isValidReject() failed"),o}const{id:r,reason:i}=s;let n;try{n=this.client.proposal.get(r).pairingTopic}catch(o){throw this.client.logger.error(`reject() -> proposal.get(${r}) failed`),o}n&&(await this.sendError({id:r,topic:n,error:i,rpcOpts:Be.wc_sessionPropose.reject}),await this.client.proposal.delete(r,we("USER_DISCONNECTED")))}),P(this,"update",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidUpdate(s)}catch(h){throw this.client.logger.error("update() -> isValidUpdate() failed"),h}const{topic:r,namespaces:i}=s,{done:n,resolve:o,reject:a}=Ls(),c=us(),l=Sr().toString(),u=this.client.session.get(r).namespaces;return this.events.once(pe("session_update",c),({error:h})=>{h?a(h):o()}),await this.client.session.update(r,{namespaces:i}),await this.sendRequest({topic:r,method:"wc_sessionUpdate",params:{namespaces:i},throwOnFailedPublish:!0,clientRpcId:c,relayRpcId:l}).catch(h=>{this.client.logger.error(h),this.client.session.update(r,{namespaces:u}),a(h)}),{acknowledged:n}}),P(this,"extend",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidExtend(s)}catch(c){throw this.client.logger.error("extend() -> isValidExtend() failed"),c}const{topic:r}=s,i=us(),{done:n,resolve:o,reject:a}=Ls();return this.events.once(pe("session_extend",i),({error:c})=>{c?a(c):o()}),await this.setExpiry(r,De(hr)),this.sendRequest({topic:r,method:"wc_sessionExtend",params:{},clientRpcId:i,throwOnFailedPublish:!0}).catch(c=>{a(c)}),{acknowledged:n}}),P(this,"request",async s=>{this.isInitialized();try{await this.isValidRequest(s)}catch(y){throw this.client.logger.error("request() -> isValidRequest() failed"),y}const{chainId:r,request:i,topic:n,expiry:o=Be.wc_sessionRequest.req.ttl}=s,a=this.client.session.get(n);(a==null?void 0:a.transportType)===Ne.relay&&await this.confirmOnlineStateOrThrow();const c=us(),l=Sr().toString(),{done:u,resolve:h,reject:d}=Ls(o,"Request expired. Please try again.");this.events.once(pe("session_request",c),({error:y,result:b})=>{y?d(y):h(b)});const p="wc_sessionRequest",w=this.getAppLinkIfEnabled(a.peer.metadata,a.transportType);if(w)return await this.sendRequest({clientRpcId:c,relayRpcId:l,topic:n,method:p,params:{request:tt(ve({},i),{expiryTimestamp:De(o)}),chainId:r},expiry:o,throwOnFailedPublish:!0,appLink:w}).catch(y=>d(y)),this.client.events.emit("session_request_sent",{topic:n,request:i,chainId:r,id:c}),await u();const f={request:tt(ve({},i),{expiryTimestamp:De(o)}),chainId:r},m=this.shouldSetTVF(p,f);return await Promise.all([new Promise(async y=>{await this.sendRequest(ve({clientRpcId:c,relayRpcId:l,topic:n,method:p,params:f,expiry:o,throwOnFailedPublish:!0},m&&{tvf:this.getTVFParams(c,f)})).catch(b=>d(b)),this.client.events.emit("session_request_sent",{topic:n,request:i,chainId:r,id:c}),y()}),new Promise(async y=>{var b;if(!((b=a.sessionConfig)!=null&&b.disableDeepLink)){const v=await ug(this.client.core.storage,bl);await ag({id:c,topic:n,wcDeepLink:v})}y()}),u()]).then(y=>y[2])}),P(this,"respond",async s=>{this.isInitialized(),await this.isValidRespond(s);const{topic:r,response:i}=s,{id:n}=i,o=this.client.session.get(r);o.transportType===Ne.relay&&await this.confirmOnlineStateOrThrow();const a=this.getAppLinkIfEnabled(o.peer.metadata,o.transportType);is(i)?await this.sendResult({id:n,topic:r,result:i.result,throwOnFailedPublish:!0,appLink:a}):Ht(i)&&await this.sendError({id:n,topic:r,error:i.error,appLink:a}),this.cleanupAfterResponse(s)}),P(this,"ping",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidPing(s)}catch(i){throw this.client.logger.error("ping() -> isValidPing() failed"),i}const{topic:r}=s;if(this.client.session.keys.includes(r)){const i=us(),n=Sr().toString(),{done:o,resolve:a,reject:c}=Ls();this.events.once(pe("session_ping",i),({error:l})=>{l?c(l):a()}),await Promise.all([this.sendRequest({topic:r,method:"wc_sessionPing",params:{},throwOnFailedPublish:!0,clientRpcId:i,relayRpcId:n}),o()])}else this.client.core.pairing.pairings.keys.includes(r)&&(this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."),await this.client.core.pairing.ping({topic:r}))}),P(this,"emit",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidEmit(s);const{topic:r,event:i,chainId:n}=s,o=Sr().toString(),a=us();await this.sendRequest({topic:r,method:"wc_sessionEvent",params:{event:i,chainId:n},throwOnFailedPublish:!0,relayRpcId:o,clientRpcId:a})}),P(this,"disconnect",async s=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidDisconnect(s);const{topic:r}=s;if(this.client.session.keys.includes(r))await this.sendRequest({topic:r,method:"wc_sessionDelete",params:we("USER_DISCONNECTED"),throwOnFailedPublish:!0}),await this.deleteSession({topic:r,emitEvent:!1});else if(this.client.core.pairing.pairings.keys.includes(r))await this.client.core.pairing.disconnect({topic:r});else{const{message:i}=R("MISMATCHED_TOPIC",`Session or pairing topic not found: ${r}`);throw new Error(i)}}),P(this,"find",s=>(this.isInitialized(),this.client.session.getAll().filter(r=>ey(r,s)))),P(this,"getPendingSessionRequests",()=>this.client.pendingRequest.getAll()),P(this,"authenticate",async(s,r)=>{var i;this.isInitialized(),this.isValidAuthenticate(s);const n=r&&this.client.core.linkModeSupportedApps.includes(r)&&((i=this.client.metadata.redirect)==null?void 0:i.linkMode),o=n?Ne.link_mode:Ne.relay;o===Ne.relay&&await this.confirmOnlineStateOrThrow();const{chains:a,statement:c="",uri:l,domain:u,nonce:h,type:d,exp:p,nbf:w,methods:f=[],expiry:m}=s,y=[...s.resources||[]],{topic:b,uri:v}=await this.client.core.pairing.create({methods:["wc_sessionAuthenticate"],transportType:o});this.client.logger.info({message:"Generated new pairing",pairing:{topic:b,uri:v}});const C=await this.client.core.crypto.generateKeyPair(),S=mn(C);if(await Promise.all([this.client.auth.authKeys.set(yn,{responseTopic:S,publicKey:C}),this.client.auth.pairingTopics.set(S,{topic:S,pairingTopic:b})]),await this.client.core.relayer.subscribe(S,{transportType:o}),this.client.logger.info(`sending request to new pairing topic: ${b}`),f.length>0){const{namespace:x}=kr(a[0]);let G=sm(x,"request",f);gn(y)&&(G=im(G,y.pop())),y.push(G)}const I=m&&m>Be.wc_sessionAuthenticate.req.ttl?m:Be.wc_sessionAuthenticate.req.ttl,_={authPayload:{type:d??"caip122",chains:a,statement:c,aud:l,domain:u,version:"1",nonce:h,iat:new Date().toISOString(),exp:p,nbf:w,resources:y},requester:{publicKey:C,metadata:this.client.metadata},expiryTimestamp:De(I)},U={eip155:{chains:a,methods:[...new Set(["personal_sign",...f])],events:["chainChanged","accountsChanged"]}},E={requiredNamespaces:{},optionalNamespaces:U,relays:[{protocol:"irn"}],pairingTopic:b,proposer:{publicKey:C,metadata:this.client.metadata},expiryTimestamp:De(Be.wc_sessionPropose.req.ttl),id:us()},{done:$,resolve:A,reject:L}=Ls(I,"Request expired"),H=us(),N=pe("session_connect",E.id),k=pe("session_request",H),O=async({error:x,session:G})=>{this.events.off(k,B),x?L(x):G&&A({session:G})},B=async x=>{var G,Q,se;if(await this.deletePendingAuthRequest(H,{message:"fulfilled",code:0}),x.error){const Re=we("WC_METHOD_UNSUPPORTED","wc_sessionAuthenticate");return x.error.code===Re.code?void 0:(this.events.off(N,O),L(x.error.message))}await this.deleteProposal(E.id),this.events.off(N,O);const{cacaos:Ce,responder:de}=x.result,Te=[],Le=[];for(const Re of Ce){await mc({cacao:Re,projectId:this.client.core.projectId})||(this.client.logger.error(Re,"Signature verification failed"),L(we("SESSION_SETTLEMENT_FAILED","Signature verification failed")));const{p:ks}=Re,Xt=gn(ks.resources),jt=[Lo(ks.iss)],Zt=Sn(ks.iss);if(Xt){const Qt=wc(Xt),Ji=yc(Xt);Te.push(...Qt),jt.push(...Ji)}for(const Qt of jt)Le.push(`${Qt}:${Zt}`)}const Xe=await this.client.core.crypto.generateSharedKey(C,de.publicKey);let $e;Te.length>0&&($e={topic:Xe,acknowledged:!0,self:{publicKey:C,metadata:this.client.metadata},peer:de,controller:de.publicKey,expiry:De(hr),requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:b,namespaces:zc([...new Set(Te)],[...new Set(Le)]),transportType:o},await this.client.core.relayer.subscribe(Xe,{transportType:o}),await this.client.session.set(Xe,$e),b&&await this.client.core.pairing.updateMetadata({topic:b,metadata:de.metadata}),$e=this.client.session.get(Xe)),(G=this.client.metadata.redirect)!=null&&G.linkMode&&(Q=de.metadata.redirect)!=null&&Q.linkMode&&(se=de.metadata.redirect)!=null&&se.universal&&r&&(this.client.core.addLinkModeSupportedApp(de.metadata.redirect.universal),this.client.session.update(Xe,{transportType:Ne.link_mode})),A({auths:Ce,session:$e})};this.events.once(N,O),this.events.once(k,B);let q;try{if(n){const x=Fs("wc_sessionAuthenticate",_,H);this.client.core.history.set(b,x);const G=await this.client.core.crypto.encode("",x,{type:Hi,encoding:bs});q=nn(r,b,G)}else await Promise.all([this.sendRequest({topic:b,method:"wc_sessionAuthenticate",params:_,expiry:s.expiry,throwOnFailedPublish:!0,clientRpcId:H}),this.sendRequest({topic:b,method:"wc_sessionPropose",params:E,expiry:Be.wc_sessionPropose.req.ttl,throwOnFailedPublish:!0,clientRpcId:E.id})])}catch(x){throw this.events.off(N,O),this.events.off(k,B),x}return await this.setProposal(E.id,E),await this.setAuthRequest(H,{request:tt(ve({},_),{verifyContext:{}}),pairingTopic:b,transportType:o}),{uri:q??v,response:$}}),P(this,"approveSessionAuthenticate",async s=>{const{id:r,auths:i}=s,n=this.client.core.eventClient.createEvent({properties:{topic:r.toString(),trace:[Rs.authenticated_session_approve_started]}});try{this.isInitialized()}catch(m){throw n.setError(oi.no_internet_connection),m}const o=this.getPendingAuthRequest(r);if(!o)throw n.setError(oi.authenticated_session_pending_request_not_found),new Error(`Could not find pending auth request with id ${r}`);const a=o.transportType||Ne.relay;a===Ne.relay&&await this.confirmOnlineStateOrThrow();const c=o.requester.publicKey,l=await this.client.core.crypto.generateKeyPair(),u=mn(c),h={type:ls,receiverPublicKey:c,senderPublicKey:l},d=[],p=[];for(const m of i){if(!await mc({cacao:m,projectId:this.client.core.projectId})){n.setError(oi.invalid_cacao);const S=we("SESSION_SETTLEMENT_FAILED","Signature verification failed");throw await this.sendError({id:r,topic:u,error:S,encodeOpts:h}),new Error(S.message)}n.addTrace(Rs.cacaos_verified);const{p:y}=m,b=gn(y.resources),v=[Lo(y.iss)],C=Sn(y.iss);if(b){const S=wc(b),I=yc(b);d.push(...S),v.push(...I)}for(const S of v)p.push(`${S}:${C}`)}const w=await this.client.core.crypto.generateSharedKey(l,c);n.addTrace(Rs.create_authenticated_session_topic);let f;if((d==null?void 0:d.length)>0){f={topic:w,acknowledged:!0,self:{publicKey:l,metadata:this.client.metadata},peer:{publicKey:c,metadata:o.requester.metadata},controller:c,expiry:De(hr),authentication:i,requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:o.pairingTopic,namespaces:zc([...new Set(d)],[...new Set(p)]),transportType:a},n.addTrace(Rs.subscribing_authenticated_session_topic);try{await this.client.core.relayer.subscribe(w,{transportType:a})}catch(m){throw n.setError(oi.subscribe_authenticated_session_topic_failure),m}n.addTrace(Rs.subscribe_authenticated_session_topic_success),await this.client.session.set(w,f),n.addTrace(Rs.store_authenticated_session),await this.client.core.pairing.updateMetadata({topic:o.pairingTopic,metadata:o.requester.metadata})}n.addTrace(Rs.publishing_authenticated_session_approve);try{await this.sendResult({topic:u,id:r,result:{cacaos:i,responder:{publicKey:l,metadata:this.client.metadata}},encodeOpts:h,throwOnFailedPublish:!0,appLink:this.getAppLinkIfEnabled(o.requester.metadata,a)})}catch(m){throw n.setError(oi.authenticated_session_approve_publish_failure),m}return await this.client.auth.requests.delete(r,{message:"fulfilled",code:0}),await this.client.core.pairing.activate({topic:o.pairingTopic}),this.client.core.eventClient.deleteEvent({eventId:n.eventId}),{session:f}}),P(this,"rejectSessionAuthenticate",async s=>{this.isInitialized();const{id:r,reason:i}=s,n=this.getPendingAuthRequest(r);if(!n)throw new Error(`Could not find pending auth request with id ${r}`);n.transportType===Ne.relay&&await this.confirmOnlineStateOrThrow();const o=n.requester.publicKey,a=await this.client.core.crypto.generateKeyPair(),c=mn(o),l={type:ls,receiverPublicKey:o,senderPublicKey:a};await this.sendError({id:r,topic:c,error:i,encodeOpts:l,rpcOpts:Be.wc_sessionAuthenticate.reject,appLink:this.getAppLinkIfEnabled(n.requester.metadata,n.transportType)}),await this.client.auth.requests.delete(r,{message:"rejected",code:0}),await this.client.proposal.delete(r,we("USER_DISCONNECTED"))}),P(this,"formatAuthMessage",s=>{this.isInitialized();const{request:r,iss:i}=s;return $u(r,i)}),P(this,"processRelayMessageCache",()=>{setTimeout(async()=>{if(this.relayMessageCache.length!==0)for(;this.relayMessageCache.length>0;)try{const s=this.relayMessageCache.shift();s&&await this.onRelayMessage(s)}catch(s){this.client.logger.error(s)}},50)}),P(this,"cleanupDuplicatePairings",async s=>{if(s.pairingTopic)try{const r=this.client.core.pairing.pairings.get(s.pairingTopic),i=this.client.core.pairing.pairings.getAll().filter(n=>{var o,a;return((o=n.peerMetadata)==null?void 0:o.url)&&((a=n.peerMetadata)==null?void 0:a.url)===s.peer.metadata.url&&n.topic&&n.topic!==r.topic});if(i.length===0)return;this.client.logger.info(`Cleaning up ${i.length} duplicate pairing(s)`),await Promise.all(i.map(n=>this.client.core.pairing.disconnect({topic:n.topic}))),this.client.logger.info("Duplicate pairings clean up finished")}catch(r){this.client.logger.error(r)}}),P(this,"deleteSession",async s=>{var r;const{topic:i,expirerHasDeleted:n=!1,emitEvent:o=!0,id:a=0}=s,{self:c}=this.client.session.get(i);await this.client.core.relayer.unsubscribe(i),await this.client.session.delete(i,we("USER_DISCONNECTED")),this.addToRecentlyDeleted(i,"session"),this.client.core.crypto.keychain.has(c.publicKey)&&await this.client.core.crypto.deleteKeyPair(c.publicKey),this.client.core.crypto.keychain.has(i)&&await this.client.core.crypto.deleteSymKey(i),n||this.client.core.expirer.del(i),this.client.core.storage.removeItem(bl).catch(l=>this.client.logger.warn(l)),this.getPendingSessionRequests().forEach(l=>{l.topic===i&&this.deletePendingSessionRequest(l.id,we("USER_DISCONNECTED"))}),i===((r=this.sessionRequestQueue.queue[0])==null?void 0:r.topic)&&(this.sessionRequestQueue.state=zt.idle),o&&this.client.events.emit("session_delete",{id:a,topic:i})}),P(this,"deleteProposal",async(s,r)=>{if(r)try{const i=this.client.proposal.get(s),n=this.client.core.eventClient.getEvent({topic:i.pairingTopic});n==null||n.setError($s.proposal_expired)}catch{}await Promise.all([this.client.proposal.delete(s,we("USER_DISCONNECTED")),r?Promise.resolve():this.client.core.expirer.del(s)]),this.addToRecentlyDeleted(s,"proposal")}),P(this,"deletePendingSessionRequest",async(s,r,i=!1)=>{await Promise.all([this.client.pendingRequest.delete(s,r),i?Promise.resolve():this.client.core.expirer.del(s)]),this.addToRecentlyDeleted(s,"request"),this.sessionRequestQueue.queue=this.sessionRequestQueue.queue.filter(n=>n.id!==s),i&&(this.sessionRequestQueue.state=zt.idle,this.client.events.emit("session_request_expire",{id:s}))}),P(this,"deletePendingAuthRequest",async(s,r,i=!1)=>{await Promise.all([this.client.auth.requests.delete(s,r),i?Promise.resolve():this.client.core.expirer.del(s)])}),P(this,"setExpiry",async(s,r)=>{this.client.session.keys.includes(s)&&(this.client.core.expirer.set(s,r),await this.client.session.update(s,{expiry:r}))}),P(this,"setProposal",async(s,r)=>{this.client.core.expirer.set(s,De(Be.wc_sessionPropose.req.ttl)),await this.client.proposal.set(s,r)}),P(this,"setAuthRequest",async(s,r)=>{const{request:i,pairingTopic:n,transportType:o=Ne.relay}=r;this.client.core.expirer.set(s,i.expiryTimestamp),await this.client.auth.requests.set(s,{authPayload:i.authPayload,requester:i.requester,expiryTimestamp:i.expiryTimestamp,id:s,pairingTopic:n,verifyContext:i.verifyContext,transportType:o})}),P(this,"setPendingSessionRequest",async s=>{const{id:r,topic:i,params:n,verifyContext:o}=s,a=n.request.expiryTimestamp||De(Be.wc_sessionRequest.req.ttl);this.client.core.expirer.set(r,a),await this.client.pendingRequest.set(r,{id:r,topic:i,params:n,verifyContext:o})}),P(this,"sendRequest",async s=>{const{topic:r,method:i,params:n,expiry:o,relayRpcId:a,clientRpcId:c,throwOnFailedPublish:l,appLink:u,tvf:h}=s,d=Fs(i,n,c);let p;const w=!!u;try{const y=w?bs:Mt;p=await this.client.core.crypto.encode(r,d,{encoding:y})}catch(y){throw await this.cleanup(),this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${r} failed`),y}let f;if(wE.includes(i)){const y=Vt(JSON.stringify(d)),b=Vt(p);f=await this.client.core.verify.register({id:b,decryptedId:y})}const m=Be[i].req;if(m.attestation=f,o&&(m.ttl=o),a&&(m.id=a),this.client.core.history.set(r,d),w){const y=nn(u,r,p);await global.Linking.openURL(y,this.client.name)}else{const y=Be[i].req;o&&(y.ttl=o),a&&(y.id=a),y.tvf=tt(ve({},h),{correlationId:d.id}),l?(y.internal=tt(ve({},y.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(r,p,y)):this.client.core.relayer.publish(r,p,y).catch(b=>this.client.logger.error(b))}return d.id}),P(this,"sendResult",async s=>{const{id:r,topic:i,result:n,throwOnFailedPublish:o,encodeOpts:a,appLink:c}=s,l=$n(r,n);let u;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const w=h?bs:Mt;u=await this.client.core.crypto.encode(i,l,tt(ve({},a||{}),{encoding:w}))}catch(w){throw await this.cleanup(),this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${i} failed`),w}let d,p;try{d=await this.client.core.history.get(i,r);const w=d.request;try{this.shouldSetTVF(w.method,w.params)&&(p=this.getTVFParams(r,w.params,n))}catch(f){this.client.logger.warn("sendResult() -> getTVFParams() failed",f)}}catch(w){throw this.client.logger.error(`sendResult() -> history.get(${i}, ${r}) failed`),w}if(h){const w=nn(c,i,u);await global.Linking.openURL(w,this.client.name)}else{const w=d.request.method,f=Be[w].res;f.tvf=tt(ve({},p),{correlationId:r}),o?(f.internal=tt(ve({},f.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(i,u,f)):this.client.core.relayer.publish(i,u,f).catch(m=>this.client.logger.error(m))}await this.client.core.history.resolve(l)}),P(this,"sendError",async s=>{const{id:r,topic:i,error:n,encodeOpts:o,rpcOpts:a,appLink:c}=s,l=eu(r,n);let u;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const p=h?bs:Mt;u=await this.client.core.crypto.encode(i,l,tt(ve({},o||{}),{encoding:p}))}catch(p){throw await this.cleanup(),this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${i} failed`),p}let d;try{d=await this.client.core.history.get(i,r)}catch(p){throw this.client.logger.error(`sendError() -> history.get(${i}, ${r}) failed`),p}if(h){const p=nn(c,i,u);await global.Linking.openURL(p,this.client.name)}else{const p=d.request.method,w=a||Be[p].res;this.client.core.relayer.publish(i,u,w)}await this.client.core.history.resolve(l)}),P(this,"cleanup",async()=>{const s=[],r=[];this.client.session.getAll().forEach(i=>{let n=!1;ys(i.expiry)&&(n=!0),this.client.core.crypto.keychain.has(i.topic)||(n=!0),n&&s.push(i.topic)}),this.client.proposal.getAll().forEach(i=>{ys(i.expiryTimestamp)&&r.push(i.id)}),await Promise.all([...s.map(i=>this.deleteSession({topic:i})),...r.map(i=>this.deleteProposal(i))])}),P(this,"onProviderMessageEvent",async s=>{!this.initialized||this.relayMessageCache.length>0?this.relayMessageCache.push(s):await this.onRelayMessage(s)}),P(this,"onRelayEventRequest",async s=>{this.requestQueue.queue.push(s),await this.processRequestsQueue()}),P(this,"processRequestsQueue",async()=>{if(this.requestQueue.state===zt.active){this.client.logger.info("Request queue already active, skipping...");return}for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`);this.requestQueue.queue.length>0;){this.requestQueue.state=zt.active;const s=this.requestQueue.queue.shift();if(s)try{await this.processRequest(s)}catch(r){this.client.logger.warn(r)}}this.requestQueue.state=zt.idle}),P(this,"processRequest",async s=>{const{topic:r,payload:i,attestation:n,transportType:o,encryptedId:a}=s,c=i.method;if(!this.shouldIgnorePairingRequest({topic:r,requestMethod:c}))switch(c){case"wc_sessionPropose":return await this.onSessionProposeRequest({topic:r,payload:i,attestation:n,encryptedId:a});case"wc_sessionSettle":return await this.onSessionSettleRequest(r,i);case"wc_sessionUpdate":return await this.onSessionUpdateRequest(r,i);case"wc_sessionExtend":return await this.onSessionExtendRequest(r,i);case"wc_sessionPing":return await this.onSessionPingRequest(r,i);case"wc_sessionDelete":return await this.onSessionDeleteRequest(r,i);case"wc_sessionRequest":return await this.onSessionRequest({topic:r,payload:i,attestation:n,encryptedId:a,transportType:o});case"wc_sessionEvent":return await this.onSessionEventRequest(r,i);case"wc_sessionAuthenticate":return await this.onSessionAuthenticateRequest({topic:r,payload:i,attestation:n,encryptedId:a,transportType:o});default:return this.client.logger.info(`Unsupported request method ${c}`)}}),P(this,"onRelayEventResponse",async s=>{const{topic:r,payload:i,transportType:n}=s,o=(await this.client.core.history.get(r,i.id)).request.method;switch(o){case"wc_sessionPropose":return this.onSessionProposeResponse(r,i,n);case"wc_sessionSettle":return this.onSessionSettleResponse(r,i);case"wc_sessionUpdate":return this.onSessionUpdateResponse(r,i);case"wc_sessionExtend":return this.onSessionExtendResponse(r,i);case"wc_sessionPing":return this.onSessionPingResponse(r,i);case"wc_sessionRequest":return this.onSessionRequestResponse(r,i);case"wc_sessionAuthenticate":return this.onSessionAuthenticateResponse(r,i);default:return this.client.logger.info(`Unsupported response method ${o}`)}}),P(this,"onRelayEventUnknownPayload",s=>{const{topic:r}=s,{message:i}=R("MISSING_OR_INVALID",`Decoded payload on topic ${r} is not identifiable as a JSON-RPC request or a response.`);throw new Error(i)}),P(this,"shouldIgnorePairingRequest",s=>{const{topic:r,requestMethod:i}=s,n=this.expectedPairingMethodMap.get(r);return!n||n.includes(i)?!1:!!(n.includes("wc_sessionAuthenticate")&&this.client.events.listenerCount("session_authenticate")>0)}),P(this,"onSessionProposeRequest",async s=>{const{topic:r,payload:i,attestation:n,encryptedId:o}=s,{params:a,id:c}=i;try{const l=this.client.core.eventClient.getEvent({topic:r});this.client.events.listenerCount("session_proposal")===0&&(console.warn("No listener for session_proposal event"),l==null||l.setError(ns.proposal_listener_not_found)),this.isValidConnect(ve({},i.params));const u=a.expiryTimestamp||De(Be.wc_sessionPropose.req.ttl),h=ve({id:c,pairingTopic:r,expiryTimestamp:u},a);await this.setProposal(c,h);const d=await this.getVerifyContext({attestationId:n,hash:Vt(JSON.stringify(i)),encryptedId:o,metadata:h.proposer.metadata});l==null||l.addTrace(Wt.emit_session_proposal),this.client.events.emit("session_proposal",{id:c,params:h,verifyContext:d})}catch(l){await this.sendError({id:c,topic:r,error:l,rpcOpts:Be.wc_sessionPropose.autoReject}),this.client.logger.error(l)}}),P(this,"onSessionProposeResponse",async(s,r,i)=>{const{id:n}=r;if(is(r)){const{result:o}=r;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",result:o});const a=this.client.proposal.get(n);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",proposal:a});const c=a.proposer.publicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",selfPublicKey:c});const l=o.responderPublicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",peerPublicKey:l});const u=await this.client.core.crypto.generateSharedKey(c,l);this.pendingSessions.set(n,{sessionTopic:u,pairingTopic:s,proposalId:n,publicKey:c});const h=await this.client.core.relayer.subscribe(u,{transportType:i});this.client.logger.trace({type:"method",method:"onSessionProposeResponse",subscriptionId:h}),await this.client.core.pairing.activate({topic:s})}else if(Ht(r)){await this.client.proposal.delete(n,we("USER_DISCONNECTED"));const o=pe("session_connect",n);if(this.events.listenerCount(o)===0)throw new Error(`emitting ${o} without any listeners, 954`);this.events.emit(o,{error:r.error})}}),P(this,"onSessionSettleRequest",async(s,r)=>{const{id:i,params:n}=r;try{this.isValidSessionSettleRequest(n);const{relay:o,controller:a,expiry:c,namespaces:l,sessionProperties:u,scopedProperties:h,sessionConfig:d}=r.params,p=[...this.pendingSessions.values()].find(m=>m.sessionTopic===s);if(!p)return this.client.logger.error(`Pending session not found for topic ${s}`);const w=this.client.proposal.get(p.proposalId),f=tt(ve(ve(ve({topic:s,relay:o,expiry:c,namespaces:l,acknowledged:!0,pairingTopic:p.pairingTopic,requiredNamespaces:w.requiredNamespaces,optionalNamespaces:w.optionalNamespaces,controller:a.publicKey,self:{publicKey:p.publicKey,metadata:this.client.metadata},peer:{publicKey:a.publicKey,metadata:a.metadata}},u&&{sessionProperties:u}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),{transportType:Ne.relay});await this.client.session.set(f.topic,f),await this.setExpiry(f.topic,f.expiry),await this.client.core.pairing.updateMetadata({topic:p.pairingTopic,metadata:f.peer.metadata}),this.client.events.emit("session_connect",{session:f}),this.events.emit(pe("session_connect",p.proposalId),{session:f}),this.pendingSessions.delete(p.proposalId),this.deleteProposal(p.proposalId,!1),this.cleanupDuplicatePairings(f),await this.sendResult({id:r.id,topic:s,result:!0,throwOnFailedPublish:!0})}catch(o){await this.sendError({id:i,topic:s,error:o}),this.client.logger.error(o)}}),P(this,"onSessionSettleResponse",async(s,r)=>{const{id:i}=r;is(r)?(await this.client.session.update(s,{acknowledged:!0}),this.events.emit(pe("session_approve",i),{})):Ht(r)&&(await this.client.session.delete(s,we("USER_DISCONNECTED")),this.events.emit(pe("session_approve",i),{error:r.error}))}),P(this,"onSessionUpdateRequest",async(s,r)=>{const{params:i,id:n}=r;try{const o=`${s}_session_update`,a=ii.get(o);if(a&&this.isRequestOutOfSync(a,n)){this.client.logger.warn(`Discarding out of sync request - ${n}`),this.sendError({id:n,topic:s,error:we("INVALID_UPDATE_REQUEST")});return}this.isValidUpdate(ve({topic:s},i));try{ii.set(o,n),await this.client.session.update(s,{namespaces:i.namespaces}),await this.sendResult({id:n,topic:s,result:!0,throwOnFailedPublish:!0})}catch(c){throw ii.delete(o),c}this.client.events.emit("session_update",{id:n,topic:s,params:i})}catch(o){await this.sendError({id:n,topic:s,error:o}),this.client.logger.error(o)}}),P(this,"isRequestOutOfSync",(s,r)=>r.toString().slice(0,-3)<s.toString().slice(0,-3)),P(this,"onSessionUpdateResponse",(s,r)=>{const{id:i}=r,n=pe("session_update",i);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);is(r)?this.events.emit(pe("session_update",i),{}):Ht(r)&&this.events.emit(pe("session_update",i),{error:r.error})}),P(this,"onSessionExtendRequest",async(s,r)=>{const{id:i}=r;try{this.isValidExtend({topic:s}),await this.setExpiry(s,De(hr)),await this.sendResult({id:i,topic:s,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_extend",{id:i,topic:s})}catch(n){await this.sendError({id:i,topic:s,error:n}),this.client.logger.error(n)}}),P(this,"onSessionExtendResponse",(s,r)=>{const{id:i}=r,n=pe("session_extend",i);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);is(r)?this.events.emit(pe("session_extend",i),{}):Ht(r)&&this.events.emit(pe("session_extend",i),{error:r.error})}),P(this,"onSessionPingRequest",async(s,r)=>{const{id:i}=r;try{this.isValidPing({topic:s}),await this.sendResult({id:i,topic:s,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_ping",{id:i,topic:s})}catch(n){await this.sendError({id:i,topic:s,error:n}),this.client.logger.error(n)}}),P(this,"onSessionPingResponse",(s,r)=>{const{id:i}=r,n=pe("session_ping",i);setTimeout(()=>{if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners 2176`);is(r)?this.events.emit(pe("session_ping",i),{}):Ht(r)&&this.events.emit(pe("session_ping",i),{error:r.error})},500)}),P(this,"onSessionDeleteRequest",async(s,r)=>{const{id:i}=r;try{this.isValidDisconnect({topic:s,reason:r.params}),Promise.all([new Promise(n=>{this.client.core.relayer.once(Fe.publish,async()=>{n(await this.deleteSession({topic:s,id:i}))})}),this.sendResult({id:i,topic:s,result:!0,throwOnFailedPublish:!0}),this.cleanupPendingSentRequestsForTopic({topic:s,error:we("USER_DISCONNECTED")})]).catch(n=>this.client.logger.error(n))}catch(n){this.client.logger.error(n)}}),P(this,"onSessionRequest",async s=>{var r,i,n;const{topic:o,payload:a,attestation:c,encryptedId:l,transportType:u}=s,{id:h,params:d}=a;try{await this.isValidRequest(ve({topic:o},d));const p=this.client.session.get(o),w=await this.getVerifyContext({attestationId:c,hash:Vt(JSON.stringify(Fs("wc_sessionRequest",d,h))),encryptedId:l,metadata:p.peer.metadata,transportType:u}),f={id:h,topic:o,params:d,verifyContext:w};await this.setPendingSessionRequest(f),u===Ne.link_mode&&(r=p.peer.metadata.redirect)!=null&&r.universal&&this.client.core.addLinkModeSupportedApp((i=p.peer.metadata.redirect)==null?void 0:i.universal),(n=this.client.signConfig)!=null&&n.disableRequestQueue?this.emitSessionRequest(f):(this.addSessionRequestToSessionRequestQueue(f),this.processSessionRequestQueue())}catch(p){await this.sendError({id:h,topic:o,error:p}),this.client.logger.error(p)}}),P(this,"onSessionRequestResponse",(s,r)=>{const{id:i}=r,n=pe("session_request",i);if(this.events.listenerCount(n)===0)throw new Error(`emitting ${n} without any listeners`);is(r)?this.events.emit(pe("session_request",i),{result:r.result}):Ht(r)&&this.events.emit(pe("session_request",i),{error:r.error})}),P(this,"onSessionEventRequest",async(s,r)=>{const{id:i,params:n}=r;try{const o=`${s}_session_event_${n.event.name}`,a=ii.get(o);if(a&&this.isRequestOutOfSync(a,i)){this.client.logger.info(`Discarding out of sync request - ${i}`);return}this.isValidEmit(ve({topic:s},n)),this.client.events.emit("session_event",{id:i,topic:s,params:n}),ii.set(o,i)}catch(o){await this.sendError({id:i,topic:s,error:o}),this.client.logger.error(o)}}),P(this,"onSessionAuthenticateResponse",(s,r)=>{const{id:i}=r;this.client.logger.trace({type:"method",method:"onSessionAuthenticateResponse",topic:s,payload:r}),is(r)?this.events.emit(pe("session_request",i),{result:r.result}):Ht(r)&&this.events.emit(pe("session_request",i),{error:r.error})}),P(this,"onSessionAuthenticateRequest",async s=>{var r;const{topic:i,payload:n,attestation:o,encryptedId:a,transportType:c}=s;try{const{requester:l,authPayload:u,expiryTimestamp:h}=n.params,d=await this.getVerifyContext({attestationId:o,hash:Vt(JSON.stringify(n)),encryptedId:a,metadata:l.metadata,transportType:c}),p={requester:l,pairingTopic:i,id:n.id,authPayload:u,verifyContext:d,expiryTimestamp:h};await this.setAuthRequest(n.id,{request:p,pairingTopic:i,transportType:c}),c===Ne.link_mode&&(r=l.metadata.redirect)!=null&&r.universal&&this.client.core.addLinkModeSupportedApp(l.metadata.redirect.universal),this.client.events.emit("session_authenticate",{topic:i,params:n.params,id:n.id,verifyContext:d})}catch(l){this.client.logger.error(l);const u=n.params.requester.publicKey,h=await this.client.core.crypto.generateKeyPair(),d=this.getAppLinkIfEnabled(n.params.requester.metadata,c),p={type:ls,receiverPublicKey:u,senderPublicKey:h};await this.sendError({id:n.id,topic:i,error:l,encodeOpts:p,rpcOpts:Be.wc_sessionAuthenticate.autoReject,appLink:d})}}),P(this,"addSessionRequestToSessionRequestQueue",s=>{this.sessionRequestQueue.queue.push(s)}),P(this,"cleanupAfterResponse",s=>{this.deletePendingSessionRequest(s.response.id,{message:"fulfilled",code:0}),setTimeout(()=>{this.sessionRequestQueue.state=zt.idle,this.processSessionRequestQueue()},D.toMiliseconds(this.requestQueueDelay))}),P(this,"cleanupPendingSentRequestsForTopic",({topic:s,error:r})=>{const i=this.client.core.history.pending;i.length>0&&i.filter(n=>n.topic===s&&n.request.method==="wc_sessionRequest").forEach(n=>{const o=n.request.id,a=pe("session_request",o);if(this.events.listenerCount(a)===0)throw new Error(`emitting ${a} without any listeners`);this.events.emit(pe("session_request",n.request.id),{error:r})})}),P(this,"processSessionRequestQueue",()=>{if(this.sessionRequestQueue.state===zt.active){this.client.logger.info("session request queue is already active.");return}const s=this.sessionRequestQueue.queue[0];if(!s){this.client.logger.info("session request queue is empty.");return}try{this.sessionRequestQueue.state=zt.active,this.emitSessionRequest(s)}catch(r){this.client.logger.error(r)}}),P(this,"emitSessionRequest",s=>{this.client.events.emit("session_request",s)}),P(this,"onPairingCreated",s=>{if(s.methods&&this.expectedPairingMethodMap.set(s.topic,s.methods),s.active)return;const r=this.client.proposal.getAll().find(i=>i.pairingTopic===s.topic);r&&this.onSessionProposeRequest({topic:s.topic,payload:Fs("wc_sessionPropose",tt(ve({},r),{requiredNamespaces:r.requiredNamespaces,optionalNamespaces:r.optionalNamespaces,relays:r.relays,proposer:r.proposer,sessionProperties:r.sessionProperties,scopedProperties:r.scopedProperties}),r.id)})}),P(this,"isValidConnect",async s=>{if(!ut(s)){const{message:l}=R("MISSING_OR_INVALID",`connect() params: ${JSON.stringify(s)}`);throw new Error(l)}const{pairingTopic:r,requiredNamespaces:i,optionalNamespaces:n,sessionProperties:o,scopedProperties:a,relays:c}=s;if(Je(r)||await this.isValidPairingTopic(r),!dy(c)){const{message:l}=R("MISSING_OR_INVALID",`connect() relays: ${c}`);throw new Error(l)}if(!Je(i)&&_s(i)!==0){const l="requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";["fatal","error","silent"].includes(this.client.logger.level)?console.warn(l):this.client.logger.warn(l),this.validateNamespaces(i,"requiredNamespaces")}if(!Je(n)&&_s(n)!==0&&this.validateNamespaces(n,"optionalNamespaces"),Je(o)||this.validateSessionProps(o,"sessionProperties"),!Je(a)){this.validateSessionProps(a,"scopedProperties");const l=Object.keys(i||{}).concat(Object.keys(n||{}));if(!Object.keys(a).every(u=>l.includes(u)))throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(a)}, required/optional namespaces: ${JSON.stringify(l)}`)}}),P(this,"validateNamespaces",(s,r)=>{const i=hy(s,"connect()",r);if(i)throw new Error(i.message)}),P(this,"isValidApprove",async s=>{if(!ut(s))throw new Error(R("MISSING_OR_INVALID",`approve() params: ${s}`).message);const{id:r,namespaces:i,relayProtocol:n,sessionProperties:o,scopedProperties:a}=s;this.checkRecentlyDeleted(r),await this.isValidProposalId(r);const c=this.client.proposal.get(r),l=co(i,"approve()");if(l)throw new Error(l.message);const u=Kc(c.requiredNamespaces,i,"approve()");if(u)throw new Error(u.message);if(!xe(n,!0)){const{message:h}=R("MISSING_OR_INVALID",`approve() relayProtocol: ${n}`);throw new Error(h)}if(Je(o)||this.validateSessionProps(o,"sessionProperties"),!Je(a)){this.validateSessionProps(a,"scopedProperties");const h=new Set(Object.keys(i));if(!Object.keys(a).every(d=>h.has(d)))throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(a)}, approved namespaces: ${Array.from(h).join(", ")}`)}}),P(this,"isValidReject",async s=>{if(!ut(s)){const{message:n}=R("MISSING_OR_INVALID",`reject() params: ${s}`);throw new Error(n)}const{id:r,reason:i}=s;if(this.checkRecentlyDeleted(r),await this.isValidProposalId(r),!fy(i)){const{message:n}=R("MISSING_OR_INVALID",`reject() reason: ${JSON.stringify(i)}`);throw new Error(n)}}),P(this,"isValidSessionSettleRequest",s=>{if(!ut(s)){const{message:l}=R("MISSING_OR_INVALID",`onSessionSettleRequest() params: ${s}`);throw new Error(l)}const{relay:r,controller:i,namespaces:n,expiry:o}=s;if(!nh(r)){const{message:l}=R("MISSING_OR_INVALID","onSessionSettleRequest() relay protocol should be a string");throw new Error(l)}const a=ny(i,"onSessionSettleRequest()");if(a)throw new Error(a.message);const c=co(n,"onSessionSettleRequest()");if(c)throw new Error(c.message);if(ys(o)){const{message:l}=R("EXPIRED","onSessionSettleRequest()");throw new Error(l)}}),P(this,"isValidUpdate",async s=>{if(!ut(s)){const{message:c}=R("MISSING_OR_INVALID",`update() params: ${s}`);throw new Error(c)}const{topic:r,namespaces:i}=s;this.checkRecentlyDeleted(r),await this.isValidSessionTopic(r);const n=this.client.session.get(r),o=co(i,"update()");if(o)throw new Error(o.message);const a=Kc(n.requiredNamespaces,i,"update()");if(a)throw new Error(a.message)}),P(this,"isValidExtend",async s=>{if(!ut(s)){const{message:i}=R("MISSING_OR_INVALID",`extend() params: ${s}`);throw new Error(i)}const{topic:r}=s;this.checkRecentlyDeleted(r),await this.isValidSessionTopic(r)}),P(this,"isValidRequest",async s=>{if(!ut(s)){const{message:c}=R("MISSING_OR_INVALID",`request() params: ${s}`);throw new Error(c)}const{topic:r,request:i,chainId:n,expiry:o}=s;this.checkRecentlyDeleted(r),await this.isValidSessionTopic(r);const{namespaces:a}=this.client.session.get(r);if(!Hc(a,n)){const{message:c}=R("MISSING_OR_INVALID",`request() chainId: ${n}`);throw new Error(c)}if(!gy(i)){const{message:c}=R("MISSING_OR_INVALID",`request() ${JSON.stringify(i)}`);throw new Error(c)}if(!yy(a,n,i.method)){const{message:c}=R("MISSING_OR_INVALID",`request() method: ${i.method}`);throw new Error(c)}if(o&&!Cy(o,mo)){const{message:c}=R("MISSING_OR_INVALID",`request() expiry: ${o}. Expiry must be a number (in seconds) between ${mo.min} and ${mo.max}`);throw new Error(c)}}),P(this,"isValidRespond",async s=>{var r;if(!ut(s)){const{message:o}=R("MISSING_OR_INVALID",`respond() params: ${s}`);throw new Error(o)}const{topic:i,response:n}=s;try{await this.isValidSessionTopic(i)}catch(o){throw(r=s==null?void 0:s.response)!=null&&r.id&&this.cleanupAfterResponse(s),o}if(!my(n)){const{message:o}=R("MISSING_OR_INVALID",`respond() response: ${JSON.stringify(n)}`);throw new Error(o)}}),P(this,"isValidPing",async s=>{if(!ut(s)){const{message:i}=R("MISSING_OR_INVALID",`ping() params: ${s}`);throw new Error(i)}const{topic:r}=s;await this.isValidSessionOrPairingTopic(r)}),P(this,"isValidEmit",async s=>{if(!ut(s)){const{message:a}=R("MISSING_OR_INVALID",`emit() params: ${s}`);throw new Error(a)}const{topic:r,event:i,chainId:n}=s;await this.isValidSessionTopic(r);const{namespaces:o}=this.client.session.get(r);if(!Hc(o,n)){const{message:a}=R("MISSING_OR_INVALID",`emit() chainId: ${n}`);throw new Error(a)}if(!wy(i)){const{message:a}=R("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}if(!by(o,n,i.name)){const{message:a}=R("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}}),P(this,"isValidDisconnect",async s=>{if(!ut(s)){const{message:i}=R("MISSING_OR_INVALID",`disconnect() params: ${s}`);throw new Error(i)}const{topic:r}=s;await this.isValidSessionOrPairingTopic(r)}),P(this,"isValidAuthenticate",s=>{const{chains:r,uri:i,domain:n,nonce:o}=s;if(!Array.isArray(r)||r.length===0)throw new Error("chains is required and must be a non-empty array");if(!xe(i,!1))throw new Error("uri is required parameter");if(!xe(n,!1))throw new Error("domain is required parameter");if(!xe(o,!1))throw new Error("nonce is required parameter");if([...new Set(r.map(c=>kr(c).namespace))].length>1)throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");const{namespace:a}=kr(r[0]);if(a!=="eip155")throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.")}),P(this,"getVerifyContext",async s=>{const{attestationId:r,hash:i,encryptedId:n,metadata:o,transportType:a}=s,c={verified:{verifyUrl:o.verifyUrl||Ci,validation:"UNKNOWN",origin:o.url||""}};try{if(a===Ne.link_mode){const u=this.getAppLinkIfEnabled(o,a);return c.verified.validation=u&&new URL(u).origin===new URL(o.url).origin?"VALID":"INVALID",c}const l=await this.client.core.verify.resolve({attestationId:r,hash:i,encryptedId:n,verifyUrl:o.verifyUrl});l&&(c.verified.origin=l.origin,c.verified.isScam=l.isScam,c.verified.validation=l.origin===new URL(o.url).origin?"VALID":"INVALID")}catch(l){this.client.logger.warn(l)}return this.client.logger.debug(`Verify context: ${JSON.stringify(c)}`),c}),P(this,"validateSessionProps",(s,r)=>{Object.values(s).forEach((i,n)=>{if(i==null){const{message:o}=R("MISSING_OR_INVALID",`${r} must contain an existing value for each key. Received: ${i} for key ${Object.keys(s)[n]}`);throw new Error(o)}})}),P(this,"getPendingAuthRequest",s=>{const r=this.client.auth.requests.get(s);return typeof r=="object"?r:void 0}),P(this,"addToRecentlyDeleted",(s,r)=>{if(this.recentlyDeletedMap.set(s,r),this.recentlyDeletedMap.size>=this.recentlyDeletedLimit){let i=0;const n=this.recentlyDeletedLimit/2;for(const o of this.recentlyDeletedMap.keys()){if(i++>=n)break;this.recentlyDeletedMap.delete(o)}}}),P(this,"checkRecentlyDeleted",s=>{const r=this.recentlyDeletedMap.get(s);if(r){const{message:i}=R("MISSING_OR_INVALID",`Record was recently deleted - ${r}: ${s}`);throw new Error(i)}}),P(this,"isLinkModeEnabled",(s,r)=>{var i,n,o,a,c,l,u,h,d;return!s||r!==Ne.link_mode?!1:((n=(i=this.client.metadata)==null?void 0:i.redirect)==null?void 0:n.linkMode)===!0&&((a=(o=this.client.metadata)==null?void 0:o.redirect)==null?void 0:a.universal)!==void 0&&((l=(c=this.client.metadata)==null?void 0:c.redirect)==null?void 0:l.universal)!==""&&((u=s==null?void 0:s.redirect)==null?void 0:u.universal)!==void 0&&((h=s==null?void 0:s.redirect)==null?void 0:h.universal)!==""&&((d=s==null?void 0:s.redirect)==null?void 0:d.linkMode)===!0&&this.client.core.linkModeSupportedApps.includes(s.redirect.universal)&&typeof(global==null?void 0:global.Linking)<"u"}),P(this,"getAppLinkIfEnabled",(s,r)=>{var i;return this.isLinkModeEnabled(s,r)?(i=s==null?void 0:s.redirect)==null?void 0:i.universal:void 0}),P(this,"handleLinkModeMessage",({url:s})=>{if(!s||!s.includes("wc_ev")||!s.includes("topic"))return;const r=ac(s,"topic")||"",i=decodeURIComponent(ac(s,"wc_ev")||""),n=this.client.session.keys.includes(r);n&&this.client.session.update(r,{transportType:Ne.link_mode}),this.client.core.dispatchEnvelope({topic:r,message:i,sessionExists:n})}),P(this,"registerLinkModeListeners",async()=>{var s;if(ma()||Ts()&&(s=this.client.metadata.redirect)!=null&&s.linkMode){const r=global==null?void 0:global.Linking;if(typeof r<"u"){r.addEventListener("url",this.handleLinkModeMessage,this.client.name);const i=await r.getInitialURL();i&&setTimeout(()=>{this.handleLinkModeMessage({url:i})},50)}}}),P(this,"shouldSetTVF",(s,r)=>{if(!r||s!=="wc_sessionRequest")return!1;const{request:i}=r;return Object.keys(El).includes(i.method)}),P(this,"getTVFParams",(s,r,i)=>{var n,o;try{const a=r.request.method,c=this.extractTxHashesFromResult(a,i);return tt(ve({correlationId:s,rpcMethods:[a],chainId:r.chainId},this.isValidContractData(r.request.params)&&{contractAddresses:[(o=(n=r.request.params)==null?void 0:n[0])==null?void 0:o.to]}),{txHashes:c})}catch(a){this.client.logger.warn("Error getting TVF params",a)}return{}}),P(this,"isValidContractData",s=>{var r;if(!s)return!1;try{const i=(s==null?void 0:s.data)||((r=s==null?void 0:s[0])==null?void 0:r.data);if(!i.startsWith("0x"))return!1;const n=i.slice(2);return/^[0-9a-fA-F]*$/.test(n)?n.length%2===0:!1}catch{}return!1}),P(this,"extractTxHashesFromResult",(s,r)=>{try{const i=El[s];if(typeof r=="string")return[r];const n=r[i.key];if(Ns(n))return s==="solana_signAllTransactions"?n.map(o=>Fg(o)):n;if(typeof n=="string")return[n]}catch(i){this.client.logger.warn("Error extracting tx hashes from result",i)}return[]})}async processPendingMessageEvents(){try{const e=this.client.session.keys,s=this.client.core.relayer.messages.getWithoutAck(e);for(const[r,i]of Object.entries(s))for(const n of i)try{await this.onProviderMessageEvent({topic:r,message:n,publishedAt:Date.now()})}catch{this.client.logger.warn(`Error processing pending message event for topic: ${r}, message: ${n}`)}}catch(e){this.client.logger.warn("processPendingMessageEvents failed",e)}}isInitialized(){if(!this.initialized){const{message:e}=R("NOT_INITIALIZED",this.name);throw new Error(e)}}async confirmOnlineStateOrThrow(){await this.client.core.relayer.confirmOnlineStateOrThrow()}registerRelayerEvents(){this.client.core.relayer.on(Fe.message,e=>{this.onProviderMessageEvent(e)})}async onRelayMessage(e){const{topic:s,message:r,attestation:i,transportType:n}=e,{publicKey:o}=this.client.auth.authKeys.keys.includes(yn)?this.client.auth.authKeys.get(yn):{publicKey:void 0};try{const a=await this.client.core.crypto.decode(s,r,{receiverPublicKey:o,encoding:n===Ne.link_mode?bs:Mt});pa(a)?(this.client.core.history.set(s,a),await this.onRelayEventRequest({topic:s,payload:a,attestation:i,transportType:n,encryptedId:Vt(r)})):fa(a)?(await this.client.core.history.resolve(a),await this.onRelayEventResponse({topic:s,payload:a,transportType:n}),this.client.core.history.delete(s,a.id)):await this.onRelayEventUnknownPayload({topic:s,payload:a,transportType:n}),await this.client.core.relayer.messages.ack(s,r)}catch(a){this.client.logger.error(a)}}registerExpirerEvents(){this.client.core.expirer.on(Pt.expired,async e=>{const{topic:s,id:r}=Au(e.target);if(r&&this.client.pendingRequest.keys.includes(r))return await this.deletePendingSessionRequest(r,R("EXPIRED"),!0);if(r&&this.client.auth.requests.keys.includes(r))return await this.deletePendingAuthRequest(r,R("EXPIRED"),!0);s?this.client.session.keys.includes(s)&&(await this.deleteSession({topic:s,expirerHasDeleted:!0}),this.client.events.emit("session_expire",{topic:s})):r&&(await this.deleteProposal(r,!0),this.client.events.emit("proposal_expire",{id:r}))})}registerPairingEvents(){this.client.core.pairing.events.on(js.create,e=>this.onPairingCreated(e)),this.client.core.pairing.events.on(js.delete,e=>{this.addToRecentlyDeleted(e.topic,"pairing")})}isValidPairingTopic(e){if(!xe(e,!1)){const{message:s}=R("MISSING_OR_INVALID",`pairing topic should be a string: ${e}`);throw new Error(s)}if(!this.client.core.pairing.pairings.keys.includes(e)){const{message:s}=R("NO_MATCHING_KEY",`pairing topic doesn't exist: ${e}`);throw new Error(s)}if(ys(this.client.core.pairing.pairings.get(e).expiry)){const{message:s}=R("EXPIRED",`pairing topic: ${e}`);throw new Error(s)}}async isValidSessionTopic(e){if(!xe(e,!1)){const{message:s}=R("MISSING_OR_INVALID",`session topic should be a string: ${e}`);throw new Error(s)}if(this.checkRecentlyDeleted(e),!this.client.session.keys.includes(e)){const{message:s}=R("NO_MATCHING_KEY",`session topic doesn't exist: ${e}`);throw new Error(s)}if(ys(this.client.session.get(e).expiry)){await this.deleteSession({topic:e});const{message:s}=R("EXPIRED",`session topic: ${e}`);throw new Error(s)}if(!this.client.core.crypto.keychain.has(e)){const{message:s}=R("MISSING_OR_INVALID",`session topic does not exist in keychain: ${e}`);throw await this.deleteSession({topic:e}),new Error(s)}}async isValidSessionOrPairingTopic(e){if(this.checkRecentlyDeleted(e),this.client.session.keys.includes(e))await this.isValidSessionTopic(e);else if(this.client.core.pairing.pairings.keys.includes(e))this.isValidPairingTopic(e);else if(xe(e,!1)){const{message:s}=R("NO_MATCHING_KEY",`session or pairing topic doesn't exist: ${e}`);throw new Error(s)}else{const{message:s}=R("MISSING_OR_INVALID",`session or pairing topic should be a string: ${e}`);throw new Error(s)}}async isValidProposalId(e){if(!py(e)){const{message:s}=R("MISSING_OR_INVALID",`proposal id should be a number: ${e}`);throw new Error(s)}if(!this.client.proposal.keys.includes(e)){const{message:s}=R("NO_MATCHING_KEY",`proposal id doesn't exist: ${e}`);throw new Error(s)}if(ys(this.client.proposal.get(e).expiryTimestamp)){await this.deleteProposal(e);const{message:s}=R("EXPIRED",`proposal id: ${e}`);throw new Error(s)}}}class OE extends ir{constructor(e,s){super(e,s,pE,Aa),this.core=e,this.logger=s}}let TE=class extends ir{constructor(e,s){super(e,s,fE,Aa),this.core=e,this.logger=s}};class kE extends ir{constructor(e,s){super(e,s,mE,Aa,r=>r.id),this.core=e,this.logger=s}}class xE extends ir{constructor(e,s){super(e,s,vE,zn,()=>yn),this.core=e,this.logger=s}}class $E extends ir{constructor(e,s){super(e,s,EE,zn),this.core=e,this.logger=s}}class RE extends ir{constructor(e,s){super(e,s,CE,zn,r=>r.id),this.core=e,this.logger=s}}var UE=Object.defineProperty,DE=(t,e,s)=>e in t?UE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,wo=(t,e,s)=>DE(t,typeof e!="symbol"?e+"":e,s);class LE{constructor(e,s){this.core=e,this.logger=s,wo(this,"authKeys"),wo(this,"pairingTopics"),wo(this,"requests"),this.authKeys=new xE(this.core,this.logger),this.pairingTopics=new $E(this.core,this.logger),this.requests=new RE(this.core,this.logger)}async init(){await this.authKeys.init(),await this.pairingTopics.init(),await this.requests.init()}}var ME=Object.defineProperty,BE=(t,e,s)=>e in t?ME(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,ne=(t,e,s)=>BE(t,typeof e!="symbol"?e+"":e,s);let jE=class Nh extends Zp{constructor(e){super(e),ne(this,"protocol",Ch),ne(this,"version",Ih),ne(this,"name",go.name),ne(this,"metadata"),ne(this,"core"),ne(this,"logger"),ne(this,"events",new er.EventEmitter),ne(this,"engine"),ne(this,"session"),ne(this,"proposal"),ne(this,"pendingRequest"),ne(this,"auth"),ne(this,"signConfig"),ne(this,"on",(r,i)=>this.events.on(r,i)),ne(this,"once",(r,i)=>this.events.once(r,i)),ne(this,"off",(r,i)=>this.events.off(r,i)),ne(this,"removeListener",(r,i)=>this.events.removeListener(r,i)),ne(this,"removeAllListeners",r=>this.events.removeAllListeners(r)),ne(this,"connect",async r=>{try{return await this.engine.connect(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"pair",async r=>{try{return await this.engine.pair(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"approve",async r=>{try{return await this.engine.approve(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"reject",async r=>{try{return await this.engine.reject(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"update",async r=>{try{return await this.engine.update(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"extend",async r=>{try{return await this.engine.extend(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"request",async r=>{try{return await this.engine.request(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"respond",async r=>{try{return await this.engine.respond(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"ping",async r=>{try{return await this.engine.ping(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"emit",async r=>{try{return await this.engine.emit(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"disconnect",async r=>{try{return await this.engine.disconnect(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"find",r=>{try{return this.engine.find(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"getPendingSessionRequests",()=>{try{return this.engine.getPendingSessionRequests()}catch(r){throw this.logger.error(r.message),r}}),ne(this,"authenticate",async(r,i)=>{try{return await this.engine.authenticate(r,i)}catch(n){throw this.logger.error(n.message),n}}),ne(this,"formatAuthMessage",r=>{try{return this.engine.formatAuthMessage(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"approveSessionAuthenticate",async r=>{try{return await this.engine.approveSessionAuthenticate(r)}catch(i){throw this.logger.error(i.message),i}}),ne(this,"rejectSessionAuthenticate",async r=>{try{return await this.engine.rejectSessionAuthenticate(r)}catch(i){throw this.logger.error(i.message),i}}),this.name=(e==null?void 0:e.name)||go.name,this.metadata=tg(e==null?void 0:e.metadata),this.signConfig=e==null?void 0:e.signConfig;const s=typeof(e==null?void 0:e.logger)<"u"&&typeof(e==null?void 0:e.logger)!="string"?e.logger:da(Li({level:(e==null?void 0:e.logger)||go.logger}));this.core=(e==null?void 0:e.core)||new dE(e),this.logger=rt(s,this.name),this.session=new TE(this.core,this.logger),this.proposal=new OE(this.core,this.logger),this.pendingRequest=new kE(this.core,this.logger),this.engine=new PE(this),this.auth=new LE(this.core,this.logger)}static async init(e){const s=new Nh(e);return await s.initialize(),s}get context(){return Et(this.logger)}get pairing(){return this.core.pairing.pairings}async initialize(){this.logger.trace("Initialized");try{await this.core.start(),await this.session.init(),await this.proposal.init(),await this.pendingRequest.init(),await this.auth.init(),await this.engine.init(),this.logger.info("SignClient Initialization Success"),setTimeout(()=>{this.engine.processRelayMessageCache()},D.toMiliseconds(D.ONE_SECOND))}catch(e){throw this.logger.info("SignClient Initialization Failure"),this.logger.error(e.message),e}}};const Il="error",qE="wss://relay.walletconnect.org",FE="wc",zE="universal_provider",an=`${FE}@2:${zE}:`,_h="https://rpc.walletconnect.org/v1/",Ar="generic",WE=`${_h}bundler`,kt={DEFAULT_CHAIN_CHANGED:"default_chain_changed"};function HE(){}function Na(t){return t==null||typeof t!="object"&&typeof t!="function"}function _a(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function KE(t){if(Na(t))return t;if(Array.isArray(t)||_a(t)||t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);const e=Object.getPrototypeOf(t),s=e.constructor;if(t instanceof Date||t instanceof Map||t instanceof Set)return new s(t);if(t instanceof RegExp){const r=new s(t);return r.lastIndex=t.lastIndex,r}if(t instanceof DataView)return new s(t.buffer.slice(0));if(t instanceof Error){const r=new s(t.message);return r.stack=t.stack,r.name=t.name,r.cause=t.cause,r}if(typeof File<"u"&&t instanceof File)return new s([t],t.name,{type:t.type,lastModified:t.lastModified});if(typeof t=="object"){const r=Object.create(e);return Object.assign(r,t)}return t}function Al(t){return typeof t=="object"&&t!==null}function Sh(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function Ph(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const VE="[object RegExp]",Oh="[object String]",Th="[object Number]",kh="[object Boolean]",xh="[object Arguments]",GE="[object Symbol]",JE="[object Date]",YE="[object Map]",XE="[object Set]",ZE="[object Array]",QE="[object ArrayBuffer]",eC="[object Object]",tC="[object DataView]",sC="[object Uint8Array]",rC="[object Uint8ClampedArray]",iC="[object Uint16Array]",nC="[object Uint32Array]",oC="[object Int8Array]",aC="[object Int16Array]",cC="[object Int32Array]",lC="[object Float32Array]",uC="[object Float64Array]";function hC(t,e){return Tr(t,void 0,t,new Map,e)}function Tr(t,e,s,r=new Map,i=void 0){const n=i==null?void 0:i(t,e,s,r);if(n!=null)return n;if(Na(t))return t;if(r.has(t))return r.get(t);if(Array.isArray(t)){const o=new Array(t.length);r.set(t,o);for(let a=0;a<t.length;a++)o[a]=Tr(t[a],a,s,r,i);return Object.hasOwn(t,"index")&&(o.index=t.index),Object.hasOwn(t,"input")&&(o.input=t.input),o}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){const o=new RegExp(t.source,t.flags);return o.lastIndex=t.lastIndex,o}if(t instanceof Map){const o=new Map;r.set(t,o);for(const[a,c]of t)o.set(a,Tr(c,a,s,r,i));return o}if(t instanceof Set){const o=new Set;r.set(t,o);for(const a of t)o.add(Tr(a,void 0,s,r,i));return o}if(typeof Buffer<"u"&&Buffer.isBuffer(t))return t.subarray();if(_a(t)){const o=new(Object.getPrototypeOf(t)).constructor(t.length);r.set(t,o);for(let a=0;a<t.length;a++)o[a]=Tr(t[a],a,s,r,i);return o}if(t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){const o=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return r.set(t,o),qs(o,t,s,r,i),o}if(typeof File<"u"&&t instanceof File){const o=new File([t],t.name,{type:t.type});return r.set(t,o),qs(o,t,s,r,i),o}if(t instanceof Blob){const o=new Blob([t],{type:t.type});return r.set(t,o),qs(o,t,s,r,i),o}if(t instanceof Error){const o=new t.constructor;return r.set(t,o),o.message=t.message,o.name=t.name,o.stack=t.stack,o.cause=t.cause,qs(o,t,s,r,i),o}if(typeof t=="object"&&dC(t)){const o=Object.create(Object.getPrototypeOf(t));return r.set(t,o),qs(o,t,s,r,i),o}return t}function qs(t,e,s=t,r,i){const n=[...Object.keys(e),...Sh(e)];for(let o=0;o<n.length;o++){const a=n[o],c=Object.getOwnPropertyDescriptor(t,a);(c==null||c.writable)&&(t[a]=Tr(e[a],a,s,r,i))}}function dC(t){switch(Ph(t)){case xh:case ZE:case QE:case tC:case kh:case JE:case lC:case uC:case oC:case aC:case cC:case YE:case Th:case eC:case VE:case XE:case Oh:case GE:case sC:case rC:case iC:case nC:return!0;default:return!1}}function pC(t,e){return hC(t,(s,r,i,n)=>{if(typeof t=="object")switch(Object.prototype.toString.call(t)){case Th:case Oh:case kh:{const o=new t.constructor(t==null?void 0:t.valueOf());return qs(o,t),o}case xh:{const o={};return qs(o,t),o.length=t.length,o[Symbol.iterator]=t[Symbol.iterator],o}default:return}})}function Nl(t){return pC(t)}function _l(t){return t!==null&&typeof t=="object"&&Ph(t)==="[object Arguments]"}function fC(t){return _a(t)}function gC(t){var s;if(typeof t!="object"||t==null)return!1;if(Object.getPrototypeOf(t)===null)return!0;if(Object.prototype.toString.call(t)!=="[object Object]"){const r=t[Symbol.toStringTag];return r==null||!((s=Object.getOwnPropertyDescriptor(t,Symbol.toStringTag))!=null&&s.writable)?!1:t.toString()===`[object ${r}]`}let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function mC(t,...e){const s=e.slice(0,-1),r=e[e.length-1];let i=t;for(let n=0;n<s.length;n++){const o=s[n];i=na(i,o,r,new Map)}return i}function na(t,e,s,r){if(Na(t)&&(t=Object(t)),e==null||typeof e!="object")return t;if(r.has(e))return KE(r.get(e));if(r.set(e,t),Array.isArray(e)){e=e.slice();for(let n=0;n<e.length;n++)e[n]=e[n]??void 0}const i=[...Object.keys(e),...Sh(e)];for(let n=0;n<i.length;n++){const o=i[n];let a=e[o],c=t[o];if(_l(a)&&(a={...a}),_l(c)&&(c={...c}),typeof Buffer<"u"&&Buffer.isBuffer(a)&&(a=Nl(a)),Array.isArray(a))if(typeof c=="object"&&c!=null){const u=[],h=Reflect.ownKeys(c);for(let d=0;d<h.length;d++){const p=h[d];u[p]=c[p]}c=u}else c=[];const l=s(c,a,o,t,e,r);l!=null?t[o]=l:Array.isArray(a)||Al(c)&&Al(a)?t[o]=na(c,a,s,r):c==null&&gC(a)?t[o]=na({},a,s,r):c==null&&fC(a)?t[o]=Nl(a):(c===void 0||a!==void 0)&&(t[o]=a)}return t}function wC(t,...e){return mC(t,...e,HE)}var yC=Object.defineProperty,bC=Object.defineProperties,vC=Object.getOwnPropertyDescriptors,Sl=Object.getOwnPropertySymbols,EC=Object.prototype.hasOwnProperty,CC=Object.prototype.propertyIsEnumerable,Pl=(t,e,s)=>e in t?yC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,cn=(t,e)=>{for(var s in e||(e={}))EC.call(e,s)&&Pl(t,s,e[s]);if(Sl)for(var s of Sl(e))CC.call(e,s)&&Pl(t,s,e[s]);return t},IC=(t,e)=>bC(t,vC(e));function vt(t,e,s){var r;const i=kr(t);return((r=e.rpcMap)==null?void 0:r[i.reference])||`${_h}?chainId=${i.namespace}:${i.reference}&projectId=${s}`}function nr(t){return t.includes(":")?t.split(":")[1]:t}function $h(t){return t.map(e=>`${e.split(":")[0]}:${e.split(":")[1]}`)}function AC(t,e){const s=Object.keys(e.namespaces).filter(i=>i.includes(t));if(!s.length)return[];const r=[];return s.forEach(i=>{const n=e.namespaces[i].accounts;r.push(...n)}),r}function ln(t={},e={}){const s=Ol(t),r=Ol(e);return wC(s,r)}function Ol(t){var e,s,r,i,n;const o={};if(!_s(t))return o;for(const[a,c]of Object.entries(t)){const l=qn(a)?[a]:c.chains,u=c.methods||[],h=c.events||[],d=c.rpcMap||{},p=Or(a);o[p]=IC(cn(cn({},o[p]),c),{chains:Jt(l,(e=o[p])==null?void 0:e.chains),methods:Jt(u,(s=o[p])==null?void 0:s.methods),events:Jt(h,(r=o[p])==null?void 0:r.events)}),(_s(d)||_s(((i=o[p])==null?void 0:i.rpcMap)||{}))&&(o[p].rpcMap=cn(cn({},d),(n=o[p])==null?void 0:n.rpcMap))}return o}function Tl(t){return t.includes(":")?t.split(":")[2]:t}function kl(t){const e={};for(const[s,r]of Object.entries(t)){const i=r.methods||[],n=r.events||[],o=r.accounts||[],a=qn(s)?[s]:r.chains?r.chains:$h(r.accounts);e[s]={chains:a,methods:i,events:n,accounts:o}}return e}function yo(t){return typeof t=="number"?t:t.includes("0x")?parseInt(t,16):(t=t.includes(":")?t.split(":")[1]:t,isNaN(Number(t))?t:Number(t))}const Rh={},ue=t=>Rh[t],bo=(t,e)=>{Rh[t]=e};var NC=Object.defineProperty,_C=(t,e,s)=>e in t?NC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,dr=(t,e,s)=>_C(t,typeof e!="symbol"?e+"":e,s);class SC{constructor(e){dr(this,"name","polkadot"),dr(this,"client"),dr(this,"httpProviders"),dr(this,"events"),dr(this,"namespace"),dr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=nr(s);e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var PC=Object.defineProperty,OC=Object.defineProperties,TC=Object.getOwnPropertyDescriptors,xl=Object.getOwnPropertySymbols,kC=Object.prototype.hasOwnProperty,xC=Object.prototype.propertyIsEnumerable,oa=(t,e,s)=>e in t?PC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,$l=(t,e)=>{for(var s in e||(e={}))kC.call(e,s)&&oa(t,s,e[s]);if(xl)for(var s of xl(e))xC.call(e,s)&&oa(t,s,e[s]);return t},Rl=(t,e)=>OC(t,TC(e)),pr=(t,e,s)=>oa(t,typeof e!="symbol"?e+"":e,s);class $C{constructor(e){pr(this,"name","eip155"),pr(this,"client"),pr(this,"chainId"),pr(this,"namespace"),pr(this,"httpProviders"),pr(this,"events"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.httpProviders=this.createHttpProviders(),this.chainId=parseInt(this.getDefaultChain())}async request(e){switch(e.request.method){case"eth_requestAccounts":return this.getAccounts();case"eth_accounts":return this.getAccounts();case"wallet_switchEthereumChain":return await this.handleSwitchChain(e);case"eth_chainId":return parseInt(this.getDefaultChain());case"wallet_getCapabilities":return await this.getCapabilities(e);case"wallet_getCallsStatus":return await this.getCallStatus(e)}return this.namespace.methods.includes(e.request.method)?await this.client.request(e):this.getHttpProvider().request(e.request)}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(parseInt(e),s),this.chainId=parseInt(e),this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId.toString();if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}createHttpProvider(e,s){const r=s||vt(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=parseInt(nr(s));e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}getHttpProvider(){const e=this.chainId,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}async handleSwitchChain(e){var s,r;let i=e.request.params?(s=e.request.params[0])==null?void 0:s.chainId:"0x0";i=i.startsWith("0x")?i:`0x${i}`;const n=parseInt(i,16);if(this.isChainApproved(n))this.setDefaultChain(`${n}`);else if(this.namespace.methods.includes("wallet_switchEthereumChain"))await this.client.request({topic:e.topic,request:{method:e.request.method,params:[{chainId:i}]},chainId:(r=this.namespace.chains)==null?void 0:r[0]}),this.setDefaultChain(`${n}`);else throw new Error(`Failed to switch to chain 'eip155:${n}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);return null}isChainApproved(e){return this.namespace.chains.includes(`${this.name}:${e}`)}async getCapabilities(e){var s,r,i,n,o;const a=(r=(s=e.request)==null?void 0:s.params)==null?void 0:r[0],c=((n=(i=e.request)==null?void 0:i.params)==null?void 0:n[1])||[],l=`${a}${c.join(",")}`;if(!a)throw new Error("Missing address parameter in `wallet_getCapabilities` request");const u=this.client.session.get(e.topic),h=((o=u==null?void 0:u.sessionProperties)==null?void 0:o.capabilities)||{};if(h!=null&&h[l])return h==null?void 0:h[l];const d=await this.client.request(e);try{await this.client.session.update(e.topic,{sessionProperties:Rl($l({},u.sessionProperties||{}),{capabilities:Rl($l({},h||{}),{[l]:d})})})}catch(p){console.warn("Failed to update session with capabilities",p)}return d}async getCallStatus(e){var s,r;const i=this.client.session.get(e.topic),n=(s=i.sessionProperties)==null?void 0:s.bundler_name;if(n){const a=this.getBundlerUrl(e.chainId,n);try{return await this.getUserOperationReceipt(a,e)}catch(c){console.warn("Failed to fetch call status from bundler",c,a)}}const o=(r=i.sessionProperties)==null?void 0:r.bundler_url;if(o)try{return await this.getUserOperationReceipt(o,e)}catch(a){console.warn("Failed to fetch call status from custom bundler",a,o)}if(this.namespace.methods.includes(e.request.method))return await this.client.request(e);throw new Error("Fetching call status not approved by the wallet.")}async getUserOperationReceipt(e,s){var r;const i=new URL(e),n=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Fs("eth_getUserOperationReceipt",[(r=s.request.params)==null?void 0:r[0]]))});if(!n.ok)throw new Error(`Failed to fetch user operation receipt - ${n.status}`);return await n.json()}getBundlerUrl(e,s){return`${WE}?projectId=${this.client.core.projectId}&chainId=${e}&bundler=${s}`}}var RC=Object.defineProperty,UC=(t,e,s)=>e in t?RC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,fr=(t,e,s)=>UC(t,typeof e!="symbol"?e+"":e,s);class DC{constructor(e){fr(this,"name","solana"),fr(this,"client"),fr(this,"httpProviders"),fr(this,"events"),fr(this,"namespace"),fr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=nr(s);e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var LC=Object.defineProperty,MC=(t,e,s)=>e in t?LC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,gr=(t,e,s)=>MC(t,typeof e!="symbol"?e+"":e,s);class BC{constructor(e){gr(this,"name","cosmos"),gr(this,"client"),gr(this,"httpProviders"),gr(this,"events"),gr(this,"namespace"),gr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=nr(s);e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var jC=Object.defineProperty,qC=(t,e,s)=>e in t?jC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,mr=(t,e,s)=>qC(t,typeof e!="symbol"?e+"":e,s);class FC{constructor(e){mr(this,"name","algorand"),mr(this,"client"),mr(this,"httpProviders"),mr(this,"events"),mr(this,"namespace"),mr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){if(!this.httpProviders[e]){const r=s||vt(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,r)}this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;e[s]=this.createHttpProvider(s,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);return typeof r>"u"?void 0:new Tt(new Bt(r,ue("disableProviderPing")))}}var zC=Object.defineProperty,WC=(t,e,s)=>e in t?zC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,wr=(t,e,s)=>WC(t,typeof e!="symbol"?e+"":e,s);class HC{constructor(e){wr(this,"name","cip34"),wr(this,"client"),wr(this,"httpProviders"),wr(this,"events"),wr(this,"namespace"),wr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{const r=this.getCardanoRPCUrl(s),i=nr(s);e[i]=this.createHttpProvider(i,r)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}getCardanoRPCUrl(e){const s=this.namespace.rpcMap;if(s)return s[e]}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||this.getCardanoRPCUrl(e);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var KC=Object.defineProperty,VC=(t,e,s)=>e in t?KC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,yr=(t,e,s)=>VC(t,typeof e!="symbol"?e+"":e,s);class GC{constructor(e){yr(this,"name","elrond"),yr(this,"client"),yr(this,"httpProviders"),yr(this,"events"),yr(this,"namespace"),yr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=nr(s);e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var JC=Object.defineProperty,YC=(t,e,s)=>e in t?JC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,br=(t,e,s)=>YC(t,typeof e!="symbol"?e+"":e,s);class XC{constructor(e){br(this,"name","multiversx"),br(this,"client"),br(this,"httpProviders"),br(this,"events"),br(this,"namespace"),br(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;const i=nr(s);e[i]=this.createHttpProvider(i,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var ZC=Object.defineProperty,QC=(t,e,s)=>e in t?ZC(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,vr=(t,e,s)=>QC(t,typeof e!="symbol"?e+"":e,s);class eI{constructor(e){vr(this,"name","near"),vr(this,"client"),vr(this,"httpProviders"),vr(this,"events"),vr(this,"namespace"),vr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){if(this.chainId=e,!this.httpProviders[e]){const r=s||vt(`${this.name}:${e}`,this.namespace);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,r)}this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{var r;e[s]=this.createHttpProvider(s,(r=this.namespace.rpcMap)==null?void 0:r[s])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace);return typeof r>"u"?void 0:new Tt(new Bt(r,ue("disableProviderPing")))}}var tI=Object.defineProperty,sI=(t,e,s)=>e in t?tI(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Er=(t,e,s)=>sI(t,typeof e!="symbol"?e+"":e,s);class rI{constructor(e){Er(this,"name","tezos"),Er(this,"client"),Er(this,"httpProviders"),Er(this,"events"),Er(this,"namespace"),Er(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,s){if(this.chainId=e,!this.httpProviders[e]){const r=s||vt(`${this.name}:${e}`,this.namespace);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,r)}this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(s=>{e[s]=this.createHttpProvider(s)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace);return typeof r>"u"?void 0:new Tt(new Bt(r))}}var iI=Object.defineProperty,nI=(t,e,s)=>e in t?iI(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Cr=(t,e,s)=>nI(t,typeof e!="symbol"?e+"":e,s);class oI{constructor(e){Cr(this,"name",Ar),Cr(this,"client"),Cr(this,"httpProviders"),Cr(this,"events"),Cr(this,"namespace"),Cr(this,"chainId"),this.namespace=e.namespace,this.events=ue("events"),this.client=ue("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace.chains=[...new Set((this.namespace.chains||[]).concat(e.chains||[]))],this.namespace.accounts=[...new Set((this.namespace.accounts||[]).concat(e.accounts||[]))],this.namespace.methods=[...new Set((this.namespace.methods||[]).concat(e.methods||[]))],this.namespace.events=[...new Set((this.namespace.events||[]).concat(e.events||[]))],this.httpProviders=this.createHttpProviders()}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider(e.chainId).request(e.request)}setDefaultChain(e,s){this.httpProviders[e]||this.setHttpProvider(e,s),this.chainId=e,this.events.emit(kt.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(s=>s.split(":")[1]===this.chainId.toString()).map(s=>s.split(":")[2]))]:[]}createHttpProviders(){var e,s;const r={};return(s=(e=this.namespace)==null?void 0:e.accounts)==null||s.forEach(i=>{const n=kr(i);r[`${n.namespace}:${n.reference}`]=this.createHttpProvider(i)}),r}getHttpProvider(e){const s=this.httpProviders[e];if(typeof s>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return s}setHttpProvider(e,s){const r=this.createHttpProvider(e,s);r&&(this.httpProviders[e]=r)}createHttpProvider(e,s){const r=s||vt(e,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${e}`);return new Tt(new Bt(r,ue("disableProviderPing")))}}var aI=Object.defineProperty,cI=Object.defineProperties,lI=Object.getOwnPropertyDescriptors,Ul=Object.getOwnPropertySymbols,uI=Object.prototype.hasOwnProperty,hI=Object.prototype.propertyIsEnumerable,aa=(t,e,s)=>e in t?aI(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,un=(t,e)=>{for(var s in e||(e={}))uI.call(e,s)&&aa(t,s,e[s]);if(Ul)for(var s of Ul(e))hI.call(e,s)&&aa(t,s,e[s]);return t},vo=(t,e)=>cI(t,lI(e)),_t=(t,e,s)=>aa(t,typeof e!="symbol"?e+"":e,s);let dI=class Uh{constructor(e){_t(this,"client"),_t(this,"namespaces"),_t(this,"optionalNamespaces"),_t(this,"sessionProperties"),_t(this,"scopedProperties"),_t(this,"events",new ua),_t(this,"rpcProviders",{}),_t(this,"session"),_t(this,"providerOpts"),_t(this,"logger"),_t(this,"uri"),_t(this,"disableProviderPing",!1),this.providerOpts=e,this.logger=typeof(e==null?void 0:e.logger)<"u"&&typeof(e==null?void 0:e.logger)!="string"?e.logger:da(Li({level:(e==null?void 0:e.logger)||Il})),this.disableProviderPing=(e==null?void 0:e.disableProviderPing)||!1}static async init(e){const s=new Uh(e);return await s.initialize(),s}async request(e,s,r){const[i,n]=this.validateChain(s);if(!this.session)throw new Error("Please call connect() before request()");return await this.getProvider(i).request({request:un({},e),chainId:`${i}:${n}`,topic:this.session.topic,expiry:r})}sendAsync(e,s,r,i){const n=new Date().getTime();this.request(e,r,i).then(o=>s(null,$n(n,o))).catch(o=>s(o,void 0))}async enable(){if(!this.client)throw new Error("Sign Client not initialized");return this.session||await this.connect({namespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties}),await this.requestAccounts()}async disconnect(){var e;if(!this.session)throw new Error("Please call connect() before enable()");await this.client.disconnect({topic:(e=this.session)==null?void 0:e.topic,reason:we("USER_DISCONNECTED")}),await this.cleanup()}async connect(e){if(!this.client)throw new Error("Sign Client not initialized");if(this.setNamespaces(e),await this.cleanupPendingPairings(),!e.skipPairing)return await this.pair(e.pairingTopic)}async authenticate(e,s){if(!this.client)throw new Error("Sign Client not initialized");this.setNamespaces(e),await this.cleanupPendingPairings();const{uri:r,response:i}=await this.client.authenticate(e,s);r&&(this.uri=r,this.events.emit("display_uri",r));const n=await i();if(this.session=n.session,this.session){const o=kl(this.session.namespaces);this.namespaces=ln(this.namespaces,o),await this.persist("namespaces",this.namespaces),this.onConnect()}return n}on(e,s){this.events.on(e,s)}once(e,s){this.events.once(e,s)}removeListener(e,s){this.events.removeListener(e,s)}off(e,s){this.events.off(e,s)}get isWalletConnect(){return!0}async pair(e){const{uri:s,approval:r}=await this.client.connect({pairingTopic:e,requiredNamespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties});s&&(this.uri=s,this.events.emit("display_uri",s));const i=await r();this.session=i;const n=kl(i.namespaces);return this.namespaces=ln(this.namespaces,n),await this.persist("namespaces",this.namespaces),await this.persist("optionalNamespaces",this.optionalNamespaces),this.onConnect(),this.session}setDefaultChain(e,s){try{if(!this.session)return;const[r,i]=this.validateChain(e),n=this.getProvider(r);n.name===Ar?n.setDefaultChain(`${r}:${i}`,s):n.setDefaultChain(i,s)}catch(r){if(!/Please call connect/.test(r.message))throw r}}async cleanupPendingPairings(e={}){this.logger.info("Cleaning up inactive pairings...");const s=this.client.pairing.getAll();if(Ns(s)){for(const r of s)e.deletePairings?this.client.core.expirer.set(r.topic,0):await this.client.core.relayer.subscriber.unsubscribe(r.topic);this.logger.info(`Inactive pairings cleared: ${s.length}`)}}abortPairingAttempt(){this.logger.warn("abortPairingAttempt is deprecated. This is now a no-op.")}async checkStorage(){this.namespaces=await this.getFromStore("namespaces")||{},this.optionalNamespaces=await this.getFromStore("optionalNamespaces")||{},this.session&&this.createProviders()}async initialize(){this.logger.trace("Initialized"),await this.createClient(),await this.checkStorage(),this.registerEventListeners()}async createClient(){var e,s;if(this.client=this.providerOpts.client||await jE.init({core:this.providerOpts.core,logger:this.providerOpts.logger||Il,relayUrl:this.providerOpts.relayUrl||qE,projectId:this.providerOpts.projectId,metadata:this.providerOpts.metadata,storageOptions:this.providerOpts.storageOptions,storage:this.providerOpts.storage,name:this.providerOpts.name,customStoragePrefix:this.providerOpts.customStoragePrefix,telemetryEnabled:this.providerOpts.telemetryEnabled}),this.providerOpts.session)try{this.session=this.client.session.get(this.providerOpts.session.topic)}catch(r){throw this.logger.error("Failed to get session",r),new Error(`The provided session: ${(s=(e=this.providerOpts)==null?void 0:e.session)==null?void 0:s.topic} doesn't exist in the Sign client`)}else{const r=this.client.session.getAll();this.session=r[0]}this.logger.trace("SignClient Initialized")}createProviders(){if(!this.client)throw new Error("Sign Client not initialized");if(!this.session)throw new Error("Session not initialized. Please call connect() before enable()");const e=[...new Set(Object.keys(this.session.namespaces).map(s=>Or(s)))];bo("client",this.client),bo("events",this.events),bo("disableProviderPing",this.disableProviderPing),e.forEach(s=>{if(!this.session)return;const r=AC(s,this.session),i=$h(r),n=ln(this.namespaces,this.optionalNamespaces),o=vo(un({},n[s]),{accounts:r,chains:i});switch(s){case"eip155":this.rpcProviders[s]=new $C({namespace:o});break;case"algorand":this.rpcProviders[s]=new FC({namespace:o});break;case"solana":this.rpcProviders[s]=new DC({namespace:o});break;case"cosmos":this.rpcProviders[s]=new BC({namespace:o});break;case"polkadot":this.rpcProviders[s]=new SC({namespace:o});break;case"cip34":this.rpcProviders[s]=new HC({namespace:o});break;case"elrond":this.rpcProviders[s]=new GC({namespace:o});break;case"multiversx":this.rpcProviders[s]=new XC({namespace:o});break;case"near":this.rpcProviders[s]=new eI({namespace:o});break;case"tezos":this.rpcProviders[s]=new rI({namespace:o});break;default:this.rpcProviders[Ar]?this.rpcProviders[Ar].updateNamespace(o):this.rpcProviders[Ar]=new oI({namespace:o})}})}registerEventListeners(){if(typeof this.client>"u")throw new Error("Sign Client is not initialized");this.client.on("session_ping",e=>{var s;const{topic:r}=e;r===((s=this.session)==null?void 0:s.topic)&&this.events.emit("session_ping",e)}),this.client.on("session_event",e=>{var s;const{params:r,topic:i}=e;if(i!==((s=this.session)==null?void 0:s.topic))return;const{event:n}=r;if(n.name==="accountsChanged"){const o=n.data;o&&Ns(o)&&this.events.emit("accountsChanged",o.map(Tl))}else if(n.name==="chainChanged"){const o=r.chainId,a=r.event.data,c=Or(o),l=yo(o)!==yo(a)?`${c}:${yo(a)}`:o;this.onChainChanged(l)}else this.events.emit(n.name,n.data);this.events.emit("session_event",e)}),this.client.on("session_update",({topic:e,params:s})=>{var r,i;if(e!==((r=this.session)==null?void 0:r.topic))return;const{namespaces:n}=s,o=(i=this.client)==null?void 0:i.session.get(e);this.session=vo(un({},o),{namespaces:n}),this.onSessionUpdate(),this.events.emit("session_update",{topic:e,params:s})}),this.client.on("session_delete",async e=>{var s;e.topic===((s=this.session)==null?void 0:s.topic)&&(await this.cleanup(),this.events.emit("session_delete",e),this.events.emit("disconnect",vo(un({},we("USER_DISCONNECTED")),{data:e.topic})))}),this.on(kt.DEFAULT_CHAIN_CHANGED,e=>{this.onChainChanged(e,!0)})}getProvider(e){return this.rpcProviders[e]||this.rpcProviders[Ar]}onSessionUpdate(){Object.keys(this.rpcProviders).forEach(e=>{var s;this.getProvider(e).updateNamespace((s=this.session)==null?void 0:s.namespaces[e])})}setNamespaces(e){const{namespaces:s={},optionalNamespaces:r={},sessionProperties:i,scopedProperties:n}=e;this.optionalNamespaces=ln(s,r),this.sessionProperties=i,this.scopedProperties=n}validateChain(e){const[s,r]=(e==null?void 0:e.split(":"))||["",""];if(!this.namespaces||!Object.keys(this.namespaces).length)return[s,r];if(s&&!Object.keys(this.namespaces||{}).map(o=>Or(o)).includes(s))throw new Error(`Namespace '${s}' is not configured. Please call connect() first with namespace config.`);if(s&&r)return[s,r];const i=Or(Object.keys(this.namespaces)[0]),n=this.rpcProviders[i].getDefaultChain();return[i,n]}async requestAccounts(){const[e]=this.validateChain();return await this.getProvider(e).requestAccounts()}async onChainChanged(e,s=!1){if(!this.namespaces)return;const[r,i]=this.validateChain(e);if(!i)return;this.updateNamespaceChain(r,i),this.events.emit("chainChanged",i);const n=this.getProvider(r).getDefaultChain();s||this.getProvider(r).setDefaultChain(i),this.emitAccountsChangedOnChainChange({namespace:r,previousChainId:n,newChainId:e}),await this.persist("namespaces",this.namespaces)}emitAccountsChangedOnChainChange({namespace:e,previousChainId:s,newChainId:r}){var i,n;try{if(s===r)return;const o=(n=(i=this.session)==null?void 0:i.namespaces[e])==null?void 0:n.accounts;if(!o)return;const a=o.filter(c=>c.includes(`${r}:`)).map(Tl);if(!Ns(a))return;this.events.emit("accountsChanged",a)}catch(o){this.logger.warn("Failed to emit accountsChanged on chain change",o)}}updateNamespaceChain(e,s){if(!this.namespaces)return;const r=this.namespaces[e]?e:`${e}:${s}`,i={chains:[],methods:[],events:[],defaultChain:s};this.namespaces[r]?this.namespaces[r]&&(this.namespaces[r].defaultChain=s):this.namespaces[r]=i}onConnect(){this.createProviders(),this.events.emit("connect",{session:this.session})}async cleanup(){this.namespaces=void 0,this.optionalNamespaces=void 0,this.sessionProperties=void 0,await this.deleteFromStore("namespaces"),await this.deleteFromStore("optionalNamespaces"),await this.deleteFromStore("sessionProperties"),this.session=void 0,await this.cleanupPendingPairings({deletePairings:!0}),await this.cleanupStorage()}async persist(e,s){var r;const i=((r=this.session)==null?void 0:r.topic)||"";await this.client.core.storage.setItem(`${an}/${e}${i}`,s)}async getFromStore(e){var s;const r=((s=this.session)==null?void 0:s.topic)||"";return await this.client.core.storage.getItem(`${an}/${e}${r}`)}async deleteFromStore(e){var s;const r=((s=this.session)==null?void 0:s.topic)||"";await this.client.core.storage.removeItem(`${an}/${e}${r}`)}async cleanupStorage(){var e;try{if(((e=this.client)==null?void 0:e.session.length)>0)return;const s=await this.client.core.storage.getKeys();for(const r of s)r.startsWith(an)&&await this.client.core.storage.removeItem(r)}catch(s){this.logger.warn("Failed to cleanup storage",s)}}};function hn(t,e){return j.getConnectorId(t)===e}function pI(t){const e=Array.from(g.state.chains.keys());let s=[];return t?(s.push([t,g.state.chains.get(t)]),hn(t,W.CONNECTOR_ID.WALLET_CONNECT)?e.forEach(r=>{r!==t&&hn(r,W.CONNECTOR_ID.WALLET_CONNECT)&&s.push([r,g.state.chains.get(r)])}):hn(t,W.CONNECTOR_ID.AUTH)&&e.forEach(r=>{r!==t&&hn(r,W.CONNECTOR_ID.AUTH)&&s.push([r,g.state.chains.get(r)])})):s=Array.from(g.state.chains.entries()),s}const ms={EIP155:"eip155",CONNECTOR_TYPE_WALLET_CONNECT:"WALLET_CONNECT",CONNECTOR_TYPE_INJECTED:"INJECTED",CONNECTOR_TYPE_ANNOUNCED:"ANNOUNCED"},Tn={NetworkImageIds:{1:"ba0ba0cd-17c6-4806-ad93-f9d174f17900",42161:"3bff954d-5cb0-47a0-9a23-d20192e74600",43114:"30c46e53-e989-45fb-4549-be3bd4eb3b00",56:"93564157-2e8e-4ce7-81df-b264dbee9b00",250:"06b26297-fe0c-4733-5d6b-ffa5498aac00",10:"ab9c186a-c52f-464b-2906-ca59d760a400",137:"41d04d42-da3b-4453-8506-668cc0727900",5e3:"e86fae9b-b770-4eea-e520-150e12c81100",295:"6a97d510-cac8-4e58-c7ce-e8681b044c00",11155111:"e909ea0a-f92a-4512-c8fc-748044ea6800",84532:"a18a7ecd-e307-4360-4746-283182228e00",1301:"4eeea7ef-0014-4649-5d1d-07271a80f600",130:"2257980a-3463-48c6-cbac-a42d2a956e00",10143:"0a728e83-bacb-46db-7844-948f05434900",100:"02b53f6a-e3d4-479e-1cb4-21178987d100",9001:"f926ff41-260d-4028-635e-91913fc28e00",324:"b310f07f-4ef7-49f3-7073-2a0a39685800",314:"5a73b3dd-af74-424e-cae0-0de859ee9400",4689:"34e68754-e536-40da-c153-6ef2e7188a00",1088:"3897a66d-40b9-4833-162f-a2c90531c900",1284:"161038da-44ae-4ec7-1208-0ea569454b00",1285:"f1d73bb6-5450-4e18-38f7-fb6484264a00",7777777:"845c60df-d429-4991-e687-91ae45791600",42220:"ab781bbc-ccc6-418d-d32d-789b15da1f00",8453:"7289c336-3981-4081-c5f4-efc26ac64a00",1313161554:"3ff73439-a619-4894-9262-4470c773a100",2020:"b8101fc0-9c19-4b6f-ec65-f6dfff106e00",2021:"b8101fc0-9c19-4b6f-ec65-f6dfff106e00",80094:"e329c2c9-59b0-4a02-83e4-212ff3779900",2741:"fc2427d1-5af9-4a9c-8da5-6f94627cd900","5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp":"a1b58899-f671-4276-6a5e-56ca5bd59700","4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z":"a1b58899-f671-4276-6a5e-56ca5bd59700",EtWTRABZaYq6iMfeYKouRu166VU2xqa1:"a1b58899-f671-4276-6a5e-56ca5bd59700","000000000019d6689c085ae165831e93":"0b4838db-0161-4ffe-022d-532bf03dba00","000000000933ea01ad0ee984209779ba":"39354064-d79b-420b-065d-f980c4b78200"},ConnectorImageIds:{[W.CONNECTOR_ID.COINBASE]:"0c2840c3-5b04-4c44-9661-fbd4b49e1800",[W.CONNECTOR_ID.COINBASE_SDK]:"0c2840c3-5b04-4c44-9661-fbd4b49e1800",[W.CONNECTOR_ID.SAFE]:"461db637-8616-43ce-035a-d89b8a1d5800",[W.CONNECTOR_ID.LEDGER]:"54a1aa77-d202-4f8d-0fb2-5d2bb6db0300",[W.CONNECTOR_ID.WALLET_CONNECT]:"ef1a1fcf-7fe8-4d69-bd6d-fda1345b4400",[W.CONNECTOR_ID.INJECTED]:"07ba87ed-43aa-4adf-4540-9e6a2b9cae00"},ConnectorNamesMap:{[W.CONNECTOR_ID.INJECTED]:"Browser Wallet",[W.CONNECTOR_ID.WALLET_CONNECT]:"WalletConnect",[W.CONNECTOR_ID.COINBASE]:"Coinbase",[W.CONNECTOR_ID.COINBASE_SDK]:"Coinbase",[W.CONNECTOR_ID.LEDGER]:"Ledger",[W.CONNECTOR_ID.SAFE]:"Safe"}},Sa={getCaipTokens(t){if(!t)return;const e={};return Object.entries(t).forEach(([s,r])=>{e[`${ms.EIP155}:${s}`]=r}),e},isLowerCaseMatch(t,e){return(t==null?void 0:t.toLowerCase())===(e==null?void 0:e.toLowerCase())}};new AbortController;const Ir={UniversalProviderErrors:{UNAUTHORIZED_DOMAIN_NOT_ALLOWED:{message:"Unauthorized: origin not allowed",alertErrorKey:"INVALID_APP_CONFIGURATION"},JWT_VALIDATION_ERROR:{message:"JWT validation error: JWT Token is not yet valid",alertErrorKey:"JWT_TOKEN_NOT_VALID"},INVALID_KEY:{message:"Unauthorized: invalid key",alertErrorKey:"INVALID_PROJECT_ID"}},ALERT_ERRORS:{SWITCH_NETWORK_NOT_FOUND:{shortMessage:"Network Not Found",longMessage:"Network not found - please make sure it is included in 'networks' array in createAppKit function"},INVALID_APP_CONFIGURATION:{shortMessage:"Invalid App Configuration",longMessage:()=>`Origin ${fI()?window.origin:"unknown"} not found on Allowlist - update configuration on cloud.reown.com`},IFRAME_LOAD_FAILED:{shortMessage:"Network Error - Could not load embedded wallet",longMessage:()=>"There was an issue loading the embedded wallet. Please try again later."},IFRAME_REQUEST_TIMEOUT:{shortMessage:"Embedded Wallet Request Timed Out",longMessage:()=>"There was an issue doing the request to the embedded wallet. Please try again later."},UNVERIFIED_DOMAIN:{shortMessage:"Invalid App Configuration",longMessage:()=>"There was an issue loading the embedded wallet. Please verify that your domain is allowed at cloud.reown.com"},JWT_TOKEN_NOT_VALID:{shortMessage:"Session Expired",longMessage:"Invalid session found on UniversalProvider - please check your time settings and connect again"},INVALID_PROJECT_ID:{shortMessage:"Invalid App Configuration",longMessage:"Invalid Project ID - update configuration"},PROJECT_ID_NOT_CONFIGURED:{shortMessage:"Project ID Not Configured",longMessage:"Project ID Not Configured - update configuration on cloud.reown.com"}}};function fI(){return typeof window<"u"}const gI={createLogger(t,e="error"){const s=Li({level:e}),{logger:r}=Ql({opts:s});return r.error=(...i)=>{for(const n of i)if(n instanceof Error){t(n,...i);return}t(void 0,...i)},r}},mI="rpc.walletconnect.org";function Dl(t,e){const s=new URL("https://rpc.walletconnect.org/v1/");return s.searchParams.set("chainId",t),s.searchParams.set("projectId",e),s.toString()}const Eo=["near:mainnet","solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp","eip155:1101","eip155:56","eip155:42161","eip155:7777777","eip155:59144","eip155:324","solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1","eip155:5000","solana:4sgjmw1sunhzsxgspuhpqldx6wiyjntz","eip155:80084","eip155:5003","eip155:100","eip155:8453","eip155:42220","eip155:1313161555","eip155:17000","eip155:1","eip155:300","eip155:1313161554","eip155:1329","eip155:84532","eip155:421614","eip155:11155111","eip155:8217","eip155:43114","solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z","eip155:999999999","eip155:11155420","eip155:80002","eip155:97","eip155:43113","eip155:137","eip155:10","eip155:1301","bip122:000000000019d6689c085ae165831e93","bip122:000000000933ea01ad0ee984209779ba"],Nr={extendRpcUrlWithProjectId(t,e){let s=!1;try{s=new URL(t).host===mI}catch{s=!1}if(s){const r=new URL(t);return r.searchParams.has("projectId")||r.searchParams.set("projectId",e),r.toString()}return t},isCaipNetwork(t){return"chainNamespace"in t&&"caipNetworkId"in t},getChainNamespace(t){return this.isCaipNetwork(t)?t.chainNamespace:W.CHAIN.EVM},getCaipNetworkId(t){return this.isCaipNetwork(t)?t.caipNetworkId:`${W.CHAIN.EVM}:${t.id}`},getDefaultRpcUrl(t,e,s){var i,n,o;const r=(o=(n=(i=t.rpcUrls)==null?void 0:i.default)==null?void 0:n.http)==null?void 0:o[0];return Eo.includes(e)?Dl(e,s):r||""},extendCaipNetwork(t,{customNetworkImageUrls:e,projectId:s,customRpcUrls:r}){var d,p,w,f,m;const i=this.getChainNamespace(t),n=this.getCaipNetworkId(t),o=(d=t.rpcUrls.default.http)==null?void 0:d[0],a=this.getDefaultRpcUrl(t,n,s),c=((f=(w=(p=t==null?void 0:t.rpcUrls)==null?void 0:p.chainDefault)==null?void 0:w.http)==null?void 0:f[0])||o,l=((m=r==null?void 0:r[n])==null?void 0:m.map(y=>y.url))||[],u=[...l,a],h=[...l];return c&&!h.includes(c)&&h.push(c),{...t,chainNamespace:i,caipNetworkId:n,assets:{imageId:Tn.NetworkImageIds[t.id],imageUrl:e==null?void 0:e[t.id]},rpcUrls:{...t.rpcUrls,default:{http:u},chainDefault:{http:h}}}},extendCaipNetworks(t,{customNetworkImageUrls:e,projectId:s,customRpcUrls:r}){return t.map(i=>Nr.extendCaipNetwork(i,{customNetworkImageUrls:e,customRpcUrls:r,projectId:s}))},getViemTransport(t,e,s){var i,n,o;const r=[];return s==null||s.forEach(a=>{r.push(Yi(a.url,a.config))}),Eo.includes(t.caipNetworkId)&&r.push(Yi(Dl(t.caipNetworkId,e),{fetchOptions:{headers:{"Content-Type":"text/plain"}}})),(o=(n=(i=t==null?void 0:t.rpcUrls)==null?void 0:i.default)==null?void 0:n.http)==null||o.forEach(a=>{r.push(Yi(a))}),Ra(r)},extendWagmiTransports(t,e,s){if(Eo.includes(t.caipNetworkId)){const r=this.getDefaultRpcUrl(t,t.caipNetworkId,e);return Ra([s,Yi(r)])}return s},getUnsupportedNetwork(t){return{id:t.split(":")[1],caipNetworkId:t,name:W.UNSUPPORTED_NETWORK_NAME,chainNamespace:t.split(":")[0],nativeCurrency:{name:"",decimals:0,symbol:""},rpcUrls:{default:{http:[]}}}},getCaipNetworkFromStorage(t){var c;const e=F.getActiveCaipNetworkId(),s=g.getAllRequestedCaipNetworks(),r=Array.from(((c=g.state.chains)==null?void 0:c.keys())||[]),i=e==null?void 0:e.split(":")[0],n=i?r.includes(i):!1,o=s==null?void 0:s.find(l=>l.caipNetworkId===e);return n&&!o&&e?this.getUnsupportedNetwork(e):o||t||(s==null?void 0:s[0])}},kn={eip155:void 0,solana:void 0,polkadot:void 0,bip122:void 0,cosmos:void 0},ct=_e({providers:{...kn},providerIds:{...kn}}),Se={state:ct,subscribeKey(t,e){return et(ct,t,e)},subscribe(t){return Qe(ct,()=>{t(ct)})},subscribeProviders(t){return Qe(ct.providers,()=>t(ct.providers))},setProvider(t,e){e&&(ct.providers[t]=Gs(e))},getProvider(t){return ct.providers[t]},setProviderId(t,e){e&&(ct.providerIds[t]=e)},getProviderId(t){if(t)return ct.providerIds[t]},reset(){ct.providers={...kn},ct.providerIds={...kn}},resetChain(t){ct.providers[t]=void 0,ct.providerIds[t]=void 0}},wI={VIEW_DIRECTION:{Next:"next",Prev:"prev"},DEFAULT_CONNECT_METHOD_ORDER:["email","social","wallet"],ANIMATION_DURATIONS:{HeaderText:120,ModalHeight:150,ViewTransition:150}},ca={filterOutDuplicatesByRDNS(t){const e=T.state.enableEIP6963?j.state.connectors:[],s=F.getRecentWallets(),r=e.map(a=>{var c;return(c=a.info)==null?void 0:c.rdns}).filter(Boolean),i=s.map(a=>a.rdns).filter(Boolean),n=r.concat(i);if(n.includes("io.metamask.mobile")&&X.isMobile()){const a=n.indexOf("io.metamask.mobile");n[a]="io.metamask"}return t.filter(a=>!n.includes(String(a==null?void 0:a.rdns)))},filterOutDuplicatesByIds(t){const e=j.state.connectors.filter(a=>a.type==="ANNOUNCED"||a.type==="INJECTED"),s=F.getRecentWallets(),r=e.map(a=>a.explorerId),i=s.map(a=>a.id),n=r.concat(i);return t.filter(a=>!n.includes(a==null?void 0:a.id))},filterOutDuplicateWallets(t){const e=this.filterOutDuplicatesByRDNS(t);return this.filterOutDuplicatesByIds(e)},markWalletsAsInstalled(t){const{connectors:e}=j.state,{featuredWalletIds:s}=T.state,r=e.filter(o=>o.type==="ANNOUNCED").reduce((o,a)=>{var c;return(c=a.info)!=null&&c.rdns&&(o[a.info.rdns]=!0),o},{});return t.map(o=>({...o,installed:!!o.rdns&&!!r[o.rdns??""]})).sort((o,a)=>{const c=Number(a.installed)-Number(o.installed);if(c!==0)return c;if(s!=null&&s.length){const l=s.indexOf(o.id),u=s.indexOf(a.id);if(l!==-1&&u!==-1)return l-u;if(l!==-1)return-1;if(u!==-1)return 1}return 0})},getConnectOrderMethod(t,e){var c;const s=(t==null?void 0:t.connectMethodsOrder)||((c=T.state.features)==null?void 0:c.connectMethodsOrder),r=e||j.state.connectors;if(s)return s;const{injected:i,announced:n}=bn.getConnectorsByType(r,K.state.recommended,K.state.featured),o=i.filter(bn.showConnector),a=n.filter(bn.showConnector);return o.length||a.length?["wallet","email","social"]:wI.DEFAULT_CONNECT_METHOD_ORDER},isExcluded(t){const e=!!t.rdns&&K.state.excludedWallets.some(r=>r.rdns===t.rdns),s=!!t.name&&K.state.excludedWallets.some(r=>Sa.isLowerCaseMatch(r.name,t.name));return e||s}},bn={getConnectorsByType(t,e,s){const{customWallets:r}=T.state,i=F.getRecentWallets(),n=ca.filterOutDuplicateWallets(e),o=ca.filterOutDuplicateWallets(s),a=t.filter(h=>h.type==="MULTI_CHAIN"),c=t.filter(h=>h.type==="ANNOUNCED"),l=t.filter(h=>h.type==="INJECTED"),u=t.filter(h=>h.type==="EXTERNAL");return{custom:r,recent:i,external:u,multiChain:a,announced:c,injected:l,recommended:n,featured:o}},showConnector(t){var i;const e=(i=t.info)==null?void 0:i.rdns,s=!!e&&K.state.excludedWallets.some(n=>!!n.rdns&&n.rdns===e),r=!!t.name&&K.state.excludedWallets.some(n=>Sa.isLowerCaseMatch(n.name,t.name));return!(t.type==="INJECTED"&&(t.name==="Browser Wallet"&&(!X.isMobile()||X.isMobile()&&!e&&!Y.checkInstalled())||s||r)||(t.type==="ANNOUNCED"||t.type==="EXTERNAL")&&(s||r))},getIsConnectedWithWC(){return Array.from(g.state.chains.values()).some(s=>j.getConnectorId(s.namespace)===W.CONNECTOR_ID.WALLET_CONNECT)},getConnectorTypeOrder({recommended:t,featured:e,custom:s,recent:r,announced:i,injected:n,multiChain:o,external:a,overriddenConnectors:c=(l=>(l=T.state.features)==null?void 0:l.connectorTypeOrder)()??[]}){const u=bn.getIsConnectedWithWC(),p=[{type:"walletConnect",isEnabled:T.state.enableWalletConnect&&!u},{type:"recent",isEnabled:r.length>0},{type:"injected",isEnabled:[...n,...i,...o].length>0},{type:"featured",isEnabled:e.length>0},{type:"custom",isEnabled:s&&s.length>0},{type:"external",isEnabled:a.length>0},{type:"recommended",isEnabled:t.length>0}].filter(y=>y.isEnabled),w=new Set(p.map(y=>y.type)),f=c.filter(y=>w.has(y)).map(y=>({type:y,isEnabled:!0})),m=p.filter(({type:y})=>!f.some(({type:v})=>v===y));return Array.from(new Set([...f,...m].map(({type:y})=>y)))}};/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const vn=globalThis,Pa=vn.ShadowRoot&&(vn.ShadyCSS===void 0||vn.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,Oa=Symbol(),Ll=new WeakMap;let Dh=class{constructor(e,s,r){if(this._$cssResult$=!0,r!==Oa)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=e,this.t=s}get styleSheet(){let e=this.o;const s=this.t;if(Pa&&e===void 0){const r=s!==void 0&&s.length===1;r&&(e=Ll.get(s)),e===void 0&&((this.o=e=new CSSStyleSheet).replaceSync(this.cssText),r&&Ll.set(s,e))}return e}toString(){return this.cssText}};const St=t=>new Dh(typeof t=="string"?t:t+"",void 0,Oa),xr=(t,...e)=>{const s=t.length===1?t[0]:e.reduce((r,i,n)=>r+(o=>{if(o._$cssResult$===!0)return o.cssText;if(typeof o=="number")return o;throw Error("Value passed to 'css' function must be a 'css' function result: "+o+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(i)+t[n+1],t[0]);return new Dh(s,t,Oa)},yI=(t,e)=>{if(Pa)t.adoptedStyleSheets=e.map(s=>s instanceof CSSStyleSheet?s:s.styleSheet);else for(const s of e){const r=document.createElement("style"),i=vn.litNonce;i!==void 0&&r.setAttribute("nonce",i),r.textContent=s.cssText,t.appendChild(r)}},Ml=Pa?t=>t:t=>t instanceof CSSStyleSheet?(e=>{let s="";for(const r of e.cssRules)s+=r.cssText;return St(s)})(t):t;/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{is:bI,defineProperty:vI,getOwnPropertyDescriptor:EI,getOwnPropertyNames:CI,getOwnPropertySymbols:II,getPrototypeOf:AI}=Object,Ss=globalThis,Bl=Ss.trustedTypes,NI=Bl?Bl.emptyScript:"",Co=Ss.reactiveElementPolyfillSupport,Ai=(t,e)=>t,la={toAttribute(t,e){switch(e){case Boolean:t=t?NI:null;break;case Object:case Array:t=t==null?t:JSON.stringify(t)}return t},fromAttribute(t,e){let s=t;switch(e){case Boolean:s=t!==null;break;case Number:s=t===null?null:Number(t);break;case Object:case Array:try{s=JSON.parse(t)}catch{s=null}}return s}},Lh=(t,e)=>!bI(t,e),jl={attribute:!0,type:String,converter:la,reflect:!1,useDefault:!1,hasChanged:Lh};Symbol.metadata??(Symbol.metadata=Symbol("metadata")),Ss.litPropertyMetadata??(Ss.litPropertyMetadata=new WeakMap);let _r=class extends HTMLElement{static addInitializer(e){this._$Ei(),(this.l??(this.l=[])).push(e)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(e,s=jl){if(s.state&&(s.attribute=!1),this._$Ei(),this.prototype.hasOwnProperty(e)&&((s=Object.create(s)).wrapped=!0),this.elementProperties.set(e,s),!s.noAccessor){const r=Symbol(),i=this.getPropertyDescriptor(e,r,s);i!==void 0&&vI(this.prototype,e,i)}}static getPropertyDescriptor(e,s,r){const{get:i,set:n}=EI(this.prototype,e)??{get(){return this[s]},set(o){this[s]=o}};return{get:i,set(o){const a=i==null?void 0:i.call(this);n==null||n.call(this,o),this.requestUpdate(e,a,r)},configurable:!0,enumerable:!0}}static getPropertyOptions(e){return this.elementProperties.get(e)??jl}static _$Ei(){if(this.hasOwnProperty(Ai("elementProperties")))return;const e=AI(this);e.finalize(),e.l!==void 0&&(this.l=[...e.l]),this.elementProperties=new Map(e.elementProperties)}static finalize(){if(this.hasOwnProperty(Ai("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(Ai("properties"))){const s=this.properties,r=[...CI(s),...II(s)];for(const i of r)this.createProperty(i,s[i])}const e=this[Symbol.metadata];if(e!==null){const s=litPropertyMetadata.get(e);if(s!==void 0)for(const[r,i]of s)this.elementProperties.set(r,i)}this._$Eh=new Map;for(const[s,r]of this.elementProperties){const i=this._$Eu(s,r);i!==void 0&&this._$Eh.set(i,s)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(e){const s=[];if(Array.isArray(e)){const r=new Set(e.flat(1/0).reverse());for(const i of r)s.unshift(Ml(i))}else e!==void 0&&s.push(Ml(e));return s}static _$Eu(e,s){const r=s.attribute;return r===!1?void 0:typeof r=="string"?r:typeof e=="string"?e.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){var e;this._$ES=new Promise(s=>this.enableUpdating=s),this._$AL=new Map,this._$E_(),this.requestUpdate(),(e=this.constructor.l)==null||e.forEach(s=>s(this))}addController(e){var s;(this._$EO??(this._$EO=new Set)).add(e),this.renderRoot!==void 0&&this.isConnected&&((s=e.hostConnected)==null||s.call(e))}removeController(e){var s;(s=this._$EO)==null||s.delete(e)}_$E_(){const e=new Map,s=this.constructor.elementProperties;for(const r of s.keys())this.hasOwnProperty(r)&&(e.set(r,this[r]),delete this[r]);e.size>0&&(this._$Ep=e)}createRenderRoot(){const e=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return yI(e,this.constructor.elementStyles),e}connectedCallback(){var e;this.renderRoot??(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),(e=this._$EO)==null||e.forEach(s=>{var r;return(r=s.hostConnected)==null?void 0:r.call(s)})}enableUpdating(e){}disconnectedCallback(){var e;(e=this._$EO)==null||e.forEach(s=>{var r;return(r=s.hostDisconnected)==null?void 0:r.call(s)})}attributeChangedCallback(e,s,r){this._$AK(e,r)}_$ET(e,s){var n;const r=this.constructor.elementProperties.get(e),i=this.constructor._$Eu(e,r);if(i!==void 0&&r.reflect===!0){const o=(((n=r.converter)==null?void 0:n.toAttribute)!==void 0?r.converter:la).toAttribute(s,r.type);this._$Em=e,o==null?this.removeAttribute(i):this.setAttribute(i,o),this._$Em=null}}_$AK(e,s){var n,o;const r=this.constructor,i=r._$Eh.get(e);if(i!==void 0&&this._$Em!==i){const a=r.getPropertyOptions(i),c=typeof a.converter=="function"?{fromAttribute:a.converter}:((n=a.converter)==null?void 0:n.fromAttribute)!==void 0?a.converter:la;this._$Em=i;const l=c.fromAttribute(s,a.type);this[i]=l??((o=this._$Ej)==null?void 0:o.get(i))??l,this._$Em=null}}requestUpdate(e,s,r){var i;if(e!==void 0){const n=this.constructor,o=this[e];if(r??(r=n.getPropertyOptions(e)),!((r.hasChanged??Lh)(o,s)||r.useDefault&&r.reflect&&o===((i=this._$Ej)==null?void 0:i.get(e))&&!this.hasAttribute(n._$Eu(e,r))))return;this.C(e,s,r)}this.isUpdatePending===!1&&(this._$ES=this._$EP())}C(e,s,{useDefault:r,reflect:i,wrapped:n},o){r&&!(this._$Ej??(this._$Ej=new Map)).has(e)&&(this._$Ej.set(e,o??s??this[e]),n!==!0||o!==void 0)||(this._$AL.has(e)||(this.hasUpdated||r||(s=void 0),this._$AL.set(e,s)),i===!0&&this._$Em!==e&&(this._$Eq??(this._$Eq=new Set)).add(e))}async _$EP(){this.isUpdatePending=!0;try{await this._$ES}catch(s){Promise.reject(s)}const e=this.scheduleUpdate();return e!=null&&await e,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var r;if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??(this.renderRoot=this.createRenderRoot()),this._$Ep){for(const[n,o]of this._$Ep)this[n]=o;this._$Ep=void 0}const i=this.constructor.elementProperties;if(i.size>0)for(const[n,o]of i){const{wrapped:a}=o,c=this[n];a!==!0||this._$AL.has(n)||c===void 0||this.C(n,void 0,o,c)}}let e=!1;const s=this._$AL;try{e=this.shouldUpdate(s),e?(this.willUpdate(s),(r=this._$EO)==null||r.forEach(i=>{var n;return(n=i.hostUpdate)==null?void 0:n.call(i)}),this.update(s)):this._$EM()}catch(i){throw e=!1,this._$EM(),i}e&&this._$AE(s)}willUpdate(e){}_$AE(e){var s;(s=this._$EO)==null||s.forEach(r=>{var i;return(i=r.hostUpdated)==null?void 0:i.call(r)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(e)),this.updated(e)}_$EM(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(e){return!0}update(e){this._$Eq&&(this._$Eq=this._$Eq.forEach(s=>this._$ET(s,this[s]))),this._$EM()}updated(e){}firstUpdated(e){}};_r.elementStyles=[],_r.shadowRootOptions={mode:"open"},_r[Ai("elementProperties")]=new Map,_r[Ai("finalized")]=new Map,Co==null||Co({ReactiveElement:_r}),(Ss.reactiveElementVersions??(Ss.reactiveElementVersions=[])).push("2.1.1");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ni=globalThis,xn=Ni.trustedTypes,ql=xn?xn.createPolicy("lit-html",{createHTML:t=>t}):void 0,Mh="$lit$",vs=`lit$${Math.random().toFixed(9).slice(2)}$`,Bh="?"+vs,_I=`<${Bh}>`,Qs=document,Ri=()=>Qs.createComment(""),Ui=t=>t===null||typeof t!="object"&&typeof t!="function",Ta=Array.isArray,SI=t=>Ta(t)||typeof(t==null?void 0:t[Symbol.iterator])=="function",Io=`[ 	
\f\r]`,li=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Fl=/-->/g,zl=/>/g,Us=RegExp(`>|${Io}(?:([^\\s"'>=/]+)(${Io}*=${Io}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),Wl=/'/g,Hl=/"/g,jh=/^(?:script|style|textarea|title)$/i,qh=t=>(e,...s)=>({_$litType$:t,strings:e,values:s}),C1=qh(1),I1=qh(2),zr=Symbol.for("lit-noChange"),ze=Symbol.for("lit-nothing"),Kl=new WeakMap,Ws=Qs.createTreeWalker(Qs,129);function Fh(t,e){if(!Ta(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return ql!==void 0?ql.createHTML(e):e}const PI=(t,e)=>{const s=t.length-1,r=[];let i,n=e===2?"<svg>":e===3?"<math>":"",o=li;for(let a=0;a<s;a++){const c=t[a];let l,u,h=-1,d=0;for(;d<c.length&&(o.lastIndex=d,u=o.exec(c),u!==null);)d=o.lastIndex,o===li?u[1]==="!--"?o=Fl:u[1]!==void 0?o=zl:u[2]!==void 0?(jh.test(u[2])&&(i=RegExp("</"+u[2],"g")),o=Us):u[3]!==void 0&&(o=Us):o===Us?u[0]===">"?(o=i??li,h=-1):u[1]===void 0?h=-2:(h=o.lastIndex-u[2].length,l=u[1],o=u[3]===void 0?Us:u[3]==='"'?Hl:Wl):o===Hl||o===Wl?o=Us:o===Fl||o===zl?o=li:(o=Us,i=void 0);const p=o===Us&&t[a+1].startsWith("/>")?" ":"";n+=o===li?c+_I:h>=0?(r.push(l),c.slice(0,h)+Mh+c.slice(h)+vs+p):c+vs+(h===-2?a:p)}return[Fh(t,n+(t[s]||"<?>")+(e===2?"</svg>":e===3?"</math>":"")),r]};class Di{constructor({strings:e,_$litType$:s},r){let i;this.parts=[];let n=0,o=0;const a=e.length-1,c=this.parts,[l,u]=PI(e,s);if(this.el=Di.createElement(l,r),Ws.currentNode=this.el.content,s===2||s===3){const h=this.el.content.firstChild;h.replaceWith(...h.childNodes)}for(;(i=Ws.nextNode())!==null&&c.length<a;){if(i.nodeType===1){if(i.hasAttributes())for(const h of i.getAttributeNames())if(h.endsWith(Mh)){const d=u[o++],p=i.getAttribute(h).split(vs),w=/([.?@])?(.*)/.exec(d);c.push({type:1,index:n,name:w[2],strings:p,ctor:w[1]==="."?TI:w[1]==="?"?kI:w[1]==="@"?xI:Wn}),i.removeAttribute(h)}else h.startsWith(vs)&&(c.push({type:6,index:n}),i.removeAttribute(h));if(jh.test(i.tagName)){const h=i.textContent.split(vs),d=h.length-1;if(d>0){i.textContent=xn?xn.emptyScript:"";for(let p=0;p<d;p++)i.append(h[p],Ri()),Ws.nextNode(),c.push({type:2,index:++n});i.append(h[d],Ri())}}}else if(i.nodeType===8)if(i.data===Bh)c.push({type:2,index:n});else{let h=-1;for(;(h=i.data.indexOf(vs,h+1))!==-1;)c.push({type:7,index:n}),h+=vs.length-1}n++}}static createElement(e,s){const r=Qs.createElement("template");return r.innerHTML=e,r}}function Wr(t,e,s=t,r){var o,a;if(e===zr)return e;let i=r!==void 0?(o=s._$Co)==null?void 0:o[r]:s._$Cl;const n=Ui(e)?void 0:e._$litDirective$;return(i==null?void 0:i.constructor)!==n&&((a=i==null?void 0:i._$AO)==null||a.call(i,!1),n===void 0?i=void 0:(i=new n(t),i._$AT(t,s,r)),r!==void 0?(s._$Co??(s._$Co=[]))[r]=i:s._$Cl=i),i!==void 0&&(e=Wr(t,i._$AS(t,e.values),i,r)),e}class OI{constructor(e,s){this._$AV=[],this._$AN=void 0,this._$AD=e,this._$AM=s}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(e){const{el:{content:s},parts:r}=this._$AD,i=((e==null?void 0:e.creationScope)??Qs).importNode(s,!0);Ws.currentNode=i;let n=Ws.nextNode(),o=0,a=0,c=r[0];for(;c!==void 0;){if(o===c.index){let l;c.type===2?l=new Vi(n,n.nextSibling,this,e):c.type===1?l=new c.ctor(n,c.name,c.strings,this,e):c.type===6&&(l=new $I(n,this,e)),this._$AV.push(l),c=r[++a]}o!==(c==null?void 0:c.index)&&(n=Ws.nextNode(),o++)}return Ws.currentNode=Qs,i}p(e){let s=0;for(const r of this._$AV)r!==void 0&&(r.strings!==void 0?(r._$AI(e,r,s),s+=r.strings.length-2):r._$AI(e[s])),s++}}class Vi{get _$AU(){var e;return((e=this._$AM)==null?void 0:e._$AU)??this._$Cv}constructor(e,s,r,i){this.type=2,this._$AH=ze,this._$AN=void 0,this._$AA=e,this._$AB=s,this._$AM=r,this.options=i,this._$Cv=(i==null?void 0:i.isConnected)??!0}get parentNode(){let e=this._$AA.parentNode;const s=this._$AM;return s!==void 0&&(e==null?void 0:e.nodeType)===11&&(e=s.parentNode),e}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(e,s=this){e=Wr(this,e,s),Ui(e)?e===ze||e==null||e===""?(this._$AH!==ze&&this._$AR(),this._$AH=ze):e!==this._$AH&&e!==zr&&this._(e):e._$litType$!==void 0?this.$(e):e.nodeType!==void 0?this.T(e):SI(e)?this.k(e):this._(e)}O(e){return this._$AA.parentNode.insertBefore(e,this._$AB)}T(e){this._$AH!==e&&(this._$AR(),this._$AH=this.O(e))}_(e){this._$AH!==ze&&Ui(this._$AH)?this._$AA.nextSibling.data=e:this.T(Qs.createTextNode(e)),this._$AH=e}$(e){var n;const{values:s,_$litType$:r}=e,i=typeof r=="number"?this._$AC(e):(r.el===void 0&&(r.el=Di.createElement(Fh(r.h,r.h[0]),this.options)),r);if(((n=this._$AH)==null?void 0:n._$AD)===i)this._$AH.p(s);else{const o=new OI(i,this),a=o.u(this.options);o.p(s),this.T(a),this._$AH=o}}_$AC(e){let s=Kl.get(e.strings);return s===void 0&&Kl.set(e.strings,s=new Di(e)),s}k(e){Ta(this._$AH)||(this._$AH=[],this._$AR());const s=this._$AH;let r,i=0;for(const n of e)i===s.length?s.push(r=new Vi(this.O(Ri()),this.O(Ri()),this,this.options)):r=s[i],r._$AI(n),i++;i<s.length&&(this._$AR(r&&r._$AB.nextSibling,i),s.length=i)}_$AR(e=this._$AA.nextSibling,s){var r;for((r=this._$AP)==null?void 0:r.call(this,!1,!0,s);e!==this._$AB;){const i=e.nextSibling;e.remove(),e=i}}setConnected(e){var s;this._$AM===void 0&&(this._$Cv=e,(s=this._$AP)==null||s.call(this,e))}}class Wn{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(e,s,r,i,n){this.type=1,this._$AH=ze,this._$AN=void 0,this.element=e,this.name=s,this._$AM=i,this.options=n,r.length>2||r[0]!==""||r[1]!==""?(this._$AH=Array(r.length-1).fill(new String),this.strings=r):this._$AH=ze}_$AI(e,s=this,r,i){const n=this.strings;let o=!1;if(n===void 0)e=Wr(this,e,s,0),o=!Ui(e)||e!==this._$AH&&e!==zr,o&&(this._$AH=e);else{const a=e;let c,l;for(e=n[0],c=0;c<n.length-1;c++)l=Wr(this,a[r+c],s,c),l===zr&&(l=this._$AH[c]),o||(o=!Ui(l)||l!==this._$AH[c]),l===ze?e=ze:e!==ze&&(e+=(l??"")+n[c+1]),this._$AH[c]=l}o&&!i&&this.j(e)}j(e){e===ze?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,e??"")}}class TI extends Wn{constructor(){super(...arguments),this.type=3}j(e){this.element[this.name]=e===ze?void 0:e}}class kI extends Wn{constructor(){super(...arguments),this.type=4}j(e){this.element.toggleAttribute(this.name,!!e&&e!==ze)}}class xI extends Wn{constructor(e,s,r,i,n){super(e,s,r,i,n),this.type=5}_$AI(e,s=this){if((e=Wr(this,e,s,0)??ze)===zr)return;const r=this._$AH,i=e===ze&&r!==ze||e.capture!==r.capture||e.once!==r.once||e.passive!==r.passive,n=e!==ze&&(r===ze||i);i&&this.element.removeEventListener(this.name,this,r),n&&this.element.addEventListener(this.name,this,e),this._$AH=e}handleEvent(e){var s;typeof this._$AH=="function"?this._$AH.call(((s=this.options)==null?void 0:s.host)??this.element,e):this._$AH.handleEvent(e)}}class $I{constructor(e,s,r){this.element=e,this.type=6,this._$AN=void 0,this._$AM=s,this.options=r}get _$AU(){return this._$AM._$AU}_$AI(e){Wr(this,e)}}const Ao=Ni.litHtmlPolyfillSupport;Ao==null||Ao(Di,Vi),(Ni.litHtmlVersions??(Ni.litHtmlVersions=[])).push("3.3.1");const RI=(t,e,s)=>{const r=(s==null?void 0:s.renderBefore)??e;let i=r._$litPart$;if(i===void 0){const n=(s==null?void 0:s.renderBefore)??null;r._$litPart$=i=new Vi(e.insertBefore(Ri(),n),n,void 0,s??{})}return i._$AI(t),i};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ks=globalThis;class En extends _r{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var s;const e=super.createRenderRoot();return(s=this.renderOptions).renderBefore??(s.renderBefore=e.firstChild),e}update(e){const s=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(e),this._$Do=RI(s,this.renderRoot,this.renderOptions)}connectedCallback(){var e;super.connectedCallback(),(e=this._$Do)==null||e.setConnected(!0)}disconnectedCallback(){var e;super.disconnectedCallback(),(e=this._$Do)==null||e.setConnected(!1)}render(){return zr}}var Yl;En._$litElement$=!0,En.finalized=!0,(Yl=Ks.litElementHydrateSupport)==null||Yl.call(Ks,{LitElement:En});const No=Ks.litElementPolyfillSupport;No==null||No({LitElement:En});(Ks.litElementVersions??(Ks.litElementVersions=[])).push("4.2.1");let _i,Ps,Os;function A1(t,e){_i=document.createElement("style"),Ps=document.createElement("style"),Os=document.createElement("style"),_i.textContent=$r(t).core.cssText,Ps.textContent=$r(t).dark.cssText,Os.textContent=$r(t).light.cssText,document.head.appendChild(_i),document.head.appendChild(Ps),document.head.appendChild(Os),zh(e)}function zh(t){Ps&&Os&&(t==="light"?(Ps.removeAttribute("media"),Os.media="enabled"):(Os.removeAttribute("media"),Ps.media="enabled"))}function UI(t){_i&&Ps&&Os&&(_i.textContent=$r(t).core.cssText,Ps.textContent=$r(t).dark.cssText,Os.textContent=$r(t).light.cssText)}function $r(t){return{core:xr`
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
      @keyframes w3m-shake {
        0% {
          transform: scale(1) rotate(0deg);
        }
        20% {
          transform: scale(1) rotate(-1deg);
        }
        40% {
          transform: scale(1) rotate(1.5deg);
        }
        60% {
          transform: scale(1) rotate(-1.5deg);
        }
        80% {
          transform: scale(1) rotate(1deg);
        }
        100% {
          transform: scale(1) rotate(0deg);
        }
      }
      @keyframes w3m-iframe-fade-out {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
      @keyframes w3m-iframe-zoom-in {
        0% {
          transform: translateY(50px);
          opacity: 0;
        }
        100% {
          transform: translateY(0px);
          opacity: 1;
        }
      }
      @keyframes w3m-iframe-zoom-in-mobile {
        0% {
          transform: scale(0.95);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }
      :root {
        --w3m-modal-width: 360px;
        --w3m-color-mix-strength: ${St(t!=null&&t["--w3m-color-mix-strength"]?`${t["--w3m-color-mix-strength"]}%`:"0%")};
        --w3m-font-family: ${St((t==null?void 0:t["--w3m-font-family"])||"Inter, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;")};
        --w3m-font-size-master: ${St((t==null?void 0:t["--w3m-font-size-master"])||"10px")};
        --w3m-border-radius-master: ${St((t==null?void 0:t["--w3m-border-radius-master"])||"4px")};
        --w3m-z-index: ${St((t==null?void 0:t["--w3m-z-index"])||999)};

        --wui-font-family: var(--w3m-font-family);

        --wui-font-size-mini: calc(var(--w3m-font-size-master) * 0.8);
        --wui-font-size-micro: var(--w3m-font-size-master);
        --wui-font-size-tiny: calc(var(--w3m-font-size-master) * 1.2);
        --wui-font-size-small: calc(var(--w3m-font-size-master) * 1.4);
        --wui-font-size-paragraph: calc(var(--w3m-font-size-master) * 1.6);
        --wui-font-size-medium: calc(var(--w3m-font-size-master) * 1.8);
        --wui-font-size-large: calc(var(--w3m-font-size-master) * 2);
        --wui-font-size-title-6: calc(var(--w3m-font-size-master) * 2.2);
        --wui-font-size-medium-title: calc(var(--w3m-font-size-master) * 2.4);
        --wui-font-size-2xl: calc(var(--w3m-font-size-master) * 4);

        --wui-border-radius-5xs: var(--w3m-border-radius-master);
        --wui-border-radius-4xs: calc(var(--w3m-border-radius-master) * 1.5);
        --wui-border-radius-3xs: calc(var(--w3m-border-radius-master) * 2);
        --wui-border-radius-xxs: calc(var(--w3m-border-radius-master) * 3);
        --wui-border-radius-xs: calc(var(--w3m-border-radius-master) * 4);
        --wui-border-radius-s: calc(var(--w3m-border-radius-master) * 5);
        --wui-border-radius-m: calc(var(--w3m-border-radius-master) * 7);
        --wui-border-radius-l: calc(var(--w3m-border-radius-master) * 9);
        --wui-border-radius-3xl: calc(var(--w3m-border-radius-master) * 20);

        --wui-font-weight-light: 400;
        --wui-font-weight-regular: 500;
        --wui-font-weight-medium: 600;
        --wui-font-weight-bold: 700;

        --wui-letter-spacing-2xl: -1.6px;
        --wui-letter-spacing-medium-title: -0.96px;
        --wui-letter-spacing-title-6: -0.88px;
        --wui-letter-spacing-large: -0.8px;
        --wui-letter-spacing-medium: -0.72px;
        --wui-letter-spacing-paragraph: -0.64px;
        --wui-letter-spacing-small: -0.56px;
        --wui-letter-spacing-tiny: -0.48px;
        --wui-letter-spacing-micro: -0.2px;
        --wui-letter-spacing-mini: -0.16px;

        --wui-spacing-0: 0px;
        --wui-spacing-4xs: 2px;
        --wui-spacing-3xs: 4px;
        --wui-spacing-xxs: 6px;
        --wui-spacing-2xs: 7px;
        --wui-spacing-xs: 8px;
        --wui-spacing-1xs: 10px;
        --wui-spacing-s: 12px;
        --wui-spacing-m: 14px;
        --wui-spacing-l: 16px;
        --wui-spacing-2l: 18px;
        --wui-spacing-xl: 20px;
        --wui-spacing-xxl: 24px;
        --wui-spacing-2xl: 32px;
        --wui-spacing-3xl: 40px;
        --wui-spacing-4xl: 90px;
        --wui-spacing-5xl: 95px;

        --wui-icon-box-size-xxs: 14px;
        --wui-icon-box-size-xs: 20px;
        --wui-icon-box-size-sm: 24px;
        --wui-icon-box-size-md: 32px;
        --wui-icon-box-size-mdl: 36px;
        --wui-icon-box-size-lg: 40px;
        --wui-icon-box-size-2lg: 48px;
        --wui-icon-box-size-xl: 64px;

        --wui-icon-size-inherit: inherit;
        --wui-icon-size-xxs: 10px;
        --wui-icon-size-xs: 12px;
        --wui-icon-size-sm: 14px;
        --wui-icon-size-md: 16px;
        --wui-icon-size-mdl: 18px;
        --wui-icon-size-lg: 20px;
        --wui-icon-size-xl: 24px;
        --wui-icon-size-xxl: 28px;

        --wui-wallet-image-size-inherit: inherit;
        --wui-wallet-image-size-sm: 40px;
        --wui-wallet-image-size-md: 56px;
        --wui-wallet-image-size-lg: 80px;

        --wui-visual-size-size-inherit: inherit;
        --wui-visual-size-sm: 40px;
        --wui-visual-size-md: 55px;
        --wui-visual-size-lg: 80px;

        --wui-box-size-md: 100px;
        --wui-box-size-lg: 120px;

        --wui-ease-out-power-2: cubic-bezier(0, 0, 0.22, 1);
        --wui-ease-out-power-1: cubic-bezier(0, 0, 0.55, 1);

        --wui-ease-in-power-3: cubic-bezier(0.66, 0, 1, 1);
        --wui-ease-in-power-2: cubic-bezier(0.45, 0, 1, 1);
        --wui-ease-in-power-1: cubic-bezier(0.3, 0, 1, 1);

        --wui-ease-inout-power-1: cubic-bezier(0.45, 0, 0.55, 1);

        --wui-duration-lg: 200ms;
        --wui-duration-md: 125ms;
        --wui-duration-sm: 75ms;

        --wui-path-network-sm: path(
          'M15.4 2.1a5.21 5.21 0 0 1 5.2 0l11.61 6.7a5.21 5.21 0 0 1 2.61 4.52v13.4c0 1.87-1 3.59-2.6 4.52l-11.61 6.7c-1.62.93-3.6.93-5.22 0l-11.6-6.7a5.21 5.21 0 0 1-2.61-4.51v-13.4c0-1.87 1-3.6 2.6-4.52L15.4 2.1Z'
        );

        --wui-path-network-md: path(
          'M43.4605 10.7248L28.0485 1.61089C25.5438 0.129705 22.4562 0.129705 19.9515 1.61088L4.53951 10.7248C2.03626 12.2051 0.5 14.9365 0.5 17.886V36.1139C0.5 39.0635 2.03626 41.7949 4.53951 43.2752L19.9515 52.3891C22.4562 53.8703 25.5438 53.8703 28.0485 52.3891L43.4605 43.2752C45.9637 41.7949 47.5 39.0635 47.5 36.114V17.8861C47.5 14.9365 45.9637 12.2051 43.4605 10.7248Z'
        );

        --wui-path-network-lg: path(
          'M78.3244 18.926L50.1808 2.45078C45.7376 -0.150261 40.2624 -0.150262 35.8192 2.45078L7.6756 18.926C3.23322 21.5266 0.5 26.3301 0.5 31.5248V64.4752C0.5 69.6699 3.23322 74.4734 7.6756 77.074L35.8192 93.5492C40.2624 96.1503 45.7376 96.1503 50.1808 93.5492L78.3244 77.074C82.7668 74.4734 85.5 69.6699 85.5 64.4752V31.5248C85.5 26.3301 82.7668 21.5266 78.3244 18.926Z'
        );

        --wui-width-network-sm: 36px;
        --wui-width-network-md: 48px;
        --wui-width-network-lg: 86px;

        --wui-height-network-sm: 40px;
        --wui-height-network-md: 54px;
        --wui-height-network-lg: 96px;

        --wui-icon-size-network-xs: 12px;
        --wui-icon-size-network-sm: 16px;
        --wui-icon-size-network-md: 24px;
        --wui-icon-size-network-lg: 42px;

        --wui-color-inherit: inherit;

        --wui-color-inverse-100: #fff;
        --wui-color-inverse-000: #000;

        --wui-cover: rgba(20, 20, 20, 0.8);

        --wui-color-modal-bg: var(--wui-color-modal-bg-base);

        --wui-color-accent-100: var(--wui-color-accent-base-100);
        --wui-color-accent-090: var(--wui-color-accent-base-090);
        --wui-color-accent-080: var(--wui-color-accent-base-080);

        --wui-color-success-100: var(--wui-color-success-base-100);
        --wui-color-success-125: var(--wui-color-success-base-125);

        --wui-color-warning-100: var(--wui-color-warning-base-100);

        --wui-color-error-100: var(--wui-color-error-base-100);
        --wui-color-error-125: var(--wui-color-error-base-125);

        --wui-color-blue-100: var(--wui-color-blue-base-100);
        --wui-color-blue-90: var(--wui-color-blue-base-90);

        --wui-icon-box-bg-error-100: var(--wui-icon-box-bg-error-base-100);
        --wui-icon-box-bg-blue-100: var(--wui-icon-box-bg-blue-base-100);
        --wui-icon-box-bg-success-100: var(--wui-icon-box-bg-success-base-100);
        --wui-icon-box-bg-inverse-100: var(--wui-icon-box-bg-inverse-base-100);

        --wui-all-wallets-bg-100: var(--wui-all-wallets-bg-100);

        --wui-avatar-border: var(--wui-avatar-border-base);

        --wui-thumbnail-border: var(--wui-thumbnail-border-base);

        --wui-wallet-button-bg: var(--wui-wallet-button-bg-base);

        --wui-box-shadow-blue: var(--wui-color-accent-glass-020);
      }

      @supports (background: color-mix(in srgb, white 50%, black)) {
        :root {
          --wui-color-modal-bg: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-modal-bg-base)
          );

          --wui-box-shadow-blue: color-mix(in srgb, var(--wui-color-accent-100) 20%, transparent);

          --wui-color-accent-100: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 100%,
            transparent
          );
          --wui-color-accent-090: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 90%,
            transparent
          );
          --wui-color-accent-080: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 80%,
            transparent
          );
          --wui-color-accent-glass-090: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 90%,
            transparent
          );
          --wui-color-accent-glass-080: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 80%,
            transparent
          );
          --wui-color-accent-glass-020: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 20%,
            transparent
          );
          --wui-color-accent-glass-015: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 15%,
            transparent
          );
          --wui-color-accent-glass-010: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 10%,
            transparent
          );
          --wui-color-accent-glass-005: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 5%,
            transparent
          );
          --wui-color-accent-002: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 2%,
            transparent
          );

          --wui-color-fg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-100)
          );
          --wui-color-fg-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-125)
          );
          --wui-color-fg-150: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-150)
          );
          --wui-color-fg-175: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-175)
          );
          --wui-color-fg-200: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-200)
          );
          --wui-color-fg-225: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-225)
          );
          --wui-color-fg-250: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-250)
          );
          --wui-color-fg-275: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-275)
          );
          --wui-color-fg-300: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-300)
          );
          --wui-color-fg-325: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-325)
          );
          --wui-color-fg-350: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-350)
          );

          --wui-color-bg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-100)
          );
          --wui-color-bg-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-125)
          );
          --wui-color-bg-150: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-150)
          );
          --wui-color-bg-175: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-175)
          );
          --wui-color-bg-200: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-200)
          );
          --wui-color-bg-225: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-225)
          );
          --wui-color-bg-250: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-250)
          );
          --wui-color-bg-275: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-275)
          );
          --wui-color-bg-300: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-300)
          );
          --wui-color-bg-325: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-325)
          );
          --wui-color-bg-350: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-350)
          );

          --wui-color-success-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-success-base-100)
          );
          --wui-color-success-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-success-base-125)
          );

          --wui-color-warning-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-warning-base-100)
          );

          --wui-color-error-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-error-base-100)
          );
          --wui-color-blue-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-blue-base-100)
          );
          --wui-color-blue-90: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-blue-base-90)
          );
          --wui-color-error-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-error-base-125)
          );

          --wui-icon-box-bg-error-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-error-base-100)
          );
          --wui-icon-box-bg-accent-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-blue-base-100)
          );
          --wui-icon-box-bg-success-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-success-base-100)
          );
          --wui-icon-box-bg-inverse-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-inverse-base-100)
          );

          --wui-all-wallets-bg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-all-wallets-bg-100)
          );

          --wui-avatar-border: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-avatar-border-base)
          );

          --wui-thumbnail-border: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-thumbnail-border-base)
          );

          --wui-wallet-button-bg: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-wallet-button-bg-base)
          );
        }
      }
    `,light:xr`
      :root {
        --w3m-color-mix: ${St((t==null?void 0:t["--w3m-color-mix"])||"#fff")};
        --w3m-accent: ${St(Es(t,"dark")["--w3m-accent"])};
        --w3m-default: #fff;

        --wui-color-modal-bg-base: ${St(Es(t,"dark")["--w3m-background"])};
        --wui-color-accent-base-100: var(--w3m-accent);

        --wui-color-blueberry-100: hsla(230, 100%, 67%, 1);
        --wui-color-blueberry-090: hsla(231, 76%, 61%, 1);
        --wui-color-blueberry-080: hsla(230, 59%, 55%, 1);
        --wui-color-blueberry-050: hsla(231, 100%, 70%, 0.1);

        --wui-color-fg-100: #e4e7e7;
        --wui-color-fg-125: #d0d5d5;
        --wui-color-fg-150: #a8b1b1;
        --wui-color-fg-175: #a8b0b0;
        --wui-color-fg-200: #949e9e;
        --wui-color-fg-225: #868f8f;
        --wui-color-fg-250: #788080;
        --wui-color-fg-275: #788181;
        --wui-color-fg-300: #6e7777;
        --wui-color-fg-325: #9a9a9a;
        --wui-color-fg-350: #363636;

        --wui-color-bg-100: #141414;
        --wui-color-bg-125: #191a1a;
        --wui-color-bg-150: #1e1f1f;
        --wui-color-bg-175: #222525;
        --wui-color-bg-200: #272a2a;
        --wui-color-bg-225: #2c3030;
        --wui-color-bg-250: #313535;
        --wui-color-bg-275: #363b3b;
        --wui-color-bg-300: #3b4040;
        --wui-color-bg-325: #252525;
        --wui-color-bg-350: #ffffff;

        --wui-color-success-base-100: #26d962;
        --wui-color-success-base-125: #30a46b;

        --wui-color-warning-base-100: #f3a13f;

        --wui-color-error-base-100: #f25a67;
        --wui-color-error-base-125: #df4a34;

        --wui-color-blue-base-100: rgba(102, 125, 255, 1);
        --wui-color-blue-base-90: rgba(102, 125, 255, 0.9);

        --wui-color-success-glass-001: rgba(38, 217, 98, 0.01);
        --wui-color-success-glass-002: rgba(38, 217, 98, 0.02);
        --wui-color-success-glass-005: rgba(38, 217, 98, 0.05);
        --wui-color-success-glass-010: rgba(38, 217, 98, 0.1);
        --wui-color-success-glass-015: rgba(38, 217, 98, 0.15);
        --wui-color-success-glass-020: rgba(38, 217, 98, 0.2);
        --wui-color-success-glass-025: rgba(38, 217, 98, 0.25);
        --wui-color-success-glass-030: rgba(38, 217, 98, 0.3);
        --wui-color-success-glass-060: rgba(38, 217, 98, 0.6);
        --wui-color-success-glass-080: rgba(38, 217, 98, 0.8);

        --wui-color-success-glass-reown-020: rgba(48, 164, 107, 0.2);

        --wui-color-warning-glass-reown-020: rgba(243, 161, 63, 0.2);

        --wui-color-error-glass-001: rgba(242, 90, 103, 0.01);
        --wui-color-error-glass-002: rgba(242, 90, 103, 0.02);
        --wui-color-error-glass-005: rgba(242, 90, 103, 0.05);
        --wui-color-error-glass-010: rgba(242, 90, 103, 0.1);
        --wui-color-error-glass-015: rgba(242, 90, 103, 0.15);
        --wui-color-error-glass-020: rgba(242, 90, 103, 0.2);
        --wui-color-error-glass-025: rgba(242, 90, 103, 0.25);
        --wui-color-error-glass-030: rgba(242, 90, 103, 0.3);
        --wui-color-error-glass-060: rgba(242, 90, 103, 0.6);
        --wui-color-error-glass-080: rgba(242, 90, 103, 0.8);

        --wui-color-error-glass-reown-020: rgba(223, 74, 52, 0.2);

        --wui-color-gray-glass-001: rgba(255, 255, 255, 0.01);
        --wui-color-gray-glass-002: rgba(255, 255, 255, 0.02);
        --wui-color-gray-glass-005: rgba(255, 255, 255, 0.05);
        --wui-color-gray-glass-010: rgba(255, 255, 255, 0.1);
        --wui-color-gray-glass-015: rgba(255, 255, 255, 0.15);
        --wui-color-gray-glass-020: rgba(255, 255, 255, 0.2);
        --wui-color-gray-glass-025: rgba(255, 255, 255, 0.25);
        --wui-color-gray-glass-030: rgba(255, 255, 255, 0.3);
        --wui-color-gray-glass-060: rgba(255, 255, 255, 0.6);
        --wui-color-gray-glass-080: rgba(255, 255, 255, 0.8);
        --wui-color-gray-glass-090: rgba(255, 255, 255, 0.9);

        --wui-color-dark-glass-100: rgba(42, 42, 42, 1);

        --wui-icon-box-bg-error-base-100: #3c2426;
        --wui-icon-box-bg-blue-base-100: #20303f;
        --wui-icon-box-bg-success-base-100: #1f3a28;
        --wui-icon-box-bg-inverse-base-100: #243240;

        --wui-all-wallets-bg-100: #222b35;

        --wui-avatar-border-base: #252525;

        --wui-thumbnail-border-base: #252525;

        --wui-wallet-button-bg-base: var(--wui-color-bg-125);

        --w3m-card-embedded-shadow-color: rgb(17 17 18 / 25%);
      }
    `,dark:xr`
      :root {
        --w3m-color-mix: ${St((t==null?void 0:t["--w3m-color-mix"])||"#000")};
        --w3m-accent: ${St(Es(t,"light")["--w3m-accent"])};
        --w3m-default: #000;

        --wui-color-modal-bg-base: ${St(Es(t,"light")["--w3m-background"])};
        --wui-color-accent-base-100: var(--w3m-accent);

        --wui-color-blueberry-100: hsla(231, 100%, 70%, 1);
        --wui-color-blueberry-090: hsla(231, 97%, 72%, 1);
        --wui-color-blueberry-080: hsla(231, 92%, 74%, 1);

        --wui-color-fg-100: #141414;
        --wui-color-fg-125: #2d3131;
        --wui-color-fg-150: #474d4d;
        --wui-color-fg-175: #636d6d;
        --wui-color-fg-200: #798686;
        --wui-color-fg-225: #828f8f;
        --wui-color-fg-250: #8b9797;
        --wui-color-fg-275: #95a0a0;
        --wui-color-fg-300: #9ea9a9;
        --wui-color-fg-325: #9a9a9a;
        --wui-color-fg-350: #d0d0d0;

        --wui-color-bg-100: #ffffff;
        --wui-color-bg-125: #f5fafa;
        --wui-color-bg-150: #f3f8f8;
        --wui-color-bg-175: #eef4f4;
        --wui-color-bg-200: #eaf1f1;
        --wui-color-bg-225: #e5eded;
        --wui-color-bg-250: #e1e9e9;
        --wui-color-bg-275: #dce7e7;
        --wui-color-bg-300: #d8e3e3;
        --wui-color-bg-325: #f3f3f3;
        --wui-color-bg-350: #202020;

        --wui-color-success-base-100: #26b562;
        --wui-color-success-base-125: #30a46b;

        --wui-color-warning-base-100: #f3a13f;

        --wui-color-error-base-100: #f05142;
        --wui-color-error-base-125: #df4a34;

        --wui-color-blue-base-100: rgba(102, 125, 255, 1);
        --wui-color-blue-base-90: rgba(102, 125, 255, 0.9);

        --wui-color-success-glass-001: rgba(38, 181, 98, 0.01);
        --wui-color-success-glass-002: rgba(38, 181, 98, 0.02);
        --wui-color-success-glass-005: rgba(38, 181, 98, 0.05);
        --wui-color-success-glass-010: rgba(38, 181, 98, 0.1);
        --wui-color-success-glass-015: rgba(38, 181, 98, 0.15);
        --wui-color-success-glass-020: rgba(38, 181, 98, 0.2);
        --wui-color-success-glass-025: rgba(38, 181, 98, 0.25);
        --wui-color-success-glass-030: rgba(38, 181, 98, 0.3);
        --wui-color-success-glass-060: rgba(38, 181, 98, 0.6);
        --wui-color-success-glass-080: rgba(38, 181, 98, 0.8);

        --wui-color-success-glass-reown-020: rgba(48, 164, 107, 0.2);

        --wui-color-warning-glass-reown-020: rgba(243, 161, 63, 0.2);

        --wui-color-error-glass-001: rgba(240, 81, 66, 0.01);
        --wui-color-error-glass-002: rgba(240, 81, 66, 0.02);
        --wui-color-error-glass-005: rgba(240, 81, 66, 0.05);
        --wui-color-error-glass-010: rgba(240, 81, 66, 0.1);
        --wui-color-error-glass-015: rgba(240, 81, 66, 0.15);
        --wui-color-error-glass-020: rgba(240, 81, 66, 0.2);
        --wui-color-error-glass-025: rgba(240, 81, 66, 0.25);
        --wui-color-error-glass-030: rgba(240, 81, 66, 0.3);
        --wui-color-error-glass-060: rgba(240, 81, 66, 0.6);
        --wui-color-error-glass-080: rgba(240, 81, 66, 0.8);

        --wui-color-error-glass-reown-020: rgba(223, 74, 52, 0.2);

        --wui-icon-box-bg-error-base-100: #f4dfdd;
        --wui-icon-box-bg-blue-base-100: #d9ecfb;
        --wui-icon-box-bg-success-base-100: #daf0e4;
        --wui-icon-box-bg-inverse-base-100: #dcecfc;

        --wui-all-wallets-bg-100: #e8f1fa;

        --wui-avatar-border-base: #f3f4f4;

        --wui-thumbnail-border-base: #eaefef;

        --wui-wallet-button-bg-base: var(--wui-color-bg-125);

        --wui-color-gray-glass-001: rgba(0, 0, 0, 0.01);
        --wui-color-gray-glass-002: rgba(0, 0, 0, 0.02);
        --wui-color-gray-glass-005: rgba(0, 0, 0, 0.05);
        --wui-color-gray-glass-010: rgba(0, 0, 0, 0.1);
        --wui-color-gray-glass-015: rgba(0, 0, 0, 0.15);
        --wui-color-gray-glass-020: rgba(0, 0, 0, 0.2);
        --wui-color-gray-glass-025: rgba(0, 0, 0, 0.25);
        --wui-color-gray-glass-030: rgba(0, 0, 0, 0.3);
        --wui-color-gray-glass-060: rgba(0, 0, 0, 0.6);
        --wui-color-gray-glass-080: rgba(0, 0, 0, 0.8);
        --wui-color-gray-glass-090: rgba(0, 0, 0, 0.9);

        --wui-color-dark-glass-100: rgba(233, 233, 233, 1);

        --w3m-card-embedded-shadow-color: rgb(224 225 233 / 25%);
      }
    `}}const N1=xr`
  *,
  *::after,
  *::before,
  :host {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-style: normal;
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    font-family: var(--wui-font-family);
    backface-visibility: hidden;
  }
`,_1=xr`
  button,
  a {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition:
      color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      border var(--wui-duration-lg) var(--wui-ease-out-power-1),
      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1),
      box-shadow var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: background-color, color, border, box-shadow, border-radius;
    outline: none;
    border: none;
    column-gap: var(--wui-spacing-3xs);
    background-color: transparent;
    text-decoration: none;
  }

  wui-flex {
    transition: border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: border-radius;
  }

  button:disabled > wui-wallet-image,
  button:disabled > wui-all-wallets-image,
  button:disabled > wui-network-image,
  button:disabled > wui-image,
  button:disabled > wui-transaction-visual,
  button:disabled > wui-logo {
    filter: grayscale(1);
  }

  @media (hover: hover) and (pointer: fine) {
    button:hover:enabled {
      background-color: var(--wui-color-gray-glass-005);
    }

    button:active:enabled {
      background-color: var(--wui-color-gray-glass-010);
    }
  }

  button:disabled > wui-icon-box {
    opacity: 0.5;
  }

  input {
    border: none;
    outline: none;
    appearance: none;
  }
`,S1=xr`
  .wui-color-inherit {
    color: var(--wui-color-inherit);
  }

  .wui-color-accent-100 {
    color: var(--wui-color-accent-100);
  }

  .wui-color-error-100 {
    color: var(--wui-color-error-100);
  }

  .wui-color-blue-100 {
    color: var(--wui-color-blue-100);
  }

  .wui-color-blue-90 {
    color: var(--wui-color-blue-90);
  }

  .wui-color-error-125 {
    color: var(--wui-color-error-125);
  }

  .wui-color-success-100 {
    color: var(--wui-color-success-100);
  }

  .wui-color-success-125 {
    color: var(--wui-color-success-125);
  }

  .wui-color-inverse-100 {
    color: var(--wui-color-inverse-100);
  }

  .wui-color-inverse-000 {
    color: var(--wui-color-inverse-000);
  }

  .wui-color-fg-100 {
    color: var(--wui-color-fg-100);
  }

  .wui-color-fg-200 {
    color: var(--wui-color-fg-200);
  }

  .wui-color-fg-300 {
    color: var(--wui-color-fg-300);
  }

  .wui-color-fg-325 {
    color: var(--wui-color-fg-325);
  }

  .wui-color-fg-350 {
    color: var(--wui-color-fg-350);
  }

  .wui-bg-color-inherit {
    background-color: var(--wui-color-inherit);
  }

  .wui-bg-color-blue-100 {
    background-color: var(--wui-color-accent-100);
  }

  .wui-bg-color-error-100 {
    background-color: var(--wui-color-error-100);
  }

  .wui-bg-color-error-125 {
    background-color: var(--wui-color-error-125);
  }

  .wui-bg-color-success-100 {
    background-color: var(--wui-color-success-100);
  }

  .wui-bg-color-success-125 {
    background-color: var(--wui-color-success-100);
  }

  .wui-bg-color-inverse-100 {
    background-color: var(--wui-color-inverse-100);
  }

  .wui-bg-color-inverse-000 {
    background-color: var(--wui-color-inverse-000);
  }

  .wui-bg-color-fg-100 {
    background-color: var(--wui-color-fg-100);
  }

  .wui-bg-color-fg-200 {
    background-color: var(--wui-color-fg-200);
  }

  .wui-bg-color-fg-300 {
    background-color: var(--wui-color-fg-300);
  }

  .wui-color-fg-325 {
    background-color: var(--wui-color-fg-325);
  }

  .wui-color-fg-350 {
    background-color: var(--wui-color-fg-350);
  }
`,gi={ERROR_CODE_UNRECOGNIZED_CHAIN_ID:4902,ERROR_CODE_DEFAULT:5e3,ERROR_INVALID_CHAIN_ID:32603,DEFAULT_ALLOWED_ANCESTORS:["http://localhost:*","https://*.pages.dev","https://*.vercel.app","https://*.ngrok-free.app","https://secure-mobile.walletconnect.com","https://secure-mobile.walletconnect.org"]};function Gi(t){return{formatters:void 0,fees:void 0,serializers:void 0,...t}}const Vl=Gi({id:"5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp",name:"Solana",network:"solana-mainnet",nativeCurrency:{name:"Solana",symbol:"SOL",decimals:9},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}},blockExplorers:{default:{name:"Solscan",url:"https://solscan.io"}},testnet:!1,chainNamespace:"solana",caipNetworkId:"solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp",deprecatedCaipNetworkId:"solana:4sGjMW1sUnHzSxGspuhpqLDx6wiyjNtZ"}),Gl=Gi({id:"EtWTRABZaYq6iMfeYKouRu166VU2xqa1",name:"Solana Devnet",network:"solana-devnet",nativeCurrency:{name:"Solana",symbol:"SOL",decimals:9},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}},blockExplorers:{default:{name:"Solscan",url:"https://solscan.io"}},testnet:!0,chainNamespace:"solana",caipNetworkId:"solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1",deprecatedCaipNetworkId:"solana:8E9rvCKLFQia2Y35HXjjpWzj8weVo44K"});Gi({id:"4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z",name:"Solana Testnet",network:"solana-testnet",nativeCurrency:{name:"Solana",symbol:"SOL",decimals:9},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}},blockExplorers:{default:{name:"Solscan",url:"https://solscan.io"}},testnet:!0,chainNamespace:"solana",caipNetworkId:"solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z"});Gi({id:"000000000019d6689c085ae165831e93",caipNetworkId:"bip122:000000000019d6689c085ae165831e93",chainNamespace:"bip122",name:"Bitcoin",nativeCurrency:{name:"Bitcoin",symbol:"BTC",decimals:8},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}}});Gi({id:"000000000933ea01ad0ee984209779ba",caipNetworkId:"bip122:000000000933ea01ad0ee984209779ba",chainNamespace:"bip122",name:"Bitcoin Testnet",nativeCurrency:{name:"Bitcoin",symbol:"BTC",decimals:8},rpcUrls:{default:{http:["https://rpc.walletconnect.org/v1"]}},testnet:!0});const DI={solana:["solana_signMessage","solana_signTransaction","solana_requestAccounts","solana_getAccounts","solana_signAllTransactions","solana_signAndSendTransaction"],eip155:["eth_accounts","eth_requestAccounts","eth_sendRawTransaction","eth_sign","eth_signTransaction","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sendTransaction","personal_sign","wallet_switchEthereumChain","wallet_addEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode","wallet_getCallsStatus","wallet_showCallsStatus","wallet_sendCalls","wallet_getCapabilities","wallet_grantPermissions","wallet_revokePermissions","wallet_getAssets"],bip122:["sendTransfer","signMessage","signPsbt","getAccountAddresses"]},Cn={getMethodsByChainNamespace(t){return DI[t]||[]},createDefaultNamespace(t){return{methods:this.getMethodsByChainNamespace(t),events:["accountsChanged","chainChanged"],chains:[],rpcMap:{}}},applyNamespaceOverrides(t,e){if(!e)return{...t};const s={...t},r=new Set;if(e.methods&&Object.keys(e.methods).forEach(i=>r.add(i)),e.chains&&Object.keys(e.chains).forEach(i=>r.add(i)),e.events&&Object.keys(e.events).forEach(i=>r.add(i)),e.rpcMap&&Object.keys(e.rpcMap).forEach(i=>{const[n]=i.split(":");n&&r.add(n)}),r.forEach(i=>{s[i]||(s[i]=this.createDefaultNamespace(i))}),e.methods&&Object.entries(e.methods).forEach(([i,n])=>{s[i]&&(s[i].methods=n)}),e.chains&&Object.entries(e.chains).forEach(([i,n])=>{s[i]&&(s[i].chains=n)}),e.events&&Object.entries(e.events).forEach(([i,n])=>{s[i]&&(s[i].events=n)}),e.rpcMap){const i=new Set;Object.entries(e.rpcMap).forEach(([n,o])=>{const[a,c]=n.split(":");!a||!c||!s[a]||(s[a].rpcMap||(s[a].rpcMap={}),i.has(a)||(s[a].rpcMap={},i.add(a)),s[a].rpcMap[c]=o)})}return s},createNamespaces(t,e){const s=t.reduce((r,i)=>{const{id:n,chainNamespace:o,rpcUrls:a}=i,c=a.default.http[0];r[o]||(r[o]=this.createDefaultNamespace(o));const l=`${o}:${n}`,u=r[o];switch(u.chains.push(l),l){case Vl.caipNetworkId:u.chains.push(Vl.deprecatedCaipNetworkId);break;case Gl.caipNetworkId:u.chains.push(Gl.deprecatedCaipNetworkId);break}return u!=null&&u.rpcMap&&c&&(u.rpcMap[n]=c),r},{});return this.applyNamespaceOverrides(s,e)},resolveReownName:async t=>{var r;const e=await bi.resolveName(t);return((r=(Object.values(e==null?void 0:e.addresses)||[])[0])==null?void 0:r.address)||!1},getChainsFromNamespaces(t={}){return Object.values(t).flatMap(e=>{const s=e.chains||[],r=e.accounts.map(i=>{const[n,o]=i.split(":");return`${n}:${o}`});return Array.from(new Set([...s,...r]))})},isSessionEventData(t){return typeof t=="object"&&t!==null&&"id"in t&&"topic"in t&&"params"in t&&typeof t.params=="object"&&t.params!==null&&"chainId"in t.params&&"event"in t.params&&typeof t.params.event=="object"&&t.params.event!==null},isOriginAllowed(t,e,s){for(const r of[...e,...s])if(r.includes("*")){const n=`^${r.replace(/[.*+?^${}()|[\]\\]/gu,"\\$&").replace(/\\\*/gu,".*")}$`;if(new RegExp(n,"u").test(t))return!0}else try{if(new URL(r).origin===t)return!0}catch{if(r===t)return!0}return!1}};class Wh{constructor({provider:e,namespace:s}){this.id=W.CONNECTOR_ID.WALLET_CONNECT,this.name=Tn.ConnectorNamesMap[W.CONNECTOR_ID.WALLET_CONNECT],this.type="WALLET_CONNECT",this.imageId=Tn.ConnectorImageIds[W.CONNECTOR_ID.WALLET_CONNECT],this.getCaipNetworks=g.getCaipNetworks.bind(g),this.caipNetworks=this.getCaipNetworks(),this.provider=e,this.chain=s}get chains(){return this.getCaipNetworks()}async connectWalletConnect(){if(!await this.authenticate()){const s=this.getCaipNetworks(),r=T.state.universalProviderConfigOverride,i=Cn.createNamespaces(s,r);await this.provider.connect({optionalNamespaces:i})}return{clientId:await this.provider.client.core.crypto.getClientId(),session:this.provider.session}}async disconnect(){await this.provider.disconnect()}async authenticate(){const e=this.chains.map(s=>s.caipNetworkId);return vi.universalProviderAuthenticate({universalProvider:this.provider,chains:e,methods:LI})}}const LI=["eth_accounts","eth_requestAccounts","eth_sendRawTransaction","eth_sign","eth_signTransaction","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sendTransaction","personal_sign","wallet_switchEthereumChain","wallet_addEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode","wallet_getCallsStatus","wallet_sendCalls","wallet_getCapabilities","wallet_grantPermissions","wallet_revokePermissions","wallet_getAssets"];class MI{constructor(e){this.availableConnectors=[],this.eventListeners=new Map,this.getCaipNetworks=s=>g.getCaipNetworks(s),e&&this.construct(e)}construct(e){this.projectId=e.projectId,this.namespace=e.namespace,this.adapterType=e.adapterType}get connectors(){return this.availableConnectors}get networks(){return this.getCaipNetworks(this.namespace)}setAuthProvider(e){this.addConnector({id:W.CONNECTOR_ID.AUTH,type:"AUTH",name:W.CONNECTOR_NAMES.AUTH,provider:e,imageId:Tn.ConnectorImageIds[W.CONNECTOR_ID.AUTH],chain:this.namespace,chains:[]})}addConnector(...e){const s=new Set;this.availableConnectors=[...e,...this.availableConnectors].filter(r=>s.has(r.id)?!1:(s.add(r.id),!0)),this.emit("connectors",this.availableConnectors)}setStatus(e,s){z.setStatus(e,s)}on(e,s){var r;this.eventListeners.has(e)||this.eventListeners.set(e,new Set),(r=this.eventListeners.get(e))==null||r.add(s)}off(e,s){const r=this.eventListeners.get(e);r&&r.delete(s)}removeAllEventListeners(){this.eventListeners.forEach(e=>{e.clear()})}emit(e,s){const r=this.eventListeners.get(e);r&&r.forEach(i=>i(s))}async connectWalletConnect(e){return{clientId:(await this.getWalletConnectConnector().connectWalletConnect()).clientId}}async switchNetwork(e){var n;const{caipNetwork:s,providerType:r}=e;if(!e.provider)return;const i="provider"in e.provider?e.provider.provider:e.provider;if(r==="WALLET_CONNECT"){i.setDefaultChain(s.caipNetworkId);return}if(i&&r==="AUTH"){const o=i,a=(n=z.state.preferredAccountTypes)==null?void 0:n[s.chainNamespace];await o.switchNetwork(s.caipNetworkId);const c=await o.getUser({chainId:s.caipNetworkId,preferredAccountType:a});this.emit("switchNetwork",c)}}getWalletConnectConnector(){const e=this.connectors.find(s=>s instanceof Wh);if(!e)throw new Error("WalletConnectConnector not found");return e}}class BI extends MI{setUniversalProvider(e){this.addConnector(new Wh({provider:e,caipNetworks:this.getCaipNetworks(),namespace:this.namespace}))}async connect(e){return Promise.resolve({id:"WALLET_CONNECT",type:"WALLET_CONNECT",chainId:Number(e.chainId),provider:this.provider,address:""})}async disconnect(){try{await this.getWalletConnectConnector().disconnect()}catch(e){console.warn("UniversalAdapter:disconnect - error",e)}}async getAccounts({namespace:e}){var i,n,o,a;const s=this.provider,r=((a=(o=(n=(i=s==null?void 0:s.session)==null?void 0:i.namespaces)==null?void 0:n[e])==null?void 0:o.accounts)==null?void 0:a.map(c=>{const[,,l]=c.split(":");return l}).filter((c,l,u)=>u.indexOf(c)===l))||[];return Promise.resolve({accounts:r.map(c=>X.createAccount(e,c,e==="bip122"?"payment":"eoa"))})}async syncConnectors(){return Promise.resolve()}async getBalance(e){var n,o,a,c,l;if(!(e.caipNetwork&&Ee.BALANCE_SUPPORTED_CHAINS.includes((n=e.caipNetwork)==null?void 0:n.chainNamespace))||(o=e.caipNetwork)!=null&&o.testnet)return{balance:"0.00",symbol:((a=e.caipNetwork)==null?void 0:a.nativeCurrency.symbol)||""};if(z.state.balanceLoading&&e.chainId===((c=g.state.activeCaipNetwork)==null?void 0:c.id))return{balance:z.state.balance||"0.00",symbol:z.state.balanceSymbol||""};const i=(await z.fetchTokenBalance()).find(u=>{var h,d;return u.chainId===`${(h=e.caipNetwork)==null?void 0:h.chainNamespace}:${e.chainId}`&&u.symbol===((d=e.caipNetwork)==null?void 0:d.nativeCurrency.symbol)});return{balance:(i==null?void 0:i.quantity.numeric)||"0.00",symbol:(i==null?void 0:i.symbol)||((l=e.caipNetwork)==null?void 0:l.nativeCurrency.symbol)||""}}async signMessage(e){var o,a,c;const{provider:s,message:r,address:i}=e;if(!s)throw new Error("UniversalAdapter:signMessage - provider is undefined");let n="";return((o=g.state.activeCaipNetwork)==null?void 0:o.chainNamespace)===W.CHAIN.SOLANA?n=(await s.request({method:"solana_signMessage",params:{message:Zl.encode(new TextEncoder().encode(r)),pubkey:i}},(a=g.state.activeCaipNetwork)==null?void 0:a.caipNetworkId)).signature:n=await s.request({method:"personal_sign",params:[r,i]},(c=g.state.activeCaipNetwork)==null?void 0:c.caipNetworkId),{signature:n}}async estimateGas(){return Promise.resolve({gas:BigInt(0)})}async sendTransaction(){return Promise.resolve({hash:""})}walletGetAssets(e){return Promise.resolve({})}async writeContract(){return Promise.resolve({hash:""})}parseUnits(){return 0n}formatUnits(){return"0"}async getCapabilities(){return Promise.resolve({})}async grantPermissions(){return Promise.resolve({})}async revokePermissions(){return Promise.resolve("0x")}async syncConnection(){return Promise.resolve({id:"WALLET_CONNECT",type:"WALLET_CONNECT",chainId:1,provider:this.provider,address:""})}async switchNetwork(e){var i,n,o,a,c,l;const{caipNetwork:s}=e,r=this.getWalletConnectConnector();if(s.chainNamespace===W.CHAIN.EVM)try{await((i=r.provider)==null?void 0:i.request({method:"wallet_switchEthereumChain",params:[{chainId:ka(s.id)}]}))}catch(u){if(u.code===gi.ERROR_CODE_UNRECOGNIZED_CHAIN_ID||u.code===gi.ERROR_INVALID_CHAIN_ID||u.code===gi.ERROR_CODE_DEFAULT||((o=(n=u==null?void 0:u.data)==null?void 0:n.originalError)==null?void 0:o.code)===gi.ERROR_CODE_UNRECOGNIZED_CHAIN_ID)try{await((l=r.provider)==null?void 0:l.request({method:"wallet_addEthereumChain",params:[{chainId:ka(s.id),rpcUrls:[(a=s==null?void 0:s.rpcUrls.chainDefault)==null?void 0:a.http],chainName:s.name,nativeCurrency:s.nativeCurrency,blockExplorerUrls:[(c=s.blockExplorers)==null?void 0:c.default.url]}]}))}catch{throw new Error("Chain is not supported")}}r.provider.setDefaultChain(s.caipNetworkId)}getWalletConnectProvider(){const e=this.connectors.find(r=>r.type==="WALLET_CONNECT");return e==null?void 0:e.provider}}const jI=["email","socials","swaps","onramp","activity","reownBranding"],dn={email:{apiFeatureName:"social_login",localFeatureName:"email",returnType:!1,isLegacy:!1,isAvailableOnBasic:!1,processApi:t=>{if(!(t!=null&&t.config))return!1;const e=t.config;return!!t.isEnabled&&e.includes("email")},processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.email:!!t},socials:{apiFeatureName:"social_login",localFeatureName:"socials",returnType:!1,isLegacy:!1,isAvailableOnBasic:!1,processApi:t=>{if(!(t!=null&&t.config))return!1;const e=t.config;return t.isEnabled&&e.length>0?e.filter(s=>s!=="email"):!1},processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.socials:typeof t=="boolean"?t?Ee.DEFAULT_REMOTE_FEATURES.socials:!1:t},swaps:{apiFeatureName:"swap",localFeatureName:"swaps",returnType:!1,isLegacy:!1,isAvailableOnBasic:!1,processApi:t=>{if(!(t!=null&&t.config))return!1;const e=t.config;return t.isEnabled&&e.length>0?e:!1},processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.swaps:typeof t=="boolean"?t?Ee.DEFAULT_REMOTE_FEATURES.swaps:!1:t},onramp:{apiFeatureName:"onramp",localFeatureName:"onramp",returnType:!1,isLegacy:!1,isAvailableOnBasic:!1,processApi:t=>{if(!(t!=null&&t.config))return!1;const e=t.config;return t.isEnabled&&e.length>0?e:!1},processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.onramp:typeof t=="boolean"?t?Ee.DEFAULT_REMOTE_FEATURES.onramp:!1:t},activity:{apiFeatureName:"activity",localFeatureName:"history",returnType:!1,isLegacy:!0,isAvailableOnBasic:!1,processApi:t=>!!t.isEnabled,processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.activity:!!t},reownBranding:{apiFeatureName:"reown_branding",localFeatureName:"reownBranding",returnType:!1,isLegacy:!1,isAvailableOnBasic:!1,processApi:t=>!!t.isEnabled,processFallback:t=>t===void 0?Ee.DEFAULT_REMOTE_FEATURES.reownBranding:!!t}},qI={localSettingsOverridden:new Set,getApiConfig(t,e){return e==null?void 0:e.find(s=>s.id===t)},addWarning(t,e){if(t!==void 0){const s=dn[e],r=s.isLegacy?`"features.${s.localFeatureName}" (now "${e}")`:`"features.${e}"`;this.localSettingsOverridden.add(r)}},processFeature(t,e,s,r,i){const n=dn[t],o=e[n.localFeatureName];if(i&&!n.isAvailableOnBasic)return!1;if(r){const a=this.getApiConfig(n.apiFeatureName,s);return(a==null?void 0:a.config)===null?this.processFallbackFeature(t,o):a!=null&&a.config?(o!==void 0&&this.addWarning(o,t),this.processApiFeature(t,a)):!1}return this.processFallbackFeature(t,o)},processApiFeature(t,e){return dn[t].processApi(e)},processFallbackFeature(t,e){return dn[t].processFallback(e)},async fetchRemoteFeatures(t){const e=t.basic??!1,s=t.features||{};this.localSettingsOverridden.clear();let r=null,i=!1;try{r=await K.fetchProjectConfig(),i=r!=null}catch(o){console.warn("[Reown Config] Failed to fetch remote project configuration. Using local/default values.",o)}const n=i&&!e?Ee.DEFAULT_REMOTE_FEATURES:Ee.DEFAULT_REMOTE_FEATURES_DISABLED;try{for(const o of jI){const a=this.processFeature(o,s,r,i,e);Object.assign(n,{[o]:a})}}catch(o){return console.warn("[Reown Config] Failed to process the configuration from Cloud. Using default values.",o),Ee.DEFAULT_REMOTE_FEATURES}if(i&&this.localSettingsOverridden.size>0){const o=`Your local configuration for ${Array.from(this.localSettingsOverridden).join(", ")} was ignored because a remote configuration was successfully fetched. Please manage these features via your project dashboard on dashboard.reown.com.`;Ms.open({shortMessage:"Local configuration ignored",longMessage:`[Reown Config Notice] ${o}`},"warning")}return n}};class FI{constructor(e){this.chainNamespaces=[],this.remoteFeatures={},this.reportedAlertErrors={},this.getCaipNetwork=(s,r)=>{var i,n,o,a;if(s){const c=(n=(i=g.getNetworkData(s))==null?void 0:i.requestedCaipNetworks)==null?void 0:n.find(h=>h.id===r);if(c)return c;const l=(o=g.getNetworkData(s))==null?void 0:o.caipNetwork;return l||((a=g.getRequestedCaipNetworks(s).filter(h=>h.chainNamespace===s))==null?void 0:a[0])}return g.state.activeCaipNetwork||this.defaultCaipNetwork},this.getCaipNetworkId=()=>{const s=this.getCaipNetwork();if(s)return s.id},this.getCaipNetworks=s=>g.getCaipNetworks(s),this.getActiveChainNamespace=()=>g.state.activeChain,this.setRequestedCaipNetworks=(s,r)=>{g.setRequestedCaipNetworks(s,r)},this.getApprovedCaipNetworkIds=()=>g.getAllApprovedCaipNetworkIds(),this.getCaipAddress=s=>g.state.activeChain===s||!s?g.state.activeCaipAddress:g.getAccountProp("caipAddress",s),this.setClientId=s=>{J.setClientId(s)},this.getProvider=s=>Se.getProvider(s),this.getProviderType=s=>Se.getProviderId(s),this.getPreferredAccountType=s=>{var r;return(r=z.state.preferredAccountTypes)==null?void 0:r[s]},this.setCaipAddress=(s,r)=>{z.setCaipAddress(s,r),s&&T.state.enableEmbedded&&this.close()},this.setBalance=(s,r,i)=>{z.setBalance(s,r,i)},this.setProfileName=(s,r)=>{z.setProfileName(s,r)},this.setProfileImage=(s,r)=>{z.setProfileImage(s,r)},this.setUser=(s,r)=>{z.setUser(s,r)},this.resetAccount=s=>{z.resetAccount(s)},this.setCaipNetwork=s=>{g.setActiveCaipNetwork(s)},this.setCaipNetworkOfNamespace=(s,r)=>{g.setChainNetworkData(r,{caipNetwork:s})},this.setAllAccounts=(s,r)=>{z.setAllAccounts(s,r),T.setHasMultipleAddresses((s==null?void 0:s.length)>1)},this.setStatus=(s,r)=>{z.setStatus(s,r),j.isConnected()?F.setConnectionStatus("connected"):F.setConnectionStatus("disconnected")},this.getAddressByChainNamespace=s=>g.getAccountProp("address",s),this.setConnectors=s=>{const r=[...j.state.allConnectors,...s];j.setConnectors(r)},this.setConnections=(s,r)=>{Y.setConnections(s,r)},this.fetchIdentity=s=>J.fetchIdentity(s),this.getReownName=s=>bi.getNamesForAddress(s),this.getConnectors=()=>j.getConnectors(),this.getConnectorImage=s=>nu.getConnectorImage(s),this.setConnectedWalletInfo=(s,r)=>{const i=Se.getProviderId(r),n=s?{...s,type:i}:void 0;z.setConnectedWalletInfo(n,r)},this.getIsConnectedState=()=>!!g.state.activeCaipAddress,this.addAddressLabel=(s,r,i)=>{z.addAddressLabel(s,r,i)},this.removeAddressLabel=(s,r)=>{z.removeAddressLabel(s,r)},this.getAddress=s=>g.state.activeChain===s||!s?z.state.address:g.getAccountProp("address",s),this.setApprovedCaipNetworksData=s=>g.setApprovedCaipNetworksData(s),this.resetNetwork=s=>{g.resetNetwork(s)},this.addConnector=s=>{j.addConnector(s)},this.resetWcConnection=()=>{Y.resetWcConnection()},this.setAddressExplorerUrl=(s,r)=>{z.setAddressExplorerUrl(s,r)},this.setSmartAccountDeployed=(s,r)=>{z.setSmartAccountDeployed(s,r)},this.setSmartAccountEnabledNetworks=(s,r)=>{g.setSmartAccountEnabledNetworks(s,r)},this.setPreferredAccountType=(s,r)=>{z.setPreferredAccountType(s,r)},this.setEIP6963Enabled=s=>{T.setEIP6963Enabled(s)},this.handleUnsafeRPCRequest=()=>{if(this.isOpen()){if(this.isTransactionStackEmpty())return;this.redirect("ApproveTransaction")}else this.open({view:"ApproveTransaction"})},this.options=e,this.version=e.sdkVersion,this.caipNetworks=this.extendCaipNetworks(e),this.chainNamespaces=this.getChainNamespacesSet(e.adapters,this.caipNetworks),this.defaultCaipNetwork=this.extendDefaultCaipNetwork(e),this.chainAdapters=this.createAdapters(e.adapters),this.readyPromise=this.initialize(e)}getChainNamespacesSet(e,s){const r=e==null?void 0:e.map(n=>n.namespace).filter(n=>!!n);if(r!=null&&r.length)return[...new Set(r)];const i=s==null?void 0:s.map(n=>n.chainNamespace);return[...new Set(i)]}async initialize(e){var s,r,i;this.initializeProjectSettings(e),this.initControllers(e),await this.initChainAdapters(),this.sendInitializeEvent(e),await this.syncExistingConnection(),this.remoteFeatures=await qI.fetchRemoteFeatures(e),T.setRemoteFeatures(this.remoteFeatures),this.remoteFeatures.onramp&&ko.setOnrampProviders(this.remoteFeatures.onramp),((s=T.state.remoteFeatures)!=null&&s.email||Array.isArray((r=T.state.remoteFeatures)==null?void 0:r.socials)&&((i=T.state.remoteFeatures)==null?void 0:i.socials.length)>0)&&await this.checkAllowedOrigins()}async checkAllowedOrigins(){const e=await K.fetchAllowedOrigins();if(e&&X.isClient()){const s=window.location.origin;Cn.isOriginAllowed(s,e,gi.DEFAULT_ALLOWED_ANCESTORS)||Ms.open(Ir.ALERT_ERRORS.INVALID_APP_CONFIGURATION,"error")}else Ms.open(Ir.ALERT_ERRORS.PROJECT_ID_NOT_CONFIGURED,"error")}sendInitializeEvent(e){var r;const{...s}=e;delete s.adapters,delete s.universalProvider,Pe.sendEvent({type:"track",event:"INITIALIZE",properties:{...s,networks:e.networks.map(i=>i.id),siweConfig:{options:((r=e.siweConfig)==null?void 0:r.options)||{}}}})}initControllers(e){this.initializeOptionsController(e),this.initializeChainController(e),this.initializeThemeController(e),this.initializeConnectionController(e),this.initializeConnectorController()}initializeThemeController(e){e.themeMode&&mt.setThemeMode(e.themeMode),e.themeVariables&&mt.setThemeVariables(e.themeVariables)}initializeChainController(e){if(!this.connectionControllerClient||!this.networkControllerClient)throw new Error("ConnectionControllerClient and NetworkControllerClient must be set");g.initialize(e.adapters??[],this.caipNetworks,{connectionControllerClient:this.connectionControllerClient,networkControllerClient:this.networkControllerClient});const s=this.getDefaultNetwork();s&&g.setActiveCaipNetwork(s)}initializeConnectionController(e){Y.setWcBasic(e.basic??!1)}initializeConnectorController(){j.initialize(this.chainNamespaces)}initializeProjectSettings(e){T.setProjectId(e.projectId),T.setSdkVersion(e.sdkVersion)}initializeOptionsController(e){var o;T.setDebug(e.debug!==!1),T.setEnableWalletConnect(e.enableWalletConnect!==!1),T.setEnableWalletGuide(e.enableWalletGuide!==!1),T.setEnableWallets(e.enableWallets!==!1),T.setEIP6963Enabled(e.enableEIP6963!==!1),T.setEnableNetworkSwitch(e.enableNetworkSwitch!==!1),T.setEnableAuthLogger(e.enableAuthLogger!==!1),T.setCustomRpcUrls(e.customRpcUrls),T.setEnableEmbedded(e.enableEmbedded),T.setAllWallets(e.allWallets),T.setIncludeWalletIds(e.includeWalletIds),T.setExcludeWalletIds(e.excludeWalletIds),T.setFeaturedWalletIds(e.featuredWalletIds),T.setTokens(e.tokens),T.setTermsConditionsUrl(e.termsConditionsUrl),T.setPrivacyPolicyUrl(e.privacyPolicyUrl),T.setCustomWallets(e.customWallets),T.setFeatures(e.features),T.setAllowUnsupportedChain(e.allowUnsupportedChain),T.setUniversalProviderConfigOverride(e.universalProviderConfigOverride),T.setPreferUniversalLinks(e.experimental_preferUniversalLinks),T.setDefaultAccountTypes(e.defaultAccountTypes);const s=F.getPreferredAccountTypes()||{},r={...T.state.defaultAccountTypes,...s};z.setPreferredAccountTypes(r);const i=this.getDefaultMetaData();if(!e.metadata&&i&&(e.metadata=i),T.setMetadata(e.metadata),T.setDisableAppend(e.disableAppend),T.setEnableEmbedded(e.enableEmbedded),T.setSIWX(e.siwx),!e.projectId){Ms.open(Ir.ALERT_ERRORS.PROJECT_ID_NOT_CONFIGURED,"error");return}if(((o=e.adapters)==null?void 0:o.find(a=>a.namespace===W.CHAIN.EVM))&&e.siweConfig){if(e.siwx)throw new Error("Cannot set both `siweConfig` and `siwx` options");T.setSIWX(e.siweConfig.mapToSIWX())}}getDefaultMetaData(){var e,s,r,i;return X.isClient()?{name:((s=(e=document.getElementsByTagName("title"))==null?void 0:e[0])==null?void 0:s.textContent)||"",description:((r=document.querySelector('meta[property="og:description"]'))==null?void 0:r.content)||"",url:window.location.origin,icons:[((i=document.querySelector('link[rel~="icon"]'))==null?void 0:i.href)||""]}:null}setUnsupportedNetwork(e){const s=this.getActiveChainNamespace();if(s){const r=Nr.getUnsupportedNetwork(`${s}:${e}`);g.setActiveCaipNetwork(r)}}getDefaultNetwork(){return Nr.getCaipNetworkFromStorage(this.defaultCaipNetwork)}extendCaipNetwork(e,s){return Nr.extendCaipNetwork(e,{customNetworkImageUrls:s.chainImages,projectId:s.projectId})}extendCaipNetworks(e){return Nr.extendCaipNetworks(e.networks,{customNetworkImageUrls:e.chainImages,customRpcUrls:e.customRpcUrls,projectId:e.projectId})}extendDefaultCaipNetwork(e){const s=e.networks.find(i=>{var n;return i.id===((n=e.defaultNetwork)==null?void 0:n.id)});return s?Nr.extendCaipNetwork(s,{customNetworkImageUrls:e.chainImages,customRpcUrls:e.customRpcUrls,projectId:e.projectId}):void 0}async disconnectNamespace(e){try{const s=this.getAdapter(e),r=Se.getProvider(e),i=Se.getProviderId(e),{caipAddress:n}=g.getAccountData(e)||{};this.setLoading(!0,e),n&&(s!=null&&s.disconnect)&&await s.disconnect({provider:r,providerType:i}),F.removeConnectedNamespace(e),Se.resetChain(e),this.setUser(void 0,e),this.setStatus("disconnected",e),this.setConnectedWalletInfo(void 0,e),j.removeConnectorId(e),g.resetAccount(e),g.resetNetwork(e),this.setLoading(!1,e)}catch(s){throw this.setLoading(!1,e),new Error(`Failed to disconnect chain ${e}: ${s.message}`)}}createClients(){this.connectionControllerClient={connectWalletConnect:async()=>{var n;const e=g.state.activeChain,s=this.getAdapter(e),r=(n=this.getCaipNetwork(e))==null?void 0:n.id;if(!s)throw new Error("Adapter not found");const i=await s.connectWalletConnect(r);this.close(),this.setClientId((i==null?void 0:i.clientId)||null),F.setConnectedNamespaces([...g.state.chains.keys()]),this.chainNamespaces.forEach(o=>{j.setConnectorId(ms.CONNECTOR_TYPE_WALLET_CONNECT,o)}),await this.syncWalletConnectAccount()},connectExternal:async({id:e,info:s,type:r,provider:i,chain:n,caipNetwork:o,socialUri:a})=>{var f,m,y,b,v,C;const c=g.state.activeChain,l=n||c,u=this.getAdapter(l);if(n&&n!==c&&!o){const S=this.getCaipNetworks().find(I=>I.chainNamespace===n);S&&this.setCaipNetwork(S)}if(!u)throw new Error("Adapter not found");const h=this.getCaipNetwork(l),d=await u.connect({id:e,info:s,type:r,provider:i,socialUri:a,chainId:(o==null?void 0:o.id)||(h==null?void 0:h.id),rpcUrl:((y=(m=(f=o==null?void 0:o.rpcUrls)==null?void 0:f.default)==null?void 0:m.http)==null?void 0:y[0])||((C=(v=(b=h==null?void 0:h.rpcUrls)==null?void 0:b.default)==null?void 0:v.http)==null?void 0:C[0])});if(!d)return;F.addConnectedNamespace(l),this.syncProvider({...d,chainNamespace:l});const p=z.state.allAccounts,{accounts:w}=(p==null?void 0:p.length)>0?{accounts:[...p]}:await u.getAccounts({namespace:l,id:e});this.setAllAccounts(w,l),this.setStatus("connected",l),this.syncConnectedWalletInfo(l)},reconnectExternal:async({id:e,info:s,type:r,provider:i})=>{var a;const n=g.state.activeChain,o=this.getAdapter(n);o!=null&&o.reconnect&&(await(o==null?void 0:o.reconnect({id:e,info:s,type:r,provider:i,chainId:(a=this.getCaipNetwork())==null?void 0:a.id})),F.addConnectedNamespace(n),this.syncConnectedWalletInfo(n))},disconnect:async e=>{const s=pI(e);try{const r=await Promise.allSettled(s.map(async([n])=>this.disconnectNamespace(n)));he.resetSend(),Y.resetWcConnection(),await vi.clearSessions(),j.setFilterByNamespace(void 0);const i=r.filter(n=>n.status==="rejected");if(i.length>0)throw new Error(i.map(n=>n.reason.message).join(", "));F.deleteConnectedSocialProvider(),Pe.sendEvent({type:"track",event:"DISCONNECT_SUCCESS",properties:{namespace:e||"all"}})}catch(r){throw new Error(`Failed to disconnect chains: ${r.message}`)}},checkInstalled:e=>e?e.some(s=>{var r;return!!((r=window.ethereum)!=null&&r[String(s)])}):!!window.ethereum,signMessage:async e=>{const s=this.getAdapter(g.state.activeChain),r=await(s==null?void 0:s.signMessage({message:e,address:z.state.address,provider:Se.getProvider(g.state.activeChain)}));return(r==null?void 0:r.signature)||""},sendTransaction:async e=>{const s=e.chainNamespace;if(Ee.SEND_SUPPORTED_NAMESPACES.includes(s)){const r=this.getAdapter(g.state.activeChain),i=Se.getProvider(s),n=await(r==null?void 0:r.sendTransaction({...e,caipNetwork:this.getCaipNetwork(),provider:i}));return(n==null?void 0:n.hash)||""}return""},estimateGas:async e=>{if(e.chainNamespace===W.CHAIN.EVM){const s=this.getAdapter(g.state.activeChain),r=Se.getProvider(g.state.activeChain),i=this.getCaipNetwork();if(!i)throw new Error("CaipNetwork is undefined");const n=await(s==null?void 0:s.estimateGas({...e,provider:r,caipNetwork:i}));return(n==null?void 0:n.gas)||0n}return 0n},getEnsAvatar:async()=>{var e;return await this.syncIdentity({address:z.state.address,chainId:Number((e=this.getCaipNetwork())==null?void 0:e.id),chainNamespace:g.state.activeChain}),z.state.profileImage||!1},getEnsAddress:async e=>await Cn.resolveReownName(e),writeContract:async e=>{const s=this.getAdapter(g.state.activeChain),r=this.getCaipNetwork(),i=this.getCaipAddress(),n=Se.getProvider(g.state.activeChain);if(!r||!i)throw new Error("CaipNetwork or CaipAddress is undefined");const o=await(s==null?void 0:s.writeContract({...e,caipNetwork:r,provider:n,caipAddress:i}));return o==null?void 0:o.hash},parseUnits:(e,s)=>{const r=this.getAdapter(g.state.activeChain);return(r==null?void 0:r.parseUnits({value:e,decimals:s}))??0n},formatUnits:(e,s)=>{const r=this.getAdapter(g.state.activeChain);return(r==null?void 0:r.formatUnits({value:e,decimals:s}))??"0"},getCapabilities:async e=>{const s=this.getAdapter(g.state.activeChain);return await(s==null?void 0:s.getCapabilities(e))},grantPermissions:async e=>{const s=this.getAdapter(g.state.activeChain);return await(s==null?void 0:s.grantPermissions(e))},revokePermissions:async e=>{const s=this.getAdapter(g.state.activeChain);return s!=null&&s.revokePermissions?await s.revokePermissions(e):"0x"},walletGetAssets:async e=>{const s=this.getAdapter(g.state.activeChain);return await(s==null?void 0:s.walletGetAssets(e))??{}},updateBalance:e=>{const s=this.getCaipNetwork(e);!s||!z.state.address||this.updateNativeBalance(z.state.address,s==null?void 0:s.id,e)}},this.networkControllerClient={switchCaipNetwork:async e=>await this.switchCaipNetwork(e),getApprovedCaipNetworksData:async()=>this.getApprovedCaipNetworksData()},Y.setClient(this.connectionControllerClient)}getApprovedCaipNetworksData(){var s,r,i,n,o;if(Se.getProviderId(g.state.activeChain)===ms.CONNECTOR_TYPE_WALLET_CONNECT){const a=(r=(s=this.universalProvider)==null?void 0:s.session)==null?void 0:r.namespaces;return{supportsAllNetworks:((o=(n=(i=this.universalProvider)==null?void 0:i.session)==null?void 0:n.peer)==null?void 0:o.metadata.name)==="MetaMask Wallet",approvedCaipNetworkIds:this.getChainsFromNamespaces(a)}}return{supportsAllNetworks:!0,approvedCaipNetworkIds:[]}}async switchCaipNetwork(e){if(!e)return;const s=e.chainNamespace;if(this.getAddressByChainNamespace(e.chainNamespace)){const i=Se.getProvider(s),n=Se.getProviderId(s);if(e.chainNamespace===g.state.activeChain){const o=this.getAdapter(s);await(o==null?void 0:o.switchNetwork({caipNetwork:e,provider:i,providerType:n}))}else if(this.setCaipNetwork(e),n===ms.CONNECTOR_TYPE_WALLET_CONNECT)this.syncWalletConnectAccount();else{const o=this.getAddressByChainNamespace(s);o&&this.syncAccount({address:o,chainId:e.id,chainNamespace:s})}}else this.setCaipNetwork(e)}getChainsFromNamespaces(e={}){return Object.values(e).flatMap(s=>{const r=s.chains||[],i=s.accounts.map(n=>{const{chainId:o,chainNamespace:a}=hs.parseCaipAddress(n);return`${a}:${o}`});return Array.from(new Set([...r,...i]))})}createAdapters(e){return this.createClients(),this.chainNamespaces.reduce((s,r)=>{var n;const i=e==null?void 0:e.find(o=>o.namespace===r);return i?(i.construct({namespace:r,projectId:(n=this.options)==null?void 0:n.projectId,networks:this.getCaipNetworks()}),s[r]=i):s[r]=new BI({namespace:r,networks:this.getCaipNetworks()}),s},{})}async initChainAdapter(e){var s;this.onConnectors(e),this.listenAdapter(e),await((s=this.chainAdapters)==null?void 0:s[e].syncConnectors(this.options,this)),await this.createUniversalProviderForAdapter(e)}async initChainAdapters(){await Promise.all(this.chainNamespaces.map(async e=>{await this.initChainAdapter(e)}))}onConnectors(e){const s=this.getAdapter(e);s==null||s.on("connectors",this.setConnectors.bind(this))}listenAdapter(e){const s=this.getAdapter(e);if(!s)return;const r=F.getConnectionStatus();r==="connected"?this.setStatus("connecting",e):r==="disconnected"?(F.clearAddressCache(),this.setStatus(r,e)):this.setStatus(r,e),s.on("switchNetwork",({address:i,chainId:n})=>{const o=this.getCaipNetworks().find(l=>l.id===n||l.caipNetworkId===n),a=g.state.activeChain===e,c=g.getAccountProp("address",e);if(o){const l=a&&i?i:c;l&&this.syncAccount({address:l,chainId:o.id,chainNamespace:e})}else this.setUnsupportedNetwork(n)}),s.on("disconnect",this.disconnect.bind(this,e)),s.on("connections",i=>{this.setConnections(i,e)}),s.on("pendingTransactions",()=>{const i=z.state.address,n=g.state.activeCaipNetwork;!i||!(n!=null&&n.id)||this.updateNativeBalance(i,n.id,n.chainNamespace)}),s.on("accountChanged",({address:i,chainId:n})=>{var a,c;const o=g.state.activeChain===e;o&&n?this.syncAccount({address:i,chainId:n,chainNamespace:e}):o&&((a=g.state.activeCaipNetwork)!=null&&a.id)?this.syncAccount({address:i,chainId:(c=g.state.activeCaipNetwork)==null?void 0:c.id,chainNamespace:e}):this.syncAccountInfo(i,n,e),this.syncAllAccounts(e)})}async createUniversalProviderForAdapter(e){var s,r,i;await this.getUniversalProvider(),this.universalProvider&&((i=(r=(s=this.chainAdapters)==null?void 0:s[e])==null?void 0:r.setUniversalProvider)==null||i.call(r,this.universalProvider))}async syncExistingConnection(){await Promise.allSettled(this.chainNamespaces.map(e=>this.syncNamespaceConnection(e)))}async syncNamespaceConnection(e){try{e===W.CHAIN.EVM&&X.isSafeApp()&&j.setConnectorId(W.CONNECTOR_ID.SAFE,e);const s=j.getConnectorId(e);switch(this.setStatus("connecting",e),s){case W.CONNECTOR_ID.WALLET_CONNECT:await this.syncWalletConnectAccount();break;case W.CONNECTOR_ID.AUTH:break;default:await this.syncAdapterConnection(e)}}catch(s){console.warn("AppKit couldn't sync existing connection",s),this.setStatus("disconnected",e)}}async syncAdapterConnection(e){var a,c,l;const s=this.getAdapter(e),r=j.getConnectorId(e),i=this.getCaipNetwork(e),o=j.getConnectors(e).find(u=>u.id===r);try{if(!s||!o)throw new Error(`Adapter or connector not found for namespace ${e}`);if(!(i!=null&&i.id))throw new Error("CaipNetwork not found");const u=await(s==null?void 0:s.syncConnection({namespace:e,id:o.id,chainId:i.id,rpcUrl:(l=(c=(a=i==null?void 0:i.rpcUrls)==null?void 0:a.default)==null?void 0:c.http)==null?void 0:l[0]}));if(u){const h=await(s==null?void 0:s.getAccounts({namespace:e,id:o.id}));h&&h.accounts.length>0?this.setAllAccounts(h.accounts,e):this.setAllAccounts([X.createAccount(e,u.address,"eoa")],e),this.syncProvider({...u,chainNamespace:e}),await this.syncAccount({...u,chainNamespace:e}),this.setStatus("connected",e)}else this.setStatus("disconnected",e)}catch{this.setStatus("disconnected",e)}}async syncWalletConnectAccount(){const e=this.chainNamespaces.map(async s=>{var a,c,l,u,h;const r=this.getAdapter(s),i=((u=(l=(c=(a=this.universalProvider)==null?void 0:a.session)==null?void 0:c.namespaces)==null?void 0:l[s])==null?void 0:u.accounts)||[],n=(h=g.state.activeCaipNetwork)==null?void 0:h.id,o=i.find(d=>{const{chainId:p}=hs.parseCaipAddress(d);return p===(n==null?void 0:n.toString())})||i[0];if(o){const d=hs.validateCaipAddress(o),{chainId:p,address:w}=hs.parseCaipAddress(d);if(Se.setProviderId(s,ms.CONNECTOR_TYPE_WALLET_CONNECT),this.caipNetworks&&g.state.activeCaipNetwork&&(r==null?void 0:r.namespace)!==W.CHAIN.EVM){const f=r==null?void 0:r.getWalletConnectProvider({caipNetworks:this.getCaipNetworks(),provider:this.universalProvider,activeCaipNetwork:g.state.activeCaipNetwork});Se.setProvider(s,f)}else Se.setProvider(s,this.universalProvider);j.setConnectorId(W.CONNECTOR_ID.WALLET_CONNECT,s),F.addConnectedNamespace(s),this.syncWalletConnectAccounts(s),await this.syncAccount({address:w,chainId:p,chainNamespace:s})}else this.setStatus("disconnected",s);this.syncConnectedWalletInfo(s),await g.setApprovedCaipNetworksData(s)});await Promise.all(e)}syncWalletConnectAccounts(e){var r,i,n,o,a;const s=(a=(o=(n=(i=(r=this.universalProvider)==null?void 0:r.session)==null?void 0:i.namespaces)==null?void 0:n[e])==null?void 0:o.accounts)==null?void 0:a.map(c=>{const{address:l}=hs.parseCaipAddress(c);return l}).filter((c,l,u)=>u.indexOf(c)===l);s&&this.setAllAccounts(s.map(c=>X.createAccount(e,c,e==="bip122"?"payment":"eoa")),e)}syncProvider({type:e,provider:s,id:r,chainNamespace:i}){Se.setProviderId(i,e),Se.setProvider(i,s),j.setConnectorId(r,i)}async syncAllAccounts(e){const s=j.getConnectorId(e);if(!s)return;const r=this.getAdapter(e),i=await(r==null?void 0:r.getAccounts({namespace:e,id:s}));i&&i.accounts.length>0&&this.setAllAccounts(i.accounts,e)}async syncAccount(e){var h,d;const s=e.chainNamespace===g.state.activeChain,r=g.getCaipNetworkByNamespace(e.chainNamespace,e.chainId),{address:i,chainId:n,chainNamespace:o}=e,{chainId:a}=F.getActiveNetworkProps(),c=n||a,l=((h=g.state.activeCaipNetwork)==null?void 0:h.name)===W.UNSUPPORTED_NETWORK_NAME,u=g.getNetworkProp("supportsAllNetworks",o);if(this.setStatus("connected",o),!(l&&!u)&&c){let p=this.getCaipNetworks().find(m=>m.id.toString()===c.toString()),w=this.getCaipNetworks().find(m=>m.chainNamespace===o);if(!u&&!p&&!w){const m=this.getApprovedCaipNetworkIds()||[],y=m.find(v=>{var C;return((C=hs.parseCaipNetworkId(v))==null?void 0:C.chainId)===c.toString()}),b=m.find(v=>{var C;return((C=hs.parseCaipNetworkId(v))==null?void 0:C.chainNamespace)===o});p=this.getCaipNetworks().find(v=>v.caipNetworkId===y),w=this.getCaipNetworks().find(v=>v.caipNetworkId===b||"deprecatedCaipNetworkId"in v&&v.deprecatedCaipNetworkId===b)}const f=p||w;(f==null?void 0:f.chainNamespace)===g.state.activeChain?T.state.enableNetworkSwitch&&!T.state.allowUnsupportedChain&&((d=g.state.activeCaipNetwork)==null?void 0:d.name)===W.UNSUPPORTED_NETWORK_NAME?g.showUnsupportedChainUI():this.setCaipNetwork(f):s||r&&this.setCaipNetworkOfNamespace(r,o),this.syncConnectedWalletInfo(o),Sa.isLowerCaseMatch(i,z.state.address)||this.syncAccountInfo(i,f==null?void 0:f.id,o),s?await this.syncBalance({address:i,chainId:f==null?void 0:f.id,chainNamespace:o}):await this.syncBalance({address:i,chainId:r==null?void 0:r.id,chainNamespace:o})}}async syncAccountInfo(e,s,r){const i=this.getCaipAddress(r),n=s||(i==null?void 0:i.split(":")[1]);if(!n)return;const o=`${r}:${n}:${e}`;this.setCaipAddress(o,r),await this.syncIdentity({address:e,chainId:n,chainNamespace:r})}async syncReownName(e,s){try{const r=await this.getReownName(e);if(r[0]){const i=r[0];this.setProfileName(i.name,s)}else this.setProfileName(null,s)}catch{this.setProfileName(null,s)}}syncConnectedWalletInfo(e){var i;const s=j.getConnectorId(e),r=Se.getProviderId(e);if(r===ms.CONNECTOR_TYPE_ANNOUNCED||r===ms.CONNECTOR_TYPE_INJECTED){if(s){const n=this.getConnectors().find(o=>o.id===s);if(n){const{info:o,name:a,imageUrl:c}=n,l=c||this.getConnectorImage(n);this.setConnectedWalletInfo({name:a,icon:l,...o},e)}}}else if(r===ms.CONNECTOR_TYPE_WALLET_CONNECT){const n=Se.getProvider(e);n!=null&&n.session&&this.setConnectedWalletInfo({...n.session.peer.metadata,name:n.session.peer.metadata.name,icon:(i=n.session.peer.metadata.icons)==null?void 0:i[0]},e)}else if(s&&s===W.CONNECTOR_ID.COINBASE){const n=this.getConnectors().find(o=>o.id===W.CONNECTOR_ID.COINBASE);this.setConnectedWalletInfo({name:"Coinbase Wallet",icon:this.getConnectorImage(n)},e)}}async syncBalance(e){!tu.getNetworksByNamespace(this.getCaipNetworks(),e.chainNamespace).find(r=>{var i;return r.id.toString()===((i=e.chainId)==null?void 0:i.toString())})||!e.chainId||await this.updateNativeBalance(e.address,e.chainId,e.chainNamespace)}async ready(){await this.readyPromise}async updateNativeBalance(e,s,r){const i=this.getAdapter(r),n=g.getCaipNetworkByNamespace(r,s);if(i){const o=await i.getBalance({address:e,chainId:s,caipNetwork:n,tokens:this.options.tokens});return this.setBalance(o.balance,o.symbol,r),o}}async initializeUniversalAdapter(){var r,i,n,o,a,c,l,u,h,d;const e=gI.createLogger((p,...w)=>{p&&this.handleAlertError(p),console.error(...w)}),s={projectId:(r=this.options)==null?void 0:r.projectId,metadata:{name:(i=this.options)!=null&&i.metadata?(n=this.options)==null?void 0:n.metadata.name:"",description:(o=this.options)!=null&&o.metadata?(a=this.options)==null?void 0:a.metadata.description:"",url:(c=this.options)!=null&&c.metadata?(l=this.options)==null?void 0:l.metadata.url:"",icons:(u=this.options)!=null&&u.metadata?(h=this.options)==null?void 0:h.metadata.icons:[""]},logger:e};T.setManualWCControl(!!((d=this.options)!=null&&d.manualWCControl)),this.universalProvider=this.options.universalProvider??await dI.init(s),this.listenWalletConnect()}listenWalletConnect(){this.universalProvider&&(this.universalProvider.on("display_uri",e=>{Y.setUri(e)}),this.universalProvider.on("connect",Y.finalizeWcConnection),this.universalProvider.on("disconnect",()=>{this.chainNamespaces.forEach(e=>{this.resetAccount(e)}),Y.resetWcConnection()}),this.universalProvider.on("chainChanged",e=>{const s=this.getCaipNetworks().find(i=>i.id==e),r=this.getCaipNetwork();if(!s){this.setUnsupportedNetwork(e);return}(r==null?void 0:r.id)!==(s==null?void 0:s.id)&&this.setCaipNetwork(s)}),this.universalProvider.on("session_event",e=>{if(Cn.isSessionEventData(e)){const{name:s,data:r}=e.params.event;s==="accountsChanged"&&Array.isArray(r)&&X.isCaipAddress(r[0])&&this.syncAccount(hs.parseCaipAddress(r[0]))}}))}createUniversalProvider(){var e;return!this.universalProviderInitPromise&&X.isClient()&&((e=this.options)!=null&&e.projectId)&&(this.universalProviderInitPromise=this.initializeUniversalAdapter()),this.universalProviderInitPromise}async getUniversalProvider(){if(!this.universalProvider)try{await this.createUniversalProvider()}catch(e){Pe.sendEvent({type:"error",event:"INTERNAL_SDK_ERROR",properties:{errorType:"UniversalProviderInitError",errorMessage:e instanceof Error?e.message:"Unknown",uncaught:!1}}),console.error("AppKit:getUniversalProvider - Cannot create provider",e)}return this.universalProvider}handleAlertError(e){const s=Object.entries(Ir.UniversalProviderErrors).find(([,{message:a}])=>e.message.includes(a)),[r,i]=s??[],{message:n,alertErrorKey:o}=i??{};if(r&&n&&!this.reportedAlertErrors[r]){const a=Ir.ALERT_ERRORS[o];a&&(Ms.open(a,"error"),this.reportedAlertErrors[r]=!0)}}getAdapter(e){var s;if(e)return(s=this.chainAdapters)==null?void 0:s[e]}createAdapter(e){var i;if(!e)return;const s=e.namespace;if(!s)return;this.createClients();const r=e;r.namespace=s,r.construct({namespace:s,projectId:(i=this.options)==null?void 0:i.projectId,networks:this.getCaipNetworks()}),this.chainNamespaces.includes(s)||this.chainNamespaces.push(s),this.chainAdapters&&(this.chainAdapters[s]=r)}async open(e){if(await this.injectModalUi(),e!=null&&e.uri&&Y.setUri(e.uri),e!=null&&e.arguments)switch(e==null?void 0:e.view){case"Swap":return We.open({...e,data:{swap:e.arguments}})}return We.open(e)}async close(){await this.injectModalUi(),We.close()}setLoading(e,s){We.setLoading(e,s)}async disconnect(e){await Y.disconnect(e)}getSIWX(){return T.state.siwx}getError(){return""}getChainId(){var e;return(e=g.state.activeCaipNetwork)==null?void 0:e.id}async switchNetwork(e){const s=this.getCaipNetworks().find(r=>r.id===e.id);if(!s){Ms.open(Ir.ALERT_ERRORS.SWITCH_NETWORK_NOT_FOUND,"error");return}await g.switchActiveNetwork(s)}getWalletProvider(){return g.state.activeChain?Se.state.providers[g.state.activeChain]:null}getWalletProviderType(){return Se.getProviderId(g.state.activeChain)}subscribeProviders(e){return Se.subscribeProviders(e)}getThemeMode(){return mt.state.themeMode}getThemeVariables(){return mt.state.themeVariables}setThemeMode(e){mt.setThemeMode(e),zh(mt.state.themeMode)}setTermsConditionsUrl(e){T.setTermsConditionsUrl(e)}setPrivacyPolicyUrl(e){T.setPrivacyPolicyUrl(e)}setThemeVariables(e){mt.setThemeVariables(e),UI(mt.state.themeVariables)}subscribeTheme(e){return mt.subscribe(e)}getWalletInfo(){return z.state.connectedWalletInfo}getAccount(e){var o;const s=j.getAuthConnector(e),r=g.getAccountData(e),i=g.state.activeChain,n=F.getConnectedConnectorId(e||i);if(r)return{allAccounts:r.allAccounts,caipAddress:r.caipAddress,address:X.getPlainAddress(r.caipAddress),isConnected:!!r.caipAddress,status:r.status,embeddedWalletInfo:s&&n===W.CONNECTOR_ID.AUTH?{user:r.user?{...r.user,username:F.getConnectedSocialUsername()}:void 0,authProvider:r.socialProvider||"email",accountType:(o=r.preferredAccountTypes)==null?void 0:o[e||i],isSmartAccountDeployed:!!r.smartAccountDeployed}:void 0}}subscribeAccount(e,s){const r=()=>{const i=this.getAccount(s);i&&e(i)};s?g.subscribeChainProp("accountState",r,s):g.subscribe(r),j.subscribe(r)}subscribeNetwork(e){return g.subscribe(({activeCaipNetwork:s})=>{e({caipNetwork:s,chainId:s==null?void 0:s.id,caipNetworkId:s==null?void 0:s.caipNetworkId})})}subscribeWalletInfo(e){return z.subscribeKey("connectedWalletInfo",e)}subscribeShouldUpdateToAddress(e){z.subscribeKey("shouldUpdateToAddress",e)}subscribeCaipNetworkChange(e){g.subscribeKey("activeCaipNetwork",e)}getState(){return Is.state}subscribeState(e){return Is.subscribe(e)}showErrorMessage(e){Lt.showError(e)}showSuccessMessage(e){Lt.showSuccess(e)}getEvent(){return{...Pe.state}}subscribeEvents(e){return Pe.subscribe(e)}replace(e){re.replace(e)}redirect(e){re.push(e)}popTransactionStack(e){re.popTransactionStack(e)}isOpen(){return We.state.open}isTransactionStackEmpty(){return re.state.transactionStack.length===0}static getInstance(){return this.instance}updateFeatures(e){T.setFeatures(e)}updateRemoteFeatures(e){T.setRemoteFeatures(e)}updateOptions(e){const r={...T.state||{},...e};T.setOptions(r)}setConnectMethodsOrder(e){T.setConnectMethodsOrder(e)}setWalletFeaturesOrder(e){T.setWalletFeaturesOrder(e)}setCollapseWallets(e){T.setCollapseWallets(e)}setSocialsOrder(e){T.setSocialsOrder(e)}getConnectMethodsOrder(){return ca.getConnectOrderMethod(T.state.features,j.getConnectors())}addNetwork(e,s){if(this.chainAdapters&&!this.chainAdapters[e])throw new Error(`Adapter for namespace ${e} doesn't exist`);const r=this.extendCaipNetwork(s,this.options);this.getCaipNetworks().find(i=>i.id===r.id)||g.addNetwork(r)}removeNetwork(e,s){if(this.chainAdapters&&!this.chainAdapters[e])throw new Error(`Adapter for namespace ${e} doesn't exist`);this.getCaipNetworks().find(i=>i.id===s)&&g.removeNetwork(e,s)}}let Jl=!1;class Hh extends FI{async open(e){j.isConnected()||await super.open(e)}async close(){await super.close(),this.options.manualWCControl&&Y.finalizeWcConnection()}async syncIdentity(e){return Promise.resolve()}async syncBalance(e){return Promise.resolve()}async injectModalUi(){if(!Jl&&X.isClient()){if(await _o(()=>import("./basic-D4Ate8cw.js"),__vite__mapDeps([6,7,1,2,3,4,5])),await _o(()=>import("./w3m-modal-CGM7s3iG.js"),__vite__mapDeps([8,7,1,2,3,4,5])),!document.querySelector("w3m-modal")){const s=document.createElement("w3m-modal");!T.state.disableAppend&&!T.state.enableEmbedded&&document.body.insertAdjacentElement("beforeend",s)}Jl=!0}}}const zI="1.7.8";function WI(t){return new Hh({...t,basic:!0,sdkVersion:`html-core-${zI}`})}const P1=Object.freeze(Object.defineProperty({__proto__:null,AppKit:Hh,createAppKit:WI},Symbol.toStringTag,{value:"Module"}));export{Ms as A,Lt as B,g as C,K as D,Pe as E,A1 as F,X as G,Nf as H,W as I,Lh as J,la as K,ze as L,We as M,zr as N,T as O,bn as P,Y as Q,re as R,vi as S,mt as T,F as U,Ee as V,ca as W,P1 as X,Ya as a,I1 as b,d1 as c,gf as d,h1 as e,Un as f,g1 as g,f1 as h,Ct as i,Qe as j,xr as k,N1 as l,En as m,_1 as n,S1 as o,_e as p,nu as q,p1 as r,et as s,gu as t,Dt as u,wI as v,_f as w,C1 as x,j as y,z};
//# sourceMappingURL=core-C0olQNtY.js.map
