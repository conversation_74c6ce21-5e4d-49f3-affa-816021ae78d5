{"version": 3, "file": "checkmark-bold-DghVFx-0.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const checkmarkBoldSvg = svg `<svg fill=\"none\" viewBox=\"0 0 14 14\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M12.9576 2.23383C13.3807 2.58873 13.4361 3.21947 13.0812 3.64263L6.37159 11.6426C6.19161 11.8572 5.92989 11.9865 5.65009 11.999C5.3703 12.0115 5.09808 11.9062 4.89965 11.7085L0.979321 7.80331C0.588042 7.41354 0.586817 6.78038 0.976585 6.3891C1.36635 5.99782 1.99952 5.99659 2.3908 6.38636L5.53928 9.52268L11.5488 2.35742C11.9037 1.93426 12.5344 1.87893 12.9576 2.23383Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=checkmark-bold.js.map"], "names": ["checkmarkBoldSvg", "svg"], "mappings": "2JACO,MAAMA,EAAmBC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}