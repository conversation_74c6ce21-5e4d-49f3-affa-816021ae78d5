{"version": 3, "file": "swapHorizontal-CEKJuCHa.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const swapHorizontalSvg = svg `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M6.76.3a1 1 0 0 1 0 1.4L4.07 4.4h9a1 1 0 1 1 0 2h-9l2.69 2.68a1 1 0 1 1-1.42 1.42L.95 6.09a1 1 0 0 1 0-1.4l4.4-4.4a1 1 0 0 1 1.4 0Zm6.49 9.21a1 1 0 0 1 1.41 0l4.39 4.4a1 1 0 0 1 0 1.4l-4.39 4.4a1 1 0 0 1-1.41-1.42l2.68-2.68h-9a1 1 0 0 1 0-2h9l-2.68-2.68a1 1 0 0 1 0-1.42Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=swapHorizontal.js.map"], "names": ["swapHorizontalSvg", "svg"], "mappings": "2JACO,MAAMA,EAAoBC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}