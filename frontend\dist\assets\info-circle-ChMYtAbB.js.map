{"version": 3, "file": "info-circle-ChMYtAbB.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const infoCircleSvg = svg `<svg fill=\"none\" viewBox=\"0 0 14 15\">\n  <path\n    fill=\"currentColor\"\n    d=\"M6 10.49a1 1 0 1 0 2 0v-2a1 1 0 0 0-2 0v2ZM7 4.49a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z\"\n  />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M7 14.99a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm5-7a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=info-circle.js.map"], "names": ["infoCircleSvg", "svg"], "mappings": "2JACO,MAAMA,EAAgBC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}