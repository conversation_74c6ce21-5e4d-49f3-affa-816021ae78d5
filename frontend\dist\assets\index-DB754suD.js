import{B as Wt,_ as M,h as Qn,y as es}from"./hooks.module-Dz_XB4AG.js";import{A as ts,p as Hr,q as tt,I as ns,r as K,u as zr,v as rs,x as as,y as A,z as is,C as ss,D as os,F as cs,L as us,G as Ke,J as ds,K as ce,M as qt,N as Zn,B as E,s as $r,O as ls,P as Wr,Q as qr,R as fs,S as Ot,V as nt,W as te,X as On,Y as ps,i as Vr,Z as Kr,$ as hs,a0 as ms,a1 as Cn,a2 as Tn,a3 as ys,a4 as gs,a5 as bs,e as Me,a6 as Ct,f as Je,a7 as de,a8 as Rt,a9 as Jr,aa as Vt,ab as ws,ac as vs,ad as Xn,d as ks,ae as xs,af as Ss,ag as _s,ah as he,ai as Es,aj as <PERSON>,ak as <PERSON>,al as Ps,g as Yr,am as Os,o as er,h as Le,an as Qr,ao as Cs,t as De,ap as tr,aq as Kt,ar as Zr,as as Ts,at as nr,au as rr,av as Xr,aw as Ls}from"./index-2wea5Wgv.js";import{c as js,s as Ms,d as Ds,g as Ns}from"./index-nibyPLVP.js";function Rs(e,t){if(e.length!==t.length)throw new ts({expectedLength:e.length,givenLength:t.length});const n=[];for(let r=0;r<e.length;r++){const a=e[r],i=t[r];n.push(ea(a,i))}return Hr(n)}function ea(e,t,n=!1){if(e==="address"){const s=t;if(!tt(s))throw new ns({address:s});return K(s.toLowerCase(),{size:n?32:null})}if(e==="string")return zr(t);if(e==="bytes")return t;if(e==="bool")return K(rs(t),{size:n?32:1});const r=e.match(as);if(r){const[s,o,c="256"]=r,u=Number.parseInt(c)/8;return A(t,{size:n?32:u,signed:o==="int"})}const a=e.match(is);if(a){const[s,o]=a;if(Number.parseInt(o)!==(t.length-2)/2)throw new ss({expectedSize:Number.parseInt(o),givenSize:(t.length-2)/2});return K(t,{dir:"right",size:n?32:null})}const i=e.match(os);if(i&&Array.isArray(t)){const[s,o]=i,c=[];for(let u=0;u<t.length;u++)c.push(ea(o,t[u],!0));return c.length===0?"0x":Hr(c)}throw new cs(e)}function Bs(e){const{source:t}=e,n=new Map,r=new us(8192),a=new Map,i=({address:s,chainId:o})=>`${s}.${o}`;return{async consume({address:s,chainId:o,client:c}){const u=i({address:s,chainId:o}),d=this.get({address:s,chainId:o,client:c});this.increment({address:s,chainId:o});const l=await d;return await t.set({address:s,chainId:o},l),r.set(u,l),l},async increment({address:s,chainId:o}){const c=i({address:s,chainId:o}),u=n.get(c)??0;n.set(c,u+1)},async get({address:s,chainId:o,client:c}){const u=i({address:s,chainId:o});let d=a.get(u);return d||(d=(async()=>{try{const p=await t.get({address:s,chainId:o,client:c}),f=r.get(u)??0;return f>0&&p<=f?f+1:(r.delete(u),p)}finally{this.reset({address:s,chainId:o})}})(),a.set(u,d)),(n.get(u)??0)+await d},reset({address:s,chainId:o}){const c=i({address:s,chainId:o});n.delete(c),a.delete(c)}}}const Us="0x5792579257925792579257925792579257925792579257925792579257925792",Gs=A(0,{size:32});async function Fs(e,t){async function n(d){if(d.endsWith(Us.slice(2))){const p=qt(Zn(d,-64,-32)),f=Zn(d,0,-64).slice(2).match(/.{1,64}/g),m=await Promise.all(f.map(h=>Gs.slice(2)!==h?e.request({method:"eth_getTransactionReceipt",params:[`0x${h}`]},{dedupe:!0}):void 0)),y=m.some(h=>h===null)?100:m.every(h=>(h==null?void 0:h.status)==="0x1")?200:m.every(h=>(h==null?void 0:h.status)==="0x0")?500:600;return{atomic:!1,chainId:Ke(p),receipts:m.filter(Boolean),status:y,version:"2.0.0"}}return e.request({method:"wallet_getCallsStatus",params:[d]})}const{atomic:r=!1,chainId:a,receipts:i,version:s="2.0.0",...o}=await n(t.id),[c,u]=(()=>{const d=o.status;return d>=100&&d<200?["pending",d]:d>=200&&d<300?["success",d]:d>=300&&d<700?["failure",d]:d==="CONFIRMED"?["success",200]:d==="PENDING"?["pending",100]:[void 0,d]})();return{...o,atomic:r,chainId:a?Ke(a):void 0,receipts:(i==null?void 0:i.map(d=>({...d,blockNumber:ce(d.blockNumber),gasUsed:ce(d.gasUsed),status:ds[d.status]})))??[],statusCode:u,status:c,version:s}}class Hs extends E{constructor(t){super(`Call bundle failed with status: ${t.statusCode}`,{name:"BundleFailedError"}),Object.defineProperty(this,"result",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.result=t}}async function ta(e,t){const{id:n,pollingInterval:r=e.pollingInterval,status:a=({statusCode:y})=>y===200||y>=300,retryCount:i=4,retryDelay:s=({count:y})=>~~(1<<y)*200,timeout:o=6e4,throwOnFailure:c=!1}=t,u=$r(["waitForCallsStatus",e.uid,n]),{promise:d,resolve:l,reject:p}=ls();let f;const m=Wr(u,{resolve:l,reject:p},y=>{const h=qr(async()=>{const v=x=>{clearTimeout(f),h(),x(),m()};try{const x=await fs(async()=>{const k=await Fs(e,{id:n});if(c&&k.status==="failure")throw new Hs(k);return k},{retryCount:i,delay:s});if(!a(x))return;v(()=>y.resolve(x))}catch(x){v(()=>y.reject(x))}},{interval:r,emitOnBegin:!0});return h});return f=o?setTimeout(()=>{m(),clearTimeout(f),p(new zs({id:n}))},o):void 0,await d}class zs extends E{constructor({id:t}){super(`Timed out while waiting for call bundle with id "${t}" to be confirmed.`,{name:"WaitForCallsStatusTimeoutError"})}}async function $s(e,t){var c;const{account:n=e.account,chainId:r,nonce:a}=t;if(!n)throw new Ot({docsPath:"/docs/eip7702/prepareAuthorization"});const i=nt(n),s=(()=>{if(t.executor)return t.executor==="self"?t.executor:nt(t.executor)})(),o={address:t.contractAddress??t.address,chainId:r,nonce:a};return typeof o.chainId>"u"&&(o.chainId=((c=e.chain)==null?void 0:c.id)??await te(e,On,"getChainId")({})),typeof o.nonce>"u"&&(o.nonce=await te(e,ps,"getTransactionCount")({address:i.address,blockTag:"pending"}),(s==="self"||s!=null&&s.address&&Vr(s.address,i.address))&&(o.nonce+=1)),o}function Ws(e){const{key:t="public",name:n="Public Client"}=e;return Kr({...e,key:t,name:n,type:"publicClient"}).extend(hs)}function qs(e){const{r:t,s:n}=ms.Signature.fromCompact(e.slice(2,130)),r=+`0x${e.slice(130)}`,[a,i]=(()=>{if(r===0||r===1)return[void 0,r];if(r===27)return[BigInt(r),0];if(r===28)return[BigInt(r),1];throw new Error("Invalid yParityOrV value")})();return typeof a<"u"?{r:A(t,{size:32}),s:A(n,{size:32}),v:a,yParity:i}:{r:A(t,{size:32}),s:A(n,{size:32}),yParity:i}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const na={p:BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff"),n:BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"),h:BigInt(1),a:BigInt("0xffffffff00000001000000000000000000000000fffffffffffffffffffffffc"),b:BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"),Gx:BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"),Gy:BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5")},ra={p:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff"),n:BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973"),h:BigInt(1),a:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffc"),b:BigInt("0xb3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef"),Gx:BigInt("0xaa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7"),Gy:BigInt("0x3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f")},aa={p:BigInt("0x1ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),n:BigInt("0x01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409"),h:BigInt(1),a:BigInt("0x1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc"),b:BigInt("0x0051953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00"),Gx:BigInt("0x00c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66"),Gy:BigInt("0x011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650")},Vs=Tn(na.p),Ks=Tn(ra.p),Js=Tn(aa.p),Ys=Cn({...na,Fp:Vs,lowS:!1},ys);Cn({...ra,Fp:Ks,lowS:!1},gs);Cn({...aa,Fp:Js,lowS:!1,allowedPrivateKeyLengths:[130,131,132]},bs);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const ar=Ys;function ia(e,t){let n;try{n=e()}catch{return}return{getItem:a=>{var i;const s=c=>c===null?null:JSON.parse(c,void 0),o=(i=n.getItem(a))!=null?i:null;return o instanceof Promise?o.then(s):s(o)},setItem:(a,i)=>n.setItem(a,JSON.stringify(i,void 0)),removeItem:a=>n.removeItem(a)}}const Jt=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return Jt(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Jt(r)(n)}}}},Qs=(e,t)=>(n,r,a)=>{let i={storage:ia(()=>localStorage),partialize:y=>y,version:0,merge:(y,h)=>({...h,...y}),...t},s=!1;const o=new Set,c=new Set;let u=i.storage;if(!u)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...y)},r,a);const d=()=>{const y=i.partialize({...r()});return u.setItem(i.name,{state:y,version:i.version})},l=a.setState;a.setState=(y,h)=>{l(y,h),d()};const p=e((...y)=>{n(...y),d()},r,a);a.getInitialState=()=>p;let f;const m=()=>{var y,h;if(!u)return;s=!1,o.forEach(x=>{var k;return x((k=r())!=null?k:p)});const v=((h=i.onRehydrateStorage)==null?void 0:h.call(i,(y=r())!=null?y:p))||void 0;return Jt(u.getItem.bind(u))(i.name).then(x=>{if(x)if(typeof x.version=="number"&&x.version!==i.version){if(i.migrate){const k=i.migrate(x.state,x.version);return k instanceof Promise?k.then(S=>[!0,S]):[!0,k]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,x.state];return[!1,void 0]}).then(x=>{var k;const[S,j]=x;if(f=i.merge(j,(k=r())!=null?k:p),n(f,!0),S)return d()}).then(()=>{v==null||v(f,void 0),f=r(),s=!0,c.forEach(x=>x(f))}).catch(x=>{v==null||v(void 0,x)})};return a.persist={setOptions:y=>{i={...i,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>m(),hasHydrated:()=>s,onHydrate:y=>(o.add(y),()=>{o.delete(y)}),onFinishHydration:y=>(c.add(y),()=>{c.delete(y)})},i.skipHydration||m(),f||p},Zs=Qs,ir=e=>{let t;const n=new Set,r=(u,d)=>{const l=typeof u=="function"?u(t):u;if(!Object.is(l,t)){const p=t;t=d??(typeof l!="object"||l===null)?l:Object.assign({},t,l),n.forEach(f=>f(t,p))}},a=()=>t,o={setState:r,getState:a,getInitialState:()=>c,subscribe:u=>(n.add(u),()=>n.delete(u))},c=t=e(r,a,o);return o},Ln=e=>e?ir(e):ir,Xs=[{inputs:[{name:"preOpGas",type:"uint256"},{name:"paid",type:"uint256"},{name:"validAfter",type:"uint48"},{name:"validUntil",type:"uint48"},{name:"targetSuccess",type:"bool"},{name:"targetResult",type:"bytes"}],name:"ExecutionResult",type:"error"},{inputs:[{name:"opIndex",type:"uint256"},{name:"reason",type:"string"}],name:"FailedOp",type:"error"},{inputs:[{name:"sender",type:"address"}],name:"SenderAddressResult",type:"error"},{inputs:[{name:"aggregator",type:"address"}],name:"SignatureValidationFailed",type:"error"},{inputs:[{components:[{name:"preOpGas",type:"uint256"},{name:"prefund",type:"uint256"},{name:"sigFailed",type:"bool"},{name:"validAfter",type:"uint48"},{name:"validUntil",type:"uint48"},{name:"paymasterContext",type:"bytes"}],name:"returnInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"senderInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"factoryInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"paymasterInfo",type:"tuple"}],name:"ValidationResult",type:"error"},{inputs:[{components:[{name:"preOpGas",type:"uint256"},{name:"prefund",type:"uint256"},{name:"sigFailed",type:"bool"},{name:"validAfter",type:"uint48"},{name:"validUntil",type:"uint48"},{name:"paymasterContext",type:"bytes"}],name:"returnInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"senderInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"factoryInfo",type:"tuple"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"paymasterInfo",type:"tuple"},{components:[{name:"aggregator",type:"address"},{components:[{name:"stake",type:"uint256"},{name:"unstakeDelaySec",type:"uint256"}],name:"stakeInfo",type:"tuple"}],name:"aggregatorInfo",type:"tuple"}],name:"ValidationResultWithAggregation",type:"error"},{anonymous:!1,inputs:[{indexed:!0,name:"userOpHash",type:"bytes32"},{indexed:!0,name:"sender",type:"address"},{indexed:!1,name:"factory",type:"address"},{indexed:!1,name:"paymaster",type:"address"}],name:"AccountDeployed",type:"event"},{anonymous:!1,inputs:[],name:"BeforeExecution",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"account",type:"address"},{indexed:!1,name:"totalDeposit",type:"uint256"}],name:"Deposited",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"aggregator",type:"address"}],name:"SignatureAggregatorChanged",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"account",type:"address"},{indexed:!1,name:"totalStaked",type:"uint256"},{indexed:!1,name:"unstakeDelaySec",type:"uint256"}],name:"StakeLocked",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"account",type:"address"},{indexed:!1,name:"withdrawTime",type:"uint256"}],name:"StakeUnlocked",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"account",type:"address"},{indexed:!1,name:"withdrawAddress",type:"address"},{indexed:!1,name:"amount",type:"uint256"}],name:"StakeWithdrawn",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"userOpHash",type:"bytes32"},{indexed:!0,name:"sender",type:"address"},{indexed:!0,name:"paymaster",type:"address"},{indexed:!1,name:"nonce",type:"uint256"},{indexed:!1,name:"success",type:"bool"},{indexed:!1,name:"actualGasCost",type:"uint256"},{indexed:!1,name:"actualGasUsed",type:"uint256"}],name:"UserOperationEvent",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"userOpHash",type:"bytes32"},{indexed:!0,name:"sender",type:"address"},{indexed:!1,name:"nonce",type:"uint256"},{indexed:!1,name:"revertReason",type:"bytes"}],name:"UserOperationRevertReason",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"account",type:"address"},{indexed:!1,name:"withdrawAddress",type:"address"},{indexed:!1,name:"amount",type:"uint256"}],name:"Withdrawn",type:"event"},{inputs:[],name:"SIG_VALIDATION_FAILED",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"initCode",type:"bytes"},{name:"sender",type:"address"},{name:"paymasterAndData",type:"bytes"}],name:"_validateSenderAndPaymaster",outputs:[],stateMutability:"view",type:"function"},{inputs:[{name:"unstakeDelaySec",type:"uint32"}],name:"addStake",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{name:"account",type:"address"}],name:"balanceOf",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"account",type:"address"}],name:"depositTo",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{name:"",type:"address"}],name:"deposits",outputs:[{name:"deposit",type:"uint112"},{name:"staked",type:"bool"},{name:"stake",type:"uint112"},{name:"unstakeDelaySec",type:"uint32"},{name:"withdrawTime",type:"uint48"}],stateMutability:"view",type:"function"},{inputs:[{name:"account",type:"address"}],name:"getDepositInfo",outputs:[{components:[{name:"deposit",type:"uint112"},{name:"staked",type:"bool"},{name:"stake",type:"uint112"},{name:"unstakeDelaySec",type:"uint32"},{name:"withdrawTime",type:"uint48"}],name:"info",type:"tuple"}],stateMutability:"view",type:"function"},{inputs:[{name:"sender",type:"address"},{name:"key",type:"uint192"}],name:"getNonce",outputs:[{name:"nonce",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"initCode",type:"bytes"}],name:"getSenderAddress",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"userOp",type:"tuple"}],name:"getUserOpHash",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"},{inputs:[{components:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"userOps",type:"tuple[]"},{name:"aggregator",type:"address"},{name:"signature",type:"bytes"}],name:"opsPerAggregator",type:"tuple[]"},{name:"beneficiary",type:"address"}],name:"handleAggregatedOps",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"ops",type:"tuple[]"},{name:"beneficiary",type:"address"}],name:"handleOps",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"key",type:"uint192"}],name:"incrementNonce",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"callData",type:"bytes"},{components:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"paymaster",type:"address"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"}],name:"mUserOp",type:"tuple"},{name:"userOpHash",type:"bytes32"},{name:"prefund",type:"uint256"},{name:"contextOffset",type:"uint256"},{name:"preOpGas",type:"uint256"}],name:"opInfo",type:"tuple"},{name:"context",type:"bytes"}],name:"innerHandleOp",outputs:[{name:"actualGasCost",type:"uint256"}],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"",type:"address"},{name:"",type:"uint192"}],name:"nonceSequenceNumber",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"op",type:"tuple"},{name:"target",type:"address"},{name:"targetCallData",type:"bytes"}],name:"simulateHandleOp",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"userOp",type:"tuple"}],name:"simulateValidation",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[],name:"unlockStake",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"withdrawAddress",type:"address"}],name:"withdrawStake",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"withdrawAddress",type:"address"},{name:"withdrawAmount",type:"uint256"}],name:"withdrawTo",outputs:[],stateMutability:"nonpayable",type:"function"},{stateMutability:"payable",type:"receive"}];function sa(e){const{authorization:t,factory:n,factoryData:r}=e;if(n==="0x7702"||n==="0x7702000000000000000000000000000000000000"){if(!t)return"0x7702000000000000000000000000000000000000";const a=t.address;return Me([a,r??"0x"])}return n?Me([n,r??"0x"]):"0x"}function oa(e){const{callGasLimit:t,callData:n,maxPriorityFeePerGas:r,maxFeePerGas:a,paymaster:i,paymasterData:s,paymasterPostOpGasLimit:o,paymasterVerificationGasLimit:c,sender:u,signature:d="0x",verificationGasLimit:l}=e,p=Me([K(A(l||0n),{size:16}),K(A(t||0n),{size:16})]),f=sa(e),m=Me([K(A(r||0n),{size:16}),K(A(a||0n),{size:16})]),y=e.nonce??0n,h=i?Me([i,K(A(c||0n),{size:16}),K(A(o||0n),{size:16}),s||"0x"]):"0x",v=e.preVerificationGas??0n;return{accountGasLimits:p,callData:n,initCode:f,gasFees:m,nonce:y,paymasterAndData:h,preVerificationGas:v,sender:u,signature:d}}const eo={PackedUserOperation:[{type:"address",name:"sender"},{type:"uint256",name:"nonce"},{type:"bytes",name:"initCode"},{type:"bytes",name:"callData"},{type:"bytes32",name:"accountGasLimits"},{type:"uint256",name:"preVerificationGas"},{type:"bytes32",name:"gasFees"},{type:"bytes",name:"paymasterAndData"}]};function to(e){const{chainId:t,entryPointAddress:n,userOperation:r}=e,a=oa(r);return{types:eo,primaryType:"PackedUserOperation",domain:{name:"ERC4337",version:"1",chainId:t,verifyingContract:n},message:a}}function no(e){const{chainId:t,entryPointAddress:n,entryPointVersion:r}=e,a=e.userOperation,{authorization:i,callData:s="0x",callGasLimit:o,maxFeePerGas:c,maxPriorityFeePerGas:u,nonce:d,paymasterAndData:l="0x",preVerificationGas:p,sender:f,verificationGasLimit:m}=a;if(r==="0.8")return Ct(to({chainId:t,entryPointAddress:n,userOperation:a}));const y=(()=>{var h,v;if(r==="0.6"){const x=(h=a.initCode)==null?void 0:h.slice(0,42),k=(v=a.initCode)==null?void 0:v.slice(42),S=sa({authorization:i,factory:x,factoryData:k});return Je([{type:"address"},{type:"uint256"},{type:"bytes32"},{type:"bytes32"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"bytes32"}],[f,d,de(S),de(s),o,m,p,c,u,de(l)])}if(r==="0.7"){const x=oa(a);return Je([{type:"address"},{type:"uint256"},{type:"bytes32"},{type:"bytes32"},{type:"bytes32"},{type:"uint256"},{type:"bytes32"},{type:"bytes32"}],[x.sender,x.nonce,de(x.initCode),de(x.callData),x.accountGasLimits,x.preVerificationGas,x.gasFees,de(x.paymasterAndData)])}throw new Error(`entryPointVersion "${r}" not supported.`)})();return de(Je([{type:"bytes32"},{type:"address"},{type:"uint256"}],[de(y),n,BigInt(t)]))}async function ro(e){const{extend:t,nonceKeyManager:n=Bs({source:{get(){return Date.now()},set(){}}}),...r}=e;let a=!1;const i=await e.getAddress();return{...t,...r,address:i,async getFactoryArgs(){return"isDeployed"in this&&await this.isDeployed()?{factory:void 0,factoryData:void 0}:e.getFactoryArgs()},async getNonce(s){const o=(s==null?void 0:s.key)??BigInt(await n.consume({address:i,chainId:e.client.chain.id,client:e.client}));return e.getNonce?await e.getNonce({...s,key:o}):await Vt(e.client,{abi:ws(["function getNonce(address, uint192) pure returns (uint256)"]),address:e.entryPoint.address,functionName:"getNonce",args:[i,o]})},async isDeployed(){return a?!0:(a=!!await te(e.client,Jr,"getCode")({address:i}),a)},...e.sign?{async sign(s){const[{factory:o,factoryData:c},u]=await Promise.all([this.getFactoryArgs(),e.sign(s)]);return o&&c?Rt({address:o,data:c,signature:u}):u}}:{},async signMessage(s){const[{factory:o,factoryData:c},u]=await Promise.all([this.getFactoryArgs(),e.signMessage(s)]);return o&&c&&o!=="0x7702"?Rt({address:o,data:c,signature:u}):u},async signTypedData(s){const[{factory:o,factoryData:c},u]=await Promise.all([this.getFactoryArgs(),e.signTypedData(s)]);return o&&c&&o!=="0x7702"?Rt({address:o,data:c,signature:u}):u},type:"smart"}}class Yt extends E{constructor({cause:t}){super("Smart Account is not deployed.",{cause:t,metaMessages:["This could arise when:","- No `factory`/`factoryData` or `initCode` properties are provided for Smart Account deployment.","- An incorrect `sender` address is provided."],name:"AccountNotDeployedError"})}}Object.defineProperty(Yt,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa20/});class Ue extends E{constructor({cause:t,data:n,message:r}={}){var i;const a=(i=r==null?void 0:r.replace("execution reverted: ",""))==null?void 0:i.replace("execution reverted","");super(`Execution reverted ${a?`with reason: ${a}`:"for an unknown reason"}.`,{cause:t,name:"ExecutionRevertedError"}),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=n}}Object.defineProperty(Ue,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32521});Object.defineProperty(Ue,"message",{enumerable:!0,configurable:!0,writable:!0,value:/execution reverted/});class Qt extends E{constructor({cause:t}){super("Failed to send funds to beneficiary.",{cause:t,name:"FailedToSendToBeneficiaryError"})}}Object.defineProperty(Qt,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa91/});class Zt extends E{constructor({cause:t}){super("Gas value overflowed.",{cause:t,metaMessages:["This could arise when:","- one of the gas values exceeded 2**120 (uint120)"].filter(Boolean),name:"GasValuesOverflowError"})}}Object.defineProperty(Zt,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa94/});class Xt extends E{constructor({cause:t}){super("The `handleOps` function was called by the Bundler with a gas limit too low.",{cause:t,name:"HandleOpsOutOfGasError"})}}Object.defineProperty(Xt,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa95/});class en extends E{constructor({cause:t,factory:n,factoryData:r,initCode:a}){super("Failed to simulate deployment for Smart Account.",{cause:t,metaMessages:["This could arise when:","- Invalid `factory`/`factoryData` or `initCode` properties are present","- Smart Account deployment execution ran out of gas (low `verificationGasLimit` value)",`- Smart Account deployment execution reverted with an error
`,n&&`factory: ${n}`,r&&`factoryData: ${r}`,a&&`initCode: ${a}`].filter(Boolean),name:"InitCodeFailedError"})}}Object.defineProperty(en,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa13/});class tn extends E{constructor({cause:t,factory:n,factoryData:r,initCode:a}){super("Smart Account initialization implementation did not create an account.",{cause:t,metaMessages:["This could arise when:","- `factory`/`factoryData` or `initCode` properties are invalid",`- Smart Account initialization implementation is incorrect
`,n&&`factory: ${n}`,r&&`factoryData: ${r}`,a&&`initCode: ${a}`].filter(Boolean),name:"InitCodeMustCreateSenderError"})}}Object.defineProperty(tn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa15/});class nn extends E{constructor({cause:t,factory:n,factoryData:r,initCode:a,sender:i}){super("Smart Account initialization implementation does not return the expected sender.",{cause:t,metaMessages:["This could arise when:",`Smart Account initialization implementation does not return a sender address
`,n&&`factory: ${n}`,r&&`factoryData: ${r}`,a&&`initCode: ${a}`,i&&`sender: ${i}`].filter(Boolean),name:"InitCodeMustReturnSenderError"})}}Object.defineProperty(nn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa14/});class rn extends E{constructor({cause:t}){super("Smart Account does not have sufficient funds to execute the User Operation.",{cause:t,metaMessages:["This could arise when:","- the Smart Account does not have sufficient funds to cover the required prefund, or","- a Paymaster was not provided"].filter(Boolean),name:"InsufficientPrefundError"})}}Object.defineProperty(rn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa21/});class an extends E{constructor({cause:t}){super("Bundler attempted to call an invalid function on the EntryPoint.",{cause:t,name:"InternalCallOnlyError"})}}Object.defineProperty(an,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa92/});class sn extends E{constructor({cause:t}){super("Bundler used an invalid aggregator for handling aggregated User Operations.",{cause:t,name:"InvalidAggregatorError"})}}Object.defineProperty(sn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa96/});class on extends E{constructor({cause:t,nonce:n}){super("Invalid Smart Account nonce used for User Operation.",{cause:t,metaMessages:[n&&`nonce: ${n}`].filter(Boolean),name:"InvalidAccountNonceError"})}}Object.defineProperty(on,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa25/});class cn extends E{constructor({cause:t}){super("Bundler has not set a beneficiary address.",{cause:t,name:"InvalidBeneficiaryError"})}}Object.defineProperty(cn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa90/});class bt extends E{constructor({cause:t}){super("Invalid fields set on User Operation.",{cause:t,name:"InvalidFieldsError"})}}Object.defineProperty(bt,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32602});class un extends E{constructor({cause:t,paymasterAndData:n}){super("Paymaster properties provided are invalid.",{cause:t,metaMessages:["This could arise when:","- the `paymasterAndData` property is of an incorrect length\n",n&&`paymasterAndData: ${n}`].filter(Boolean),name:"InvalidPaymasterAndDataError"})}}Object.defineProperty(un,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa93/});class Pe extends E{constructor({cause:t}){super("Paymaster deposit for the User Operation is too low.",{cause:t,metaMessages:["This could arise when:","- the Paymaster has deposited less than the expected amount via the `deposit` function"].filter(Boolean),name:"PaymasterDepositTooLowError"})}}Object.defineProperty(Pe,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32508});Object.defineProperty(Pe,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa31/});class dn extends E{constructor({cause:t}){super("The `validatePaymasterUserOp` function on the Paymaster reverted.",{cause:t,name:"PaymasterFunctionRevertedError"})}}Object.defineProperty(dn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa33/});class ln extends E{constructor({cause:t}){super("The Paymaster contract has not been deployed.",{cause:t,name:"PaymasterNotDeployedError"})}}Object.defineProperty(ln,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa30/});class wt extends E{constructor({cause:t}){super("UserOperation rejected because paymaster (or signature aggregator) is throttled/banned.",{cause:t,name:"PaymasterRateLimitError"})}}Object.defineProperty(wt,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32504});class vt extends E{constructor({cause:t}){super("UserOperation rejected because paymaster (or signature aggregator) is throttled/banned.",{cause:t,name:"PaymasterStakeTooLowError"})}}Object.defineProperty(vt,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32505});class fn extends E{constructor({cause:t}){super("Paymaster `postOp` function reverted.",{cause:t,name:"PaymasterPostOpFunctionRevertedError"})}}Object.defineProperty(fn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa50/});class pn extends E{constructor({cause:t,factory:n,factoryData:r,initCode:a}){super("Smart Account has already been deployed.",{cause:t,metaMessages:["Remove the following properties and try again:",n&&"`factory`",r&&"`factoryData`",a&&"`initCode`"].filter(Boolean),name:"SenderAlreadyConstructedError"})}}Object.defineProperty(pn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa10/});class kt extends E{constructor({cause:t}){super("UserOperation rejected because account signature check failed (or paymaster signature, if the paymaster uses its data as signature).",{cause:t,name:"SignatureCheckFailedError"})}}Object.defineProperty(kt,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32507});class hn extends E{constructor({cause:t}){super("The `validateUserOp` function on the Smart Account reverted.",{cause:t,name:"SmartAccountFunctionRevertedError"})}}Object.defineProperty(hn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa23/});class xt extends E{constructor({cause:t}){super("UserOperation rejected because account specified unsupported signature aggregator.",{cause:t,name:"UnsupportedSignatureAggregatorError"})}}Object.defineProperty(xt,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32506});class mn extends E{constructor({cause:t}){super("User Operation expired.",{cause:t,metaMessages:["This could arise when:","- the `validAfter` or `validUntil` values returned from `validateUserOp` on the Smart Account are not satisfied"].filter(Boolean),name:"UserOperationExpiredError"})}}Object.defineProperty(mn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa22/});class yn extends E{constructor({cause:t}){super("Paymaster for User Operation expired.",{cause:t,metaMessages:["This could arise when:","- the `validAfter` or `validUntil` values returned from `validatePaymasterUserOp` on the Paymaster are not satisfied"].filter(Boolean),name:"UserOperationPaymasterExpiredError"})}}Object.defineProperty(yn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa32/});class gn extends E{constructor({cause:t}){super("Signature provided for the User Operation is invalid.",{cause:t,metaMessages:["This could arise when:","- the `signature` for the User Operation is incorrectly computed, and unable to be verified by the Smart Account"].filter(Boolean),name:"UserOperationSignatureError"})}}Object.defineProperty(gn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa24/});class bn extends E{constructor({cause:t}){super("Signature provided for the User Operation is invalid.",{cause:t,metaMessages:["This could arise when:","- the `signature` for the User Operation is incorrectly computed, and unable to be verified by the Paymaster"].filter(Boolean),name:"UserOperationPaymasterSignatureError"})}}Object.defineProperty(bn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa34/});class St extends E{constructor({cause:t}){super("User Operation rejected by EntryPoint's `simulateValidation` during account creation or validation.",{cause:t,name:"UserOperationRejectedByEntryPointError"})}}Object.defineProperty(St,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32500});class _t extends E{constructor({cause:t}){super("User Operation rejected by Paymaster's `validatePaymasterUserOp`.",{cause:t,name:"UserOperationRejectedByPaymasterError"})}}Object.defineProperty(_t,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32501});class Et extends E{constructor({cause:t}){super("User Operation rejected with op code validation error.",{cause:t,name:"UserOperationRejectedByOpCodeError"})}}Object.defineProperty(Et,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32502});class It extends E{constructor({cause:t}){super("UserOperation out of time-range: either wallet or paymaster returned a time-range, and it is already expired (or will expire soon).",{cause:t,name:"UserOperationOutOfTimeRangeError"})}}Object.defineProperty(It,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32503});class ao extends E{constructor({cause:t}){super(`An error occurred while executing user operation: ${t==null?void 0:t.shortMessage}`,{cause:t,name:"UnknownBundlerError"})}}class wn extends E{constructor({cause:t}){super("User Operation verification gas limit exceeded.",{cause:t,metaMessages:["This could arise when:","- the gas used for verification exceeded the `verificationGasLimit`"].filter(Boolean),name:"VerificationGasLimitExceededError"})}}Object.defineProperty(wn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa40/});class vn extends E{constructor({cause:t}){super("User Operation verification gas limit is too low.",{cause:t,metaMessages:["This could arise when:","- the `verificationGasLimit` is too low to verify the User Operation"].filter(Boolean),name:"VerificationGasLimitTooLowError"})}}Object.defineProperty(vn,"message",{enumerable:!0,configurable:!0,writable:!0,value:/aa41/});class io extends E{constructor(t,{callData:n,callGasLimit:r,docsPath:a,factory:i,factoryData:s,initCode:o,maxFeePerGas:c,maxPriorityFeePerGas:u,nonce:d,paymaster:l,paymasterAndData:p,paymasterData:f,paymasterPostOpGasLimit:m,paymasterVerificationGasLimit:y,preVerificationGas:h,sender:v,signature:x,verificationGasLimit:k}){const S=vs({callData:n,callGasLimit:r,factory:i,factoryData:s,initCode:o,maxFeePerGas:typeof c<"u"&&`${Xn(c)} gwei`,maxPriorityFeePerGas:typeof u<"u"&&`${Xn(u)} gwei`,nonce:d,paymaster:l,paymasterAndData:p,paymasterData:f,paymasterPostOpGasLimit:m,paymasterVerificationGasLimit:y,preVerificationGas:h,sender:v,signature:x,verificationGasLimit:k});super(t.shortMessage,{cause:t,docsPath:a,metaMessages:[...t.metaMessages?[...t.metaMessages," "]:[],"Request Arguments:",S].filter(Boolean),name:"UserOperationExecutionError"}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cause=t}}class so extends E{constructor({hash:t}){super(`User Operation receipt with hash "${t}" could not be found. The User Operation may not have been processed yet.`,{name:"UserOperationReceiptNotFoundError"})}}class oo extends E{constructor({hash:t}){super(`User Operation with hash "${t}" could not be found.`,{name:"UserOperationNotFoundError"})}}class sr extends E{constructor({hash:t}){super(`Timed out while waiting for User Operation with hash "${t}" to be confirmed.`,{name:"WaitForUserOperationReceiptTimeoutError"})}}const co=[Ue,bt,Pe,wt,vt,kt,xt,It,St,_t,Et];function uo(e,t){const n=(e.details||"").toLowerCase();if(Yt.message.test(n))return new Yt({cause:e});if(Qt.message.test(n))return new Qt({cause:e});if(Zt.message.test(n))return new Zt({cause:e});if(Xt.message.test(n))return new Xt({cause:e});if(en.message.test(n))return new en({cause:e,factory:t.factory,factoryData:t.factoryData,initCode:t.initCode});if(tn.message.test(n))return new tn({cause:e,factory:t.factory,factoryData:t.factoryData,initCode:t.initCode});if(nn.message.test(n))return new nn({cause:e,factory:t.factory,factoryData:t.factoryData,initCode:t.initCode,sender:t.sender});if(rn.message.test(n))return new rn({cause:e});if(an.message.test(n))return new an({cause:e});if(on.message.test(n))return new on({cause:e,nonce:t.nonce});if(sn.message.test(n))return new sn({cause:e});if(cn.message.test(n))return new cn({cause:e});if(un.message.test(n))return new un({cause:e});if(Pe.message.test(n))return new Pe({cause:e});if(dn.message.test(n))return new dn({cause:e});if(ln.message.test(n))return new ln({cause:e});if(fn.message.test(n))return new fn({cause:e});if(hn.message.test(n))return new hn({cause:e});if(pn.message.test(n))return new pn({cause:e,factory:t.factory,factoryData:t.factoryData,initCode:t.initCode});if(mn.message.test(n))return new mn({cause:e});if(yn.message.test(n))return new yn({cause:e});if(bn.message.test(n))return new bn({cause:e});if(gn.message.test(n))return new gn({cause:e});if(wn.message.test(n))return new wn({cause:e});if(vn.message.test(n))return new vn({cause:e});const r=e.walk(a=>co.some(i=>i.code===a.code));if(r){if(r.code===Ue.code)return new Ue({cause:e,data:r.data,message:r.details});if(r.code===bt.code)return new bt({cause:e});if(r.code===Pe.code)return new Pe({cause:e});if(r.code===wt.code)return new wt({cause:e});if(r.code===vt.code)return new vt({cause:e});if(r.code===kt.code)return new kt({cause:e});if(r.code===xt.code)return new xt({cause:e});if(r.code===It.code)return new It({cause:e});if(r.code===St.code)return new St({cause:e});if(r.code===_t.code)return new _t({cause:e});if(r.code===Et.code)return new Et({cause:e})}return new ao({cause:e})}function ca(e,{calls:t,docsPath:n,...r}){const a=(()=>{const i=uo(e,r);if(t&&i instanceof Ue){const s=lo(i),o=t==null?void 0:t.filter(c=>c.abi);if(s&&o.length>0)return fo({calls:o,revertData:s})}return i})();return new io(a,{docsPath:n,...r})}function lo(e){let t;return e.walk(n=>{var a,i,s,o;const r=n;if(typeof r.data=="string"||typeof((a=r.data)==null?void 0:a.revertData)=="string"||!(r instanceof E)&&typeof r.message=="string"){const c=(o=(s=((i=r.data)==null?void 0:i.revertData)||r.data||r.message).match)==null?void 0:o.call(s,/(0x[A-Za-z0-9]*)/);if(c)return t=c[1],!0}return!1}),t}function fo(e){const{calls:t,revertData:n}=e,{abi:r,functionName:a,args:i,to:s}=(()=>{const c=t==null?void 0:t.filter(d=>!!d.abi);if(c.length===1)return c[0];const u=c.filter(d=>{try{return!!ks({abi:d.abi,data:n})}catch{return!1}});return u.length===1?u[0]:{abi:[],functionName:c.reduce((d,l)=>`${d?`${d} | `:""}${l.functionName}`,""),args:void 0,to:void 0}})(),o=n==="0x"?new xs({functionName:a}):new Ss({abi:r,data:n,functionName:a});return new _s(o,{abi:r,args:i,contractAddress:s,functionName:a})}function po(e){const t={};return e.callGasLimit&&(t.callGasLimit=BigInt(e.callGasLimit)),e.preVerificationGas&&(t.preVerificationGas=BigInt(e.preVerificationGas)),e.verificationGasLimit&&(t.verificationGasLimit=BigInt(e.verificationGasLimit)),e.paymasterPostOpGasLimit&&(t.paymasterPostOpGasLimit=BigInt(e.paymasterPostOpGasLimit)),e.paymasterVerificationGasLimit&&(t.paymasterVerificationGasLimit=BigInt(e.paymasterVerificationGasLimit)),t}function Tt(e){const t={};return typeof e.callData<"u"&&(t.callData=e.callData),typeof e.callGasLimit<"u"&&(t.callGasLimit=A(e.callGasLimit)),typeof e.factory<"u"&&(t.factory=e.factory),typeof e.factoryData<"u"&&(t.factoryData=e.factoryData),typeof e.initCode<"u"&&(t.initCode=e.initCode),typeof e.maxFeePerGas<"u"&&(t.maxFeePerGas=A(e.maxFeePerGas)),typeof e.maxPriorityFeePerGas<"u"&&(t.maxPriorityFeePerGas=A(e.maxPriorityFeePerGas)),typeof e.nonce<"u"&&(t.nonce=A(e.nonce)),typeof e.paymaster<"u"&&(t.paymaster=e.paymaster),typeof e.paymasterAndData<"u"&&(t.paymasterAndData=e.paymasterAndData||"0x"),typeof e.paymasterData<"u"&&(t.paymasterData=e.paymasterData),typeof e.paymasterPostOpGasLimit<"u"&&(t.paymasterPostOpGasLimit=A(e.paymasterPostOpGasLimit)),typeof e.paymasterVerificationGasLimit<"u"&&(t.paymasterVerificationGasLimit=A(e.paymasterVerificationGasLimit)),typeof e.preVerificationGas<"u"&&(t.preVerificationGas=A(e.preVerificationGas)),typeof e.sender<"u"&&(t.sender=e.sender),typeof e.signature<"u"&&(t.signature=e.signature),typeof e.verificationGasLimit<"u"&&(t.verificationGasLimit=A(e.verificationGasLimit)),typeof e.authorization<"u"&&(t.eip7702Auth=ho(e.authorization)),t}function ho(e){return{address:e.address,chainId:A(e.chainId),nonce:A(e.nonce),r:e.r?A(BigInt(e.r),{size:32}):K("0x",{size:32}),s:e.s?A(BigInt(e.s),{size:32}):K("0x",{size:32}),yParity:e.yParity?A(e.yParity,{size:1}):K("0x",{size:32})}}async function mo(e,t){const{chainId:n,entryPointAddress:r,context:a,...i}=t,s=Tt(i),{paymasterPostOpGasLimit:o,paymasterVerificationGasLimit:c,...u}=await e.request({method:"pm_getPaymasterData",params:[{...s,callGasLimit:s.callGasLimit??"0x0",verificationGasLimit:s.verificationGasLimit??"0x0",preVerificationGas:s.preVerificationGas??"0x0"},r,A(n),a]});return{...u,...o&&{paymasterPostOpGasLimit:ce(o)},...c&&{paymasterVerificationGasLimit:ce(c)}}}async function yo(e,t){const{chainId:n,entryPointAddress:r,context:a,...i}=t,s=Tt(i),{paymasterPostOpGasLimit:o,paymasterVerificationGasLimit:c,...u}=await e.request({method:"pm_getPaymasterStubData",params:[{...s,callGasLimit:s.callGasLimit??"0x0",verificationGasLimit:s.verificationGasLimit??"0x0",preVerificationGas:s.preVerificationGas??"0x0"},r,A(n),a]});return{...u,...o&&{paymasterPostOpGasLimit:ce(o)},...c&&{paymasterVerificationGasLimit:ce(c)}}}const go=["factory","fees","gas","paymaster","nonce","signature","authorization"];async function jn(e,t){var Y;const n=t,{account:r=e.account,parameters:a=go,stateOverride:i}=n;if(!r)throw new Ot;const s=nt(r),o=e,c=n.paymaster??(o==null?void 0:o.paymaster),u=typeof c=="string"?c:void 0,{getPaymasterStubData:d,getPaymasterData:l}=(()=>{if(c===!0)return{getPaymasterStubData:P=>te(o,yo,"getPaymasterStubData")(P),getPaymasterData:P=>te(o,mo,"getPaymasterData")(P)};if(typeof c=="object"){const{getPaymasterStubData:P,getPaymasterData:D}=c;return{getPaymasterStubData:D&&P?P:D,getPaymasterData:D&&P?D:void 0}}return{getPaymasterStubData:void 0,getPaymasterData:void 0}})(),p=n.paymasterContext?n.paymasterContext:o==null?void 0:o.paymasterContext;let f={...n,paymaster:u,sender:s.address};const[m,y,h,v,x]=await Promise.all([(async()=>n.calls?s.encodeCalls(n.calls.map(P=>{const D=P;return D.abi?{data:he(D),to:D.to,value:D.value}:D})):n.callData)(),(async()=>{if(!a.includes("factory"))return;if(n.initCode)return{initCode:n.initCode};if(n.factory&&n.factoryData)return{factory:n.factory,factoryData:n.factoryData};const{factory:P,factoryData:D}=await s.getFactoryArgs();return s.entryPoint.version==="0.6"?{initCode:P&&D?Me([P,D]):void 0}:{factory:P,factoryData:D}})(),(async()=>{var P;if(a.includes("fees")){if(typeof n.maxFeePerGas=="bigint"&&typeof n.maxPriorityFeePerGas=="bigint")return f;if((P=o==null?void 0:o.userOperation)!=null&&P.estimateFeesPerGas){const D=await o.userOperation.estimateFeesPerGas({account:s,bundlerClient:o,userOperation:f});return{...f,...D}}try{const D=o.client??e,_e=await te(D,Es,"estimateFeesPerGas")({chain:D.chain,type:"eip1559"});return{maxFeePerGas:typeof n.maxFeePerGas=="bigint"?n.maxFeePerGas:BigInt(2n*_e.maxFeePerGas),maxPriorityFeePerGas:typeof n.maxPriorityFeePerGas=="bigint"?n.maxPriorityFeePerGas:BigInt(2n*_e.maxPriorityFeePerGas)}}catch{return}}})(),(async()=>{if(a.includes("nonce"))return typeof n.nonce=="bigint"?n.nonce:s.getNonce()})(),(async()=>{if(a.includes("authorization")){if(typeof n.authorization=="object")return n.authorization;if(s.authorization&&!await s.isDeployed())return{...await $s(s.client,s.authorization),r:"0xfffffffffffffffffffffffffffffff000000000000000000000000000000000",s:"0x7aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",yParity:1}}})()]);typeof m<"u"&&(f.callData=m),typeof y<"u"&&(f={...f,...y}),typeof h<"u"&&(f={...f,...h}),typeof v<"u"&&(f.nonce=v),typeof x<"u"&&(f.authorization=x),a.includes("signature")&&(typeof n.signature<"u"?f.signature=n.signature:f.signature=await s.getStubSignature(f)),s.entryPoint.version==="0.6"&&!f.initCode&&(f.initCode="0x");let k;async function S(){return k||(e.chain?e.chain.id:(k=await te(e,On,"getChainId")({}),k))}let j=!1;if(a.includes("paymaster")&&d&&!u&&!n.paymasterAndData){const{isFinal:P=!1,sponsor:D,..._e}=await d({chainId:await S(),entryPointAddress:s.entryPoint.address,context:p,...f});j=P,f={...f,..._e}}if(s.entryPoint.version==="0.6"&&!f.paymasterAndData&&(f.paymasterAndData="0x"),a.includes("gas")){if((Y=s.userOperation)!=null&&Y.estimateGas){const P=await s.userOperation.estimateGas(f);f={...f,...P}}if(typeof f.callGasLimit>"u"||typeof f.preVerificationGas>"u"||typeof f.verificationGasLimit>"u"||f.paymaster&&typeof f.paymasterPostOpGasLimit>"u"||f.paymaster&&typeof f.paymasterVerificationGasLimit>"u"){const P=await te(o,ua,"estimateUserOperationGas")({account:s,callGasLimit:0n,preVerificationGas:0n,verificationGasLimit:0n,stateOverride:i,...f.paymaster?{paymasterPostOpGasLimit:0n,paymasterVerificationGasLimit:0n}:{},...f});f={...f,callGasLimit:f.callGasLimit??P.callGasLimit,preVerificationGas:f.preVerificationGas??P.preVerificationGas,verificationGasLimit:f.verificationGasLimit??P.verificationGasLimit,paymasterPostOpGasLimit:f.paymasterPostOpGasLimit??P.paymasterPostOpGasLimit,paymasterVerificationGasLimit:f.paymasterVerificationGasLimit??P.paymasterVerificationGasLimit}}}if(a.includes("paymaster")&&l&&!u&&!n.paymasterAndData&&!j){const P=await l({chainId:await S(),entryPointAddress:s.entryPoint.address,context:p,...f});f={...f,...P}}return delete f.calls,delete f.parameters,delete f.paymasterContext,typeof f.paymaster!="string"&&delete f.paymaster,f}async function ua(e,t){var c;const{account:n=e.account,entryPointAddress:r,stateOverride:a}=t;if(!n&&!t.sender)throw new Ot;const i=n?nt(n):void 0,s=Is(a),o=i?await te(e,jn,"prepareUserOperation")({...t,parameters:["authorization","factory","nonce","paymaster","signature"]}):t;try{const u=[Tt(o),r??((c=i==null?void 0:i.entryPoint)==null?void 0:c.address)],d=await e.request({method:"eth_estimateUserOperationGas",params:s?[...u,s]:[...u]});return po(d)}catch(u){const d=t.calls;throw ca(u,{...o,...d?{calls:d}:{}})}}function bo(e){return e.request({method:"eth_supportedEntryPoints"})}function wo(e){const t={...e};return e.callGasLimit&&(t.callGasLimit=BigInt(e.callGasLimit)),e.maxFeePerGas&&(t.maxFeePerGas=BigInt(e.maxFeePerGas)),e.maxPriorityFeePerGas&&(t.maxPriorityFeePerGas=BigInt(e.maxPriorityFeePerGas)),e.nonce&&(t.nonce=BigInt(e.nonce)),e.paymasterPostOpGasLimit&&(t.paymasterPostOpGasLimit=BigInt(e.paymasterPostOpGasLimit)),e.paymasterVerificationGasLimit&&(t.paymasterVerificationGasLimit=BigInt(e.paymasterVerificationGasLimit)),e.preVerificationGas&&(t.preVerificationGas=BigInt(e.preVerificationGas)),e.verificationGasLimit&&(t.verificationGasLimit=BigInt(e.verificationGasLimit)),t}async function vo(e,{hash:t}){const n=await e.request({method:"eth_getUserOperationByHash",params:[t]},{dedupe:!0});if(!n)throw new oo({hash:t});const{blockHash:r,blockNumber:a,entryPoint:i,transactionHash:s,userOperation:o}=n;return{blockHash:r,blockNumber:BigInt(a),entryPoint:i,transactionHash:s,userOperation:wo(o)}}function ko(e){const t={...e};return e.actualGasCost&&(t.actualGasCost=BigInt(e.actualGasCost)),e.actualGasUsed&&(t.actualGasUsed=BigInt(e.actualGasUsed)),e.logs&&(t.logs=e.logs.map(n=>As(n))),e.receipt&&(t.receipt=Ps(t.receipt)),t}async function da(e,{hash:t}){const n=await e.request({method:"eth_getUserOperationReceipt",params:[t]},{dedupe:!0});if(!n)throw new so({hash:t});return ko(n)}async function xo(e,t){var c,u;const{account:n=e.account,entryPointAddress:r}=t;if(!n&&!t.sender)throw new Ot;const a=n?nt(n):void 0,i=a?await te(e,jn,"prepareUserOperation")(t):t,s=t.signature||await((c=a==null?void 0:a.signUserOperation)==null?void 0:c.call(a,i)),o=Tt({...i,signature:s});try{return await e.request({method:"eth_sendUserOperation",params:[o,r??((u=a==null?void 0:a.entryPoint)==null?void 0:u.address)]},{retryCount:0})}catch(d){const l=t.calls;throw ca(d,{...i,...l?{calls:l}:{},signature:s})}}function So(e,t){const{hash:n,pollingInterval:r=e.pollingInterval,retryCount:a,timeout:i=12e4}=t;let s=0;const o=$r(["waitForUserOperationReceipt",e.uid,n]);return new Promise((c,u)=>{const d=Wr(o,{resolve:c,reject:u},l=>{const p=m=>{f(),m(),d()},f=qr(async()=>{a&&s>=a&&p(()=>l.reject(new sr({hash:n})));try{const m=await te(e,da,"getUserOperationReceipt")({hash:n});p(()=>l.resolve(m))}catch(m){const y=m;y.name!=="UserOperationReceiptNotFoundError"&&p(()=>l.reject(y))}s++},{emitOnBegin:!0,interval:r});return i&&setTimeout(()=>p(()=>l.reject(new sr({hash:n}))),i),f})})}function _o(e){return{estimateUserOperationGas:t=>ua(e,t),getChainId:()=>On(e),getSupportedEntryPoints:()=>bo(e),getUserOperation:t=>vo(e,t),getUserOperationReceipt:t=>da(e,t),prepareUserOperation:t=>jn(e,t),sendUserOperation:t=>xo(e,t),waitForUserOperationReceipt:t=>So(e,t)}}function Eo(e){const{client:t,key:n="bundler",name:r="Bundler Client",paymaster:a,paymasterContext:i,transport:s,userOperation:o}=e;return Object.assign(Kr({...e,chain:e.chain??(t==null?void 0:t.chain),key:n,name:r,transport:s,type:"bundlerClient"}),{client:t,paymaster:a,paymasterContext:i,userOperation:o}).extend(_o)}const Io="******************************************",ze="4.3.6",la="@coinbase/wallet-sdk",Ao=()=>({chains:[]}),Po=()=>({keys:{}}),Oo=()=>({account:{}}),Co=()=>({subAccount:void 0}),To=()=>({subAccountConfig:{}}),Lo=()=>({spendPermissions:[]}),jo=()=>({config:{version:ze}}),F=Ln(Zs((...e)=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ao(...e)),Po(...e)),Oo(...e)),Co(...e)),Lo(...e)),jo(...e)),To(...e)),{name:"cbwsdk.store",storage:ia(()=>localStorage),partialize:e=>({chains:e.chains,keys:e.keys,account:e.account,subAccount:e.subAccount,spendPermissions:e.spendPermissions,config:e.config})})),Mo={get:()=>F.getState().subAccountConfig,set:e=>{F.setState(t=>({subAccountConfig:Object.assign(Object.assign({},t.subAccountConfig),e)}))},clear:()=>{F.setState({subAccountConfig:{}})}},Do={get:()=>F.getState().subAccount,set:e=>{F.setState(t=>({subAccount:t.subAccount?Object.assign(Object.assign({},t.subAccount),e):Object.assign({address:e.address},e)}))},clear:()=>{F.setState({subAccount:void 0})}},No={get:()=>F.getState().spendPermissions,set:e=>{F.setState({spendPermissions:e})},clear:()=>{F.setState({spendPermissions:[]})}},Ro={get:()=>F.getState().account,set:e=>{F.setState(t=>({account:Object.assign(Object.assign({},t.account),e)}))},clear:()=>{F.setState({account:{}})}},Bo={get:()=>F.getState().chains,set:e=>{F.setState({chains:e})},clear:()=>{F.setState({chains:[]})}},Uo={get:e=>F.getState().keys[e],set:(e,t)=>{F.setState(n=>({keys:Object.assign(Object.assign({},n.keys),{[e]:t})}))},clear:()=>{F.setState({keys:{}})}},fa={get:()=>F.getState().config,set:e=>{F.setState(t=>({config:Object.assign(Object.assign({},t.config),e)}))}},Go={subAccounts:Do,subAccountsConfig:Mo,spendPermissions:No,account:Ro,chains:Bo,keys:Uo,config:fa},w=Object.assign(Object.assign({},F),Go),Fo='!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ClientAnalytics=t():e.ClientAnalytics=t()}(this,(function(){return(()=>{var e={792:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=t},562:e=>{var t,n;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return ********&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var i=e[r]<<16|e[r+1]<<8|e[r+2],a=0;a<4;a++)8*r+6*a<=8*e.length?n.push(t.charAt(i>>>6*(3-a)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\\/]/gi,"");for(var n=[],r=0,i=0;r<e.length;i=++r%4)0!=i&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*i+8)-1)<<2*i|t.indexOf(e.charAt(r))>>>6-2*i);return n}},e.exports=n},335:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},762:(e,t,n)=>{var r,i,a,o,s;r=n(562),i=n(792).utf8,a=n(335),o=n(792).bin,(s=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?o.stringToBytes(e):i.stringToBytes(e):a(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var n=r.bytesToWords(e),c=8*e.length,u=1732584193,l=-271733879,d=-1732584194,p=271733878,m=0;m<n.length;m++)n[m]=********&(n[m]<<8|n[m]>>>24)|4278255360&(n[m]<<24|n[m]>>>8);n[c>>>5]|=128<<c%32,n[14+(c+64>>>9<<4)]=c;var f=s._ff,v=s._gg,g=s._hh,b=s._ii;for(m=0;m<n.length;m+=16){var h=u,w=l,y=d,T=p;u=f(u,l,d,p,n[m+0],7,-680876936),p=f(p,u,l,d,n[m+1],12,-389564586),d=f(d,p,u,l,n[m+2],17,606105819),l=f(l,d,p,u,n[m+3],22,-1044525330),u=f(u,l,d,p,n[m+4],7,-176418897),p=f(p,u,l,d,n[m+5],12,1200080426),d=f(d,p,u,l,n[m+6],17,-1473231341),l=f(l,d,p,u,n[m+7],22,-45705983),u=f(u,l,d,p,n[m+8],7,1770035416),p=f(p,u,l,d,n[m+9],12,-1958414417),d=f(d,p,u,l,n[m+10],17,-42063),l=f(l,d,p,u,n[m+11],22,-1990404162),u=f(u,l,d,p,n[m+12],7,1804603682),p=f(p,u,l,d,n[m+13],12,-40341101),d=f(d,p,u,l,n[m+14],17,-1502002290),u=v(u,l=f(l,d,p,u,n[m+15],22,1236535329),d,p,n[m+1],5,-165796510),p=v(p,u,l,d,n[m+6],9,-1069501632),d=v(d,p,u,l,n[m+11],14,643717713),l=v(l,d,p,u,n[m+0],20,-373897302),u=v(u,l,d,p,n[m+5],5,-701558691),p=v(p,u,l,d,n[m+10],9,38016083),d=v(d,p,u,l,n[m+15],14,-660478335),l=v(l,d,p,u,n[m+4],20,-405537848),u=v(u,l,d,p,n[m+9],5,568446438),p=v(p,u,l,d,n[m+14],9,-1019803690),d=v(d,p,u,l,n[m+3],14,-187363961),l=v(l,d,p,u,n[m+8],20,1163531501),u=v(u,l,d,p,n[m+13],5,-1444681467),p=v(p,u,l,d,n[m+2],9,-51403784),d=v(d,p,u,l,n[m+7],14,1735328473),u=g(u,l=v(l,d,p,u,n[m+12],20,-1926607734),d,p,n[m+5],4,-378558),p=g(p,u,l,d,n[m+8],11,-2022574463),d=g(d,p,u,l,n[m+11],16,1839030562),l=g(l,d,p,u,n[m+14],23,-35309556),u=g(u,l,d,p,n[m+1],4,-1530992060),p=g(p,u,l,d,n[m+4],11,1272893353),d=g(d,p,u,l,n[m+7],16,-155497632),l=g(l,d,p,u,n[m+10],23,-1094730640),u=g(u,l,d,p,n[m+13],4,681279174),p=g(p,u,l,d,n[m+0],11,-358537222),d=g(d,p,u,l,n[m+3],16,-722521979),l=g(l,d,p,u,n[m+6],23,76029189),u=g(u,l,d,p,n[m+9],4,-640364487),p=g(p,u,l,d,n[m+12],11,-421815835),d=g(d,p,u,l,n[m+15],16,530742520),u=b(u,l=g(l,d,p,u,n[m+2],23,-995338651),d,p,n[m+0],6,-198630844),p=b(p,u,l,d,n[m+7],10,1126891415),d=b(d,p,u,l,n[m+14],15,-1416354905),l=b(l,d,p,u,n[m+5],21,-57434055),u=b(u,l,d,p,n[m+12],6,1700485571),p=b(p,u,l,d,n[m+3],10,-1894986606),d=b(d,p,u,l,n[m+10],15,-1051523),l=b(l,d,p,u,n[m+1],21,-2054922799),u=b(u,l,d,p,n[m+8],6,1873313359),p=b(p,u,l,d,n[m+15],10,-30611744),d=b(d,p,u,l,n[m+6],15,-1560198380),l=b(l,d,p,u,n[m+13],21,1309151649),u=b(u,l,d,p,n[m+4],6,-145523070),p=b(p,u,l,d,n[m+11],10,-1120210379),d=b(d,p,u,l,n[m+2],15,718787259),l=b(l,d,p,u,n[m+9],21,-343485551),u=u+h>>>0,l=l+w>>>0,d=d+y>>>0,p=p+T>>>0}return r.endian([u,l,d,p])})._ff=function(e,t,n,r,i,a,o){var s=e+(t&n|~t&r)+(i>>>0)+o;return(s<<a|s>>>32-a)+t},s._gg=function(e,t,n,r,i,a,o){var s=e+(t&r|n&~r)+(i>>>0)+o;return(s<<a|s>>>32-a)+t},s._hh=function(e,t,n,r,i,a,o){var s=e+(t^n^r)+(i>>>0)+o;return(s<<a|s>>>32-a)+t},s._ii=function(e,t,n,r,i,a,o){var s=e+(n^(t|~r))+(i>>>0)+o;return(s<<a|s>>>32-a)+t},s._blocksize=16,s._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var n=r.wordsToBytes(s(e,t));return t&&t.asBytes?n:t&&t.asString?o.bytesToString(n):r.bytesToHex(n)}},2:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Perfume:()=>ze,incrementUjNavigation:()=>Le,markStep:()=>Re,markStepOnce:()=>qe});var r,i,a={isResourceTiming:!1,isElementTiming:!1,maxTime:3e4,reportOptions:{},enableNavigationTracking:!0},o=window,s=o.console,c=o.navigator,u=o.performance,l=function(){return c.deviceMemory},d=function(){return c.hardwareConcurrency},p="mark.",m=function(){return u&&!!u.getEntriesByType&&!!u.now&&!!u.mark},f="4g",v=!1,g={},b={value:0},h={value:{beacon:0,css:0,fetch:0,img:0,other:0,script:0,total:0,xmlhttprequest:0}},w={value:0},y={value:0},T={},k={isHidden:!1,didChange:!1},_=function(){k.isHidden=!1,document.hidden&&(k.isHidden=document.hidden,k.didChange=!0)},S=function(e,t){try{var n=new PerformanceObserver((function(e){t(e.getEntries())}));return n.observe({type:e,buffered:!0}),n}catch(e){s.warn("Perfume.js:",e)}return null},E=function(){return!!(d()&&d()<=4)||!!(l()&&l()<=4)},x=function(e,t){switch(e){case"slow-2g":case"2g":case"3g":return!0;default:return E()||t}},O=function(e){return parseFloat(e.toFixed(4))},j=function(e){return"number"!=typeof e?null:O(e/Math.pow(1024,2))},N=function(e,t,n,r,i){var s,u=function(){a.analyticsTracker&&(k.isHidden&&!["CLS","INP"].includes(e)||a.analyticsTracker({attribution:r,metricName:e,data:t,navigatorInformation:c?{deviceMemory:l()||0,hardwareConcurrency:d()||0,serviceWorkerStatus:"serviceWorker"in c?c.serviceWorker.controller?"controlled":"supported":"unsupported",isLowEndDevice:E(),isLowEndExperience:x(f,v)}:{},rating:n,navigationType:i}))};["CLS","INP"].includes(e)?u():(s=u,"requestIdleCallback"in o?o.requestIdleCallback(s,{timeout:3e3}):s())},I=function(e){e.forEach((function(e){if(!("self"!==e.name||e.startTime<b.value)){var t=e.duration-50;t>0&&(w.value+=t,y.value+=t)}}))};!function(e){e.instant="instant",e.quick="quick",e.moderate="moderate",e.slow="slow",e.unavoidable="unavoidable"}(r||(r={}));var P,M,B,C,D,A=((i={})[r.instant]={vitalsThresholds:[100,200],maxOutlierThreshold:1e4},i[r.quick]={vitalsThresholds:[200,500],maxOutlierThreshold:1e4},i[r.moderate]={vitalsThresholds:[500,1e3],maxOutlierThreshold:1e4},i[r.slow]={vitalsThresholds:[1e3,2e3],maxOutlierThreshold:1e4},i[r.unavoidable]={vitalsThresholds:[2e3,5e3],maxOutlierThreshold:2e4},i),L={RT:[100,200],TBT:[200,600],NTBT:[200,600]},U=function(e,t){return L[e]?t<=L[e][0]?"good":t<=L[e][1]?"needsImprovement":"poor":null},R=function(e,t,n){Object.keys(t).forEach((function(e){"number"==typeof t[e]&&(t[e]=O(t[e]))})),N(e,t,null,n||{})},q=function(e){var t=e.attribution,n=e.name,r=e.rating,i=e.value,o=e.navigationType;"FCP"===n&&(b.value=i),["FCP","LCP"].includes(n)&&!T[0]&&(T[0]=S("longtask",I)),"FID"===n&&setTimeout((function(){k.didChange||(q({attribution:t,name:"TBT",rating:U("TBT",w.value),value:w.value,navigationType:o}),R("dataConsumption",h.value))}),1e4);var s=O(i);s<=a.maxTime&&s>=0&&N(n,s,r,t,o)},F=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},z=function(e){if("loading"===document.readyState)return"loading";var t=F();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},K=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},$=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var r=e,i=r.id?"#"+r.id:K(r)+(r.className&&r.className.length?"."+r.className.replace(/\\s+/g,"."):"");if(n.length+i.length>(t||100)-1)return n||i;if(n=n?i+">"+n:i,r.id)break;e=r.parentNode}}catch(e){}return n},Q=-1,W=function(){return Q},H=function(e){addEventListener("pageshow",(function(t){t.persisted&&(Q=t.timeStamp,e(t))}),!0)},V=function(){var e=F();return e&&e.activationStart||0},J=function(e,t){var n=F(),r="navigate";return W()>=0?r="back-forward-cache":n&&(r=document.prerendering||V()>0?"prerender":document.wasDiscarded?"restore":n.type.replace(/_/g,"-")),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},X=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},G=function(e,t){var n=function n(r){"pagehide"!==r.type&&"hidden"!==document.visibilityState||(e(r),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},Z=function(e,t,n,r){var i,a;return function(o){t.value>=0&&(o||r)&&((a=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=a,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},Y=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},ee=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},te=-1,ne=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},re=function(e){"hidden"===document.visibilityState&&te>-1&&(te="visibilitychange"===e.type?e.timeStamp:0,ae())},ie=function(){addEventListener("visibilitychange",re,!0),addEventListener("prerenderingchange",re,!0)},ae=function(){removeEventListener("visibilitychange",re,!0),removeEventListener("prerenderingchange",re,!0)},oe=function(){return te<0&&(te=ne(),ie(),H((function(){setTimeout((function(){te=ne(),ie()}),0)}))),{get firstHiddenTime(){return te}}},se=function(e,t){t=t||{},ee((function(){var n,r=[1800,3e3],i=oe(),a=J("FCP"),o=X("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<i.firstHiddenTime&&(a.value=Math.max(e.startTime-V(),0),a.entries.push(e),n(!0)))}))}));o&&(n=Z(e,a,r,t.reportAllChanges),H((function(i){a=J("FCP"),n=Z(e,a,r,t.reportAllChanges),Y((function(){a.value=performance.now()-i.timeStamp,n(!0)}))})))}))},ce={passive:!0,capture:!0},ue=new Date,le=function(e,t){P||(P=t,M=e,B=new Date,me(removeEventListener),de())},de=function(){if(M>=0&&M<B-ue){var e={entryType:"first-input",name:P.type,target:P.target,cancelable:P.cancelable,startTime:P.timeStamp,processingStart:P.timeStamp+M};C.forEach((function(t){t(e)})),C=[]}},pe=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){le(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,ce),removeEventListener("pointercancel",r,ce)};addEventListener("pointerup",n,ce),addEventListener("pointercancel",r,ce)}(t,e):le(t,e)}},me=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,pe,ce)}))},fe=0,ve=1/0,ge=0,be=function(e){e.forEach((function(e){e.interactionId&&(ve=Math.min(ve,e.interactionId),ge=Math.max(ge,e.interactionId),fe=ge?(ge-ve)/7+1:0)}))},he=function(){return D?fe:performance.interactionCount||0},we=0,ye=function(){return he()-we},Te=[],ke={},_e=function(e){var t=Te[Te.length-1],n=ke[e.interactionId];if(n||Te.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};ke[r.id]=r,Te.push(r)}Te.sort((function(e,t){return t.latency-e.latency})),Te.splice(10).forEach((function(e){delete ke[e.id]}))}},Se={},Ee=function e(t){document.prerendering?ee((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},xe=function(e,t){t=t||{};var n=[800,1800],r=J("TTFB"),i=Z(e,r,n,t.reportAllChanges);Ee((function(){var a=F();if(a){var o=a.responseStart;if(o<=0||o>performance.now())return;r.value=Math.max(o-V(),0),r.entries=[a],i(!0),H((function(){r=J("TTFB",0),(i=Z(e,r,n,t.reportAllChanges))(!0)}))}}))},Oe=function(e){e.forEach((function(e){e.identifier&&q({attribution:{identifier:e.identifier},name:"ET",rating:null,value:e.startTime})}))},je=function(e){e.forEach((function(e){if(a.isResourceTiming&&R("resourceTiming",e),e.decodedBodySize&&e.initiatorType){var t=e.decodedBodySize/1e3;h.value[e.initiatorType]+=t,h.value.total+=t}}))},Ne=function(){!function(e,t){xe((function(e){!function(e){if(e.entries.length){var t=e.entries[0],n=t.activationStart||0,r=Math.max(t.domainLookupStart-n,0),i=Math.max(t.connectStart-n,0),a=Math.max(t.requestStart-n,0);e.attribution={waitingTime:r,dnsTime:i-r,connectionTime:a-i,requestTime:e.value-a,navigationEntry:t}}else e.attribution={waitingTime:0,dnsTime:0,connectionTime:0,requestTime:0}}(e),function(e){e.value>0&&q(e)}(e)}),t)}(0,a.reportOptions.ttfb),function(e,t){!function(e,t){t=t||{},ee((function(){var e,n=[.1,.25],r=J("CLS"),i=-1,a=0,o=[],s=function(e){i>-1&&function(e){!function(e){if(e.entries.length){var t=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(t&&t.sources&&t.sources.length){var n=(r=t.sources).find((function(e){return e.node&&1===e.node.nodeType}))||r[0];if(n)return void(e.attribution={largestShiftTarget:$(n.node),largestShiftTime:t.startTime,largestShiftValue:t.value,largestShiftSource:n,largestShiftEntry:t,loadState:z(t.startTime)})}}var r;e.attribution={}}(e),function(e){q(e)}(e)}(e)},c=function(t){t.forEach((function(t){if(!t.hadRecentInput){var n=o[0],i=o[o.length-1];a&&t.startTime-i.startTime<1e3&&t.startTime-n.startTime<5e3?(a+=t.value,o.push(t)):(a=t.value,o=[t]),a>r.value&&(r.value=a,r.entries=o,e())}}))},u=X("layout-shift",c);u&&(e=Z(s,r,n,t.reportAllChanges),se((function(t){i=t.value,r.value<0&&(r.value=0,e())})),G((function(){c(u.takeRecords()),e(!0)})),H((function(){a=0,i=-1,r=J("CLS",0),e=Z(s,r,n,t.reportAllChanges),Y((function(){return e()}))})))}))}(0,t)}(0,a.reportOptions.cls),function(e,t){se((function(e){!function(e){if(e.entries.length){var t=F(),n=e.entries[e.entries.length-1];if(t){var r=t.activationStart||0,i=Math.max(0,t.responseStart-r);return void(e.attribution={timeToFirstByte:i,firstByteToFCP:e.value-i,loadState:z(e.entries[0].startTime),navigationEntry:t,fcpEntry:n})}}e.attribution={timeToFirstByte:0,firstByteToFCP:e.value,loadState:z(W())}}(e),function(e){q(e)}(e)}),t)}(0,a.reportOptions.fcp),function(e,t){!function(e,t){t=t||{},ee((function(){var n,r=[100,300],i=oe(),a=J("FID"),o=function(e){e.startTime<i.firstHiddenTime&&(a.value=e.processingStart-e.startTime,a.entries.push(e),n(!0))},s=function(e){e.forEach(o)},c=X("first-input",s);n=Z(e,a,r,t.reportAllChanges),c&&G((function(){s(c.takeRecords()),c.disconnect()}),!0),c&&H((function(){var i;a=J("FID"),n=Z(e,a,r,t.reportAllChanges),C=[],M=-1,P=null,me(addEventListener),i=o,C.push(i),de()}))}))}((function(e){!function(e){var t=e.entries[0];e.attribution={eventTarget:$(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:z(t.startTime)}}(e),function(e){q(e)}(e)}),t)}(0,a.reportOptions.fid),function(e,t){!function(e,t){t=t||{},ee((function(){var n,r=[2500,4e3],i=oe(),a=J("LCP"),o=function(e){var t=e[e.length-1];if(t){var r=Math.max(t.startTime-V(),0);r<i.firstHiddenTime&&(a.value=r,a.entries=[t],n())}},s=X("largest-contentful-paint",o);if(s){n=Z(e,a,r,t.reportAllChanges);var c=function(){Se[a.id]||(o(s.takeRecords()),s.disconnect(),Se[a.id]=!0,n(!0))};["keydown","click"].forEach((function(e){addEventListener(e,c,{once:!0,capture:!0})})),G(c,!0),H((function(i){a=J("LCP"),n=Z(e,a,r,t.reportAllChanges),Y((function(){a.value=performance.now()-i.timeStamp,Se[a.id]=!0,n(!0)}))}))}}))}((function(e){!function(e){if(e.entries.length){var t=F();if(t){var n=t.activationStart||0,r=e.entries[e.entries.length-1],i=r.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===r.url}))[0],a=Math.max(0,t.responseStart-n),o=Math.max(a,i?(i.requestStart||i.startTime)-n:0),s=Math.max(o,i?i.responseEnd-n:0),c=Math.max(s,r?r.startTime-n:0),u={element:$(r.element),timeToFirstByte:a,resourceLoadDelay:o-a,resourceLoadTime:s-o,elementRenderDelay:c-s,navigationEntry:t,lcpEntry:r};return r.url&&(u.url=r.url),i&&(u.lcpResourceEntry=i),void(e.attribution=u)}}e.attribution={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadTime:0,elementRenderDelay:e.value}}(e),function(e){q(e)}(e)}),t)}(0,a.reportOptions.lcp),function(e,t){!function(e,t){t=t||{},ee((function(){var n=[200,500];"interactionCount"in performance||D||(D=X("event",be,{type:"event",buffered:!0,durationThreshold:0}));var r,i=J("INP"),a=function(e){e.forEach((function(e){e.interactionId&&_e(e),"first-input"===e.entryType&&!Te.some((function(t){return t.entries.some((function(t){return e.duration===t.duration&&e.startTime===t.startTime}))}))&&_e(e)}));var t,n=(t=Math.min(Te.length-1,Math.floor(ye()/50)),Te[t]);n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())},o=X("event",a,{durationThreshold:t.durationThreshold||40});r=Z(e,i,n,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),G((function(){a(o.takeRecords()),i.value<0&&ye()>0&&(i.value=0,i.entries=[]),r(!0)})),H((function(){Te=[],we=he(),i=J("INP"),r=Z(e,i,n,t.reportAllChanges)})))}))}((function(t){!function(e){if(e.entries.length){var t=e.entries.sort((function(e,t){return t.duration-e.duration||t.processingEnd-t.processingStart-(e.processingEnd-e.processingStart)}))[0];e.attribution={eventTarget:$(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:z(t.startTime)}}else e.attribution={}}(t),e(t)}),t)}((function(e){return q(e)}),a.reportOptions.inp),a.isResourceTiming&&S("resource",je),a.isElementTiming&&S("element",Oe)},Ie=function(e){var t="usageDetails"in e?e.usageDetails:{};R("storageEstimate",{quota:j(e.quota),usage:j(e.usage),caches:j(t.caches),indexedDB:j(t.indexedDB),serviceWorker:j(t.serviceWorkerRegistrations)})},Pe={finalMarkToStepsMap:{},startMarkToStepsMap:{},active:{},navigationSteps:{}},Me=function(e){delete Pe.active[e]},Be=function(){return Pe.navigationSteps},Ce=function(e){var t;return null!==(t=Be()[e])&&void 0!==t?t:{}},De=function(e,t,n){var r="step."+e,i=u.getEntriesByName(p+t).length>0;if(u.getEntriesByName(p+n).length>0&&a.steps){var o=A[a.steps[e].threshold],s=o.maxOutlierThreshold,c=o.vitalsThresholds;if(i){var l=u.measure(r,p+t,p+n),d=l.duration;if(d<=s){var m=function(e,t){return e<=t[0]?"good":e<=t[1]?"needsImprovement":"poor"}(d,c);d>=0&&(N("userJourneyStep",d,m,{stepName:e},void 0),u.measure("step.".concat(e,"_vitals_").concat(m),{start:l.startTime+l.duration,end:l.startTime+l.duration,detail:{type:"stepVital",duration:d}}))}}}},Ae=function(){var e=Be(),t=Pe.startMarkToStepsMap,n=Object.keys(e).length;if(0===n)return{};var r={},i=n-1,a=Ce(i);if(Object.keys(a).forEach((function(e){var n,i=null!==(n=t[e])&&void 0!==n?n:[];Object.keys(i).forEach((function(e){r[e]=!0}))})),n>1){var o=Ce(i-1);Object.keys(o).forEach((function(e){var n,i=null!==(n=t[e])&&void 0!==n?n:[];Object.keys(i).forEach((function(e){r[e]=!0}))}))}return r},Le=function(){var e,t=Object.keys(Pe.navigationSteps).length;Pe.navigationSteps[t]={};var n=Ae();null===(e=a.onMarkStep)||void 0===e||e.call(a,"",Object.keys(n))},Ue=function(e){var t,n,r,i,o,s,c;if(Pe.finalMarkToStepsMap[e]){!function(e){var t=Pe.navigationSteps,n=Pe.finalMarkToStepsMap,r=Object.keys(t).length;if(0!==r){var i=r-1,a=Ce(i);if(a&&n[e]){var o=n[e];o&&Object.keys(o).forEach((function(e){if(a[e]){var n=Ce(i)||{};n[e]=!1,t[i]=n}if(r>1){var o=i-1,s=Ce(o);s[e]&&(s[e]=!1,t[o]=s)}}))}}}(e);var u=Pe.finalMarkToStepsMap[e];Object.keys(u).forEach((function(t){var n=u[t];n.forEach(Me),Promise.all(n.map((function(n){return function(e,t,n,r){return new(n||(n=Promise))((function(e,t){function i(e){try{o(r.next(e))}catch(e){t(e)}}function a(e){try{o(r.throw(e))}catch(e){t(e)}}function o(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(i,a)}o((r=r.apply(undefined,[])).next())}))}(0,0,void 0,(function(){return function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}}(this,(function(r){switch(r.label){case 0:return[4,De(n,t,e)];case 1:return r.sent(),[2]}}))}))}))).catch((function(){}))}))}else r=e,i=Pe.navigationSteps,o=Object.keys(i).length,(c=Ce(s=(o>0?o:1)-1)||[])[r]=!0,i[s]=c,function(e){var t,n=null!==(t=Pe.startMarkToStepsMap[e])&&void 0!==t?t:[];Object.keys(n).forEach((function(e){Pe.active[e]||(Pe.active[e]=!0)}))}(e);if(a.enableNavigationTracking){var l=Ae();null===(t=a.onMarkStep)||void 0===t||t.call(a,e,Object.keys(l))}else null===(n=a.onMarkStep)||void 0===n||n.call(a,e,Object.keys(Pe.active))},Re=function(e){u.mark(p+e),Ue(e)},qe=function(e){0===u.getEntriesByName(p+e).length&&(u.mark(p+e),Ue(e))},Fe=0,ze=function(){function e(e){if(void 0===e&&(e={}),this.v="9.0.0-rc.3",a.analyticsTracker=e.analyticsTracker,a.isResourceTiming=!!e.resourceTiming,a.isElementTiming=!!e.elementTiming,a.maxTime=e.maxMeasureTime||a.maxTime,a.reportOptions=e.reportOptions||a.reportOptions,a.steps=e.steps,a.onMarkStep=e.onMarkStep,a.enableNavigationTracking=e.enableNavigationTracking,m()){"PerformanceObserver"in o&&Ne(),void 0!==document.hidden&&document.addEventListener("visibilitychange",_);var t=function(){if(!m())return{};var e=u.getEntriesByType("navigation")[0];if(!e)return{};var t=e.responseStart,n=e.responseEnd;return{fetchTime:n-e.fetchStart,workerTime:e.workerStart>0?n-e.workerStart:0,totalTime:n-e.requestStart,downloadTime:n-t,timeToFirstByte:t-e.requestStart,headerSize:e.transferSize-e.encodedBodySize||0,dnsLookupTime:e.domainLookupEnd-e.domainLookupStart,redirectTime:e.redirectEnd-e.redirectStart}}();R("navigationTiming",t),t.redirectTime&&q({attribution:{},name:"RT",rating:U("RT",t.redirectTime),value:t.redirectTime}),R("networkInformation",function(){if("connection"in c){var e=c.connection;return"object"!=typeof e?{}:(f=e.effectiveType,v=!!e.saveData,{downlink:e.downlink,effectiveType:e.effectiveType,rtt:e.rtt,saveData:!!e.saveData})}return{}}()),c&&c.storage&&"function"==typeof c.storage.estimate&&c.storage.estimate().then(Ie),a.steps&&a.steps&&(Pe.startMarkToStepsMap={},Pe.finalMarkToStepsMap={},Pe.active={},Pe.navigationSteps={},Object.entries(a.steps).forEach((function(e){var t,n,r=e[0],i=e[1].marks,a=i[0],o=i[1],s=null!==(n=Pe.startMarkToStepsMap[a])&&void 0!==n?n:{};if(s[r]=!0,Pe.startMarkToStepsMap[a]=s,Pe.finalMarkToStepsMap[o]){var c=Pe.finalMarkToStepsMap[o][a]||[];c.push(r),Pe.finalMarkToStepsMap[o][a]=c}else Pe.finalMarkToStepsMap[o]=((t={})[a]=[r],t)})))}}return e.prototype.start=function(e){m()&&!g[e]&&(g[e]=!0,u.mark("mark_".concat(e,"_start")))},e.prototype.end=function(e,t,n){if(void 0===t&&(t={}),void 0===n&&(n=!0),m()&&g[e]){u.mark("mark_".concat(e,"_end")),delete g[e];var r=function(e){u.measure(e,"mark_".concat(e,"_start"),"mark_".concat(e,"_end"));var t=u.getEntriesByName(e).pop();return t&&"measure"===t.entryType?t.duration:-1}(e);n&&R(e,O(r),t)}},e.prototype.endPaint=function(e,t){var n=this;setTimeout((function(){n.end(e,t)}))},e.prototype.clear=function(e){delete g[e],u.clearMarks&&(u.clearMarks("mark_".concat(e,"_start")),u.clearMarks("mark_".concat(e,"_end")))},e.prototype.markNTBT=function(){var e=this;this.start("ntbt"),y.value=0,clearTimeout(Fe),Fe=setTimeout((function(){e.end("ntbt",{},!1),q({attribution:{},name:"NTBT",rating:U("NTBT",y.value),value:y.value}),y.value=0}),2e3)},e}()},426:(e,t)=>{"use strict";Symbol.for("react.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator;var n={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},r=Object.assign,i={};function a(e,t,r){this.props=e,this.context=t,this.refs=i,this.updater=r||n}function o(){}function s(e,t,r){this.props=e,this.context=t,this.refs=i,this.updater=r||n}a.prototype.isReactComponent={},a.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},a.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},o.prototype=a.prototype;var c=s.prototype=new o;c.constructor=s,r(c,a.prototype),c.isPureReactComponent=!0;Array.isArray,Object.prototype.hasOwnProperty;var u={current:null};t.useCallback=function(e,t){return u.current.useCallback(e,t)},t.useEffect=function(e,t){return u.current.useEffect(e,t)},t.useRef=function(e){return u.current.useRef(e)}},784:(e,t,n)=>{"use strict";e.exports=n(426)},353:function(e,t,n){var r;!function(i,a){"use strict";var o="function",s="undefined",c="object",u="string",l="major",d="model",p="name",m="type",f="vendor",v="version",g="architecture",b="console",h="mobile",w="tablet",y="smarttv",T="wearable",k="embedded",_="Amazon",S="Apple",E="ASUS",x="BlackBerry",O="Browser",j="Chrome",N="Firefox",I="Google",P="Huawei",M="LG",B="Microsoft",C="Motorola",D="Opera",A="Samsung",L="Sharp",U="Sony",R="Xiaomi",q="Zebra",F="Facebook",z="Chromium OS",K="Mac OS",$=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},Q=function(e,t){return typeof e===u&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},H=function(e,t){if(typeof e===u)return e=e.replace(/^\\s\\s*/,""),typeof t===s?e:e.substring(0,350)},V=function(e,t){for(var n,r,i,s,u,l,d=0;d<t.length&&!u;){var p=t[d],m=t[d+1];for(n=r=0;n<p.length&&!u&&p[n];)if(u=p[n++].exec(e))for(i=0;i<m.length;i++)l=u[++r],typeof(s=m[i])===c&&s.length>0?2===s.length?typeof s[1]==o?this[s[0]]=s[1].call(this,l):this[s[0]]=s[1]:3===s.length?typeof s[1]!==o||s[1].exec&&s[1].test?this[s[0]]=l?l.replace(s[1],s[2]):a:this[s[0]]=l?s[1].call(this,l,s[2]):a:4===s.length&&(this[s[0]]=l?s[3].call(this,l.replace(s[1],s[2])):a):this[s]=l||a;d+=2}},J=function(e,t){for(var n in t)if(typeof t[n]===c&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(Q(t[n][r],e))return"?"===n?a:n}else if(Q(t[n],e))return"?"===n?a:n;return e},X={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},G={browser:[[/\\b(?:crmo|crios)\\/([\\w\\.]+)/i],[v,[p,"Chrome"]],[/edg(?:e|ios|a)?\\/([\\w\\.]+)/i],[v,[p,"Edge"]],[/(opera mini)\\/([-\\w\\.]+)/i,/(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,/(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i],[p,v],[/opios[\\/ ]+([\\w\\.]+)/i],[v,[p,D+" Mini"]],[/\\bopr\\/([\\w\\.]+)/i],[v,[p,D]],[/(kindle)\\/([\\w\\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\\/ ]?([\\w\\.]*)/i,/(ba?idubrowser)[\\/ ]?([\\w\\.]+)/i,/(?:ms|\\()(ie) ([\\w\\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,/(heytap|ovi)browser\\/([\\d\\.]+)/i,/(weibo)__([\\d\\.]+)/i],[p,v],[/(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i],[v,[p,"UC"+O]],[/microm.+\\bqbcore\\/([\\w\\.]+)/i,/\\bqbcore\\/([\\w\\.]+).+microm/i],[v,[p,"WeChat(Win) Desktop"]],[/micromessenger\\/([\\w\\.]+)/i],[v,[p,"WeChat"]],[/konqueror\\/([\\w\\.]+)/i],[v,[p,"Konqueror"]],[/trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i],[v,[p,"IE"]],[/ya(?:search)?browser\\/([\\w\\.]+)/i],[v,[p,"Yandex"]],[/(avast|avg)\\/([\\w\\.]+)/i],[[p,/(.+)/,"$1 Secure "+O],v],[/\\bfocus\\/([\\w\\.]+)/i],[v,[p,N+" Focus"]],[/\\bopt\\/([\\w\\.]+)/i],[v,[p,D+" Touch"]],[/coc_coc\\w+\\/([\\w\\.]+)/i],[v,[p,"Coc Coc"]],[/dolfin\\/([\\w\\.]+)/i],[v,[p,"Dolphin"]],[/coast\\/([\\w\\.]+)/i],[v,[p,D+" Coast"]],[/miuibrowser\\/([\\w\\.]+)/i],[v,[p,"MIUI "+O]],[/fxios\\/([-\\w\\.]+)/i],[v,[p,N]],[/\\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\\/([\\w\\.]+)/i],[[p,/(.+)/,"$1 "+O],v],[/(comodo_dragon)\\/([\\w\\.]+)/i],[[p,/_/g," "],v],[/(electron)\\/([\\w\\.]+) safari/i,/(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\\/ ]?([\\w\\.]+)/i],[p,v],[/(metasr)[\\/ ]?([\\w\\.]+)/i,/(lbbrowser)/i,/\\[(linkedin)app\\]/i],[p],[/((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i],[[p,F],v],[/(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,/(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,/safari (line)\\/([\\w\\.]+)/i,/\\b(line)\\/([\\w\\.]+)\\/iab/i,/(chromium|instagram)[\\/ ]([-\\w\\.]+)/i],[p,v],[/\\bgsa\\/([\\w\\.]+) .*safari\\//i],[v,[p,"GSA"]],[/musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i],[v,[p,"TikTok"]],[/headlesschrome(?:\\/([\\w\\.]+)| )/i],[v,[p,j+" Headless"]],[/ wv\\).+(chrome)\\/([\\w\\.]+)/i],[[p,j+" WebView"],v],[/droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i],[v,[p,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i],[p,v],[/version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i],[v,[p,"Mobile Safari"]],[/version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i],[v,p],[/webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i],[p,[v,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\\/([\\w\\.]+)/i],[p,v],[/(navigator|netscape\\d?)\\/([-\\w\\.]+)/i],[[p,"Netscape"],v],[/mobile vr; rv:([\\w\\.]+)\\).+firefox/i],[v,[p,N+" Reality"]],[/ekiohf.+(flow)\\/([\\w\\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,/(firefox)\\/([\\w\\.]+)/i,/(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,/(links) \\(([\\w\\.]+)/i,/panasonic;(viera)/i],[p,v],[/(cobalt)\\/([\\w\\.]+)/i],[p,[v,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,W]],[/((?:i[346]|x)86)[;\\)]/i],[[g,"ia32"]],[/\\b(aarch64|arm(v?8e?l?|_?64))\\b/i],[[g,"arm64"]],[/\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i],[[g,/ower/,"",W]],[/(sun4\\w)[;\\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i],[[g,W]]],device:[[/\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i],[d,[f,A],[m,w]],[/\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,/samsung[- ]([-\\w]+)/i,/sec-(sgh\\w+)/i],[d,[f,A],[m,h]],[/(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i],[d,[f,S],[m,h]],[/\\((ipad);[-\\w\\),; ]+apple/i,/applecoremedia\\/[\\w\\.]+ \\((ipad)/i,/\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i],[d,[f,S],[m,w]],[/(macintosh);/i],[d,[f,S]],[/\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i],[d,[f,L],[m,h]],[/\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i],[d,[f,P],[m,w]],[/(?:huawei|honor)([-\\w ]+)[;\\)]/i,/\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i],[d,[f,P],[m,h]],[/\\b(poco[\\w ]+)(?: bui|\\))/i,/\\b; (\\w+) build\\/hm\\1/i,/\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,/\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,/\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i],[[d,/_/g," "],[f,R],[m,h]],[/\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i],[[d,/_/g," "],[f,R],[m,w]],[/; (\\w+) bui.+ oppo/i,/\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i],[d,[f,"OPPO"],[m,h]],[/vivo (\\w+)(?: bui|\\))/i,/\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i],[d,[f,"Vivo"],[m,h]],[/\\b(rmx[12]\\d{3})(?: bui|;|\\))/i],[d,[f,"Realme"],[m,h]],[/\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,/\\bmot(?:orola)?[- ](\\w*)/i,/((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i],[d,[f,C],[m,h]],[/\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i],[d,[f,C],[m,w]],[/((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i],[d,[f,M],[m,w]],[/(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,/\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,/\\blg-?([\\d\\w]+) bui/i],[d,[f,M],[m,h]],[/(ideatab[-\\w ]+)/i,/lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i],[d,[f,"Lenovo"],[m,w]],[/(?:maemo|nokia).*(n900|lumia \\d+)/i,/nokia[-_ ]?([-\\w\\.]*)/i],[[d,/_/g," "],[f,"Nokia"],[m,h]],[/(pixel c)\\b/i],[d,[f,I],[m,w]],[/droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i],[d,[f,I],[m,h]],[/droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i],[d,[f,U],[m,h]],[/sony tablet [ps]/i,/\\b(?:sony)?sgp\\w+(?: bui|\\))/i],[[d,"Xperia Tablet"],[f,U],[m,w]],[/ (kb2005|in20[12]5|be20[12][59])\\b/i,/(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i],[d,[f,"OnePlus"],[m,h]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,/(kf[a-z]+)( bui|\\)).+silk\\//i],[d,[f,_],[m,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i],[[d,/(.+)/g,"Fire Phone $1"],[f,_],[m,h]],[/(playbook);[-\\w\\),; ]+(rim)/i],[d,f,[m,w]],[/\\b((?:bb[a-f]|st[hv])100-\\d)/i,/\\(bb10; (\\w+)/i],[d,[f,x],[m,h]],[/(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i],[d,[f,E],[m,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i],[d,[f,E],[m,h]],[/(nexus 9)/i],[d,[f,"HTC"],[m,w]],[/(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,/(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i],[f,[d,/_/g," "],[m,h]],[/droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i],[d,[f,"Acer"],[m,w]],[/droid.+; (m[1-5] note) bui/i,/\\bmz-([-\\w]{2,})/i],[d,[f,"Meizu"],[m,h]],[/(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\\w]*)/i,/(hp) ([\\w ]+\\w)/i,/(asus)-?(\\w+)/i,/(microsoft); (lumia[\\w ]+)/i,/(lenovo)[-_ ]?([-\\w]+)/i,/(jolla)/i,/(oppo) ?([\\w ]+) bui/i],[f,d,[m,h]],[/(kobo)\\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\\/([\\w\\.]+)/i,/(nook)[\\w ]+build\\/(\\w+)/i,/(dell) (strea[kpr\\d ]*[\\dko])/i,/(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,/(trinity)[- ]*(t\\d{3}) bui/i,/(gigaset)[- ]+(q\\w{1,9}) bui/i,/(vodafone) ([\\w ]+)(?:\\)| bui)/i],[f,d,[m,w]],[/(surface duo)/i],[d,[f,B],[m,w]],[/droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i],[d,[f,"Fairphone"],[m,h]],[/(u304aa)/i],[d,[f,"AT&T"],[m,h]],[/\\bsie-(\\w*)/i],[d,[f,"Siemens"],[m,h]],[/\\b(rct\\w+) b/i],[d,[f,"RCA"],[m,w]],[/\\b(venue[\\d ]{2,7}) b/i],[d,[f,"Dell"],[m,w]],[/\\b(q(?:mv|ta)\\w+) b/i],[d,[f,"Verizon"],[m,w]],[/\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i],[d,[f,"Barnes & Noble"],[m,w]],[/\\b(tm\\d{3}\\w+) b/i],[d,[f,"NuVision"],[m,w]],[/\\b(k88) b/i],[d,[f,"ZTE"],[m,w]],[/\\b(nx\\d{3}j) b/i],[d,[f,"ZTE"],[m,h]],[/\\b(gen\\d{3}) b.+49h/i],[d,[f,"Swiss"],[m,h]],[/\\b(zur\\d{3}) b/i],[d,[f,"Swiss"],[m,w]],[/\\b((zeki)?tb.*\\b) b/i],[d,[f,"Zeki"],[m,w]],[/\\b([yr]\\d{2}) b/i,/\\b(dragon[- ]+touch |dt)(\\w{5}) b/i],[[f,"Dragon Touch"],d,[m,w]],[/\\b(ns-?\\w{0,9}) b/i],[d,[f,"Insignia"],[m,w]],[/\\b((nxa|next)-?\\w{0,9}) b/i],[d,[f,"NextBook"],[m,w]],[/\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],d,[m,h]],[/\\b(lvtel\\-)?(v1[12]) b/i],[[f,"LvTel"],d,[m,h]],[/\\b(ph-1) /i],[d,[f,"Essential"],[m,h]],[/\\b(v(100md|700na|7011|917g).*\\b) b/i],[d,[f,"Envizen"],[m,w]],[/\\b(trio[-\\w\\. ]+) b/i],[d,[f,"MachSpeed"],[m,w]],[/\\btu_(1491) b/i],[d,[f,"Rotor"],[m,w]],[/(shield[\\w ]+) b/i],[d,[f,"Nvidia"],[m,w]],[/(sprint) (\\w+)/i],[f,d,[m,h]],[/(kin\\.[onetw]{3})/i],[[d,/\\./g," "],[f,B],[m,h]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i],[d,[f,q],[m,w]],[/droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i],[d,[f,q],[m,h]],[/smart-tv.+(samsung)/i],[f,[m,y]],[/hbbtv.+maple;(\\d+)/i],[[d,/^/,"SmartTV"],[f,A],[m,y]],[/(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i],[[f,M],[m,y]],[/(apple) ?tv/i],[f,[d,S+" TV"],[m,y]],[/crkey/i],[[d,j+"cast"],[f,I],[m,y]],[/droid.+aft(\\w)( bui|\\))/i],[d,[f,_],[m,y]],[/\\(dtv[\\);].+(aquos)/i,/(aquos-tv[\\w ]+)\\)/i],[d,[f,L],[m,y]],[/(bravia[\\w ]+)( bui|\\))/i],[d,[f,U],[m,y]],[/(mitv-\\w{5}) bui/i],[d,[f,R],[m,y]],[/Hbbtv.*(technisat) (.*);/i],[f,d,[m,y]],[/\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,/hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i],[[f,H],[d,H],[m,y]],[/\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i],[[m,y]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,d,[m,b]],[/droid.+; (shield) bui/i],[d,[f,"Nvidia"],[m,b]],[/(playstation [345portablevi]+)/i],[d,[f,U],[m,b]],[/\\b(xbox(?: one)?(?!; xbox))[\\); ]/i],[d,[f,B],[m,b]],[/((pebble))app/i],[f,d,[m,T]],[/(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i],[d,[f,S],[m,T]],[/droid.+; (glass) \\d/i],[d,[f,I],[m,T]],[/droid.+; (wt63?0{2,3})\\)/i],[d,[f,q],[m,T]],[/(quest( 2| pro)?)/i],[d,[f,F],[m,T]],[/(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i],[f,[m,k]],[/(aeobc)\\b/i],[d,[f,_],[m,k]],[/droid .+?; ([^;]+?)(?: bui|\\) applew).+? mobile safari/i],[d,[m,h]],[/droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i],[d,[m,w]],[/\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i],[[m,w]],[/(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i],[[m,h]],[/(android[-\\w\\. ]{0,9});.+buil/i],[d,[f,"Generic"]]],engine:[[/windows.+ edge\\/([\\w\\.]+)/i],[v,[p,"EdgeHTML"]],[/webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i],[v,[p,"Blink"]],[/(presto)\\/([\\w\\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i,/ekioh(flow)\\/([\\w\\.]+)/i,/(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,/(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,/\\b(libweb)/i],[p,v],[/rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i],[v,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,v],[/(windows) nt 6\\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i,/(windows)[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i],[p,[v,J,X]],[/(win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i],[[p,"Windows"],[v,J,X]],[/ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,/ios;fbsv\\/([\\d\\.]+)/i,/cfnetwork\\/.+darwin/i],[[v,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\\w\\. ]*)/i,/(macintosh|mac_powerpc\\b)(?!.+haiku)/i],[[p,K],[v,/_/g,"."]],[/droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i],[v,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,/(blackberry)\\w*\\/([\\w\\.]*)/i,/(tizen|kaios)[\\/ ]([\\w\\.]+)/i,/\\((series40);/i],[p,v],[/\\(bb(10);/i],[v,[p,x]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i],[v,[p,"Symbian"]],[/mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i],[v,[p,N+" OS"]],[/web0s;.+rt(tv)/i,/\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i],[v,[p,"webOS"]],[/watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i],[v,[p,"watchOS"]],[/crkey\\/([\\d\\.]+)/i],[v,[p,j+"cast"]],[/(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i],[[p,z],v],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\\/(\\d+\\.[\\w\\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\\);]+)/i,/\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,/(mint)[\\/\\(\\) ]?(\\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,/(hurd|linux) ?([\\w\\.]*)/i,/(gnu) ?([\\w\\.]*)/i,/\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i,/(haiku) (\\w+)/i],[p,v],[/(sunos) ?([\\w\\.\\d]*)/i],[[p,"Solaris"],v],[/((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,/(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,/\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\\w\\.]*)/i],[p,v]]},Z=function(e,t){if(typeof e===c&&(t=e,e=a),!(this instanceof Z))return new Z(e,t).getResult();var n=typeof i!==s&&i.navigator?i.navigator:a,r=e||(n&&n.userAgent?n.userAgent:""),b=n&&n.userAgentData?n.userAgentData:a,y=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(G,t):G,T=n&&n.userAgent==r;return this.getBrowser=function(){var e,t={};return t[p]=a,t[v]=a,V.call(t,r,y.browser),t[l]=typeof(e=t[v])===u?e.replace(/[^\\d\\.]/g,"").split(".")[0]:a,T&&n&&n.brave&&typeof n.brave.isBrave==o&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[g]=a,V.call(e,r,y.cpu),e},this.getDevice=function(){var e={};return e[f]=a,e[d]=a,e[m]=a,V.call(e,r,y.device),T&&!e[m]&&b&&b.mobile&&(e[m]=h),T&&"Macintosh"==e[d]&&n&&typeof n.standalone!==s&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[d]="iPad",e[m]=w),e},this.getEngine=function(){var e={};return e[p]=a,e[v]=a,V.call(e,r,y.engine),e},this.getOS=function(){var e={};return e[p]=a,e[v]=a,V.call(e,r,y.os),T&&!e[p]&&b&&"Unknown"!=b.platform&&(e[p]=b.platform.replace(/chrome os/i,z).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===u&&e.length>350?H(e,350):e,this},this.setUA(r),this};Z.VERSION="1.0.35",Z.BROWSER=$([p,v,l]),Z.CPU=$([g]),Z.DEVICE=$([d,f,m,b,h,y,w,T,k]),Z.ENGINE=Z.OS=$([p,v]),typeof t!==s?(e.exports&&(t=e.exports=Z),t.UAParser=Z):n.amdO?(r=function(){return Z}.call(t,n,t,e))===a||(e.exports=r):typeof i!==s&&(i.UAParser=Z);var Y=typeof i!==s&&(i.jQuery||i.Zepto);if(Y&&!Y.ua){var ee=new Z;Y.ua=ee.getResult(),Y.ua.get=function(){return ee.getUA()},Y.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var n in t)Y.ua[n]=t[n]}}}("object"==typeof window?window:this)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.amdO={},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return(()=>{"use strict";n.r(r),n.d(r,{ActionType:()=>f,AmplitudePlatformName:()=>g,AnalyticsEventImportance:()=>l,AnalyticsQueries:()=>e,AuthStatus:()=>b,ComponentType:()=>m,IThresholdTier:()=>Jt,MetricType:()=>d,PlatformName:()=>v,SessionActions:()=>h,SessionAutomatedEvents:()=>w,SessionRank:()=>y,SubjectType:()=>p,UserTypeCommerce:()=>c,UserTypeInsto:()=>i,UserTypeRetail:()=>t,UserTypeRetailBusinessBanking:()=>s,UserTypeRetailEmployeeInternal:()=>a,UserTypeRetailEmployeePersonal:()=>o,UserTypeWallet:()=>u,automatedEvents:()=>xn,automatedMappingConfig:()=>In,clearMarkEntry:()=>Vn,clearPerformanceMarkEntries:()=>Xn,config:()=>A,createEventConfig:()=>On,createNewSpan:()=>Ln,createNewTrace:()=>Un,device:()=>W,endPerfMark:()=>Jn,exposeExperiment:()=>wn,flushQueue:()=>or,generateUUID:()=>V,getAnalyticsHeaders:()=>sr,getReferrerData:()=>le,getTracingHeaders:()=>An,getTracingId:()=>Dn,getUrlHostname:()=>pe,getUrlParams:()=>me,getUrlPathname:()=>fe,getUserContext:()=>ar,identify:()=>Tn,identifyFlow:()=>xe,identity:()=>H,identityFlow:()=>Se,incrementUjNavigation:()=>an,init:()=>yn,initNextJsTrackPageview:()=>_n,initTrackPageview:()=>kn,isEventKeyFormatValid:()=>we,isSessionEnded:()=>pt,location:()=>re,logEvent:()=>$t,logMetric:()=>Ht,logPageView:()=>on,logTrace:()=>Rn,markNTBT:()=>tn,markStep:()=>nn,markStepOnce:()=>rn,onVisibilityChange:()=>ln,optIn:()=>En,optOut:()=>Sn,perfMark:()=>Wn,persistentData:()=>oe,postMessage:()=>K,recordSessionDuration:()=>pn,removeFromIdentifyFlow:()=>Ee,savePersistentData:()=>st,sendScheduledEvents:()=>Bt,setBreadcrumbs:()=>ie,setConfig:()=>U,setLocation:()=>ae,setPagePath:()=>ve,setPageview:()=>Kt,setPersistentData:()=>se,setSessionStart:()=>dt,setTime:()=>Ue,startPerfMark:()=>Hn,timeStone:()=>Le,useEventLogger:()=>Yn,useLogEventOnMount:()=>tr,usePerformanceMarks:()=>rr});let e=function(e){return e.fbclid="fbclid",e.gclid="gclid",e.msclkid="msclkid",e.ptclid="ptclid",e.ttclid="ttclid",e.utm_source="utm_source",e.utm_medium="utm_medium",e.utm_campaign="utm_campaign",e.utm_term="utm_term",e.utm_content="utm_content",e}({});const t=0,i=1,a=2,o=3,s=4,c=5,u=6;let l=function(e){return e.low="low",e.high="high",e}({}),d=function(e){return e.count="count",e.rate="rate",e.gauge="gauge",e.distribution="distribution",e.histogram="histogram",e}({}),p=function(e){return e.commerce_merchant="commerce_merchant",e.device="device",e.edp_fingerprint_id="edp_fingerprint_id",e.nft_user="nft_user",e.user="user",e.wallet_user="wallet_user",e.uuid="user_uuid",e}({}),m=function(e){return e.unknown="unknown",e.banner="banner",e.button="button",e.card="card",e.chart="chart",e.content_script="content_script",e.dropdown="dropdown",e.link="link",e.page="page",e.modal="modal",e.table="table",e.search_bar="search_bar",e.service_worker="service_worker",e.text="text",e.text_input="text_input",e.tray="tray",e.checkbox="checkbox",e.icon="icon",e}({}),f=function(e){return e.unknown="unknown",e.blur="blur",e.click="click",e.change="change",e.dismiss="dismiss",e.focus="focus",e.hover="hover",e.select="select",e.measurement="measurement",e.move="move",e.process="process",e.render="render",e.scroll="scroll",e.view="view",e.search="search",e.keyPress="keyPress",e}({}),v=function(e){return e.unknown="unknown",e.web="web",e.android="android",e.ios="ios",e.mobile_web="mobile_web",e.tablet_web="tablet_web",e.server="server",e.windows="windows",e.macos="macos",e.extension="extension",e}({}),g=function(e){return e.web="Web",e.ios="iOS",e.android="Android",e}({}),b=function(e){return e[e.notLoggedIn=0]="notLoggedIn",e[e.loggedIn=1]="loggedIn",e}({}),h=function(e){return e.ac="ac",e.af="af",e.ah="ah",e.al="al",e.am="am",e.ar="ar",e.as="as",e}({}),w=function(e){return e.pv="pv",e}({}),y=function(e){return e.xs="xs",e.s="s",e.m="m",e.l="l",e.xl="xl",e.xxl="xxl",e}({});const T="https://analytics-service-dev.cbhq.net",k=3e5,_=5e3,S="analytics-db",E="experiment-exposure-db",x="Analytics SDK:",O=Object.values(e),j="pageview",N="session_duration",I={navigationTiming:{eventName:"perf_navigation_timing"},redirectTime:{eventName:"perf_redirect_time"},RT:{eventName:"perf_redirect_time"},TTFB:{eventName:"perf_time_to_first_byte"},networkInformation:{eventName:"perf_network_information"},storageEstimate:{eventName:"perf_storage_estimate"},FCP:{eventName:"perf_first_contentful_paint"},FID:{eventName:"perf_first_input_delay"},LCP:{eventName:"perf_largest_contentful_paint"},CLS:{eventName:"perf_cumulative_layout_shift"},TBT:{eventName:"perf_total_blocking_time"},NTBT:{eventName:"perf_navigation_total_blocking_time"},INP:{eventName:"perf_interact_to_next_paint"},ET:{eventName:"perf_element_timing"},userJourneyStep:{eventName:"perf_user_journey_step"}},P="1",M="web";function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}const C=/^(https?:\\/\\/)/;function D(e){return{eventsEndpoint:e+"/amp",metricsEndPoint:e+"/metrics",exposureEndpoint:e+"/track-exposures",tracesEndpoint:e+"/traces"}}const A=B({authCookie:"logged_in",amplitudeApiKey:"",batchEventsPeriod:_,batchEventsThreshold:30,batchMetricsPeriod:_,batchMetricsThreshold:30,batchTracesPeriod:_,batchTracesThreshold:30,headers:{},interactionManager:null,isAlwaysAuthed:!1,isProd:!1,isInternalApplication:!1,onError:(e,t)=>{console.error(x,e,t)},platform:v.unknown,projectName:"",ricTimeoutScheduleEvent:1e3,ricTimeoutSetDevice:500,showDebugLogging:!1,trackUserId:!1,version:null,apiEndpoint:T},D(T),{steps:{}}),L=[].reduce(((e,t)=>n=>e(t(n))),(e=>{if(!e.isProd)return e.isInternalApplication?(e.apiEndpoint="https://analytics-service-internal-dev.cbhq.net",B({},e,D(e.apiEndpoint))):e;const t=(e=>e.apiEndpoint?C.test(e.apiEndpoint)?e.apiEndpoint:`https://${e.apiEndpoint}`:e.isInternalApplication?"https://analytics-service-internal.cbhq.net":"https://as.coinbase.com")(e);return B({},e,{apiEndpoint:t},D(t))})),U=e=>{const{batchEventsThreshold:t,batchMetricsThreshold:n,batchTracesThreshold:r}=e,i=[t,n,r];for(const e of i)if((e||0)>30){console.warn("You are setting the threshhold for the batch limit to be greater than 30. This may cause request overload.");break}Object.assign(A,L(e))},R=[v.web,v.mobile_web,v.tablet_web];function q(){return"android"===A.platform}function F(){return"ios"===A.platform}function z(){return R.includes(A.platform)}function K(e){if(z()&&navigator&&"serviceWorker"in navigator&&navigator.serviceWorker.controller)try{navigator.serviceWorker.controller.postMessage(e)}catch(e){e instanceof Error&&A.onError(e)}}var $=n(353),Q=n.n($);const W={amplitudeOSName:null,amplitudeOSVersion:null,amplitudeDeviceModel:null,amplitudePlatform:null,browserName:null,browserMajor:null,osName:null,userAgent:null,width:null,height:null},H={countryCode:null,deviceId:null,device_os:null,isOptOut:!1,languageCode:null,locale:null,jwt:null,session_lcc_id:null,userAgent:null,userId:null},V=e=>e?(e^16*Math.random()>>e/4).toString(16):"10000000-1000-4000-8000-100000000000".replace(/[018]/g,V),J=()=>A.isAlwaysAuthed||!!H.userId,X=()=>{const e={};return H.countryCode&&(e.country_code=H.countryCode),e},G=()=>{const{platform:e}=A;if(e===v.web)switch(!0){case window.matchMedia("(max-width: 560px)").matches:return v.mobile_web;case window.matchMedia("(max-width: 1024px, min-width: 561px)").matches:return v.tablet_web}return e},Z=()=>{var e,t,n,r,i;z()?("requestIdleCallback"in window?window.requestIdleCallback(ne,{timeout:A.ricTimeoutSetDevice}):ne(),W.amplitudePlatform=g.web,W.userAgent=(null==(e=window)||null==(e=e.navigator)?void 0:e.userAgent)||null,ee({height:null!=(t=null==(n=window)?void 0:n.innerHeight)?t:null,width:null!=(r=null==(i=window)?void 0:i.innerWidth)?r:null})):F()?(W.amplitudePlatform=g.ios,W.userAgent=H.userAgent,W.userAgent&&ne()):q()&&(W.userAgent=H.userAgent,W.amplitudePlatform=g.android,W.userAgent&&ne())},Y=e=>{Object.assign(H,e),z()&&K({identity:{isAuthed:!!H.userId,locale:H.locale||null}})},ee=e=>{W.height=e.height,W.width=e.width},te=()=>{U({platform:G()}),z()&&K({config:{platform:A.platform}})},ne=()=>{var e;performance.mark&&performance.mark("ua_parser_start");const t=new(Q())(null!=(e=W.userAgent)?e:"").getResult();W.browserName=t.browser.name||null,W.browserMajor=t.browser.major||null,W.osName=t.os.name||null,W.amplitudeOSName=W.browserName,W.amplitudeOSVersion=W.browserMajor,W.amplitudeDeviceModel=W.osName,K({device:{browserName:W.browserName,osName:W.osName}}),performance.mark&&(performance.mark("ua_parser_end"),performance.measure("ua_parser","ua_parser_start","ua_parser_end"))},re={breadcrumbs:[],initialUAAData:{},pageKey:"",pageKeyRegex:{},pagePath:"",prevPageKey:"",prevPagePath:""};function ie(e){Object.assign(re,{breadcrumbs:e})}function ae(e){Object.assign(re,e)}const oe={eventId:0,sequenceNumber:0,sessionId:0,lastEventTime:0,sessionStart:0,sessionUUID:null,userId:null,ac:0,af:0,ah:0,al:0,am:0,ar:0,as:0,pv:0};function se(e){Object.assign(oe,e)}function ce(){var e,t;return null!=(e=null==(t=document)?void 0:t.referrer)?e:""}function ue(){return ue=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ue.apply(this,arguments)}const le=()=>{const e=ce();if(!e)return{};const t=new URL(e);return t.hostname===pe()?{}:{referrer:e,referring_domain:t.hostname}},de=()=>{const e=new URLSearchParams(me()),t={};return O.forEach((n=>{e.has(n)&&(t[n]=(e.get(n)||"").toLowerCase())})),t},pe=()=>{var e;return(null==(e=window)||null==(e=e.location)?void 0:e.hostname)||""},me=()=>{var e;return(null==(e=window)||null==(e=e.location)?void 0:e.search)||""},fe=()=>{var e;return(null==(e=window)||null==(e=e.location)?void 0:e.pathname)||""},ve=()=>{const e=A.overrideWindowLocation?re.pagePath:fe()+me();e&&e!==re.pagePath&&(e!==re.pagePath&&ge(),re.pagePath=e,re.pageKeyRegex&&Object.keys(re.pageKeyRegex).some((e=>{if(re.pageKeyRegex[e].test(re.pagePath))return re.pageKey=e,!0})))},ge=()=>{if(z()){const e=ce();if(!re.prevPagePath&&e){const t=new URL(e);if(t.hostname===pe())return void(re.prevPagePath=t.pathname)}}re.prevPagePath=re.pagePath,re.prevPageKey=re.pageKey},be=e=>{z()&&Object.assign(e,z()?(Object.keys(re.initialUAAData).length>0||(new URLSearchParams(me()),re.initialUAAData=ue({},(()=>{const e={};return O.forEach((t=>{oe[t]&&(e[t]=oe[t])})),e})(),de(),le())),re.initialUAAData):re.initialUAAData)},he=/^[a-zd]+(_[a-zd]+)*$/;function we(e){return he.test(e)}function ye(){return ye=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ye.apply(this,arguments)}const Te=["action","component_type","component_name","context","logging_id"],ke=["num_non_hardware_accounts","ujs"],_e="ujs_",Se={};function Ee(e){e.forEach((e=>{ke.includes(e)&&delete Se[e]}))}function xe(e){var t;const n=Object.entries(e).reduce(((e,t)=>{const[n,r]=t;return!Te.includes(n)&&ke.includes(n)?we(n)?ye({},e,{[n]:r}):(A.onError(new Error("IdentityFlow property names must have snake case format"),{[n]:r}),e):e}),{});null!=(t=n.ujs)&&t.length&&(n.ujs=n.ujs.map((e=>`${_e}${e}`))),Object.assign(Se,n)}function Oe(){return A.platform!==v.unknown||(A.onError(new Error("SDK platform not initialized")),!1)}const je={eventsQueue:[],eventsScheduled:!1,metricsQueue:[],metricsScheduled:!1,tracesQueue:[],tracesScheduled:!1};function Ne(e){Object.assign(je,e)}const Ie={ac:0,af:0,ah:0,al:0,am:0,ar:0,as:0,pv:0,sqs:0},Pe={ac:20,af:5,ah:1,al:1,am:0,ar:10,as:20},Me={pv:25},Be={xs:0,s:1,m:1,l:2,xl:2,xxl:2},Ce=e=>e<15?y.xs:e<60?y.s:e<240?y.m:e<960?y.l:e<3840?y.xl:y.xxl,De=e=>{Object.assign(Ie,e)};function Ae(){return(new Date).getTime()}const Le={timeStart:Ae(),timeOnPagePath:0,timeOnPageKey:0,prevTimeOnPagePath:0,prevTimeOnPageKey:0,sessionDuration:0,sessionEnd:0,sessionStart:0,prevSessionDuration:0};function Ue(e){Object.assign(Le,e)}const Re=(e,t)=>t.some((t=>e instanceof t));let qe,Fe;const ze=new WeakMap,Ke=new WeakMap,$e=new WeakMap,Qe=new WeakMap,We=new WeakMap;let He={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return Ke.get(e);if("objectStoreNames"===t)return e.objectStoreNames||$e.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return Je(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function Ve(e){return"function"==typeof e?(t=e)!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(Fe||(Fe=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(Xe(this),e),Je(ze.get(this))}:function(...e){return Je(t.apply(Xe(this),e))}:function(e,...n){const r=t.call(Xe(this),e,...n);return $e.set(r,e.sort?e.sort():[e]),Je(r)}:(e instanceof IDBTransaction&&function(e){if(Ke.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",i),e.removeEventListener("error",a),e.removeEventListener("abort",a)},i=()=>{t(),r()},a=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",i),e.addEventListener("error",a),e.addEventListener("abort",a)}));Ke.set(e,t)}(e),Re(e,qe||(qe=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction]))?new Proxy(e,He):e);var t}function Je(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",i),e.removeEventListener("error",a)},i=()=>{t(Je(e.result)),r()},a=()=>{n(e.error),r()};e.addEventListener("success",i),e.addEventListener("error",a)}));return t.then((t=>{t instanceof IDBCursor&&ze.set(t,e)})).catch((()=>{})),We.set(t,e),t}(e);if(Qe.has(e))return Qe.get(e);const t=Ve(e);return t!==e&&(Qe.set(e,t),We.set(t,e)),t}const Xe=e=>We.get(e),Ge=["get","getKey","getAll","getAllKeys","count"],Ze=["put","add","delete","clear"],Ye=new Map;function et(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(Ye.get(t))return Ye.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,i=Ze.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!i&&!Ge.includes(n))return;const a=async function(e,...t){const a=this.transaction(e,i?"readwrite":"readonly");let o=a.store;return r&&(o=o.index(t.shift())),(await Promise.all([o[n](...t),i&&a.done]))[0]};return Ye.set(t,a),a}var tt;tt=He,He={...tt,get:(e,t,n)=>et(e,t)||tt.get(e,t,n),has:(e,t)=>!!et(e,t)||tt.has(e,t)};const nt={isReady:!1,idbKeyval:null};function rt(e){Object.assign(nt,e)}const it={},at=async e=>{if(!nt.idbKeyval)return Promise.resolve(null);try{return await nt.idbKeyval.get(e)}catch(e){return A.onError(new Error("IndexedDB:Get:InternalError")),Promise.resolve(null)}},ot=async(e,t)=>{if(nt.idbKeyval)try{await nt.idbKeyval.set(e,t)}catch(e){A.onError(new Error("IndexedDB:Set:InternalError"))}},st=()=>{"server"!==A.platform&&(se({sessionStart:Le.sessionStart,ac:Ie.ac,af:Ie.af,ah:Ie.ah,al:Ie.al,am:Ie.am,ar:Ie.ar,as:Ie.as,pv:Ie.pv}),H.userId&&se({userId:H.userId}),ot(S,oe))},ct="rgb(5,177,105)",ut=e=>{const{metricName:t,data:n}=e,r=e.importance||l.low;if(!A.showDebugLogging||!console)return;const i=`%c ${x}`,a=`color:${ct};font-size:11px;`,o=`Importance: ${r}`;console.group(i,a,t,o),n.forEach((e=>{e.event_type?console.log(e.event_type,e):console.log(e)})),console.groupEnd()},lt=e=>{const{metricName:t,data:n}=e,r=e.importance||l.low;if(!A.showDebugLogging||!console)return;const i=`color:${ct};font-size:11px;`,a=`%c ${x}`,o=`Importance: ${r}`;console.log(a,i,t,n,o)},dt=()=>{const e=Ae();oe.sessionId&&oe.lastEventTime&&oe.sessionUUID&&!pt(e)||(oe.sessionId=e,oe.sessionUUID=V(),Ue({sessionStart:e}),lt({metricName:"Started new session:",data:{persistentData:oe,timeStone:Le}})),oe.lastEventTime=e},pt=e=>e-oe.lastEventTime>18e5;function mt(){return mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mt.apply(this,arguments)}const ft=e=>{var t;(e=>{switch(e.action){case f.click:Ie.ac+=1;break;case f.focus:Ie.af+=1;break;case f.hover:Ie.ah+=1;break;case f.move:Ie.am+=1;break;case f.scroll:Ie.al+=1;break;case f.search:Ie.ar+=1;break;case f.select:Ie.as+=1}})(t=e),t.event_type!==j?t.event_type===N&&((e=>{if(!e.session_rank)return;const t=e.session_rank;Object.values(h).forEach((e=>{Ie.sqs+=Ie[e]*Pe[e]})),Object.values(w).forEach((e=>{Ie.sqs+=Ie[e]*Me[e]})),Ie.sqs*=Be[t]})(t),Object.assign(t,Ie),De({ac:0,af:0,ah:0,al:0,am:0,ar:0,as:0,pv:0,sqs:0})):Ie.pv+=1;const n=e.event_type;delete e.event_type;const r=e.deviceId?e.deviceId:null,i=e.timestamp;return delete e.timestamp,se({eventId:oe.eventId+1}),se({sequenceNumber:oe.sequenceNumber+1}),dt(),st(),{device_id:H.deviceId||r||null,user_id:H.userId,timestamp:i,event_id:oe.eventId,session_id:oe.sessionId||-1,event_type:n,version_name:A.version||null,platform:W.amplitudePlatform,os_name:W.amplitudeOSName,os_version:W.amplitudeOSVersion,device_model:W.amplitudeDeviceModel,language:H.languageCode,event_properties:mt({},e,{session_uuid:oe.sessionUUID,height:W.height,width:W.width}),user_properties:X(),uuid:V(),library:{name:"@cbhq/client-analytics",version:"10.6.0"},sequence_number:oe.sequenceNumber,user_agent:W.userAgent||H.userAgent}},vt=e=>e.map((e=>ft(e)));function gt(){return gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gt.apply(this,arguments)}const bt=e=>e.map((e=>(e=>{const t=e.tags||{},n=gt({authed:J()?"true":"false",platform:A.platform},t,{project_name:A.projectName,version_name:A.version||null});return{metric_name:e.metricName,page_path:e.pagePath||null,value:e.value,tags:n,type:e.metricType}})(e))),ht=e=>0!==je.metricsQueue.length&&(je.metricsQueue.length>=A.batchMetricsThreshold||(je.metricsScheduled||(je.metricsScheduled=!0,setTimeout((()=>{je.metricsScheduled=!1,e(bt(je.metricsQueue)),je.metricsQueue=[]}),A.batchMetricsPeriod)),!1)),wt=e=>0!==je.tracesQueue.length&&(je.tracesQueue.length>=A.batchTracesThreshold||(je.tracesScheduled||(je.tracesScheduled=!0,setTimeout((()=>{je.tracesScheduled=!1,e(je.tracesQueue),je.tracesQueue=[]}),A.batchTracesPeriod)),!1)),yt=e=>{var t;z()&&null!=(t=window)&&t.requestIdleCallback?window.requestIdleCallback(e,{timeout:A.ricTimeoutScheduleEvent}):(q()||F())&&A.interactionManager?A.interactionManager.runAfterInteractions(e):e()};function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tt.apply(this,arguments)}const kt="application/x-www-form-urlencoded; charset=UTF-8",_t=e=>{const{data:t,importance:n,isJSON:r,onError:i,url:a}=e,o=r?"application/json":kt,s=n||l.low,c=r?JSON.stringify(t):new URLSearchParams(t).toString();function u(){const e=new XMLHttpRequest;e.open("POST",a,!0),Object.keys(A.headers||{}).forEach((t=>{e.setRequestHeader(t,A.headers[t])})),e.setRequestHeader("Content-Type",kt),H.jwt&&e.setRequestHeader("authorization",`Bearer ${H.jwt}`),e.send(c)}if(!z()||r||!("sendBeacon"in navigator)||s!==l.low||A.headers&&0!==Object.keys(A.headers).length)if(z()&&!r)u();else{const e=Tt({},A.headers,{"Content-Type":o});H.jwt&&(e.Authorization=`Bearer ${H.jwt}`),fetch(a,{method:"POST",mode:"no-cors",headers:e,body:c}).catch((e=>{i(e,{context:"AnalyticsSDKApiError"})}))}else{const e=new Blob([c],{type:kt});try{navigator.sendBeacon.bind(navigator)(a,e)||u()}catch(e){console.error(e),u()}}};var St=n(762),Et=n.n(St);const xt=(e,t,n)=>{const r=e||"";return Et()("2"+r+t+n)};function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ot.apply(this,arguments)}class jt extends Error{constructor(e){super(e),this.name="CircularJsonReference",this.message=e,"function"==typeof Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error(e).stack}}class Nt extends jt{constructor(...e){super(...e),this.name="DomReferenceInAnalyticsEvent"}}function It(){return It=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},It.apply(this,arguments)}const Pt=(e,t=l.low)=>{var n;e&&je.eventsQueue.push(e),nt.isReady&&(!A.trackUserId||H.userId?(t===l.high||(n=Mt,0!==je.eventsQueue.length&&(je.eventsQueue.length>=A.batchEventsThreshold||(je.eventsScheduled||(je.eventsScheduled=!0,setTimeout((()=>{je.eventsScheduled=!1,n(vt(je.eventsQueue)),je.eventsQueue=[]}),A.batchEventsPeriod)),0))))&&Bt():je.eventsQueue.length>10&&(A.trackUserId=!1,A.onError(new Error("userId not set in Logged-in"))))},Mt=(e,t=l.low)=>{if(H.isOptOut||0===e.length)return;let n;try{n=JSON.stringify(e)}catch(t){const r=e.map((e=>e.event_type)).join(", "),[i,a]=(e=>{try{const n=[];for(const r of e){const e=Ot({},r);r.event_properties&&(e.event_properties=Ot({},e.event_properties,{currentTarget:null,target:null,relatedTarget:null,_dispatchInstances:null,_targetInst:null,view:(t=r.event_properties.view,["string","number","boolean"].includes(typeof t)?r.event_properties.view:null)})),n.push(e)}return[!0,JSON.stringify(n)]}catch(e){return[!1,""]}var t})(e);if(!i)return void A.onError(new jt(t instanceof Error?t.message:"unknown"),{listEventType:r});n=a,A.onError(new Nt("Found DOM element reference"),{listEventType:r,stringifiedEventData:n})}const r=Ae().toString(),i=It({},{e:n,v:"2",upload_time:r},{client:A.amplitudeApiKey,checksum:xt(A.amplitudeApiKey,n,r)});_t({url:A.eventsEndpoint,data:i,importance:t,onError:A.onError}),ut({metricName:"Batch Events",data:e,importance:t})},Bt=()=>{Mt(vt(je.eventsQueue)),Ne({eventsQueue:[]})};function Ct(){return Ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ct.apply(this,arguments)}const Dt=Object.values(f),At=Object.values(m),Lt=e=>Dt.includes(e)?e:f.unknown,Ut=e=>At.includes(e)?e:m.unknown,Rt=(e,t,n)=>{const r={auth:J()?b.loggedIn:b.notLoggedIn,action:Lt(e),component_type:Ut(t),logging_id:n,platform:A.platform,project_name:A.projectName};return"number"==typeof H.userTypeEnum&&(r.user_type_enum=H.userTypeEnum),r},qt=e=>{const t=Ae();if(!e)return A.onError(new Error("missing logData")),Ct({},Rt(f.unknown,m.unknown),{locale:H.locale,session_lcc_id:H.session_lcc_id,timestamp:t,time_start:Le.timeStart});const n=Ct({},e,Rt(e.action,e.componentType,e.loggingId),{locale:H.locale,session_lcc_id:H.session_lcc_id,timestamp:t,time_start:Le.timeStart});return delete n.componentType,delete n.loggingId,n},Ft={blacklistRegex:[],isEnabled:!1};function zt(){return{page_key:re.pageKey,page_path:re.pagePath,prev_page_key:re.prevPageKey,prev_page_path:re.prevPagePath}}function Kt(e){Object.assign(Ft,e)}function $t(e,t,n=l.low){if(H.isOptOut)return;if(!Oe())return;const r=qt(t);!function(e){Ft.isEnabled&&(ve(),Object.assign(e,zt()))}(r),be(r),function(e){Object.keys(Se).length>0&&Object.assign(e,Se)}(r),r.has_double_fired=!1,r.event_type=e,n===l.high?Pt(r,n):yt((()=>{Pt(r)}))}function Qt(e,t=!1){t?_t({url:A.metricsEndPoint,data:{metrics:e},isJSON:!0,onError:A.onError}):yt((()=>{_t({url:A.metricsEndPoint,data:{metrics:e},isJSON:!0,onError:A.onError})})),ut({metricName:"Batch Metrics",data:e})}function Wt(){return Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wt.apply(this,arguments)}function Ht(e){if(!Oe())return;v.server!==A.platform&&!e.pagePath&&re.pagePath&&(e.pagePath=re.pagePath);const t=Object.keys(Se).length?Wt({},e.tags,Se):e.tags;t&&Object.assign(e,{tags:t}),je.metricsQueue.push(e),ht(Qt)&&(Qt(bt(je.metricsQueue)),je.metricsQueue=[])}function Vt(){return Vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vt.apply(this,arguments)}let Jt=function(e){return e.instant="instant",e.quick="quick",e.moderate="moderate",e.slow="slow",e.unavoidable="unavoidable",e}({});function Xt(e){return e.toLowerCase()}let Gt={};const Zt=(e,t)=>{null!=A&&A.onMarkStep&&A.onMarkStep(e,t),xe({ujs:t})};let Yt;const en={Perfume:()=>{},markStep:e=>{},markStepOnce:e=>{},incrementUjNavigation:()=>{}},tn=()=>{z()&&Yt&&Yt.markNTBT&&Yt.markNTBT()},nn=e=>{z()&&Yt&&en.markStep&&en.markStep(e)},rn=e=>{z()&&Yt&&en.markStepOnce&&en.markStepOnce(e)},an=()=>{z()&&Yt&&en.incrementUjNavigation&&en.incrementUjNavigation()};function on(e={callMarkNTBT:!0}){"unknown"!==A.platform&&(Ft.blacklistRegex.some((e=>e.test(fe())))||($t(j,{action:f.render,componentType:m.page}),e.callMarkNTBT&&tn()))}let sn=!1,cn=!1;const un=e=>{sn=!e.persisted},ln=(e,t="hidden",n=!1)=>{cn||(addEventListener("pagehide",un),addEventListener("beforeunload",(()=>{})),cn=!0),addEventListener("visibilitychange",(({timeStamp:n})=>{document.visibilityState===t&&e({timeStamp:n,isUnloading:sn})}),{capture:!0,once:n})},dn=36e3;function pn(){const e=pt(Ae());if(e&&(O.forEach((e=>{oe[e]&&delete oe[e]})),st()),!oe.lastEventTime||!Le.sessionStart||!e)return;const t=Math.round((oe.lastEventTime-Le.sessionStart)/1e3);if(t<1||t>dn)return;const n=Ce(t);$t(N,{action:f.measurement,componentType:m.page,session_duration:t,session_end:oe.lastEventTime,session_start:Le.sessionStart,session_rank:n})}function mn(){return mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mn.apply(this,arguments)}const fn=[],vn=[],gn=()=>{const e=fn.shift();e&&e()},bn=()=>{const e=vn.shift();e&&e()};let hn={};function wn(e){const t=function(e){return{test_name:e.testName,group_name:e.group,subject_id:e.subjectId,exposed_at:Ae(),subject_type:e.subjectType,platform:A.platform}}(e);hn[e.testName]=hn[e.testName]||0,hn[e.testName]+k>Ae()?lt({metricName:`Event: exposeExperiment ${e.testName} not sent`,data:t}):(hn[e.testName]=Ae(),ot(E,hn),lt({metricName:`Event: exposeExperiment ${e.testName} sent`,data:t}),_t({url:A.exposureEndpoint,data:[t],onError:(t,n)=>{hn[e.testName]=0,ot(E,hn),A.onError(t,n)},isJSON:!0,importance:l.high}))}const yn=e=>{var t,r,i;U(e),z()&&(H.languageCode=(null==(t=navigator)?void 0:t.languages[0])||(null==(r=navigator)?void 0:r.language)||""),te(),(()=>{var e;if(z()&&null!=(e=window)&&e.indexedDB){const e=function(e,t,{blocked:n,upgrade:r,blocking:i,terminated:a}={}){const o=indexedDB.open(e,t),s=Je(o);return r&&o.addEventListener("upgradeneeded",(e=>{r(Je(o.result),e.oldVersion,e.newVersion,Je(o.transaction),e)})),n&&o.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),s.then((e=>{a&&e.addEventListener("close",(()=>a())),i&&e.addEventListener("versionchange",(e=>i(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),s}("keyval-store",1,{upgrade(e){e.createObjectStore("keyval")}});rt({idbKeyval:{get:async t=>(await e).get("keyval",t),set:async(t,n)=>(await e).put("keyval",n,t),delete:async t=>(await e).delete("keyval",t),keys:async()=>(await e).getAllKeys("keyval")}})}else rt({idbKeyval:{get:async e=>new Promise((t=>{t(it[e])})),set:async(e,t)=>new Promise((n=>{it[e]=t,n(e)})),delete:async e=>new Promise((()=>{delete it[e]})),keys:async()=>new Promise((e=>{e(Object.keys(it))}))}})})(),lt({metricName:"Initialized Analytics:",data:{deviceId:H.deviceId}}),fn.push((()=>{Pt()})),(async()=>{const e=await at(S);rt({isReady:!0}),gn(),e&&(bn(),se({eventId:e.eventId||oe.eventId,sequenceNumber:e.sequenceNumber||oe.sequenceNumber,sessionId:e.sessionId||oe.sessionId,lastEventTime:e.lastEventTime||oe.lastEventTime,sessionUUID:e.sessionUUID||oe.sessionUUID}),function(e){se(mn({},function(e){const t={};return O.forEach((n=>{e[n]&&(t[n]=e[n])})),t}(e),de()))}(e),Ue({sessionStart:e.sessionStart||oe.sessionStart}),De({ac:e.ac||Ie.ac,af:e.af||Ie.af,ah:e.ah||Ie.ah,al:e.al||Ie.al,am:e.am||Ie.am,ar:e.ar||Ie.ar,as:e.as||Ie.as,pv:e.pv||Ie.pv}),A.trackUserId&&Y({userId:e.userId||H.userId}),pn(),lt({metricName:"Initialized Analytics IndexedDB:",data:e}))})(),async function(){at(E).then((e=>{hn=null!=e?e:{}})).catch((e=>{e instanceof Error&&A.onError(e)}))}(),Z(),z()&&(ln((()=>{se({lastEventTime:Ae()}),st(),Bt()}),"hidden"),ln((()=>{pn()}),"visible")),z()&&(i=()=>{var e,t,n,r;te(),ee({width:null!=(e=null==(t=window)?void 0:t.innerWidth)?e:null,height:null!=(n=null==(r=window)?void 0:r.innerHeight)?n:null})},addEventListener("resize",(()=>{requestAnimationFrame((()=>{i()}))}))),(()=>{if(z())try{const e=n(2);en.markStep=e.markStep,en.markStepOnce=e.markStepOnce,en.incrementUjNavigation=e.incrementUjNavigation,Yt=new e.Perfume({analyticsTracker:e=>{const{data:t,attribution:n,metricName:r,navigatorInformation:i,rating:a}=e,o=I[r],s=(null==n?void 0:n.category)||null;if(!o&&!s)return;const c=(null==i?void 0:i.deviceMemory)||0,u=(null==i?void 0:i.hardwareConcurrency)||0,l=(null==i?void 0:i.isLowEndDevice)||!1,p=(null==i?void 0:i.isLowEndExperience)||!1,v=(null==i?void 0:i.serviceWorkerStatus)||"unsupported",g=Vt({deviceMemory:c,hardwareConcurrency:u,isLowEndDevice:l,isLowEndExperience:p,serviceWorkerStatus:v},Gt),b={is_low_end_device:l,is_low_end_experience:p,page_key:re.pageKey||"",save_data:t.saveData||!1,service_worker:v,is_perf_metric:!0};if("navigationTiming"===r)t&&"number"==typeof t.redirectTime&&Ht({metricName:I.redirectTime.eventName,metricType:d.histogram,tags:b,value:t.redirectTime||0});else if("TTFB"===r)$t(o.eventName,Vt({action:f.measurement,componentType:m.page,duration:t||null,vitalsScore:a||null},g)),Ht({metricName:I.TTFB.eventName,metricType:d.histogram,tags:Vt({},b),value:t}),a&&Ht({metricName:`perf_web_vitals_ttfb_${a}`,metricType:d.count,tags:b,value:1});else if("networkInformation"===r)null!=t&&t.effectiveType&&(Gt=t,$t(o.eventName,{action:f.measurement,componentType:m.page,networkInformationDownlink:t.downlink,networkInformationEffectiveType:t.effectiveType,networkInformationRtt:t.rtt,networkInformationSaveData:t.saveData,navigatorDeviceMemory:c,navigatorHardwareConcurrency:u}));else if("storageEstimate"===r)$t(o.eventName,Vt({action:f.measurement,componentType:m.page},t,g)),Ht({metricName:"perf_storage_estimate_caches",metricType:d.histogram,tags:b,value:t.caches}),Ht({metricName:"perf_storage_estimate_indexed_db",metricType:d.histogram,tags:b,value:t.indexedDB});else if("CLS"===r)$t(o.eventName,Vt({action:f.measurement,componentType:m.page,score:100*t||null,vitalsScore:a||null},g)),a&&Ht({metricName:`perf_web_vitals_cls_${a}`,metricType:d.count,tags:b,value:1});else if("FID"===r){const e=(null==n?void 0:n.performanceEntry)||null,r=parseInt((null==e?void 0:e.processingStart)||"");$t(o.eventName,Vt({action:f.measurement,componentType:m.page,duration:t||null,processingStart:null!=e&&e.processingStart?r:null,startTime:null!=e&&e.startTime?parseInt(e.startTime):null,vitalsScore:a||null},g)),a&&Ht({metricName:`perf_web_vitals_fidVitals_${a}`,metricType:d.count,tags:b,value:1})}else"userJourneyStep"===r?($t("perf_user_journey_step",Vt({action:f.measurement,componentType:m.page,duration:t||null,rating:null!=a?a:null,step_name:(null==n?void 0:n.stepName)||""},g)),Ht({metricName:`user_journey_step.${A.projectName}.${A.platform}.${(null==n?void 0:n.stepName)||""}_vitals_${a}`,metricType:d.count,tags:b,value:1}),Ht({metricName:`user_journey_step.${A.projectName}.${A.platform}.${(null==n?void 0:n.stepName)||""}`,metricType:d.distribution,tags:b,value:t||null})):I[r]&&t&&($t(o.eventName,Vt({action:f.measurement,componentType:m.page,duration:t||null,vitalsScore:a||null},g)),a&&(Ht({metricName:`perf_web_vitals_${Xt(r)}_${a}`,metricType:d.count,tags:b,value:1}),"LCP"===r&&Ht({metricName:`perf_web_vitals_${Xt(r)}`,metricType:d.distribution,tags:b,value:t})))},maxMeasureTime:3e4,steps:A.steps,onMarkStep:Zt})}catch(e){e instanceof Error&&A.onError(e)}})()},Tn=e=>{Y(e),e.userAgent&&Z(),lt({metricName:"Identify:",data:{countryCode:H.countryCode,deviceId:H.deviceId,userId:H.userId}})},kn=({blacklistRegex:e,pageKeyRegex:t,browserHistory:n})=>{Kt({blacklistRegex:e||[],isEnabled:!0}),ae({pageKeyRegex:t}),on({callMarkNTBT:!1}),n.listen((()=>{on()}))},_n=({blacklistRegex:e,pageKeyRegex:t,nextJsRouter:n})=>{Kt({blacklistRegex:e||[],isEnabled:!0}),ae({pageKeyRegex:t}),on({callMarkNTBT:!1}),n.events.on("routeChangeComplete",(()=>{on()}))},Sn=()=>{Y({isOptOut:!0}),ot(S,{})},En=()=>{Y({isOptOut:!1})},xn={Button:{label:"cb_button",uuid:"e921a074-40e6-4371-8700-134d5cd633e6",componentType:m.button}};function On(e,t,n){return{componentName:e,actions:t,data:n}}function jn(){return jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jn.apply(this,arguments)}function Nn(e,t,n){const{componentName:r,data:i}=n;$t(e.label,jn({componentType:e.componentType,action:t,loggingId:e.uuid,component_name:r},i))}const In={actionMapping:{onPress:f.click,onHover:f.hover},handlers:{Button:{[f.click]:e=>Nn(xn.Button,f.click,e),[f.hover]:e=>Nn(xn.Button,f.hover,e)}}};function Pn(e,t=!1){t?_t({url:A.tracesEndpoint,data:{traces:e},isJSON:!0,onError:A.onError}):yt((()=>{_t({url:A.tracesEndpoint,data:{traces:e},isJSON:!0,onError:A.onError})})),ut({metricName:"Batch Traces",data:e})}function Mn(){return Mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mn.apply(this,arguments)}const Bn=1e6;function Cn(e){return e*Bn}function Dn(e=function(){var e;return null==(e=window)?void 0:e.crypto}()){const t=new Uint32Array(2);return null==e||e.getRandomValues(t),((BigInt(t[0])<<BigInt(32))+BigInt(t[1])).toString()}function An(e,t){return{"x-datadog-origin":"rum","x-datadog-parent-id":t,"x-datadog-sampling-priority":"1","x-datadog-trace-id":e}}function Ln(e){var t;const{name:n,traceId:r,spanId:i,start:a,duration:o,resource:s,meta:c}=e;return{duration:o?Cn(o):0,name:n,resource:s,service:A.projectName,span_id:null!=i?i:Dn(),start:a?Cn(a):0,trace_id:null!=r?r:Dn(),parent_id:P,type:M,meta:Mn({platform:A.platform},re.pageKey?{page_key:re.pageKey}:{},null!=(t=Se.ujs)&&t.length?{last_ujs:Se.ujs[Se.ujs.length-1]}:{},null!=c?c:{})}}function Un(e){return[Ln(e)]}function Rn(e,t){Oe()&&function(e){return e.length>0}(e)&&(t&&function(e,t){e.forEach((e=>function(e,t){const n=Mn({},e.meta,t.meta),r={start:t.start?Cn(t.start):e.start,duration:t.duration?Cn(t.duration):e.duration};Object.assign(e,t,Mn({meta:n},r))}(e,t)))}(e,t),je.tracesQueue.push(e),wt(Pn)&&(Pn(je.tracesQueue),je.tracesQueue=[]))}function qn(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function Fn(){return Fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fn.apply(this,arguments)}function zn(){return void 0!==typeof window&&"performance"in window&&"mark"in performance&&"getEntriesByName"in performance}function Kn(e,t){return`perf_${e}${null!=t&&t.label?`_${t.label}`:""}`}function $n(e,t,n){return`${Kn(e,n)}__${t}`}let Qn={};function Wn(e,t,n){if(!zn())return;const r=$n(e,t,n);if(performance.mark(r),"end"===t){const t=Kn(e,n);!function(e,t,n){try{performance.measure(e,t,n)}catch(e){A.onError(e)}}(t,$n(e,"start",n),r);const i=performance.getEntriesByName(t).pop();i&&Ht(Fn({metricName:e,metricType:d.distribution,value:i.duration},null!=n&&n.tags?{tags:n.tags}:{}))}}function Hn(e,t){if(!zn())return;const n=$n(e,"start",t);Qn[n]||(Wn(e,"start",t),Qn[n]=!0)}function Vn(e,t){const n=$n(e,"start",t),r=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(Qn,[n].map(qn));Qn=r}function Jn(e,t){if(!zn())return;const n=$n(e,"start",t);Qn[n]&&(Wn(e,"end",t),Vn(e,t))}function Xn(){zn()&&(performance.clearMarks(),Qn={})}var Gn=n(784);function Zn(){return Zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zn.apply(this,arguments)}function Yn(e,t,n=l.low){const r=(0,Gn.useRef)(t);return(0,Gn.useEffect)((()=>{r.current=t}),[t]),(0,Gn.useCallback)((t=>{$t(e,Zn({},r.current,t),n)}),[e,n])}function er(){return er=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},er.apply(this,arguments)}function tr(e,t,n=l.low){(0,Gn.useEffect)((()=>{const r=er({},t,{action:f.render});$t(e,r,n)}),[])}function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nr.apply(this,arguments)}const rr=function(e,t){return{markStartPerf:(0,Gn.useCallback)((()=>Hn(e,t)),[e,t]),markEndPerf:(0,Gn.useCallback)((n=>Jn(e,nr({},t,n))),[e,t])}};function ir(){return ir=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ir.apply(this,arguments)}function ar(){return Object.entries(ir({},Se,zt(),{sessionUUID:oe.sessionUUID,userId:oe.userId})).reduce(((e,t)=>{return null!=(n=t[1])&&""!==n?ir({},e,{[t[0]]:t[1]}):e;var n}),{})}async function or(){return new Promise((e=>{Mt(vt(je.eventsQueue)),Qt(bt(je.metricsQueue),!0),Pn(je.tracesQueue,!0),Ne({eventsQueue:[],metricsQueue:[],tracesQueue:[]}),e()}))}function sr(){return{"X-CB-Device-ID":H.deviceId||"unknown","X-CB-Is-Logged-In":H.userId?"true":"false","X-CB-Pagekey":re.pageKey||"unknown","X-CB-UJS":(e=Se.ujs,void 0===e||0===e.length?"":e.join(",")),"X-CB-Platform":A.platform||"unknown","X-CB-Project-Name":A.projectName||"unknown","X-CB-Session-UUID":oe.sessionUUID||"unknown","X-CB-Version-Name":A.version?String(A.version):"unknown"};var e}})(),r})()}));',Ho=()=>new Promise((e,t)=>{if(window.ClientAnalytics)return e();try{const n=document.createElement("script");n.textContent=Fo,n.type="text/javascript",document.head.appendChild(n),zo(),document.head.removeChild(n),e()}catch{console.error("Failed to execute inlined telemetry script"),t()}}),zo=()=>{var e,t,n;if(typeof window<"u"){const r=(n=(e=w.config.get().deviceId)!==null&&e!==void 0?e:(t=window.crypto)===null||t===void 0?void 0:t.randomUUID())!==null&&n!==void 0?n:"";if(window.ClientAnalytics){const{init:a,identify:i,PlatformName:s}=window.ClientAnalytics;a({isProd:!0,amplitudeApiKey:"c66737ad47ec354ced777935b0af822e",platform:s.web,projectName:"base_account_sdk",showDebugLogging:!1,version:"1.0.0",apiEndpoint:"https://cca-lite.coinbase.com"}),i({deviceId:r}),w.config.set({deviceId:r})}}},G={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901,unsupportedChain:4902}},kn={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."},4902:{standard:"EIP-3085",message:"Unrecognized chain ID."}},pa="Unspecified error message.",$o="Unspecified server error.";function Mn(e,t=pa){if(e&&Number.isInteger(e)){const n=e.toString();if(xn(kn,n))return kn[n].message;if(ha(e))return $o}return t}function Wo(e){if(!Number.isInteger(e))return!1;const t=e.toString();return!!(kn[t]||ha(e))}function qo(e,{shouldIncludeStack:t=!1}={}){const n={};if(e&&typeof e=="object"&&!Array.isArray(e)&&xn(e,"code")&&Wo(e.code)){const r=e;n.code=r.code,r.message&&typeof r.message=="string"?(n.message=r.message,xn(r,"data")&&(n.data=r.data)):(n.message=Mn(n.code),n.data={originalError:or(e)})}else n.code=G.rpc.internal,n.message=cr(e,"message")?e.message:pa,n.data={originalError:or(e)};return t&&(n.stack=cr(e,"stack")?e.stack:void 0),n}function ha(e){return e>=-32099&&e<=-32e3}function or(e){return e&&typeof e=="object"&&!Array.isArray(e)?Object.assign({},e):e}function xn(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function cr(e,t){return typeof e=="object"&&e!==null&&t in e&&typeof e[t]=="string"}const b={rpc:{parse:e=>Q(G.rpc.parse,e),invalidRequest:e=>Q(G.rpc.invalidRequest,e),invalidParams:e=>Q(G.rpc.invalidParams,e),methodNotFound:e=>Q(G.rpc.methodNotFound,e),internal:e=>Q(G.rpc.internal,e),server:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return Q(t,e)},invalidInput:e=>Q(G.rpc.invalidInput,e),resourceNotFound:e=>Q(G.rpc.resourceNotFound,e),resourceUnavailable:e=>Q(G.rpc.resourceUnavailable,e),transactionRejected:e=>Q(G.rpc.transactionRejected,e),methodNotSupported:e=>Q(G.rpc.methodNotSupported,e),limitExceeded:e=>Q(G.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>Ce(G.provider.userRejectedRequest,e),unauthorized:e=>Ce(G.provider.unauthorized,e),unsupportedMethod:e=>Ce(G.provider.unsupportedMethod,e),disconnected:e=>Ce(G.provider.disconnected,e),chainDisconnected:e=>Ce(G.provider.chainDisconnected,e),unsupportedChain:e=>Ce(G.provider.unsupportedChain,e),custom:e=>{if(!e||typeof e!="object"||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:n,data:r}=e;if(!n||typeof n!="string")throw new Error('"message" must be a nonempty string');return new ya(t,n,r)}}};function Q(e,t){const[n,r]=ma(t);return new Dn(e,n||Mn(e),r)}function Ce(e,t){const[n,r]=ma(t);return new ya(e,n||Mn(e),r)}function ma(e){if(e){if(typeof e=="string")return[e];if(typeof e=="object"&&!Array.isArray(e)){const{message:t,data:n}=e;if(t&&typeof t!="string")throw new Error("Must specify string message.");return[t||void 0,n]}}return[]}class Dn extends Error{constructor(t,n,r){if(!Number.isInteger(t))throw new Error('"code" must be an integer.');if(!n||typeof n!="string")throw new Error('"message" must be a nonempty string.');super(n),this.code=t,r!==void 0&&(this.data=r)}}class ya extends Dn{constructor(t,n,r){if(!Vo(t))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(t,n,r)}}function Vo(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}function ur(e){return typeof e=="object"&&e!==null&&"code"in e&&"data"in e&&e.code===-32090&&typeof e.data=="object"&&e.data!==null&&"type"in e.data&&e.data.type==="INSUFFICIENT_FUNDS"}function ga(e){return typeof e=="object"&&e!==null&&"details"in e}function Ko(e){try{const t=JSON.parse(e.details);return new Dn(t.code,t.message,t.data)}catch{return null}}function ba(){return e=>e}const rt=ba(),Jo=ba();function fe(e){return Math.floor(e)}const wa=/^[0-9]*$/,va=/^[a-f0-9]*$/;function Ee(e){return Nn(crypto.getRandomValues(new Uint8Array(e)))}function Nn(e){return[...e].map(t=>t.toString(16).padStart(2,"0")).join("")}function gt(e){return new Uint8Array(e.match(/.{1,2}/g).map(t=>Number.parseInt(t,16)))}function Ye(e,t=!1){const n=e.toString("hex");return rt(t?`0x${n}`:n)}function Bt(e){return Ye(_n(e),!0)}function re(e){return Jo(e.toString(10))}function je(e){return rt(`0x${BigInt(e).toString(16)}`)}function ka(e){return e.startsWith("0x")||e.startsWith("0X")}function Rn(e){return ka(e)?e.slice(2):e}function xa(e){return ka(e)?`0x${e.slice(2)}`:`0x${e}`}function Lt(e){if(typeof e!="string")return!1;const t=Rn(e).toLowerCase();return va.test(t)}function Sn(e,t=!1){if(typeof e=="string"){const n=Rn(e).toLowerCase();if(va.test(n))return rt(t?`0x${n}`:n)}throw b.rpc.invalidParams(`"${String(e)}" is not a hexadecimal string`)}function Bn(e,t=!1){let n=Sn(e,!1);return n.length%2===1&&(n=rt(`0${n}`)),t?rt(`0x${n}`):n}function me(e){if(typeof e=="string"){const t=Rn(e).toLowerCase();if(Lt(t)&&t.length===40)return xa(t)}throw b.rpc.invalidParams(`Invalid Ethereum address: ${String(e)}`)}function _n(e){if(Buffer.isBuffer(e))return e;if(typeof e=="string"){if(Lt(e)){const t=Bn(e,!1);return Buffer.from(t,"hex")}return Buffer.from(e,"utf8")}throw b.rpc.invalidParams(`Not binary data: ${String(e)}`)}function Qe(e){if(typeof e=="number"&&Number.isInteger(e))return fe(e);if(typeof e=="string"){if(wa.test(e))return fe(Number(e));if(Lt(e))return fe(Number(BigInt(Bn(e,!0))))}throw b.rpc.invalidParams(`Not an integer: ${String(e)}`)}function $e(e){if(e!==null&&(typeof e=="bigint"||Qo(e)))return BigInt(e.toString(10));if(typeof e=="number")return BigInt(Qe(e));if(typeof e=="string"){if(wa.test(e))return BigInt(e);if(Lt(e))return BigInt(Bn(e,!0))}throw b.rpc.invalidParams(`Not an integer: ${String(e)}`)}function Yo(e){if(typeof e=="string")return JSON.parse(e);if(typeof e=="object")return e;throw b.rpc.invalidParams(`Not a JSON string or an object: ${String(e)}`)}function Qo(e){if(e==null||typeof e.constructor!="function")return!1;const{constructor:t}=e;return typeof t.config=="function"&&typeof t.EUCLID=="number"}const Zo=`Coinbase Wallet SDK requires the Cross-Origin-Opener-Policy header to not be set to 'same-origin'. This is to ensure that the SDK can communicate with the Coinbase Smart Wallet app.

Please see https://www.smartwallet.dev/guides/tips/popup-tips#cross-origin-opener-policy for more information.`,Xo=()=>{let e;return{getCrossOriginOpenerPolicy:()=>e===void 0?"undefined":e,checkCrossOriginOpenerPolicy:async()=>{if(typeof window>"u"){e="non-browser-env";return}try{const t=`${window.location.origin}${window.location.pathname}`,n=await fetch(t,{method:"HEAD"});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const r=n.headers.get("Cross-Origin-Opener-Policy");e=r??"null",e==="same-origin"&&console.error(Zo)}catch(t){console.error("Error checking Cross-Origin-Opener-Policy:",t.message),e="error"}}}},{checkCrossOriginOpenerPolicy:ec,getCrossOriginOpenerPolicy:tc}=Xo();async function Ze(e,t){const n=Object.assign(Object.assign({},e),{jsonrpc:"2.0",id:crypto.randomUUID()}),r=await window.fetch(t,{method:"POST",body:JSON.stringify(n),mode:"cors",headers:{"Content-Type":"application/json","X-Cbw-Sdk-Version":ze,"X-Cbw-Sdk-Platform":la}}),{result:a,error:i}=await r.json();if(i)throw i;return a}function nc(){return globalThis.coinbaseWalletExtension}function rc(){var e,t;try{const n=globalThis;return(e=n.ethereum)!==null&&e!==void 0?e:(t=n.top)===null||t===void 0?void 0:t.ethereum}catch{return}}function ac({metadata:e,preference:t}){var n,r;const{appName:a,appLogoUrl:i,appChainIds:s}=e;if(t.options!=="smartWalletOnly"){const c=nc();if(c)return(n=c.setAppInfo)===null||n===void 0||n.call(c,a,i,s,t),c}const o=rc();if(o!=null&&o.isCoinbaseBrowser)return(r=o.setAppInfo)===null||r===void 0||r.call(o,a,i,s,t),o}function ic(e){if(!e||typeof e!="object"||Array.isArray(e))throw b.rpc.invalidParams({message:"Expected a single, non-array, object argument.",data:e});const{method:t,params:n}=e;if(typeof t!="string"||t.length===0)throw b.rpc.invalidParams({message:"'args.method' must be a non-empty string.",data:e});if(n!==void 0&&!Array.isArray(n)&&(typeof n!="object"||n===null))throw b.rpc.invalidParams({message:"'args.params' must be an object or array if provided.",data:e});switch(t){case"eth_sign":case"eth_signTypedData_v2":case"eth_subscribe":case"eth_unsubscribe":throw b.provider.unsupportedMethod()}}function sc(e){if(e){if(!["all","smartWalletOnly","eoaOnly"].includes(e.options))throw new Error(`Invalid options: ${e.options}`);if(e.attribution&&e.attribution.auto!==void 0&&e.attribution.dataSuffix!==void 0)throw new Error("Attribution cannot contain both auto and dataSuffix properties");if(e.telemetry&&typeof e.telemetry!="boolean")throw new Error("Telemetry must be a boolean")}}function dr(e){if(typeof e!="function")throw new Error("toAccount is not a function")}const oc="https://keys.coinbase.com/connect",Sa="https://rpc.wallet.coinbase.com",lr="https://www.walletlink.org",cc="https://go.cb-w.com/walletlink";var O;(function(e){e.unknown="unknown",e.banner="banner",e.button="button",e.card="card",e.chart="chart",e.content_script="content_script",e.dropdown="dropdown",e.link="link",e.page="page",e.modal="modal",e.table="table",e.search_bar="search_bar",e.service_worker="service_worker",e.text="text",e.text_input="text_input",e.tray="tray",e.checkbox="checkbox",e.icon="icon"})(O||(O={}));var C;(function(e){e.unknown="unknown",e.blur="blur",e.click="click",e.change="change",e.dismiss="dismiss",e.focus="focus",e.hover="hover",e.select="select",e.measurement="measurement",e.move="move",e.process="process",e.render="render",e.scroll="scroll",e.view="view",e.search="search",e.keyPress="keyPress",e.error="error"})(C||(C={}));var T;(function(e){e.low="low",e.high="high"})(T||(T={}));function L(e,t,n){var r,a,i,s;window.ClientAnalytics&&((r=window.ClientAnalytics)===null||r===void 0||r.logEvent(e,Object.assign(Object.assign({},t),{sdkVersion:ze,appName:(i=(a=w.config.get().metadata)===null||a===void 0?void 0:a.appName)!==null&&i!==void 0?i:"",appOrigin:window.location.origin,appPreferredSigner:(s=w.config.get().preference)===null||s===void 0?void 0:s.options}),n))}const uc=()=>{L("communicator.popup_setup.started",{action:C.unknown,componentType:O.unknown},T.high)},dc=()=>{L("communicator.popup_setup.completed",{action:C.unknown,componentType:O.unknown},T.high)},lc=()=>{L("communicator.popup_unload.received",{action:C.unknown,componentType:O.unknown},T.high)},Un=({snackbarContext:e})=>{L(`snackbar.${e}.shown`,{action:C.render,componentType:O.modal,snackbarContext:e},T.high)},Ne=({snackbarContext:e,snackbarAction:t})=>{L(`snackbar.${e}.action_clicked`,{action:C.click,componentType:O.button,snackbarContext:e,snackbarAction:t},T.high)},fc='@namespace svg "http://www.w3.org/2000/svg";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:"\\201C" "\\201D" "\\2018" "\\2019";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}';function _a(){const e=document.createElement("style");e.type="text/css",e.appendChild(document.createTextNode(fc)),document.documentElement.appendChild(e)}function Ea(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Ea(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Xe(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Ea(e))&&(r&&(r+=" "),r+=t);return r}function pc(){try{return window.frameElement!==null}catch{return!1}}function hc(){try{return pc()&&window.top?window.top.location:window.location}catch{return window.location}}function mc(){var e;return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test((e=window==null?void 0:window.navigator)===null||e===void 0?void 0:e.userAgent)}function Ia(){var e,t;return(t=(e=window==null?void 0:window.matchMedia)===null||e===void 0?void 0:e.call(window,"(prefers-color-scheme: dark)").matches)!==null&&t!==void 0?t:!1}const yc=".-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:2147483647}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}",gc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+",bc="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";class Aa{constructor(){this.items=new Map,this.nextItemKey=0,this.root=null,this.darkMode=Ia()}attach(t){this.root=document.createElement("div"),this.root.className="-cbwsdk-snackbar-root",t.appendChild(this.root),this.render()}presentItem(t){const n=this.nextItemKey++;return this.items.set(n,t),this.render(),()=>{this.items.delete(n),this.render()}}clear(){this.items.clear(),this.render()}render(){this.root&&Wt(M("div",null,M(Pa,{darkMode:this.darkMode},Array.from(this.items.entries()).map(([t,n])=>M(wc,Object.assign({},n,{key:t}))))),this.root)}}const Pa=e=>M("div",{class:Xe("-cbwsdk-snackbar-container")},M("style",null,yc),M("div",{class:"-cbwsdk-snackbar"},e.children)),wc=({autoExpand:e,message:t,menuItems:n})=>{const[r,a]=Qn(!0),[i,s]=Qn(e??!1);es(()=>{const c=[window.setTimeout(()=>{a(!1)},1),window.setTimeout(()=>{s(!0)},1e4)];return()=>{c.forEach(window.clearTimeout)}});const o=()=>{s(!i)};return M("div",{class:Xe("-cbwsdk-snackbar-instance",r&&"-cbwsdk-snackbar-instance-hidden",i&&"-cbwsdk-snackbar-instance-expanded")},M("div",{class:"-cbwsdk-snackbar-instance-header",onClick:o},M("img",{src:gc,class:"-cbwsdk-snackbar-instance-header-cblogo"})," ",M("div",{class:"-cbwsdk-snackbar-instance-header-message"},t),M("div",{class:"-gear-container"},!i&&M("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},M("circle",{cx:"12",cy:"12",r:"12",fill:"#F5F7F8"})),M("img",{src:bc,class:"-gear-icon",title:"Expand"}))),n&&n.length>0&&M("div",{class:"-cbwsdk-snackbar-instance-menu"},n.map((c,u)=>M("div",{class:Xe("-cbwsdk-snackbar-instance-menu-item",c.isRed&&"-cbwsdk-snackbar-instance-menu-item-is-red"),onClick:c.onClick,key:u},M("svg",{width:c.svgWidth,height:c.svgHeight,viewBox:"0 0 10 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},M("path",{"fill-rule":c.defaultFillRule,"clip-rule":c.defaultClipRule,d:c.path,fill:"#AAAAAA"})),M("span",{class:Xe("-cbwsdk-snackbar-instance-menu-item-info",c.isRed&&"-cbwsdk-snackbar-instance-menu-item-info-is-red")},c.info)))))},Oa="M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z";class vc{constructor(){this.attached=!1,this.snackbar=new Aa}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");const t=document.documentElement,n=document.createElement("div");n.className="-cbwsdk-css-reset",t.appendChild(n),this.snackbar.attach(n),this.attached=!0,_a()}showConnecting(t){let n;return t.isUnlinkedErrorState?n={autoExpand:!0,message:"Connection lost",menuItems:[{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]}:n={message:"Confirm on phone",menuItems:[{isRed:!0,info:"Cancel transaction",svgWidth:"11",svgHeight:"11",path:"M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z",defaultFillRule:"inherit",defaultClipRule:"inherit",onClick:t.onCancel},{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:Oa,defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]},this.snackbar.presentItem(n)}}const fr=420,pr=700,kc={isRed:!1,info:"Retry",svgWidth:"10",svgHeight:"11",path:Oa,defaultFillRule:"evenodd",defaultClipRule:"evenodd"},xc="Popup was blocked. Try again.";let st=null;function Sc(e){const t=(window.innerWidth-fr)/2+window.screenX,n=(window.innerHeight-pr)/2+window.screenY;Ec(e);function r(){const i=`wallet_${crypto.randomUUID()}`,s=window.open(e,i,`width=${fr}, height=${pr}, left=${t}, top=${n}`);return s==null||s.focus(),s||null}let a=r();if(!a){const i=Gn();return new Promise((s,o)=>{Un({snackbarContext:"popup_blocked"}),i.presentItem({autoExpand:!0,message:xc,menuItems:[Object.assign(Object.assign({},kc),{onClick:()=>{Ne({snackbarContext:"popup_blocked",snackbarAction:"confirm"}),a=r(),a?s(a):o(b.rpc.internal("Popup window was blocked")),i.clear()}})]})})}return Promise.resolve(a)}function _c(e){e&&!e.closed&&e.close()}function Ec(e){const t={sdkName:la,sdkVersion:ze,origin:window.location.origin,coop:tc()};for(const[n,r]of Object.entries(t))e.searchParams.has(n)||e.searchParams.append(n,r.toString())}function Gn(){if(!st){const e=document.createElement("div");e.className="-cbwsdk-css-reset",document.body.appendChild(e),st=new Aa,st.attach(e)}return st}class Ic{constructor({url:t=oc,metadata:n,preference:r}){this.popup=null,this.listeners=new Map,this.postMessage=async a=>{(await this.waitForPopupLoaded()).postMessage(a,this.url.origin)},this.postRequestAndWaitForResponse=async a=>{const i=this.onMessage(({requestId:s})=>s===a.id);return this.postMessage(a),await i},this.onMessage=async a=>new Promise((i,s)=>{const o=c=>{if(c.origin!==this.url.origin)return;const u=c.data;a(u)&&(i(u),window.removeEventListener("message",o),this.listeners.delete(o))};window.addEventListener("message",o),this.listeners.set(o,{reject:s})}),this.disconnect=()=>{_c(this.popup),this.popup=null,this.listeners.forEach(({reject:a},i)=>{a(b.provider.userRejectedRequest("Request rejected")),window.removeEventListener("message",i)}),this.listeners.clear()},this.waitForPopupLoaded=async()=>this.popup&&!this.popup.closed?(this.popup.focus(),this.popup):(uc(),this.popup=await Sc(this.url),this.onMessage(({event:a})=>a==="PopupUnload").then(()=>{this.disconnect(),lc()}).catch(()=>{}),this.onMessage(({event:a})=>a==="PopupLoaded").then(a=>{this.postMessage({requestId:a.id,data:{version:ze,metadata:this.metadata,preference:this.preference,location:window.location.toString()}})}).then(()=>{if(!this.popup)throw b.rpc.internal();return dc(),this.popup})),this.url=new URL(t),this.metadata=n,this.preference=r}}function $(e){return e.errorMessage!==void 0}function Ac(e){const t=qo(Pc(e),{shouldIncludeStack:!0}),n=new URL("https://docs.cloud.coinbase.com/wallet-sdk/docs/errors");return n.searchParams.set("version",ze),n.searchParams.set("code",t.code.toString()),n.searchParams.set("message",t.message),Object.assign(Object.assign({},t),{docUrl:n.href})}function Pc(e){var t;if(typeof e=="string")return{message:e,code:G.rpc.internal};if($(e)){const n=e.errorMessage,r=(t=e.errorCode)!==null&&t!==void 0?t:n.match(/(denied|rejected)/i)?G.provider.userRejectedRequest:void 0;return Object.assign(Object.assign({},e),{message:n,code:r,data:{method:e.method}})}return e}var Ca={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(n=!1));function a(c,u,d){this.fn=c,this.context=u,this.once=d||!1}function i(c,u,d,l,p){if(typeof d!="function")throw new TypeError("The listener must be a function");var f=new a(d,l||c,p),m=n?n+u:u;return c._events[m]?c._events[m].fn?c._events[m]=[c._events[m],f]:c._events[m].push(f):(c._events[m]=f,c._eventsCount++),c}function s(c,u){--c._eventsCount===0?c._events=new r:delete c._events[u]}function o(){this._events=new r,this._eventsCount=0}o.prototype.eventNames=function(){var u=[],d,l;if(this._eventsCount===0)return u;for(l in d=this._events)t.call(d,l)&&u.push(n?l.slice(1):l);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(d)):u},o.prototype.listeners=function(u){var d=n?n+u:u,l=this._events[d];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,f=l.length,m=new Array(f);p<f;p++)m[p]=l[p].fn;return m},o.prototype.listenerCount=function(u){var d=n?n+u:u,l=this._events[d];return l?l.fn?1:l.length:0},o.prototype.emit=function(u,d,l,p,f,m){var y=n?n+u:u;if(!this._events[y])return!1;var h=this._events[y],v=arguments.length,x,k;if(h.fn){switch(h.once&&this.removeListener(u,h.fn,void 0,!0),v){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,d),!0;case 3:return h.fn.call(h.context,d,l),!0;case 4:return h.fn.call(h.context,d,l,p),!0;case 5:return h.fn.call(h.context,d,l,p,f),!0;case 6:return h.fn.call(h.context,d,l,p,f,m),!0}for(k=1,x=new Array(v-1);k<v;k++)x[k-1]=arguments[k];h.fn.apply(h.context,x)}else{var S=h.length,j;for(k=0;k<S;k++)switch(h[k].once&&this.removeListener(u,h[k].fn,void 0,!0),v){case 1:h[k].fn.call(h[k].context);break;case 2:h[k].fn.call(h[k].context,d);break;case 3:h[k].fn.call(h[k].context,d,l);break;case 4:h[k].fn.call(h[k].context,d,l,p);break;default:if(!x)for(j=1,x=new Array(v-1);j<v;j++)x[j-1]=arguments[j];h[k].fn.apply(h[k].context,x)}}return!0},o.prototype.on=function(u,d,l){return i(this,u,d,l,!1)},o.prototype.once=function(u,d,l){return i(this,u,d,l,!0)},o.prototype.removeListener=function(u,d,l,p){var f=n?n+u:u;if(!this._events[f])return this;if(!d)return s(this,f),this;var m=this._events[f];if(m.fn)m.fn===d&&(!p||m.once)&&(!l||m.context===l)&&s(this,f);else{for(var y=0,h=[],v=m.length;y<v;y++)(m[y].fn!==d||p&&!m[y].once||l&&m[y].context!==l)&&h.push(m[y]);h.length?this._events[f]=h.length===1?h[0]:h:s(this,f)}return this},o.prototype.removeAllListeners=function(u){var d;return u?(d=n?n+u:u,this._events[d]&&s(this,d)):(this._events=new r,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=n,o.EventEmitter=o,e.exports=o})(Ca);var Oc=Ca.exports;const Cc=Yr(Oc);class Tc extends Cc{}class Oe{constructor(t,n){this.scope=t,this.module=n}storeObject(t,n){this.setItem(t,JSON.stringify(n))}loadObject(t){const n=this.getItem(t);return n?JSON.parse(n):void 0}setItem(t,n){localStorage.setItem(this.scopedKey(t),n)}getItem(t){return localStorage.getItem(this.scopedKey(t))}removeItem(t){localStorage.removeItem(this.scopedKey(t))}clear(){const t=this.scopedKey(""),n=[];for(let r=0;r<localStorage.length;r++){const a=localStorage.key(r);typeof a=="string"&&a.startsWith(t)&&n.push(a)}n.forEach(r=>localStorage.removeItem(r))}scopedKey(t){return`-${this.scope}${this.module?`:${this.module}`:""}:${t}`}static clearAll(){new Oe("CBWSDK").clear(),new Oe("walletlink").clear()}}const Lc=({signerType:e})=>{L("provider.signer.loaded_from_storage",{action:C.measurement,componentType:O.unknown,signerType:e},T.low)},jc=({method:e,correlationId:t})=>{L("provider.request.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t},T.high)},Mc=({method:e,correlationId:t,signerType:n,errorMessage:r})=>{L("provider.request.error",{action:C.error,componentType:O.unknown,method:e,signerType:n,correlationId:t,errorMessage:r},T.high)},Dc=({method:e,signerType:t,correlationId:n})=>{L("provider.request.responded",{action:C.unknown,componentType:O.unknown,method:e,signerType:t,correlationId:n},T.high)},Nc=()=>{L("provider.enable_function.called",{action:C.measurement,componentType:O.unknown},T.high)},Rc=()=>{L("signer.selection.requested",{action:C.unknown,componentType:O.unknown},T.high)},Bc=e=>{L("signer.selection.responded",{action:C.unknown,componentType:O.unknown,signerType:e},T.high)},ot=Ln(()=>({correlationIds:new Map})),ee={get:e=>ot.getState().correlationIds.get(e),set:(e,t)=>{ot.setState(n=>{const r=new Map(n.correlationIds);return r.set(e,t),{correlationIds:r}})},delete:e=>{ot.setState(t=>{const n=new Map(t.correlationIds);return n.delete(e),{correlationIds:n}})},clear:()=>{ot.setState({correlationIds:new Map})}},Uc=({method:e,correlationId:t})=>{var n;L("scw_signer.handshake.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Gc=({method:e,correlationId:t,errorMessage:n})=>{var r;L("scw_signer.handshake.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n,enableAutoSubAccounts:(r=w.subAccountsConfig.get())===null||r===void 0?void 0:r.enableAutoSubAccounts},T.high)},Fc=({method:e,correlationId:t})=>{var n;L("scw_signer.handshake.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Hc=({method:e,correlationId:t})=>{var n;L("scw_signer.request.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},zc=({method:e,correlationId:t,errorMessage:n})=>{var r;L("scw_signer.request.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n,enableAutoSubAccounts:(r=w.subAccountsConfig.get())===null||r===void 0?void 0:r.enableAutoSubAccounts},T.high)},$c=({method:e,correlationId:t})=>{var n;L("scw_signer.request.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Wc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.request.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},qc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.request.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Vc=({method:e,correlationId:t,errorMessage:n})=>{var r;L("scw_sub_account.request.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n,enableAutoSubAccounts:(r=w.subAccountsConfig.get())===null||r===void 0?void 0:r.enableAutoSubAccounts},T.high)},Kc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.add_owner.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Jc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.add_owner.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Yc=({method:e,correlationId:t,errorMessage:n})=>{var r;L("scw_sub_account.add_owner.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n,enableAutoSubAccounts:(r=w.subAccountsConfig.get())===null||r===void 0?void 0:r.enableAutoSubAccounts},T.high)},Qc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.insufficient_balance.error_handling.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Zc=({method:e,correlationId:t})=>{var n;L("scw_sub_account.insufficient_balance.error_handling.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t,enableAutoSubAccounts:(n=w.subAccountsConfig.get())===null||n===void 0?void 0:n.enableAutoSubAccounts},T.high)},Xc=({method:e,correlationId:t,errorMessage:n})=>{var r;L("scw_sub_account.insufficient_balance.error_handling.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n,enableAutoSubAccounts:(r=w.subAccountsConfig.get())===null||r===void 0?void 0:r.enableAutoSubAccounts},T.high)},Ie=e=>"message"in e&&typeof e.message=="string"?e.message:"",Ta=Ln(()=>({}));function hr(e){e.forEach(t=>{var n,r,a,i,s,o,c,u;if(!t.rpcUrl)return;const d=Os({id:t.id,rpcUrls:{default:{http:[t.rpcUrl]}},name:(r=(n=t.nativeCurrency)===null||n===void 0?void 0:n.name)!==null&&r!==void 0?r:"",nativeCurrency:{name:(i=(a=t.nativeCurrency)===null||a===void 0?void 0:a.name)!==null&&i!==void 0?i:"",symbol:(o=(s=t.nativeCurrency)===null||s===void 0?void 0:s.symbol)!==null&&o!==void 0?o:"",decimals:(u=(c=t.nativeCurrency)===null||c===void 0?void 0:c.decimal)!==null&&u!==void 0?u:18}}),l=Ws({chain:d,transport:er(t.rpcUrl)}),p=Eo({client:l,transport:er(t.rpcUrl)});Ta.setState({[t.id]:{client:l,bundlerClient:p}})})}function La(e){var t;return(t=Ta.getState()[e])===null||t===void 0?void 0:t.client}function J(e,t,n){if(e==null)throw t??b.rpc.invalidParams({message:"value must be present",data:e})}function ve(e,t){if(!Array.isArray(e))throw b.rpc.invalidParams({message:t??"value must be an array",data:e})}function ct(e){if(typeof e!="object"||e===null)throw b.rpc.internal("sub account info is not an object");if(!("address"in e))throw b.rpc.internal("sub account is invalid");if("address"in e&&typeof e.address=="string"&&!tt(e.address))throw b.rpc.internal("sub account address is invalid");if("factory"in e&&typeof e.factory=="string"&&!tt(e.factory))throw b.rpc.internal("sub account factory address is invalid");if("factoryData"in e&&typeof e.factoryData=="string"&&!Le(e.factoryData))throw b.rpc.internal("sub account factory data is invalid")}async function eu(){return crypto.subtle.generateKey({name:"ECDH",namedCurve:"P-256"},!0,["deriveKey"])}async function tu(e,t){return crypto.subtle.deriveKey({name:"ECDH",public:t},e,{name:"AES-GCM",length:256},!1,["encrypt","decrypt"])}async function nu(e,t){const n=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.encrypt({name:"AES-GCM",iv:n},e,new TextEncoder().encode(t));return{iv:n,cipherText:r}}async function ru(e,{iv:t,cipherText:n}){const r=await crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,n);return new TextDecoder().decode(r)}function ja(e){switch(e){case"public":return"spki";case"private":return"pkcs8"}}async function Ma(e,t){const n=ja(e),r=await crypto.subtle.exportKey(n,t);return Nn(new Uint8Array(r))}async function Da(e,t){const n=ja(e),r=gt(t).buffer;return await crypto.subtle.importKey(n,new Uint8Array(r),{name:"ECDH",namedCurve:"P-256"},!0,e==="private"?["deriveKey"]:[])}async function au(e,t){const n=JSON.stringify(e,(r,a)=>{if(!(a instanceof Error))return a;const i=a;return Object.assign(Object.assign({},i.code?{code:i.code}:{}),{message:i.message})});return nu(t,n)}async function iu(e,t){return JSON.parse(await ru(t,e))}const su="0.1.1";function ou(){return su}class R extends Error{constructor(t,n={}){const r=(()=>{var c;if(n.cause instanceof R){if(n.cause.details)return n.cause.details;if(n.cause.shortMessage)return n.cause.shortMessage}return(c=n.cause)!=null&&c.message?n.cause.message:n.details})(),a=n.cause instanceof R&&n.cause.docsPath||n.docsPath,s=`https://oxlib.sh${a??""}`,o=[t||"An error occurred.",...n.metaMessages?["",...n.metaMessages]:[],...r||a?["",r?`Details: ${r}`:void 0,a?`See: ${s}`:void 0]:[]].filter(c=>typeof c=="string").join(`
`);super(o,n.cause?{cause:n.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:`ox@${ou()}`}),this.cause=n.cause,this.details=r,this.docs=s,this.docsPath=a,this.shortMessage=t}walk(t){return Na(this,t)}}function Na(e,t){return t!=null&&t(e)?e:e&&typeof e=="object"&&"cause"in e&&e.cause?Na(e.cause,t):t?null:e}/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function cu(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Fn(e,...t){if(!cu(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function mr(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function uu(e,t){Fn(e);const n=t.outputLen;if(e.length<n)throw new Error("digestInto() expects output buffer of length at least "+n)}function En(...e){for(let t=0;t<e.length;t++)e[t].fill(0)}function Ut(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ae(e,t){return e<<32-t|e>>>t}function du(e){if(typeof e!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function Ra(e){return typeof e=="string"&&(e=du(e)),Fn(e),e}let lu=class{};function fu(e){const t=r=>e().update(Ra(r)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t}function pu(e,t,n,r){if(typeof e.setBigUint64=="function")return e.setBigUint64(t,n,r);const a=BigInt(32),i=BigInt(**********),s=Number(n>>a&i),o=Number(n&i),c=r?4:0,u=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+u,o,r)}function hu(e,t,n){return e&t^~e&n}function mu(e,t,n){return e&t^e&n^t&n}let yu=class extends lu{constructor(t,n,r,a){super(),this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.blockLen=t,this.outputLen=n,this.padOffset=r,this.isLE=a,this.buffer=new Uint8Array(t),this.view=Ut(this.buffer)}update(t){mr(this),t=Ra(t),Fn(t);const{view:n,buffer:r,blockLen:a}=this,i=t.length;for(let s=0;s<i;){const o=Math.min(a-this.pos,i-s);if(o===a){const c=Ut(t);for(;a<=i-s;s+=a)this.process(c,s);continue}r.set(t.subarray(s,s+o),this.pos),this.pos+=o,s+=o,this.pos===a&&(this.process(n,0),this.pos=0)}return this.length+=t.length,this.roundClean(),this}digestInto(t){mr(this),uu(t,this),this.finished=!0;const{buffer:n,view:r,blockLen:a,isLE:i}=this;let{pos:s}=this;n[s++]=128,En(this.buffer.subarray(s)),this.padOffset>a-s&&(this.process(r,0),s=0);for(let l=s;l<a;l++)n[l]=0;pu(r,a-8,BigInt(this.length*8),i),this.process(r,0);const o=Ut(t),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,d=this.get();if(u>d.length)throw new Error("_sha2: outputLen bigger than state");for(let l=0;l<u;l++)o.setUint32(4*l,d[l],i)}digest(){const{buffer:t,outputLen:n}=this;this.digestInto(t);const r=t.slice(0,n);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:n,buffer:r,length:a,finished:i,destroyed:s,pos:o}=this;return t.destroyed=s,t.finished=i,t.length=a,t.pos=o,a%n&&t.buffer.set(r),t}clone(){return this._cloneInto()}};const ye=Uint32Array.from([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),gu=Uint32Array.from([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),ge=new Uint32Array(64);let bu=class extends yu{constructor(t=32){super(64,t,8,!1),this.A=ye[0]|0,this.B=ye[1]|0,this.C=ye[2]|0,this.D=ye[3]|0,this.E=ye[4]|0,this.F=ye[5]|0,this.G=ye[6]|0,this.H=ye[7]|0}get(){const{A:t,B:n,C:r,D:a,E:i,F:s,G:o,H:c}=this;return[t,n,r,a,i,s,o,c]}set(t,n,r,a,i,s,o,c){this.A=t|0,this.B=n|0,this.C=r|0,this.D=a|0,this.E=i|0,this.F=s|0,this.G=o|0,this.H=c|0}process(t,n){for(let l=0;l<16;l++,n+=4)ge[l]=t.getUint32(n,!1);for(let l=16;l<64;l++){const p=ge[l-15],f=ge[l-2],m=ae(p,7)^ae(p,18)^p>>>3,y=ae(f,17)^ae(f,19)^f>>>10;ge[l]=y+ge[l-7]+m+ge[l-16]|0}let{A:r,B:a,C:i,D:s,E:o,F:c,G:u,H:d}=this;for(let l=0;l<64;l++){const p=ae(o,6)^ae(o,11)^ae(o,25),f=d+p+hu(o,c,u)+gu[l]+ge[l]|0,y=(ae(r,2)^ae(r,13)^ae(r,22))+mu(r,a,i)|0;d=u,u=c,c=o,o=s+f|0,s=i,i=a,a=r,r=f+y|0}r=r+this.A|0,a=a+this.B|0,i=i+this.C|0,s=s+this.D|0,o=o+this.E|0,c=c+this.F|0,u=u+this.G|0,d=d+this.H|0,this.set(r,a,i,s,o,c,u,d)}roundClean(){En(ge)}destroy(){this.set(0,0,0,0,0,0,0,0),En(this.buffer)}};const wu=fu(()=>new bu),vu=wu,ku="#__bigint";function jt(e,t,n){return JSON.stringify(e,(r,a)=>typeof a=="bigint"?a.toString()+ku:a,n)}function xu(e,t){if(Fe(e)>t)throw new Bu({givenSize:Fe(e),maxSize:t})}function Su(e,t){if(typeof t=="number"&&t>0&&t>Fe(e)-1)throw new qa({offset:t,position:"start",size:Fe(e)})}function _u(e,t,n){if(typeof t=="number"&&typeof n=="number"&&Fe(e)!==n-t)throw new qa({offset:n,position:"end",size:Fe(e)})}const ue={zero:48,nine:57,A:65,F:70,a:97,f:102};function yr(e){if(e>=ue.zero&&e<=ue.nine)return e-ue.zero;if(e>=ue.A&&e<=ue.F)return e-(ue.A-10);if(e>=ue.a&&e<=ue.f)return e-(ue.a-10)}function Hn(e,t){if(xe(e)>t)throw new Mu({givenSize:xe(e),maxSize:t})}function Eu(e,t){if(typeof t=="number"&&t>0&&t>xe(e)-1)throw new Ha({offset:t,position:"start",size:xe(e)})}function Iu(e,t,n){if(typeof t=="number"&&typeof n=="number"&&xe(e)!==n-t)throw new Ha({offset:n,position:"end",size:xe(e)})}function Ba(e,t={}){const{dir:n,size:r=32}=t;if(r===0)return e;const a=e.replace("0x","");if(a.length>r*2)throw new Du({size:Math.ceil(a.length/2),targetSize:r,type:"Hex"});return`0x${a[n==="right"?"padEnd":"padStart"](r*2,"0")}`}const Au=new TextEncoder,Pu=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Ou(e,t={}){const{strict:n=!1}=t;if(!e)throw new gr(e);if(typeof e!="string")throw new gr(e);if(n&&!/^0x[0-9a-fA-F]*$/.test(e))throw new br(e);if(!e.startsWith("0x"))throw new br(e)}function Mt(...e){return`0x${e.reduce((t,n)=>t+n.replace("0x",""),"")}`}function Ua(e){return e instanceof Uint8Array?Ge(e):Array.isArray(e)?Ge(new Uint8Array(e)):e}function Ge(e,t={}){let n="";for(let a=0;a<e.length;a++)n+=Pu[e[a]];const r=`0x${n}`;return typeof t.size=="number"?(Hn(r,t.size),Fa(r,t.size)):r}function ke(e,t={}){const{signed:n,size:r}=t,a=BigInt(e);let i;r?n?i=(1n<<BigInt(r)*8n-1n)-1n:i=2n**(BigInt(r)*8n)-1n:typeof e=="number"&&(i=BigInt(Number.MAX_SAFE_INTEGER));const s=typeof i=="bigint"&&n?-i-1n:0;if(i&&a>i||a<s){const u=typeof e=="bigint"?"n":"";throw new ju({max:i?`${i}${u}`:void 0,min:`${s}${u}`,signed:n,size:r,value:`${e}${u}`})}const c=`0x${(n&&a<0?(1n<<BigInt(r*8))+BigInt(a):a).toString(16)}`;return r?Cu(c,r):c}function Ga(e,t={}){return Ge(Au.encode(e),t)}function Cu(e,t){return Ba(e,{dir:"left",size:t})}function Fa(e,t){return Ba(e,{dir:"right",size:t})}function Z(e,t,n,r={}){const{strict:a}=r;Eu(e,t);const i=`0x${e.replace("0x","").slice((t??0)*2,(n??e.length)*2)}`;return a&&Iu(i,t,n),i}function xe(e){return Math.ceil((e.length-2)/2)}function Tu(e,t={}){const{signed:n}=t;t.size&&Hn(e,t.size);const r=BigInt(e);if(!n)return r;const a=(e.length-2)/2,i=(1n<<BigInt(a)*8n)-1n,s=i>>1n;return r<=s?r:r-i-1n}function Lu(e,t={}){const{strict:n=!1}=t;try{return Ou(e,{strict:n}),!0}catch{return!1}}class ju extends R{constructor({max:t,min:n,signed:r,size:a,value:i}){super(`Number \`${i}\` is not in safe${a?` ${a*8}-bit`:""}${r?" signed":" unsigned"} integer range ${t?`(\`${n}\` to \`${t}\`)`:`(above \`${n}\`)`}`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.IntegerOutOfRangeError"})}}class gr extends R{constructor(t){super(`Value \`${typeof t=="object"?jt(t):t}\` of type \`${typeof t}\` is an invalid hex type.`,{metaMessages:['Hex types must be represented as `"0x${string}"`.']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexTypeError"})}}class br extends R{constructor(t){super(`Value \`${t}\` is an invalid hex value.`,{metaMessages:['Hex values must start with `"0x"` and contain only hexadecimal characters (0-9, a-f, A-F).']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexValueError"})}}let Mu=class extends R{constructor({givenSize:t,maxSize:n}){super(`Size cannot exceed \`${n}\` bytes. Given size: \`${t}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeOverflowError"})}},Ha=class extends R{constructor({offset:t,position:n,size:r}){super(`Slice ${n==="start"?"starting":"ending"} at offset \`${t}\` is out-of-bounds (size: \`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SliceOffsetOutOfBoundsError"})}};class Du extends R{constructor({size:t,targetSize:n,type:r}){super(`${r.charAt(0).toUpperCase()}${r.slice(1).toLowerCase()} size (\`${t}\`) exceeds padding size (\`${n}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeExceedsPaddingSizeError"})}}function Nu(e){if(!(e instanceof Uint8Array)){if(!e)throw new ut(e);if(typeof e!="object")throw new ut(e);if(!("BYTES_PER_ELEMENT"in e))throw new ut(e);if(e.BYTES_PER_ELEMENT!==1||e.constructor.name!=="Uint8Array")throw new ut(e)}}function za(e){return e instanceof Uint8Array?e:typeof e=="string"?Wa(e):$a(e)}function $a(e){return e instanceof Uint8Array?e:new Uint8Array(e)}function Wa(e,t={}){const{size:n}=t;let r=e;n&&(Hn(e,n),r=Fa(e,n));let a=r.slice(2);a.length%2&&(a=`0${a}`);const i=a.length/2,s=new Uint8Array(i);for(let o=0,c=0;o<i;o++){const u=yr(a.charCodeAt(c++)),d=yr(a.charCodeAt(c++));if(u===void 0||d===void 0)throw new R(`Invalid byte sequence ("${a[c-2]}${a[c-1]}" in "${a}").`);s[o]=u*16+d}return s}function Fe(e){return e.length}function wr(e,t,n,r={}){const{strict:a}=r;Su(e,t);const i=e.slice(t,n);return a&&_u(i,t,n),i}function vr(e,t={}){const{size:n}=t;typeof n<"u"&&xu(e,n);const r=Ge(e,t);return Tu(r,t)}function Ru(e){try{return Nu(e),!0}catch{return!1}}class ut extends R{constructor(t){super(`Value \`${typeof t=="object"?jt(t):t}\` of type \`${typeof t}\` is an invalid Bytes value.`,{metaMessages:["Bytes values must be of type `Bytes`."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.InvalidBytesTypeError"})}}class Bu extends R{constructor({givenSize:t,maxSize:n}){super(`Size cannot exceed \`${n}\` bytes. Given size: \`${t}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}}class qa extends R{constructor({offset:t,position:n,size:r}){super(`Slice ${n==="start"?"starting":"ending"} at offset \`${t}\` is out-of-bounds (size: \`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SliceOffsetOutOfBoundsError"})}}function Va(e,t={}){const{as:n=typeof e=="string"?"Hex":"Bytes"}=t,r=vu(za(e));return n==="Bytes"?r:Ge(r)}function Ka(e,t={}){const{compressed:n}=t,{prefix:r,x:a,y:i}=e;if(n===!1||typeof a=="bigint"&&typeof i=="bigint"){if(r!==4)throw new kr({prefix:r,cause:new zu});return}if(n===!0||typeof a=="bigint"&&typeof i>"u"){if(r!==3&&r!==2)throw new kr({prefix:r,cause:new Hu});return}throw new Fu({publicKey:e})}function Uu(e){const t=(()=>{if(Lu(e))return Ja(e);if(Ru(e))return Gu(e);const{prefix:n,x:r,y:a}=e;return typeof r=="bigint"&&typeof a=="bigint"?{prefix:n??4,x:r,y:a}:{prefix:n,x:r}})();return Ka(t),t}function Gu(e){return Ja(Ge(e))}function Ja(e){if(e.length!==132&&e.length!==130&&e.length!==68)throw new $u({publicKey:e});if(e.length===130){const r=BigInt(Z(e,0,32)),a=BigInt(Z(e,32,64));return{prefix:4,x:r,y:a}}if(e.length===132){const r=Number(Z(e,0,1)),a=BigInt(Z(e,1,33)),i=BigInt(Z(e,33,65));return{prefix:r,x:a,y:i}}const t=Number(Z(e,0,1)),n=BigInt(Z(e,1,33));return{prefix:t,x:n}}function zn(e,t={}){Ka(e);const{prefix:n,x:r,y:a}=e,{includePrefix:i=!0}=t;return Mt(i?ke(n,{size:1}):"0x",ke(r,{size:32}),typeof a=="bigint"?ke(a,{size:32}):"0x")}class Fu extends R{constructor({publicKey:t}){super(`Value \`${jt(t)}\` is not a valid public key.`,{metaMessages:["Public key must contain:","- an `x` and `prefix` value (compressed)","- an `x`, `y`, and `prefix` value (uncompressed)"]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"PublicKey.InvalidError"})}}class kr extends R{constructor({prefix:t,cause:n}){super(`Prefix "${t}" is invalid.`,{cause:n}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"PublicKey.InvalidPrefixError"})}}class Hu extends R{constructor(){super("Prefix must be 2 or 3 for compressed public keys."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"PublicKey.InvalidCompressedPrefixError"})}}class zu extends R{constructor(){super("Prefix must be 4 for uncompressed public keys."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"PublicKey.InvalidUncompressedPrefixError"})}}let $u=class extends R{constructor({publicKey:t}){super(`Value \`${t}\` is an invalid public key size.`,{metaMessages:["Expected: 33 bytes (compressed + prefix), 64 bytes (uncompressed) or 65 bytes (uncompressed + prefix).",`Received ${xe(Ua(t))} bytes.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"PublicKey.InvalidSerializedSizeError"})}};const xr=2n**256n-1n;function Wu(e,t={}){const{recovered:n}=t;if(typeof e.r>"u")throw new Gt({signature:e});if(typeof e.s>"u")throw new Gt({signature:e});if(n&&typeof e.yParity>"u")throw new Gt({signature:e});if(e.r<0n||e.r>xr)throw new Yu({value:e.r});if(e.s<0n||e.s>xr)throw new Qu({value:e.s});if(typeof e.yParity=="number"&&e.yParity!==0&&e.yParity!==1)throw new $n({value:e.yParity})}function Ya(e){if(e.length!==130&&e.length!==132)throw new Ju({signature:e});const t=BigInt(Z(e,0,32)),n=BigInt(Z(e,32,64)),r=(()=>{const a=+`0x${e.slice(130)}`;if(!Number.isNaN(a))try{return Vu(a)}catch{throw new $n({value:a})}})();return typeof r>"u"?{r:t,s:n}:{r:t,s:n,yParity:r}}function qu(e){Wu(e);const t=e.r,n=e.s;return Mt(ke(t,{size:32}),ke(n,{size:32}),typeof e.yParity=="number"?ke(Ku(e.yParity),{size:1}):"0x")}function Vu(e){if(e===0||e===27)return 0;if(e===1||e===28)return 1;if(e>=35)return e%2===0?1:0;throw new Zu({value:e})}function Ku(e){if(e===0)return 27;if(e===1)return 28;throw new $n({value:e})}class Ju extends R{constructor({signature:t}){super(`Value \`${t}\` is an invalid signature size.`,{metaMessages:["Expected: 64 bytes or 65 bytes.",`Received ${xe(Ua(t))} bytes.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidSerializedSizeError"})}}class Gt extends R{constructor({signature:t}){super(`Signature \`${jt(t)}\` is missing either an \`r\`, \`s\`, or \`yParity\` property.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.MissingPropertiesError"})}}class Yu extends R{constructor({value:t}){super(`Value \`${t}\` is an invalid r value. r must be a positive integer less than 2^256.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidRError"})}}class Qu extends R{constructor({value:t}){super(`Value \`${t}\` is an invalid s value. s must be a positive integer less than 2^256.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidSError"})}}class $n extends R{constructor({value:t}){super(`Value \`${t}\` is an invalid y-parity value. Y-parity must be 0 or 1.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidYParityError"})}}class Zu extends R{constructor({value:t}){super(`Value \`${t}\` is an invalid v value. v must be 27, 28 or >=35.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidVError"})}}const Xu=new TextDecoder,dt=Object.fromEntries(Array.from("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").map((e,t)=>[t,e.charCodeAt(0)]));({...Object.fromEntries(Array.from("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").map((e,t)=>[e.charCodeAt(0),t]))});function ed(e,t={}){const{pad:n=!0,url:r=!1}=t,a=new Uint8Array(Math.ceil(e.length/3)*4);for(let c=0,u=0;u<e.length;c+=4,u+=3){const d=(e[u]<<16)+(e[u+1]<<8)+(e[u+2]|0);a[c]=dt[d>>18],a[c+1]=dt[d>>12&63],a[c+2]=dt[d>>6&63],a[c+3]=dt[d&63]}const i=e.length%3,s=Math.floor(e.length/3)*4+(i&&i+1);let o=Xu.decode(new Uint8Array(a.buffer,0,s));return n&&i===1&&(o+="=="),n&&i===2&&(o+="="),r&&(o=o.replaceAll("+","-").replaceAll("/","_")),o}function td(e,t={}){return ed(Wa(e),t)}Uint8Array.from([105,171,180,181,160,222,75,198,42,42,32,31,141,37,186,233]);function nd(e={}){const{flag:t=5,rpId:n=window.location.hostname,signCount:r=0}=e,a=Va(Ga(n)),i=ke(t,{size:1}),s=ke(r,{size:4});return Mt(a,i,s)}function rd(e){const{challenge:t,crossOrigin:n=!1,extraClientData:r,origin:a=window.location.origin}=e;return JSON.stringify({type:"webauthn.get",challenge:td(t,{url:!0,pad:!1}),origin:a,crossOrigin:n,...r})}function ad(e){const{challenge:t,crossOrigin:n,extraClientData:r,flag:a,origin:i,rpId:s,signCount:o,userVerification:c="required"}=e,u=nd({flag:a,rpId:s,signCount:o}),d=rd({challenge:t,crossOrigin:n,extraClientData:r,origin:i}),l=Va(Ga(d)),p=d.indexOf('"challenge"'),f=d.indexOf('"type"'),m={authenticatorData:u,clientDataJSON:d,challengeIndex:p,typeIndex:f,userVerificationRequired:c==="required"},y=Mt(u,l);return{metadata:m,payload:y}}async function id(e={}){const{extractable:t=!1}=e,n=await globalThis.crypto.subtle.generateKey({name:"ECDSA",namedCurve:"P-256"},t,["sign","verify"]),r=await globalThis.crypto.subtle.exportKey("raw",n.publicKey),a=Uu(new Uint8Array(r));return{privateKey:n.privateKey,publicKey:a}}async function sd(e){const{payload:t,privateKey:n}=e,r=await globalThis.crypto.subtle.sign({name:"ECDSA",hash:"SHA-256"},n,za(t)),a=$a(new Uint8Array(r)),i=vr(wr(a,0,32));let s=vr(wr(a,32,64));return s>ar.CURVE.n/2n&&(s=ar.CURVE.n-s),{r:i,s}}function od(e,t){const n=typeof indexedDB<"u"?js(e,t):void 0;return{getItem:async r=>{const a=await Ns(r,n);return a||null},removeItem:async r=>Ds(r,n),setItem:async(r,a)=>Ms(r,a,n)}}const cd="cbwsdk",ud="keys",Wn="activeId",He=od(cd,ud);async function dd(){const e=await id({extractable:!1}),t=Z(zn(e.publicKey),1);return await He.setItem(t,e),await He.setItem(Wn,t),e}async function ld(){const e=await He.getItem(Wn);if(!e)return null;const t=await He.getItem(e);return t||null}async function fd(){const e=await ld();if(!e){const t=await dd(),n=Z(zn(t.publicKey),1);return await He.setItem(n,t),await He.setItem(Wn,n),t}return e}async function pd(){const e=await fd(),t=Z(zn(e.publicKey),1),n=async r=>{const{payload:a,metadata:i}=ad({challenge:r,origin:"https://keys.coinbase.com",userVerification:"preferred"}),s=await sd({payload:a,privateKey:e.privateKey});return{signature:qu(s),raw:{},webauthn:i}};return{id:t,publicKey:t,async sign({hash:r}){return n(r)},async signMessage({message:r}){return n(Qr(r))},async signTypedData(r){return n(Ct(r))},type:"webAuthn"}}async function In(){return{account:await pd()}}const Sr={storageKey:"ownPrivateKey",keyType:"private"},_r={storageKey:"ownPublicKey",keyType:"public"},Er={storageKey:"peerPublicKey",keyType:"public"};class hd{constructor(){this.ownPrivateKey=null,this.ownPublicKey=null,this.peerPublicKey=null,this.sharedSecret=null}async getOwnPublicKey(){return await this.loadKeysIfNeeded(),this.ownPublicKey}async getSharedSecret(){return await this.loadKeysIfNeeded(),this.sharedSecret}async setPeerPublicKey(t){this.sharedSecret=null,this.peerPublicKey=t,await this.storeKey(Er,t),await this.loadKeysIfNeeded()}async clear(){this.ownPrivateKey=null,this.ownPublicKey=null,this.peerPublicKey=null,this.sharedSecret=null,w.keys.clear()}async generateKeyPair(){const t=await eu();this.ownPrivateKey=t.privateKey,this.ownPublicKey=t.publicKey,await this.storeKey(Sr,t.privateKey),await this.storeKey(_r,t.publicKey)}async loadKeysIfNeeded(){if(this.ownPrivateKey===null&&(this.ownPrivateKey=await this.loadKey(Sr)),this.ownPublicKey===null&&(this.ownPublicKey=await this.loadKey(_r)),(this.ownPrivateKey===null||this.ownPublicKey===null)&&await this.generateKeyPair(),this.peerPublicKey===null&&(this.peerPublicKey=await this.loadKey(Er)),this.sharedSecret===null){if(this.ownPrivateKey===null||this.peerPublicKey===null)return;this.sharedSecret=await tu(this.ownPrivateKey,this.peerPublicKey)}}async loadKey(t){const n=w.keys.get(t.storageKey);return n?Da(t.keyType,n):null}async storeKey(t,n){const r=await Ma(t.keyType,n);w.keys.set(t.storageKey,r)}}function Ve(e,t){if(!(typeof e!="object"||e===null))return t.split(/[.[\]]+/).filter(Boolean).reduce((n,r)=>{if(typeof n=="object"&&n!==null)return n[r]},e)}const Qa="******************************************",Za="******************************************",ne=[{inputs:[],stateMutability:"nonpayable",type:"constructor"},{inputs:[{name:"owner",type:"bytes"}],name:"AlreadyOwner",type:"error"},{inputs:[],name:"Initialized",type:"error"},{inputs:[{name:"owner",type:"bytes"}],name:"InvalidEthereumAddressOwner",type:"error"},{inputs:[{name:"key",type:"uint256"}],name:"InvalidNonceKey",type:"error"},{inputs:[{name:"owner",type:"bytes"}],name:"InvalidOwnerBytesLength",type:"error"},{inputs:[],name:"LastOwner",type:"error"},{inputs:[{name:"index",type:"uint256"}],name:"NoOwnerAtIndex",type:"error"},{inputs:[{name:"ownersRemaining",type:"uint256"}],name:"NotLastOwner",type:"error"},{inputs:[{name:"selector",type:"bytes4"}],name:"SelectorNotAllowed",type:"error"},{inputs:[],name:"Unauthorized",type:"error"},{inputs:[],name:"UnauthorizedCallContext",type:"error"},{inputs:[],name:"UpgradeFailed",type:"error"},{inputs:[{name:"index",type:"uint256"},{name:"expectedOwner",type:"bytes"},{name:"actualOwner",type:"bytes"}],name:"WrongOwnerAtIndex",type:"error"},{anonymous:!1,inputs:[{indexed:!0,name:"index",type:"uint256"},{indexed:!1,name:"owner",type:"bytes"}],name:"AddOwner",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"index",type:"uint256"},{indexed:!1,name:"owner",type:"bytes"}],name:"RemoveOwner",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"implementation",type:"address"}],name:"Upgraded",type:"event"},{stateMutability:"payable",type:"fallback"},{inputs:[],name:"REPLAYABLE_NONCE_KEY",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"owner",type:"address"}],name:"addOwnerAddress",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"x",type:"bytes32"},{name:"y",type:"bytes32"}],name:"addOwnerPublicKey",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"functionSelector",type:"bytes4"}],name:"canSkipChainIdValidation",outputs:[{name:"",type:"bool"}],stateMutability:"pure",type:"function"},{inputs:[],name:"domainSeparator",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"},{inputs:[],name:"eip712Domain",outputs:[{name:"fields",type:"bytes1"},{name:"name",type:"string"},{name:"version",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"},{name:"salt",type:"bytes32"},{name:"extensions",type:"uint256[]"}],stateMutability:"view",type:"function"},{inputs:[],name:"entryPoint",outputs:[{name:"",type:"address"}],stateMutability:"view",type:"function"},{inputs:[{name:"target",type:"address"},{name:"value",type:"uint256"},{name:"data",type:"bytes"}],name:"execute",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{components:[{name:"target",type:"address"},{name:"value",type:"uint256"},{name:"data",type:"bytes"}],name:"calls",type:"tuple[]"}],name:"executeBatch",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{name:"calls",type:"bytes[]"}],name:"executeWithoutChainIdValidation",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"userOp",type:"tuple"}],name:"getUserOpHashWithoutChainId",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"},{inputs:[],name:"implementation",outputs:[{name:"$",type:"address"}],stateMutability:"view",type:"function"},{inputs:[{name:"owners",type:"bytes[]"}],name:"initialize",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{name:"account",type:"address"}],name:"isOwnerAddress",outputs:[{name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{name:"account",type:"bytes"}],name:"isOwnerBytes",outputs:[{name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{name:"x",type:"bytes32"},{name:"y",type:"bytes32"}],name:"isOwnerPublicKey",outputs:[{name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{name:"hash",type:"bytes32"},{name:"signature",type:"bytes"}],name:"isValidSignature",outputs:[{name:"result",type:"bytes4"}],stateMutability:"view",type:"function"},{inputs:[],name:"nextOwnerIndex",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"index",type:"uint256"}],name:"ownerAtIndex",outputs:[{name:"",type:"bytes"}],stateMutability:"view",type:"function"},{inputs:[],name:"ownerCount",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[],name:"proxiableUUID",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"},{inputs:[{name:"index",type:"uint256"},{name:"owner",type:"bytes"}],name:"removeLastOwner",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"index",type:"uint256"},{name:"owner",type:"bytes"}],name:"removeOwnerAtIndex",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[],name:"removedOwnersCount",outputs:[{name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"hash",type:"bytes32"}],name:"replaySafeHash",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"},{inputs:[{name:"newImplementation",type:"address"},{name:"data",type:"bytes"}],name:"upgradeToAndCall",outputs:[],stateMutability:"payable",type:"function"},{inputs:[{components:[{name:"sender",type:"address"},{name:"nonce",type:"uint256"},{name:"initCode",type:"bytes"},{name:"callData",type:"bytes"},{name:"callGasLimit",type:"uint256"},{name:"verificationGasLimit",type:"uint256"},{name:"preVerificationGas",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymasterAndData",type:"bytes"},{name:"signature",type:"bytes"}],name:"userOp",type:"tuple"},{name:"userOpHash",type:"bytes32"},{name:"missingAccountFunds",type:"uint256"}],name:"validateUserOp",outputs:[{name:"validationData",type:"uint256"}],stateMutability:"nonpayable",type:"function"},{stateMutability:"payable",type:"receive"}],Xa=[{inputs:[{name:"implementation_",type:"address"}],stateMutability:"payable",type:"constructor"},{inputs:[],name:"OwnerRequired",type:"error"},{inputs:[{name:"owners",type:"bytes[]"},{name:"nonce",type:"uint256"}],name:"createAccount",outputs:[{name:"account",type:"address"}],stateMutability:"payable",type:"function"},{inputs:[{name:"owners",type:"bytes[]"},{name:"nonce",type:"uint256"}],name:"getAddress",outputs:[{name:"",type:"address"}],stateMutability:"view",type:"function"},{inputs:[],name:"implementation",outputs:[{name:"",type:"address"}],stateMutability:"view",type:"function"},{inputs:[],name:"initCodeHash",outputs:[{name:"",type:"bytes32"}],stateMutability:"view",type:"function"}];function Ir(e){var t;if(!Array.isArray(e.params))return null;switch(e.method){case"personal_sign":return e.params[1];case"eth_signTypedData_v4":return e.params[0];case"eth_signTransaction":case"eth_sendTransaction":case"wallet_sendCalls":return(t=e.params[0])===null||t===void 0?void 0:t.from;default:return null}}function md(e,t){if(!Array.isArray(e.params))throw b.rpc.invalidParams();const n=[...e.params];switch(e.method){case"eth_signTransaction":case"eth_sendTransaction":case"wallet_sendCalls":n[0].from=t;break;case"eth_signTypedData_v4":n[0]=t;break;case"personal_sign":n[1]=t;break}return Object.assign(Object.assign({},e),{params:n})}function Ar(e){var t;if(!e||!Array.isArray(e)||!(!((t=e[0])===null||t===void 0)&&t.chainId)||typeof e[0].chainId!="string"&&typeof e[0].chainId!="number")throw b.rpc.invalidParams()}function yd(e){if(!e||!Array.isArray(e)||e.length!==1&&e.length!==2||typeof e[0]!="string"||!tt(e[0]))throw b.rpc.invalidParams();if(e.length===2){if(!Array.isArray(e[1]))throw b.rpc.invalidParams();for(const t of e[1])if(typeof t!="string"||!t.startsWith("0x"))throw b.rpc.invalidParams()}}function At(e,t){const n=Object.assign({},e);if(t&&e.method.startsWith("wallet_")){let r=Ve(n,"params.0.capabilities");if(typeof r>"u"&&(r={}),typeof r!="object")throw b.rpc.invalidParams();r=Object.assign(Object.assign({},t),r),n.params&&Array.isArray(n.params)&&(n.params[0]=Object.assign(Object.assign({},n.params[0]),{capabilities:r}))}return n}async function Ft(){var e;const t=(e=w.subAccountsConfig.get())!==null&&e!==void 0?e:{},n={};if(t.enableAutoSubAccounts){const{account:r}=t.toOwnerAccount?await t.toOwnerAccount():await In();if(!r)throw b.provider.unauthorized("No owner account found");n.addSubAccount={account:{type:"create",keys:[{type:r.address?"address":"webauthn-p256",publicKey:r.address||r.publicKey}]}}}w.subAccountsConfig.set({capabilities:n})}function gd(e){if(!(e.method==="coinbase_fetchPermissions"&&e.params===void 0)){if(e.method==="coinbase_fetchPermissions"&&Array.isArray(e.params)&&e.params.length===1&&typeof e.params[0]=="object"){if(typeof e.params[0].account!="string"||!e.params[0].chainId.startsWith("0x"))throw b.rpc.invalidParams("FetchPermissions - Invalid params: params[0].account must be a hex string");if(typeof e.params[0].chainId!="string"||!e.params[0].chainId.startsWith("0x"))throw b.rpc.invalidParams("FetchPermissions - Invalid params: params[0].chainId must be a hex string");if(typeof e.params[0].spender!="string"||!e.params[0].spender.startsWith("0x"))throw b.rpc.invalidParams("FetchPermissions - Invalid params: params[0].spender must be a hex string");return}throw b.rpc.invalidParams()}}function bd(e){var t,n,r;if(e.params!==void 0)return e;const a=(t=w.getState().account.accounts)===null||t===void 0?void 0:t[0],i=(n=w.getState().account.chain)===null||n===void 0?void 0:n.id,s=(r=w.getState().subAccount)===null||r===void 0?void 0:r.address;if(!a||!s||!i)throw b.rpc.invalidParams("FetchPermissions - one or more of account, sub account, or chain id is missing, connect to sub account via wallet_connect first");return{method:"coinbase_fetchPermissions",params:[{account:a,chainId:A(i),spender:s}]}}function wd({spendPermission:e,chainId:t}){return{domain:{name:"Spend Permission Manager",version:"1",chainId:t,verifyingContract:Za},types:{SpendPermission:[{name:"account",type:"address"},{name:"spender",type:"address"},{name:"token",type:"address"},{name:"allowance",type:"uint160"},{name:"period",type:"uint48"},{name:"start",type:"uint48"},{name:"end",type:"uint48"},{name:"salt",type:"uint256"},{name:"extraData",type:"bytes"}]},primaryType:"SpendPermission",message:{account:e.account,spender:e.spender,token:e.token,allowance:e.allowance,period:e.period,start:e.start,end:e.end,salt:e.salt,extraData:e.extraData}}}function vd({spendPermissionBatch:e,chainId:t}){return{domain:{name:"Spend Permission Manager",version:"1",chainId:t,verifyingContract:Za},types:{SpendPermissionBatch:[{name:"account",type:"address"},{name:"period",type:"uint48"},{name:"start",type:"uint48"},{name:"end",type:"uint48"},{name:"permissions",type:"PermissionDetails[]"}],PermissionDetails:[{name:"spender",type:"address"},{name:"token",type:"address"},{name:"allowance",type:"uint160"},{name:"salt",type:"uint256"},{name:"extraData",type:"bytes"}]},primaryType:"SpendPermissionBatch",message:{account:e.account,period:e.period,start:e.start,end:e.end,permissions:e.permissions.map(n=>({spender:n.spender,token:n.token,allowance:n.allowance,salt:n.salt,extraData:n.extraData}))}}}async function ei({client:e,id:t}){var n;const r=await ta(e,{id:t});if(r.status==="success")return(n=r.receipts)===null||n===void 0?void 0:n[0].transactionHash;throw b.rpc.internal("failed to send transaction")}function ti({calls:e,from:t,chainId:n,capabilities:r}){const a=fa.get().paymasterUrls;let i={method:"wallet_sendCalls",params:[{version:"1.0",calls:e,chainId:A(n),from:t,atomicRequired:!0,capabilities:r}]};return a!=null&&a[n]&&(i=At(i,{paymasterService:{url:a==null?void 0:a[n]}})),i}async function kd(){const e=Gn();return await new Promise(n=>{Un({snackbarContext:"sub_account_insufficient_balance"}),e.presentItem({autoExpand:!0,message:"Insufficient spend permission. Choose how to proceed:",menuItems:[{isRed:!1,info:"Create new Spend Permission",svgWidth:"10",svgHeight:"11",path:"",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:()=>{Ne({snackbarContext:"sub_account_insufficient_balance",snackbarAction:"create_permission"}),e.clear(),n("update_permission")}},{isRed:!1,info:"Continue in Popup",svgWidth:"10",svgHeight:"11",path:"",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:()=>{Ne({snackbarContext:"sub_account_insufficient_balance",snackbarAction:"continue_in_popup"}),e.clear(),n("continue_popup")}},{isRed:!0,info:"Cancel",svgWidth:"10",svgHeight:"11",path:"",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:()=>{Ne({snackbarContext:"sub_account_insufficient_balance",snackbarAction:"cancel"}),e.clear(),n("cancel")}}]})})}function xd({errorData:e,sourceAddress:t}){var n;const r=[];for(const[a,{amount:i,sources:s}]of Object.entries((n=e==null?void 0:e.required)!==null&&n!==void 0?n:{})){if(s.filter(c=>ce(c.balance)>=ce(i)&&c.address.toLowerCase()===(t==null?void 0:t.toLowerCase())).length===0)throw new Error("Source address has insufficient balance for a token");r.push({token:a,requiredAmount:ce(i)})}return r}function Sd(e){return typeof e=="object"&&e!==null&&"calls"in e}function _d(e){return Array.isArray(e)&&e.length===1&&typeof e[0]=="object"&&e[0]!==null&&"to"in e[0]}function Ed(e){return Cs(de(De(e)),0,16)}function Id({attribution:e,dappOrigin:t}){if(e){if("auto"in e&&e.auto&&t)return Ed(t);if("dataSuffix"in e)return e.dataSuffix}}function Ad(e,t){var n;if(!Array.isArray(e==null?void 0:e.params))return!1;const r=(n=e.params[0])===null||n===void 0?void 0:n.capabilities;return!r||typeof r!="object"?!1:t in r}function lt(e,t){const n=e.filter(r=>r!==t);return[t,...n]}function ft(e,t){return[...e.filter(r=>r!==t),t]}async function Pd(){const e=w.spendPermissions.get(),t=w.subAccounts.get(),n=w.account.get().accounts;return n?{accounts:n==null?void 0:n.map(a=>({address:a,capabilities:{subAccounts:t?[t]:void 0,spendPermissions:e.length>0?{permissions:e}:void 0}}))}:null}function Od(e){return e.replaceAll("+","-").replaceAll("/","_").replace(/=+$/,"")}function pt(e){const t=btoa(String.fromCharCode(...new Uint8Array(e)));return Od(t)}function Cd({webauthn:e,signature:t,id:n}){const r=Ya(t);return{id:n,rawId:pt(tr(n)),response:{authenticatorData:pt(Kt(e.authenticatorData)),clientDataJSON:pt(tr(e.clientDataJSON)),signature:pt(Td(r.r,r.s))},type:JSON.parse(e.clientDataJSON).type}}function Td(e,t){const n=Kt(qt(A(e))),r=Kt(qt(A(t))),a=n.length,i=r.length,s=a+i+4,o=new Uint8Array(s+2);return o[0]=48,o[1]=s,o[2]=2,o[3]=a,o.set(n,4),o[a+4]=2,o[a+5]=i,o.set(r,a+6),o}var Ld=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};async function jd(e){const{owner:t,ownerIndex:n,address:r,client:a,factoryData:i}=e,s={abi:Xs,address:Io,version:"0.6"},o={abi:Xa,address:Qa};return ro({client:a,entryPoint:s,extend:{abi:ne,factory:o},async decodeCalls(c){const u=Zr({abi:ne,data:c});if(u.functionName==="execute")return[{to:u.args[0],value:u.args[1],data:u.args[2]}];if(u.functionName==="executeBatch")return u.args[0].map(d=>({to:d.target,value:d.value,data:d.data}));throw new E(`unable to decode calls for "${u.functionName}"`)},async encodeCalls(c){var u,d;return c.length===1?he({abi:ne,functionName:"execute",args:[c[0].to,(u=c[0].value)!==null&&u!==void 0?u:BigInt(0),(d=c[0].data)!==null&&d!==void 0?d:"0x"]}):he({abi:ne,functionName:"executeBatch",args:[c.map(l=>{var p,f;return{data:(p=l.data)!==null&&p!==void 0?p:"0x",target:l.to,value:(f=l.value)!==null&&f!==void 0?f:BigInt(0)}})]})},async getAddress(){return r},async getFactoryArgs(){return i?{factory:o.address,factoryData:i}:{factory:o.address,factoryData:i}},async getStubSignature(){return t.type==="webAuthn"?"0x0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000012000000000000000000000000000000000000000000000000000000000000000170000000000000000000000000000000000000000000000000000000000000001949fc7c88032b9fcb5f6efc7a7b8c63668eae9871b765e23123bb473ff57aa831a7c0d9276168ebcc29f2875a0239cffdf2a9cd1c2007c5c77c071db9264df1d000000000000000000000000000000000000000000000000000000000000002549960de5880e8c687434170f6476605b8fe4aeb9a28632c7995cf3ba831d97630500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008a7b2274797065223a22776562617574686e2e676574222c226368616c6c656e6765223a2273496a396e6164474850596759334b7156384f7a4a666c726275504b474f716d59576f4d57516869467773222c226f726967696e223a2268747470733a2f2f7369676e2e636f696e626173652e636f6d222c2263726f73734f726967696e223a66616c73657d00000000000000000000000000000000000000000000":We({ownerIndex:n,signature:"0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c"})},async sign(c){const u=await this.getAddress(),d=Ht({address:u,chainId:a.chain.id,hash:c.hash}),l=await ht({hash:d,owner:t});return We({ownerIndex:n,signature:l})},async signMessage(c){const{message:u}=c,d=await this.getAddress(),l=Ht({address:d,chainId:a.chain.id,hash:Qr(u)}),p=await ht({hash:l,owner:t});return We({ownerIndex:n,signature:p})},async signTypedData(c){const{domain:u,types:d,primaryType:l,message:p}=c,f=await this.getAddress(),m=Ht({address:f,chainId:a.chain.id,hash:Ct({domain:u,message:p,primaryType:l,types:d})}),y=await ht({hash:m,owner:t});return We({ownerIndex:n,signature:y})},async signUserOperation(c){const{chainId:u=a.chain.id}=c,d=Ld(c,["chainId"]),l=await this.getAddress(),p=no({chainId:u,entryPointAddress:s.address,entryPointVersion:s.version,userOperation:Object.assign(Object.assign({},d),{sender:l})}),f=await ht({hash:p,owner:t});return We({ownerIndex:n,signature:f})},userOperation:{async estimateGas(c){var u;if(t.type==="webAuthn")return{verificationGasLimit:BigInt(Math.max(Number((u=c.verificationGasLimit)!==null&&u!==void 0?u:BigInt(0)),8e5))}}}})}async function ht({hash:e,owner:t}){if(t.type==="webAuthn"){const{signature:n,webauthn:r}=await t.sign({hash:e});return Md({signature:n,webauthn:r})}if(t.sign)return t.sign({hash:e});throw new E("`owner` does not support raw sign.")}function Ht({address:e,chainId:t,hash:n}){return Ct({domain:{chainId:t,name:"Coinbase Smart Wallet",verifyingContract:e,version:"1"},types:{CoinbaseSmartWalletMessage:[{name:"hash",type:"bytes32"}]},primaryType:"CoinbaseSmartWalletMessage",message:{hash:n}})}function Md({webauthn:e,signature:t}){const{r:n,s:r}=Ya(t);return Je([{components:[{name:"authenticatorData",type:"bytes"},{name:"clientDataJSON",type:"bytes"},{name:"challengeIndex",type:"uint256"},{name:"typeIndex",type:"uint256"},{name:"r",type:"uint256"},{name:"s",type:"uint256"}],type:"tuple"}],[{authenticatorData:e.authenticatorData,clientDataJSON:zr(e.clientDataJSON),challengeIndex:BigInt(e.challengeIndex),typeIndex:BigInt(e.typeIndex),r:n,s:r}])}function We(e){const{ownerIndex:t=0}=e,n=(()=>{if(Ts(e.signature)!==65)return e.signature;const r=qs(e.signature);return Rs(["bytes32","bytes32","uint8"],[r.r,r.s,r.yParity===0?27:28])})();return Je([{components:[{name:"ownerIndex",type:"uint8"},{name:"signatureData",type:"bytes"}],type:"tuple"}],[{ownerIndex:t,signatureData:n}])}async function Dd({address:e,client:t,factory:n,factoryData:r,owner:a,ownerIndex:i,parentAddress:s,attribution:o}){var c;const u={address:e,factory:n,factoryData:r},d=(c=t.chain)===null||c===void 0?void 0:c.id;if(!d)throw b.rpc.internal("chainId not found");const l=await jd({owner:a,ownerIndex:i??1,address:e,client:t,factoryData:r}),p=async f=>{var m,y,h,v,x,k;try{switch(f.method){case"wallet_addSubAccount":return u;case"eth_accounts":return[u.address];case"eth_coinbase":return u.address;case"net_version":return d.toString();case"eth_chainId":return A(d);case"eth_sendTransaction":{ve(f.params);const S=f.params[0];J(S.to,b.rpc.invalidParams("to is required"));const j={to:S.to,data:Sn((m=S.data)!==null&&m!==void 0?m:"0x",!0),value:Sn((y=S.value)!==null&&y!==void 0?y:"0x",!0),from:(h=S.from)!==null&&h!==void 0?h:u.address},Y=ti({calls:[j],chainId:d,from:j.from}),P=await p(Y);return ei({client:t,id:P})}case"wallet_sendCalls":{ve(f.params);const S=Ve(f.params[0],"chainId");if(!S)throw b.rpc.invalidParams("chainId is required");if(!Le(S))throw b.rpc.invalidParams("chainId must be a hex encoded integer");if(!f.params[0])throw b.rpc.invalidParams("params are required");if(!("calls"in f.params[0]))throw b.rpc.invalidParams("calls are required");let j={method:"wallet_prepareCalls",params:[{version:"1.0",calls:f.params[0].calls,chainId:S,from:u.address,capabilities:"capabilities"in f.params[0]?f.params[0].capabilities:{}}]};s&&(j=At(j,{funding:[{type:"spendPermission",data:{autoApply:!0,sources:[s],preference:"PREFER_DIRECT_BALANCE"}}]}));let Y=await p(j);const P=await((x=(v=a).sign)===null||x===void 0?void 0:x.call(v,{hash:nr(Y.signatureRequest.hash)}));let D;if(!P)throw b.rpc.internal("signature not found");return Le(P)?D={type:"secp256k1",data:{address:a.address,signature:P}}:D={type:"webauthn",data:{signature:JSON.stringify(Cd(Object.assign({id:(k=a.id)!==null&&k!==void 0?k:"1"},P))),publicKey:a.publicKey}},(await p({method:"wallet_sendPreparedCalls",params:[{version:"1.0",type:Y.type,data:Y.userOp,chainId:Y.chainId,signature:D}]}))[0]}case"wallet_sendPreparedCalls":{ve(f.params);const S=Ve(f.params[0],"chainId");if(!S)throw b.rpc.invalidParams("chainId is required");if(!Le(S))throw b.rpc.invalidParams("chainId must be a hex encoded integer");return await t.request({method:"wallet_sendPreparedCalls",params:f.params})}case"wallet_prepareCalls":{ve(f.params);const S=Ve(f.params[0],"chainId");if(!S)throw b.rpc.invalidParams("chainId is required");if(!Le(S))throw b.rpc.invalidParams("chainId must be a hex encoded integer");if(!f.params[0])throw b.rpc.invalidParams("params are required");if(!Ve(f.params[0],"calls"))throw b.rpc.invalidParams("calls are required");const j=f.params[0];return o&&j.capabilities&&!("attribution"in j.capabilities)&&(j.capabilities.attribution=o),await t.request({method:"wallet_prepareCalls",params:[Object.assign(Object.assign({},f.params[0]),{chainId:S})]})}case"personal_sign":{if(ve(f.params),!Le(f.params[0]))throw b.rpc.invalidParams("message must be a hex encoded string");const S=nr(f.params[0]);return l.signMessage({message:S})}case"eth_signTypedData_v4":{ve(f.params);const S=typeof f.params[1]=="string"?JSON.parse(f.params[1]):f.params[1];return l.signTypedData(S)}case"eth_signTypedData_v1":case"eth_signTypedData_v3":case"wallet_addEthereumChain":case"wallet_switchEthereumChain":default:throw b.rpc.methodNotSupported()}}catch(S){if(ga(S)){const j=Ko(S);if(j)throw j}throw S}};return{request:p}}async function ni({address:e,client:t,publicKey:n,factory:r,factoryData:a}){if(!await Jr(t,{address:e})&&r&&a){if(rr(r)!==rr(Qa))throw b.rpc.internal("unknown factory address");const o=Zr({abi:Xa,data:a});if(o.functionName!=="createAccount")throw b.rpc.internal("unknown factory function");const[c]=o.args;return c.findIndex(u=>u.toLowerCase()===Pr(n).toLowerCase())}const s=await Vt(t,{address:e,abi:ne,functionName:"ownerCount"});for(let o=Number(s)-1;o>=0;o--){const c=await Vt(t,{address:e,abi:ne,functionName:"ownerAtIndex",args:[BigInt(o)]}),u=Pr(n);if(c.toLowerCase()===u.toLowerCase())return o}return-1}function Pr(e){return tt(e)?K(e):e}async function Nd(){const e=Gn();return new Promise(t=>{Un({snackbarContext:"sub_account_add_owner"}),e.presentItem({autoExpand:!0,message:"App requires a signer update",menuItems:[{isRed:!1,info:"Confirm",svgWidth:"10",svgHeight:"11",path:"",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:()=>{Ne({snackbarContext:"sub_account_add_owner",snackbarAction:"confirm"}),e.clear(),t("authenticate")}},{isRed:!0,info:"Cancel",svgWidth:"10",svgHeight:"11",path:"",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:()=>{Ne({snackbarContext:"sub_account_add_owner",snackbarAction:"cancel"}),e.clear(),t("cancel")}}]})})}async function Rd({ownerAccount:e,globalAccountRequest:t}){var n,r;const a=w.account.get(),i=w.subAccounts.get(),s=(n=a.accounts)===null||n===void 0?void 0:n.find(m=>m.toLowerCase()!==(i==null?void 0:i.address.toLowerCase()));J(s,b.provider.unauthorized("no global account")),J((r=a.chain)===null||r===void 0?void 0:r.id,b.provider.unauthorized("no chain id")),J(i==null?void 0:i.address,b.provider.unauthorized("no sub account"));const o=[];if(e.type==="local"&&e.address&&o.push({to:i.address,data:he({abi:ne,functionName:"addOwnerAddress",args:[e.address]}),value:De(0)}),e.publicKey){const[m,y]=Xr([{type:"bytes32"},{type:"bytes32"}],e.publicKey);o.push({to:i.address,data:he({abi:ne,functionName:"addOwnerPublicKey",args:[m,y]}),value:De(0)})}const c={method:"wallet_sendCalls",params:[{version:"1",calls:o,chainId:A(84532),from:s}]};if(await Nd()==="cancel")throw b.provider.unauthorized("user cancelled");const d=await t(c),l=La(a.chain.id);if(J(l,b.rpc.internal(`client not found for chainId ${a.chain.id}`)),(await ta(l,{id:d})).status!=="success")throw b.rpc.internal("add owner call failed");const f=await ni({address:i.address,publicKey:e.type==="local"&&e.address?e.address:e.publicKey,client:l});if(f===-1)throw b.rpc.internal("failed to find owner index");return f}async function Bd({errorData:e,globalAccountAddress:t,subAccountAddress:n,client:r,request:a,subAccountRequest:i,globalAccountRequest:s}){var o;const c=(o=r.chain)===null||o===void 0?void 0:o.id;J(c,b.rpc.internal("invalid chainId"));const u=xd({errorData:e,sourceAddress:t}),d=await kd();if(d==="cancel")throw new Error("User cancelled funding");let l;const p=60*60*24,f=3;if(d==="update_permission"){if(u.length===1){const k=u[0],S=wd({spendPermission:{token:k.token,allowance:A(k.requiredAmount*BigInt(f)),period:p,account:t,spender:n,start:0,end:0xffffffffffff,salt:A(BigInt(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),extraData:"0x"},chainId:c});l={method:"eth_signTypedData_v4",params:[t,S]}}else{const k=vd({spendPermissionBatch:{account:t,period:p,start:0,end:0xffffffffffff,permissions:u.map(S=>({token:S.token,allowance:A(S.requiredAmount*BigInt(f)),period:p,account:t,spender:n,salt:"0x0",extraData:"0x"}))},chainId:c});l={method:"eth_signTypedData_v4",params:[t,k]}}try{await s(l)}catch(k){throw console.error(k),new Error("User denied spend permission request")}return i(a)}const m=u.map(k=>k.token.toLowerCase()==="******************************************".toLowerCase()?{to:n,value:A(k.requiredAmount),data:"0x"}:{to:k.token,value:"0x0",data:he({abi:Ls,functionName:"transfer",args:[n,k.requiredAmount]})});let y;if(a.method==="wallet_sendCalls"&&Sd(a.params))y=a.params[0];else if(a.method==="eth_sendTransaction"&&_d(a.params))y=ti({calls:[a.params[0]],chainId:c,from:a.params[0].from}).params[0];else throw new Error("Could not get original call");const h=he({abi:ne,functionName:"executeBatch",args:[y.calls.map(k=>{var S,j;return{target:k.to,value:ce((S=k.value)!==null&&S!==void 0?S:"0x0"),data:(j=k.data)!==null&&j!==void 0?j:"0x"}})]}),v=[...m,{data:h,to:n,value:"0x0"}],x=await s({method:"wallet_sendCalls",params:[Object.assign(Object.assign({},y),{calls:v,from:t})]});return a.method==="eth_sendTransaction"?ei({client:r,id:x}):x}class ri{constructor(t){var n,r,a,i;this.communicator=t.communicator,this.callback=t.callback,this.keyManager=new hd;const{account:s,chains:o}=w.getState();this.accounts=(n=s.accounts)!==null&&n!==void 0?n:[],this.chain=(r=s.chain)!==null&&r!==void 0?r:{id:(i=(a=t.metadata.appChainIds)===null||a===void 0?void 0:a[0])!==null&&i!==void 0?i:1},o&&hr(o)}async handshake(t){var n,r,a;const i=ee.get(t);Uc({method:t.method,correlationId:i});try{await((r=(n=this.communicator).waitForPopupLoaded)===null||r===void 0?void 0:r.call(n));const s=await this.createRequestMessage({handshake:{method:t.method,params:(a=t.params)!==null&&a!==void 0?a:[]}},i),o=await this.communicator.postRequestAndWaitForResponse(s);if("failure"in o.content)throw o.content.failure;const c=await Da("public",o.sender);await this.keyManager.setPeerPublicKey(c);const u=await this.decryptResponseMessage(o);this.handleResponse(t,u),Fc({method:t.method,correlationId:i})}catch(s){throw Gc({method:t.method,correlationId:i,errorMessage:Ie(s)}),s}}async request(t){const n=ee.get(t);Hc({method:t.method,correlationId:n});try{const r=await this._request(t);return $c({method:t.method,correlationId:n}),r}catch(r){throw zc({method:t.method,correlationId:n,errorMessage:Ie(r)}),r}}async _request(t){var n,r,a,i,s,o,c,u,d,l,p,f,m,y;if(this.accounts.length===0)switch(t.method){case"eth_requestAccounts":return await((r=(n=this.communicator).waitForPopupLoaded)===null||r===void 0?void 0:r.call(n)),await Ft(),await this.request({method:"wallet_connect",params:[{version:"1",capabilities:Object.assign({},(i=(a=w.subAccountsConfig.get())===null||a===void 0?void 0:a.capabilities)!==null&&i!==void 0?i:{})}]}),this.accounts;case"wallet_switchEthereumChain":{Ar(t.params),this.chain.id=Number(t.params[0].chainId);return}case"wallet_connect":{await((o=(s=this.communicator).waitForPopupLoaded)===null||o===void 0?void 0:o.call(s)),await Ft();let h={};Ad(t,"addSubAccount")&&(h=(u=(c=w.subAccountsConfig.get())===null||c===void 0?void 0:c.capabilities)!==null&&u!==void 0?u:{});const v=At(t,h);return this.sendRequestToPopup(v)}case"wallet_sendCalls":case"wallet_sign":return this.sendRequestToPopup(t);default:throw b.provider.unauthorized()}if(this.shouldRequestUseSubAccountSigner(t)){const h=ee.get(t);Wc({method:t.method,correlationId:h});try{const v=await this.sendRequestToSubAccountSigner(t);return qc({method:t.method,correlationId:h}),v}catch(v){throw Vc({method:t.method,correlationId:h,errorMessage:Ie(v)}),v}}switch(t.method){case"eth_requestAccounts":case"eth_accounts":{const h=w.subAccounts.get(),v=w.subAccountsConfig.get();return h!=null&&h.address&&(this.accounts=v!=null&&v.enableAutoSubAccounts?lt(this.accounts,h.address):ft(this.accounts,h.address)),(d=this.callback)===null||d===void 0||d.call(this,"connect",{chainId:A(this.chain.id)}),this.accounts}case"eth_coinbase":return this.accounts[0];case"net_version":return this.chain.id;case"eth_chainId":return A(this.chain.id);case"wallet_getCapabilities":return this.handleGetCapabilitiesRequest(t);case"wallet_switchEthereumChain":return this.handleSwitchChainRequest(t);case"eth_ecRecover":case"personal_sign":case"wallet_sign":case"personal_ecRecover":case"eth_signTransaction":case"eth_sendTransaction":case"eth_signTypedData_v1":case"eth_signTypedData_v3":case"eth_signTypedData_v4":case"eth_signTypedData":case"wallet_addEthereumChain":case"wallet_watchAsset":case"wallet_sendCalls":case"wallet_showCallsStatus":case"wallet_grantPermissions":return this.sendRequestToPopup(t);case"wallet_connect":{const h=await Pd();if(h)return h;await((p=(l=this.communicator).waitForPopupLoaded)===null||p===void 0?void 0:p.call(l)),await Ft();const v=w.subAccountsConfig.get(),x=At(t,(f=v==null?void 0:v.capabilities)!==null&&f!==void 0?f:{});return(m=this.callback)===null||m===void 0||m.call(this,"connect",{chainId:A(this.chain.id)}),this.sendRequestToPopup(x)}case"wallet_getSubAccounts":{const h=w.subAccounts.get();if(h!=null&&h.address)return{subAccounts:[h]};if(!this.chain.rpcUrl)throw b.rpc.internal("No RPC URL set for chain");const v=await Ze(t,this.chain.rpcUrl);if(ve(v.subAccounts,"subAccounts"),v.subAccounts.length>0){ct(v.subAccounts[0]);const x=v.subAccounts[0];w.subAccounts.set({address:x.address,factory:x.factory,factoryData:x.factoryData})}return v}case"wallet_addSubAccount":return this.addSubAccount(t);case"coinbase_fetchPermissions":{gd(t);const h=bd(t),v=await Ze(h,Sa),x=Ke((y=h.params)===null||y===void 0?void 0:y[0].chainId);return w.spendPermissions.set(v.permissions.map(k=>Object.assign(Object.assign({},k),{chainId:x}))),v}default:if(!this.chain.rpcUrl)throw b.rpc.internal("No RPC URL set for chain");return Ze(t,this.chain.rpcUrl)}}async sendRequestToPopup(t){var n,r;await((r=(n=this.communicator).waitForPopupLoaded)===null||r===void 0?void 0:r.call(n));const a=await this.sendEncryptedRequest(t),i=await this.decryptResponseMessage(a);return this.handleResponse(t,i)}async handleResponse(t,n){var r,a,i,s,o;const c=n.result;if("error"in c)throw c.error;switch(t.method){case"eth_requestAccounts":{const u=c.value;this.accounts=u,w.account.set({accounts:u,chain:this.chain}),(r=this.callback)===null||r===void 0||r.call(this,"accountsChanged",u);break}case"wallet_connect":{const u=c.value,d=u.accounts.map(v=>v.address);this.accounts=d,w.account.set({accounts:d});const l=u.accounts.at(0),p=l==null?void 0:l.capabilities;if(p!=null&&p.subAccounts){const v=p==null?void 0:p.subAccounts;ve(v,"subAccounts"),ct(v[0]),w.subAccounts.set({address:v[0].address,factory:v[0].factory,factoryData:v[0].factoryData})}let f=[this.accounts[0]];const m=w.subAccounts.get(),y=w.subAccountsConfig.get();m!=null&&m.address&&(this.accounts=y!=null&&y.enableAutoSubAccounts?lt(this.accounts,m.address):ft(this.accounts,m.address));const h=(i=(a=u==null?void 0:u.accounts)===null||a===void 0?void 0:a[0].capabilities)===null||i===void 0?void 0:i.spendPermissions;h&&"permissions"in h&&w.spendPermissions.set(h==null?void 0:h.permissions),(s=this.callback)===null||s===void 0||s.call(this,"accountsChanged",f);break}case"wallet_addSubAccount":{ct(c.value);const u=c.value;w.subAccounts.set(u);const d=w.subAccountsConfig.get();this.accounts=d!=null&&d.enableAutoSubAccounts?lt(this.accounts,u.address):ft(this.accounts,u.address),(o=this.callback)===null||o===void 0||o.call(this,"accountsChanged",this.accounts);break}}return c.value}async cleanup(){var t,n;const r=w.config.get().metadata;await this.keyManager.clear(),w.account.clear(),w.subAccounts.clear(),w.spendPermissions.clear(),w.chains.clear(),this.accounts=[],this.chain={id:(n=(t=r==null?void 0:r.appChainIds)===null||t===void 0?void 0:t[0])!==null&&n!==void 0?n:1}}async handleSwitchChainRequest(t){Ar(t.params);const n=Qe(t.params[0].chainId);if(this.updateChain(n))return null;const a=await this.sendRequestToPopup(t);return a===null&&this.updateChain(n),a}async handleGetCapabilitiesRequest(t){yd(t.params);const n=t.params[0],r=t.params[1];if(!this.accounts.some(o=>Vr(o,n)))throw b.provider.unauthorized("no active account found when getting capabilities");const a=w.getState().account.capabilities;if(!a)return{};if(!r||r.length===0)return a;const i=new Set(r.map(o=>Ke(o)));return Object.fromEntries(Object.entries(a).filter(([o])=>{try{const c=Ke(o);return i.has(c)}catch{return!1}}))}async sendEncryptedRequest(t){const n=await this.keyManager.getSharedSecret();if(!n)throw b.provider.unauthorized("No shared secret found when encrypting request");const r=await au({action:t,chainId:this.chain.id},n),a=ee.get(t),i=await this.createRequestMessage({encrypted:r},a);return this.communicator.postRequestAndWaitForResponse(i)}async createRequestMessage(t,n){const r=await Ma("public",await this.keyManager.getOwnPublicKey());return{id:crypto.randomUUID(),correlationId:n,sender:r,content:t,timestamp:new Date}}async decryptResponseMessage(t){var n,r,a;const i=t.content;if("failure"in i)throw i.failure;const s=await this.keyManager.getSharedSecret();if(!s)throw b.provider.unauthorized("Invalid session: no shared secret found when decrypting response");const o=await iu(i.encrypted,s),c=(n=o.data)===null||n===void 0?void 0:n.chains;if(c){const d=(r=o.data)===null||r===void 0?void 0:r.nativeCurrencies,l=Object.entries(c).map(([p,f])=>{const m=d==null?void 0:d[Number(p)];return Object.assign({id:Number(p),rpcUrl:f},m?{nativeCurrency:m}:{})});w.chains.set(l),this.updateChain(this.chain.id,l),hr(l)}const u=(a=o.data)===null||a===void 0?void 0:a.capabilities;return u&&w.account.set({capabilities:u}),o}updateChain(t,n){var r;const a=w.getState(),i=n??a.chains,s=i==null?void 0:i.find(o=>o.id===t);return s?(s!==this.chain&&(this.chain=s,w.account.set({chain:s}),(r=this.callback)===null||r===void 0||r.call(this,"chainChanged",je(s.id))),!0):!1}async addSubAccount(t){var n,r,a,i;const o=w.getState().subAccount,c=w.subAccountsConfig.get();if(o!=null&&o.address)return this.accounts=c!=null&&c.enableAutoSubAccounts?lt(this.accounts,o.address):ft(this.accounts,o.address),(n=this.callback)===null||n===void 0||n.call(this,"accountsChanged",this.accounts),o;if(await((a=(r=this.communicator).waitForPopupLoaded)===null||a===void 0?void 0:a.call(r)),Array.isArray(t.params)&&t.params.length>0&&t.params[0].account&&t.params[0].account.type==="create"){let d;if(t.params[0].account.keys&&t.params[0].account.keys.length>0)d=t.params[0].account.keys;else{const l=(i=w.subAccountsConfig.get())!==null&&i!==void 0?i:{},{account:p}=l.toOwnerAccount?await l.toOwnerAccount():await In();if(!p)throw b.provider.unauthorized("could not get subaccount owner account when adding sub account");d=[{type:p.address?"address":"webauthn-p256",publicKey:p.address||p.publicKey}]}t.params[0].account.keys=d}const u=await this.sendRequestToPopup(t);return ct(u),u}shouldRequestUseSubAccountSigner(t){const n=Ir(t),r=w.subAccounts.get();return n?n.toLowerCase()===(r==null?void 0:r.address.toLowerCase()):!1}async sendRequestToSubAccountSigner(t){var n;const r=w.subAccounts.get(),a=w.subAccountsConfig.get(),i=w.config.get();J(r==null?void 0:r.address,b.provider.unauthorized("no active sub account when sending request to sub account signer"));const s=a!=null&&a.toOwnerAccount?await a.toOwnerAccount():await In();J(s==null?void 0:s.account,b.provider.unauthorized("no active sub account owner when sending request to sub account signer")),Ir(t)===void 0&&(t=md(t,r.address));const c=La(this.chain.id);J(c,b.rpc.internal(`client not found for chainId ${this.chain.id} when sending request to sub account signer`));const u=this.accounts.find(m=>m.toLowerCase()!==r.address.toLowerCase());J(u,b.provider.unauthorized("no global account found when sending request to sub account signer"));const d=Id({attribution:(n=i.preference)===null||n===void 0?void 0:n.attribution,dappOrigin:window.location.origin}),l=s.account.type==="local"?s.account.address:s.account.publicKey;let p=await ni({address:r.address,factory:r.factory,factoryData:r.factoryData,publicKey:l,client:c});if(p===-1){const m=ee.get(t);Kc({method:t.method,correlationId:m});try{p=await Rd({ownerAccount:s.account,globalAccountRequest:this.sendRequestToPopup.bind(this)}),Jc({method:t.method,correlationId:m})}catch(y){return Yc({method:t.method,correlationId:m,errorMessage:Ie(y)}),b.provider.unauthorized("failed to add sub account owner when sending request to sub account signer")}}const{request:f}=await Dd({address:r.address,owner:s.account,client:c,factory:r.factory,factoryData:r.factoryData,parentAddress:u,attribution:d?{suffix:d}:void 0,ownerIndex:p});try{return await f(t)}catch(m){let y;if(ga(m))y=JSON.parse(m.details);else if(ur(m))y=m;else throw m;if(!(ur(y)&&y.data)||!y.data)throw m;const h=ee.get(t);Qc({method:t.method,correlationId:h});try{const v=await Bd({errorData:y.data,globalAccountAddress:u,subAccountAddress:r.address,client:c,request:t,subAccountRequest:f,globalAccountRequest:this.request.bind(this)});return Zc({method:t.method,correlationId:h}),v}catch(v){throw console.error(v),Xc({method:t.method,correlationId:h,errorMessage:Ie(v)}),m}}}}const Ud=({method:e,correlationId:t})=>{L("walletlink_signer.handshake.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t},T.high)},Gd=({method:e,correlationId:t,errorMessage:n})=>{L("walletlink_signer.handshake.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n},T.high)},Fd=({method:e,correlationId:t})=>{L("walletlink_signer.handshake.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t},T.high)},Hd=({method:e,correlationId:t})=>{L("walletlink_signer.request.started",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t},T.high)},zd=({method:e,correlationId:t,errorMessage:n})=>{L("walletlink_signer.request.error",{action:C.error,componentType:O.unknown,method:e,correlationId:t,errorMessage:n},T.high)},$d=({method:e,correlationId:t})=>{L("walletlink_signer.request.completed",{action:C.unknown,componentType:O.unknown,method:e,correlationId:t},T.high)},Or=()=>{L("walletlink_signer.walletlink_connection.connection_failed",{action:C.measurement,componentType:O.unknown},T.high)},Wd=()=>{L("walletlink_signer.walletlink_connection.fetch_unseen_events_failed",{action:C.measurement,componentType:O.unknown},T.high)};var N={},H={};Object.defineProperty(H,"__esModule",{value:!0});H.output=H.exists=H.hash=H.bytes=H.bool=H.number=H.isBytes=void 0;function Pt(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}H.number=Pt;function ai(e){if(typeof e!="boolean")throw new Error(`boolean expected, not ${e}`)}H.bool=ai;function ii(e){return e instanceof Uint8Array||e!=null&&typeof e=="object"&&e.constructor.name==="Uint8Array"}H.isBytes=ii;function qn(e,...t){if(!ii(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}H.bytes=qn;function si(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Pt(e.outputLen),Pt(e.blockLen)}H.hash=si;function oi(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}H.exists=oi;function ci(e,t){qn(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}H.output=ci;const qd={number:Pt,bool:ai,bytes:qn,hash:si,exists:oi,output:ci};H.default=qd;var _={};Object.defineProperty(_,"__esModule",{value:!0});_.add5L=_.add5H=_.add4H=_.add4L=_.add3H=_.add3L=_.add=_.rotlBL=_.rotlBH=_.rotlSL=_.rotlSH=_.rotr32L=_.rotr32H=_.rotrBL=_.rotrBH=_.rotrSL=_.rotrSH=_.shrSL=_.shrSH=_.toBig=_.split=_.fromBig=void 0;const mt=BigInt(2**32-1),An=BigInt(32);function Vn(e,t=!1){return t?{h:Number(e&mt),l:Number(e>>An&mt)}:{h:Number(e>>An&mt)|0,l:Number(e&mt)|0}}_.fromBig=Vn;function ui(e,t=!1){let n=new Uint32Array(e.length),r=new Uint32Array(e.length);for(let a=0;a<e.length;a++){const{h:i,l:s}=Vn(e[a],t);[n[a],r[a]]=[i,s]}return[n,r]}_.split=ui;const di=(e,t)=>BigInt(e>>>0)<<An|BigInt(t>>>0);_.toBig=di;const li=(e,t,n)=>e>>>n;_.shrSH=li;const fi=(e,t,n)=>e<<32-n|t>>>n;_.shrSL=fi;const pi=(e,t,n)=>e>>>n|t<<32-n;_.rotrSH=pi;const hi=(e,t,n)=>e<<32-n|t>>>n;_.rotrSL=hi;const mi=(e,t,n)=>e<<64-n|t>>>n-32;_.rotrBH=mi;const yi=(e,t,n)=>e>>>n-32|t<<64-n;_.rotrBL=yi;const gi=(e,t)=>t;_.rotr32H=gi;const bi=(e,t)=>e;_.rotr32L=bi;const wi=(e,t,n)=>e<<n|t>>>32-n;_.rotlSH=wi;const vi=(e,t,n)=>t<<n|e>>>32-n;_.rotlSL=vi;const ki=(e,t,n)=>t<<n-32|e>>>64-n;_.rotlBH=ki;const xi=(e,t,n)=>e<<n-32|t>>>64-n;_.rotlBL=xi;function Si(e,t,n,r){const a=(t>>>0)+(r>>>0);return{h:e+n+(a/2**32|0)|0,l:a|0}}_.add=Si;const _i=(e,t,n)=>(e>>>0)+(t>>>0)+(n>>>0);_.add3L=_i;const Ei=(e,t,n,r)=>t+n+r+(e/2**32|0)|0;_.add3H=Ei;const Ii=(e,t,n,r)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0);_.add4L=Ii;const Ai=(e,t,n,r,a)=>t+n+r+a+(e/2**32|0)|0;_.add4H=Ai;const Pi=(e,t,n,r,a)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0)+(a>>>0);_.add5L=Pi;const Oi=(e,t,n,r,a,i)=>t+n+r+a+i+(e/2**32|0)|0;_.add5H=Oi;const Vd={fromBig:Vn,split:ui,toBig:di,shrSH:li,shrSL:fi,rotrSH:pi,rotrSL:hi,rotrBH:mi,rotrBL:yi,rotr32H:gi,rotr32L:bi,rotlSH:wi,rotlSL:vi,rotlBH:ki,rotlBL:xi,add:Si,add3L:_i,add3H:Ei,add4L:Ii,add4H:Ai,add5H:Oi,add5L:Pi};_.default=Vd;var Ci={},Dt={};Object.defineProperty(Dt,"__esModule",{value:!0});Dt.crypto=void 0;Dt.crypto=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;(function(e){/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */Object.defineProperty(e,"__esModule",{value:!0}),e.randomBytes=e.wrapXOFConstructorWithOpts=e.wrapConstructorWithOpts=e.wrapConstructor=e.checkOpts=e.Hash=e.concatBytes=e.toBytes=e.utf8ToBytes=e.asyncLoop=e.nextTick=e.hexToBytes=e.bytesToHex=e.byteSwap32=e.byteSwapIfBE=e.byteSwap=e.isLE=e.rotl=e.rotr=e.createView=e.u32=e.u8=e.isBytes=void 0;const t=Dt,n=H;function r(g){return g instanceof Uint8Array||g!=null&&typeof g=="object"&&g.constructor.name==="Uint8Array"}e.isBytes=r;const a=g=>new Uint8Array(g.buffer,g.byteOffset,g.byteLength);e.u8=a;const i=g=>new Uint32Array(g.buffer,g.byteOffset,Math.floor(g.byteLength/4));e.u32=i;const s=g=>new DataView(g.buffer,g.byteOffset,g.byteLength);e.createView=s;const o=(g,I)=>g<<32-I|g>>>I;e.rotr=o;const c=(g,I)=>g<<I|g>>>32-I>>>0;e.rotl=c,e.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;const u=g=>g<<24&**********|g<<8&16711680|g>>>8&65280|g>>>24&255;e.byteSwap=u,e.byteSwapIfBE=e.isLE?g=>g:g=>(0,e.byteSwap)(g);function d(g){for(let I=0;I<g.length;I++)g[I]=(0,e.byteSwap)(g[I])}e.byteSwap32=d;const l=Array.from({length:256},(g,I)=>I.toString(16).padStart(2,"0"));function p(g){(0,n.bytes)(g);let I="";for(let U=0;U<g.length;U++)I+=l[g[U]];return I}e.bytesToHex=p;const f={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function m(g){if(g>=f._0&&g<=f._9)return g-f._0;if(g>=f._A&&g<=f._F)return g-(f._A-10);if(g>=f._a&&g<=f._f)return g-(f._a-10)}function y(g){if(typeof g!="string")throw new Error("hex string expected, got "+typeof g);const I=g.length,U=I/2;if(I%2)throw new Error("padded hex string expected, got unpadded hex of length "+I);const B=new Uint8Array(U);for(let z=0,q=0;z<U;z++,q+=2){const Jn=m(g.charCodeAt(q)),Yn=m(g.charCodeAt(q+1));if(Jn===void 0||Yn===void 0){const Xi=g[q]+g[q+1];throw new Error('hex string expected, got non-hex character "'+Xi+'" at index '+q)}B[z]=Jn*16+Yn}return B}e.hexToBytes=y;const h=async()=>{};e.nextTick=h;async function v(g,I,U){let B=Date.now();for(let z=0;z<g;z++){U(z);const q=Date.now()-B;q>=0&&q<I||(await(0,e.nextTick)(),B+=q)}}e.asyncLoop=v;function x(g){if(typeof g!="string")throw new Error(`utf8ToBytes expected string, got ${typeof g}`);return new Uint8Array(new TextEncoder().encode(g))}e.utf8ToBytes=x;function k(g){return typeof g=="string"&&(g=x(g)),(0,n.bytes)(g),g}e.toBytes=k;function S(...g){let I=0;for(let B=0;B<g.length;B++){const z=g[B];(0,n.bytes)(z),I+=z.length}const U=new Uint8Array(I);for(let B=0,z=0;B<g.length;B++){const q=g[B];U.set(q,z),z+=q.length}return U}e.concatBytes=S;class j{clone(){return this._cloneInto()}}e.Hash=j;const Y={}.toString;function P(g,I){if(I!==void 0&&Y.call(I)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(g,I)}e.checkOpts=P;function D(g){const I=B=>g().update(k(B)).digest(),U=g();return I.outputLen=U.outputLen,I.blockLen=U.blockLen,I.create=()=>g(),I}e.wrapConstructor=D;function _e(g){const I=(B,z)=>g(z).update(k(B)).digest(),U=g({});return I.outputLen=U.outputLen,I.blockLen=U.blockLen,I.create=B=>g(B),I}e.wrapConstructorWithOpts=_e;function Qi(g){const I=(B,z)=>g(z).update(k(B)).digest(),U=g({});return I.outputLen=U.outputLen,I.blockLen=U.blockLen,I.create=B=>g(B),I}e.wrapXOFConstructorWithOpts=Qi;function Zi(g=32){if(t.crypto&&typeof t.crypto.getRandomValues=="function")return t.crypto.getRandomValues(new Uint8Array(g));throw new Error("crypto.getRandomValues must be defined")}e.randomBytes=Zi})(Ci);Object.defineProperty(N,"__esModule",{value:!0});N.shake256=N.shake128=N.keccak_512=N.keccak_384=N.keccak_256=N.keccak_224=N.sha3_512=N.sha3_384=N.sha3_256=N.sha3_224=N.Keccak=N.keccakP=void 0;const Te=H,at=_,le=Ci,Ti=[],Li=[],ji=[],Kd=BigInt(0),qe=BigInt(1),Jd=BigInt(2),Yd=BigInt(7),Qd=BigInt(256),Zd=BigInt(113);for(let e=0,t=qe,n=1,r=0;e<24;e++){[n,r]=[r,(2*n+3*r)%5],Ti.push(2*(5*r+n)),Li.push((e+1)*(e+2)/2%64);let a=Kd;for(let i=0;i<7;i++)t=(t<<qe^(t>>Yd)*Zd)%Qd,t&Jd&&(a^=qe<<(qe<<BigInt(i))-qe);ji.push(a)}const[Xd,el]=(0,at.split)(ji,!0),Cr=(e,t,n)=>n>32?(0,at.rotlBH)(e,t,n):(0,at.rotlSH)(e,t,n),Tr=(e,t,n)=>n>32?(0,at.rotlBL)(e,t,n):(0,at.rotlSL)(e,t,n);function Mi(e,t=24){const n=new Uint32Array(10);for(let r=24-t;r<24;r++){for(let s=0;s<10;s++)n[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){const o=(s+8)%10,c=(s+2)%10,u=n[c],d=n[c+1],l=Cr(u,d,1)^n[o],p=Tr(u,d,1)^n[o+1];for(let f=0;f<50;f+=10)e[s+f]^=l,e[s+f+1]^=p}let a=e[2],i=e[3];for(let s=0;s<24;s++){const o=Li[s],c=Cr(a,i,o),u=Tr(a,i,o),d=Ti[s];a=e[d],i=e[d+1],e[d]=c,e[d+1]=u}for(let s=0;s<50;s+=10){for(let o=0;o<10;o++)n[o]=e[s+o];for(let o=0;o<10;o++)e[s+o]^=~n[(o+2)%10]&n[(o+4)%10]}e[0]^=Xd[r],e[1]^=el[r]}n.fill(0)}N.keccakP=Mi;class it extends le.Hash{constructor(t,n,r,a=!1,i=24){if(super(),this.blockLen=t,this.suffix=n,this.outputLen=r,this.enableXOF=a,this.rounds=i,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Te.number)(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,le.u32)(this.state)}keccak(){le.isLE||(0,le.byteSwap32)(this.state32),Mi(this.state32,this.rounds),le.isLE||(0,le.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Te.exists)(this);const{blockLen:n,state:r}=this;t=(0,le.toBytes)(t);const a=t.length;for(let i=0;i<a;){const s=Math.min(n-this.pos,a-i);for(let o=0;o<s;o++)r[this.pos++]^=t[i++];this.pos===n&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:n,pos:r,blockLen:a}=this;t[r]^=n,n&128&&r===a-1&&this.keccak(),t[a-1]^=128,this.keccak()}writeInto(t){(0,Te.exists)(this,!1),(0,Te.bytes)(t),this.finish();const n=this.state,{blockLen:r}=this;for(let a=0,i=t.length;a<i;){this.posOut>=r&&this.keccak();const s=Math.min(r-this.posOut,i-a);t.set(n.subarray(this.posOut,this.posOut+s),a),this.posOut+=s,a+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Te.number)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Te.output)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){const{blockLen:n,suffix:r,outputLen:a,rounds:i,enableXOF:s}=this;return t||(t=new it(n,r,a,s,i)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=i,t.suffix=r,t.outputLen=a,t.enableXOF=s,t.destroyed=this.destroyed,t}}N.Keccak=it;const Se=(e,t,n)=>(0,le.wrapConstructor)(()=>new it(t,e,n));N.sha3_224=Se(6,144,224/8);N.sha3_256=Se(6,136,256/8);N.sha3_384=Se(6,104,384/8);N.sha3_512=Se(6,72,512/8);N.keccak_224=Se(1,144,224/8);N.keccak_256=Se(1,136,256/8);N.keccak_384=Se(1,104,384/8);N.keccak_512=Se(1,72,512/8);const Di=(e,t,n)=>(0,le.wrapXOFConstructorWithOpts)((r={})=>new it(t,e,r.dkLen===void 0?n:r.dkLen,!0));N.shake128=Di(31,168,128/8);N.shake256=Di(31,136,256/8);const{keccak_256:tl}=N;function Ni(e){return Buffer.allocUnsafe(e).fill(0)}function nl(e){return`0x${e.toString(16)}`}function rl(e){const t=nl(e);return new Buffer(Ui(t.slice(2)),"hex")}function al(e){return e.toString(2).length}function Ri(e,t){let n=e.toString(16);n.length%2!==0&&(n="0"+n);const r=n.match(/.{1,2}/g).map(a=>parseInt(a,16));for(;r.length<t;)r.unshift(0);return Buffer.from(r)}function il(e,t){const n=e<0n;let r;if(n){const a=(1n<<BigInt(t))-1n;r=(~e&a)+1n}else r=e;return r&=(1n<<BigInt(t))-1n,r}function Bi(e,t,n){const r=Ni(t);return e=Nt(e),n?e.length<t?(e.copy(r),r):e.slice(0,t):e.length<t?(e.copy(r,t-e.length),r):e.slice(-t)}function sl(e,t){return Bi(e,t,!0)}function Nt(e){if(!Buffer.isBuffer(e))if(Array.isArray(e))e=Buffer.from(e);else if(typeof e=="string")Gi(e)?e=Buffer.from(Ui(Fi(e)),"hex"):e=Buffer.from(e);else if(typeof e=="number")e=rl(e);else if(e==null)e=Buffer.allocUnsafe(0);else if(typeof e=="bigint")e=Ri(e);else if(e.toArray)e=Buffer.from(e.toArray());else throw new Error("invalid type");return e}function ol(e){return e=Nt(e),"0x"+e.toString("hex")}function cl(e,t){if(e=Nt(e),t||(t=256),t!==256)throw new Error("unsupported");return Buffer.from(tl(new Uint8Array(e)))}function Ui(e){return e.length%2?"0"+e:e}function Gi(e){return typeof e=="string"&&e.match(/^0x[0-9A-Fa-f]*$/)}function Fi(e){return typeof e=="string"&&e.startsWith("0x")?e.slice(2):e}var Hi={zeros:Ni,setLength:Bi,setLengthRight:sl,isHexString:Gi,stripHexPrefix:Fi,toBuffer:Nt,bufferToHex:ol,keccak:cl,bitLengthFromBigInt:al,bufferBEFromBigInt:Ri,twosFromBigInt:il};const W=Hi;function zi(e){return e.startsWith("int[")?"int256"+e.slice(3):e==="int"?"int256":e.startsWith("uint[")?"uint256"+e.slice(4):e==="uint"?"uint256":e.startsWith("fixed[")?"fixed128x128"+e.slice(5):e==="fixed"?"fixed128x128":e.startsWith("ufixed[")?"ufixed128x128"+e.slice(6):e==="ufixed"?"ufixed128x128":e}function Re(e){return Number.parseInt(/^\D+(\d+)$/.exec(e)[1],10)}function Lr(e){var t=/^\D+(\d+)x(\d+)$/.exec(e);return[Number.parseInt(t[1],10),Number.parseInt(t[2],10)]}function $i(e){var t=e.match(/(.*)\[(.*?)\]$/);return t?t[2]===""?"dynamic":Number.parseInt(t[2],10):null}function Ae(e){var t=typeof e;if(t==="string"||t==="number")return BigInt(e);if(t==="bigint")return e;throw new Error("Argument is not a number")}function se(e,t){var n,r,a,i;if(e==="address")return se("uint160",Ae(t));if(e==="bool")return se("uint8",t?1:0);if(e==="string")return se("bytes",new Buffer(t,"utf8"));if(dl(e)){if(typeof t.length>"u")throw new Error("Not an array?");if(n=$i(e),n!=="dynamic"&&n!==0&&t.length>n)throw new Error("Elements exceed array size: "+n);a=[],e=e.slice(0,e.lastIndexOf("[")),typeof t=="string"&&(t=JSON.parse(t));for(i in t)a.push(se(e,t[i]));if(n==="dynamic"){var s=se("uint256",t.length);a.unshift(s)}return Buffer.concat(a)}else{if(e==="bytes")return t=new Buffer(t),a=Buffer.concat([se("uint256",t.length),t]),t.length%32!==0&&(a=Buffer.concat([a,W.zeros(32-t.length%32)])),a;if(e.startsWith("bytes")){if(n=Re(e),n<1||n>32)throw new Error("Invalid bytes<N> width: "+n);return W.setLengthRight(t,32)}else if(e.startsWith("uint")){if(n=Re(e),n%8||n<8||n>256)throw new Error("Invalid uint<N> width: "+n);r=Ae(t);const o=W.bitLengthFromBigInt(r);if(o>n)throw new Error("Supplied uint exceeds width: "+n+" vs "+o);if(r<0)throw new Error("Supplied uint is negative");return W.bufferBEFromBigInt(r,32)}else if(e.startsWith("int")){if(n=Re(e),n%8||n<8||n>256)throw new Error("Invalid int<N> width: "+n);r=Ae(t);const o=W.bitLengthFromBigInt(r);if(o>n)throw new Error("Supplied int exceeds width: "+n+" vs "+o);const c=W.twosFromBigInt(r,256);return W.bufferBEFromBigInt(c,32)}else if(e.startsWith("ufixed")){if(n=Lr(e),r=Ae(t),r<0)throw new Error("Supplied ufixed is negative");return se("uint256",r*BigInt(2)**BigInt(n[1]))}else if(e.startsWith("fixed"))return n=Lr(e),se("int256",Ae(t)*BigInt(2)**BigInt(n[1]))}throw new Error("Unsupported or invalid type: "+e)}function ul(e){return e==="string"||e==="bytes"||$i(e)==="dynamic"}function dl(e){return e.lastIndexOf("]")===e.length-1}function ll(e,t){var n=[],r=[],a=32*e.length;for(var i in e){var s=zi(e[i]),o=t[i],c=se(s,o);ul(s)?(n.push(se("uint256",a)),r.push(c),a+=c.length):n.push(c)}return Buffer.concat(n.concat(r))}function Wi(e,t){if(e.length!==t.length)throw new Error("Number of types are not matching the values");for(var n,r,a=[],i=0;i<e.length;i++){var s=zi(e[i]),o=t[i];if(s==="bytes")a.push(o);else if(s==="string")a.push(new Buffer(o,"utf8"));else if(s==="bool")a.push(new Buffer(o?"01":"00","hex"));else if(s==="address")a.push(W.setLength(o,20));else if(s.startsWith("bytes")){if(n=Re(s),n<1||n>32)throw new Error("Invalid bytes<N> width: "+n);a.push(W.setLengthRight(o,n))}else if(s.startsWith("uint")){if(n=Re(s),n%8||n<8||n>256)throw new Error("Invalid uint<N> width: "+n);r=Ae(o);const c=W.bitLengthFromBigInt(r);if(c>n)throw new Error("Supplied uint exceeds width: "+n+" vs "+c);a.push(W.bufferBEFromBigInt(r,n/8))}else if(s.startsWith("int")){if(n=Re(s),n%8||n<8||n>256)throw new Error("Invalid int<N> width: "+n);r=Ae(o);const c=W.bitLengthFromBigInt(r);if(c>n)throw new Error("Supplied int exceeds width: "+n+" vs "+c);const u=W.twosFromBigInt(r,n);a.push(W.bufferBEFromBigInt(u,n/8))}else throw new Error("Unsupported or invalid type: "+s)}return Buffer.concat(a)}function fl(e,t){return W.keccak(Wi(e,t))}var pl={rawEncode:ll,solidityPack:Wi,soliditySHA3:fl};const X=Hi,et=pl,qi={type:"object",properties:{types:{type:"object",additionalProperties:{type:"array",items:{type:"object",properties:{name:{type:"string"},type:{type:"string"}},required:["name","type"]}}},primaryType:{type:"string"},domain:{type:"object"},message:{type:"object"}},required:["types","primaryType","domain","message"]},zt={encodeData(e,t,n,r=!0){const a=["bytes32"],i=[this.hashType(e,n)];if(r){const s=(o,c,u)=>{if(n[c]!==void 0)return["bytes32",u==null?"0x0000000000000000000000000000000000000000000000000000000000000000":X.keccak(this.encodeData(c,u,n,r))];if(u===void 0)throw new Error(`missing value for field ${o} of type ${c}`);if(c==="bytes")return["bytes32",X.keccak(u)];if(c==="string")return typeof u=="string"&&(u=Buffer.from(u,"utf8")),["bytes32",X.keccak(u)];if(c.lastIndexOf("]")===c.length-1){const d=c.slice(0,c.lastIndexOf("[")),l=u.map(p=>s(o,d,p));return["bytes32",X.keccak(et.rawEncode(l.map(([p])=>p),l.map(([,p])=>p)))]}return[c,u]};for(const o of n[e]){const[c,u]=s(o.name,o.type,t[o.name]);a.push(c),i.push(u)}}else for(const s of n[e]){let o=t[s.name];if(o!==void 0)if(s.type==="bytes")a.push("bytes32"),o=X.keccak(o),i.push(o);else if(s.type==="string")a.push("bytes32"),typeof o=="string"&&(o=Buffer.from(o,"utf8")),o=X.keccak(o),i.push(o);else if(n[s.type]!==void 0)a.push("bytes32"),o=X.keccak(this.encodeData(s.type,o,n,r)),i.push(o);else{if(s.type.lastIndexOf("]")===s.type.length-1)throw new Error("Arrays currently unimplemented in encodeData");a.push(s.type),i.push(o)}}return et.rawEncode(a,i)},encodeType(e,t){let n="",r=this.findTypeDependencies(e,t).filter(a=>a!==e);r=[e].concat(r.sort());for(const a of r){if(!t[a])throw new Error("No type definition specified: "+a);n+=a+"("+t[a].map(({name:s,type:o})=>o+" "+s).join(",")+")"}return n},findTypeDependencies(e,t,n=[]){if(e=e.match(/^\w*/)[0],n.includes(e)||t[e]===void 0)return n;n.push(e);for(const r of t[e])for(const a of this.findTypeDependencies(r.type,t,n))!n.includes(a)&&n.push(a);return n},hashStruct(e,t,n,r=!0){return X.keccak(this.encodeData(e,t,n,r))},hashType(e,t){return X.keccak(this.encodeType(e,t))},sanitizeData(e){const t={};for(const n in qi.properties)e[n]&&(t[n]=e[n]);return t.types&&(t.types=Object.assign({EIP712Domain:[]},t.types)),t},hash(e,t=!0){const n=this.sanitizeData(e),r=[Buffer.from("1901","hex")];return r.push(this.hashStruct("EIP712Domain",n.domain,n.types,t)),n.primaryType!=="EIP712Domain"&&r.push(this.hashStruct(n.primaryType,n.message,n.types,t)),X.keccak(Buffer.concat(r))}};var hl={TYPED_MESSAGE_SCHEMA:qi,TypedDataUtils:zt,hashForSignTypedDataLegacy:function(e){return ml(e.data)},hashForSignTypedData_v3:function(e){return zt.hash(e.data,!1)},hashForSignTypedData_v4:function(e){return zt.hash(e.data)}};function ml(e){const t=new Error("Expect argument to be non-empty array");if(typeof e!="object"||!e.length)throw t;const n=e.map(function(i){return i.type==="bytes"?X.toBuffer(i.value):i.value}),r=e.map(function(i){return i.type}),a=e.map(function(i){if(!i.name)throw t;return i.type+" "+i.name});return et.soliditySHA3(["bytes32","bytes32"],[et.soliditySHA3(new Array(e.length).fill("string"),a),et.soliditySHA3(r,n)])}const yt=Yr(hl),yl="walletUsername",Pn="Addresses",gl="AppVersion";class bl{constructor(t){this.secret=t}async encrypt(t){const n=this.secret;if(n.length!==64)throw new Error("secret must be 256 bits");const r=crypto.getRandomValues(new Uint8Array(12)),a=await crypto.subtle.importKey("raw",gt(n),{name:"aes-gcm"},!1,["encrypt","decrypt"]),i=new TextEncoder,s=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:r},a,i.encode(t)),o=16,c=s.slice(s.byteLength-o),u=s.slice(0,s.byteLength-o),d=new Uint8Array(c),l=new Uint8Array(u),p=new Uint8Array([...r,...d,...l]);return Nn(p)}async decrypt(t){const n=this.secret;if(n.length!==64)throw new Error("secret must be 256 bits");return new Promise((r,a)=>{(async()=>{const i=await crypto.subtle.importKey("raw",gt(n),{name:"aes-gcm"},!1,["encrypt","decrypt"]),s=gt(t),o=s.slice(0,12),c=s.slice(12,28),u=s.slice(28),d=new Uint8Array([...u,...c]),l={name:"AES-GCM",iv:new Uint8Array(o)};try{const p=await window.crypto.subtle.decrypt(l,i,d),f=new TextDecoder;r(f.decode(p))}catch(p){a(p)}})()})}}class wl{constructor(t,n,r){this.linkAPIUrl=t,this.sessionId=n;const a=`${n}:${r}`;this.auth=`Basic ${btoa(a)}`}async markUnseenEventsAsSeen(t){return Promise.all(t.map(n=>fetch(`${this.linkAPIUrl}/events/${n.eventId}/seen`,{method:"POST",headers:{Authorization:this.auth}}))).catch(n=>console.error("Unable to mark events as seen:",n))}async fetchUnseenEvents(){var t;const n=await fetch(`${this.linkAPIUrl}/events?unseen=true`,{headers:{Authorization:this.auth}});if(n.ok){const{events:r,error:a}=await n.json();if(a)throw new Error(`Check unseen events failed: ${a}`);const i=(t=r==null?void 0:r.filter(s=>s.event==="Web3Response").map(s=>({type:"Event",sessionId:this.sessionId,eventId:s.id,event:s.event,data:s.data})))!==null&&t!==void 0?t:[];return this.markUnseenEventsAsSeen(i),i}throw new Error(`Check unseen events failed: ${n.status}`)}}var oe;(function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED"})(oe||(oe={}));class V{setConnectionStateListener(t){this.connectionStateListener=t}setIncomingDataListener(t){this.incomingDataListener=t}constructor(t,n=WebSocket){this.WebSocketClass=n,this.webSocket=null,this.isDisconnecting=!1,this.url=t.replace(/^http/,"ws"),this.instanceId=V.instanceCounter++,V.activeInstances.add(this.instanceId)}async connect(){if(this.webSocket)throw new Error("webSocket object is not null");if(this.isDisconnecting)throw new Error("WebSocket is disconnecting, cannot reconnect on same instance");return new Promise((t,n)=>{var r;let a;try{this.webSocket=a=new this.WebSocketClass(this.url)}catch(i){n(i);return}(r=this.connectionStateListener)===null||r===void 0||r.call(this,oe.CONNECTING),a.onclose=i=>{var s;this.clearWebSocket(),a.readyState!==WebSocket.OPEN&&n(new Error(`websocket error ${i.code}: ${i.reason}`)),(s=this.connectionStateListener)===null||s===void 0||s.call(this,oe.DISCONNECTED)},a.onopen=i=>{var s;t(),(s=this.connectionStateListener)===null||s===void 0||s.call(this,oe.CONNECTED),V.pendingData.length>0&&([...V.pendingData].forEach(c=>this.sendData(c)),V.pendingData=[])},a.onmessage=i=>{var s,o;if(i.data==="h")(s=this.incomingDataListener)===null||s===void 0||s.call(this,{type:"Heartbeat"});else try{const c=JSON.parse(i.data);(o=this.incomingDataListener)===null||o===void 0||o.call(this,c)}catch{}}})}disconnect(){var t;const{webSocket:n}=this;if(n){this.isDisconnecting=!0,this.clearWebSocket(),(t=this.connectionStateListener)===null||t===void 0||t.call(this,oe.DISCONNECTED),this.connectionStateListener=void 0,this.incomingDataListener=void 0;try{n.close()}catch{}}}sendData(t){const{webSocket:n}=this;if(!n){V.pendingData.push(t),this.isDisconnecting||this.connect();return}if(n.readyState!==WebSocket.OPEN){V.pendingData.push(t);return}n.send(t)}clearWebSocket(){const{webSocket:t}=this;t&&(this.webSocket=null,t.onclose=null,t.onerror=null,t.onmessage=null,t.onopen=null)}cleanup(){V.activeInstances.delete(this.instanceId)}}V.instanceCounter=0;V.activeInstances=new Set;V.pendingData=[];const jr=1e4,vl=6e4;class kl{constructor({session:t,linkAPIUrl:n,listener:r}){this.destroyed=!1,this.lastHeartbeatResponse=0,this.nextReqId=fe(1),this.reconnectAttempts=0,this.isReconnecting=!1,this._connected=!1,this._linked=!1,this.requestResolutions=new Map,this.handleSessionMetadataUpdated=i=>{if(!i)return;new Map([["__destroyed",this.handleDestroyed],["EthereumAddress",this.handleAccountUpdated],["WalletUsername",this.handleWalletUsernameUpdated],["AppVersion",this.handleAppVersionUpdated],["ChainId",o=>i.JsonRpcUrl&&this.handleChainUpdated(o,i.JsonRpcUrl)]]).forEach((o,c)=>{const u=i[c];u!==void 0&&o(u)})},this.handleDestroyed=i=>{var s;i==="1"&&((s=this.listener)===null||s===void 0||s.resetAndReload())},this.handleAccountUpdated=async i=>{var s;try{const o=await this.cipher.decrypt(i);(s=this.listener)===null||s===void 0||s.accountUpdated(o)}catch{}},this.handleMetadataUpdated=async(i,s)=>{var o;try{const c=await this.cipher.decrypt(s);(o=this.listener)===null||o===void 0||o.metadataUpdated(i,c)}catch{}},this.handleWalletUsernameUpdated=async i=>{this.handleMetadataUpdated(yl,i)},this.handleAppVersionUpdated=async i=>{this.handleMetadataUpdated(gl,i)},this.handleChainUpdated=async(i,s)=>{var o;try{const c=await this.cipher.decrypt(i),u=await this.cipher.decrypt(s);(o=this.listener)===null||o===void 0||o.chainUpdated(c,u)}catch{}},this.session=t,this.cipher=new bl(t.secret),this.listener=r,this.linkAPIUrl=n,this.WebSocketClass=WebSocket;const a=this.createWebSocket();this.ws=a,this.http=new wl(n,t.id,t.key),this.setupVisibilityChangeHandler()}createWebSocket(){const t=new V(`${this.linkAPIUrl}/rpc`,this.WebSocketClass);return this.activeWsInstance=t,t.setConnectionStateListener(async n=>{if(t!==this.activeWsInstance)return;let r=!1;switch(n){case oe.DISCONNECTED:this.heartbeatIntervalId&&(clearInterval(this.heartbeatIntervalId),this.heartbeatIntervalId=void 0),this.lastHeartbeatResponse=0,r=!1,this.destroyed||(async()=>{if(this.isReconnecting)return;this.isReconnecting=!0;const i=this.reconnectAttempts===0?0:3e3;await new Promise(s=>setTimeout(s,i)),!this.destroyed&&t===this.activeWsInstance?(this.reconnectAttempts++,"cleanup"in this.ws&&typeof this.ws.cleanup=="function"&&this.ws.cleanup(),this.ws=this.createWebSocket(),this.ws.connect().catch(()=>{Or()}).finally(()=>{this.isReconnecting=!1})):this.isReconnecting=!1})();break;case oe.CONNECTED:this.reconnectAttempts=0;try{r=await this.handleConnected(),this.fetchUnseenEventsAPI().catch(()=>{})}catch{break}this.connected=r,this.updateLastHeartbeat(),this.heartbeatIntervalId&&clearInterval(this.heartbeatIntervalId),this.heartbeatIntervalId=window.setInterval(()=>{this.heartbeat()},jr),setTimeout(()=>{this.heartbeat()},100);break;case oe.CONNECTING:break}n!==oe.CONNECTED&&(this.connected=r)}),t.setIncomingDataListener(n=>{var r;switch(n.type){case"Heartbeat":this.updateLastHeartbeat();return;case"IsLinkedOK":case"Linked":{const a=n.type==="IsLinkedOK"?n.linked:void 0;this.linked=a||n.onlineGuests>0;break}case"GetSessionConfigOK":case"SessionConfigUpdated":{this.handleSessionMetadataUpdated(n.metadata);break}case"Event":{this.handleIncomingEvent(n);break}}n.id!==void 0&&((r=this.requestResolutions.get(n.id))===null||r===void 0||r(n))}),t}setupVisibilityChangeHandler(){this.visibilityChangeHandler=()=>{!document.hidden&&!this.destroyed&&(this.connected?this.heartbeat():this.reconnectWithFreshWebSocket())},this.focusHandler=()=>{!this.destroyed&&!this.connected&&this.reconnectWithFreshWebSocket()},document.addEventListener("visibilitychange",this.visibilityChangeHandler),window.addEventListener("focus",this.focusHandler),window.addEventListener("pageshow",t=>{t.persisted&&this.focusHandler&&this.focusHandler()})}reconnectWithFreshWebSocket(){if(this.destroyed)return;const t=this.ws;this.activeWsInstance=void 0,t.disconnect(),"cleanup"in t&&typeof t.cleanup=="function"&&t.cleanup(),this.ws=this.createWebSocket(),this.ws.connect().catch(()=>{Or()})}connect(){if(this.destroyed)throw new Error("instance is destroyed");this.ws.connect()}async destroy(){this.destroyed||(await this.makeRequest({type:"SetSessionConfig",id:fe(this.nextReqId++),sessionId:this.session.id,metadata:{__destroyed:"1"}},{timeout:1e3}),this.destroyed=!0,this.activeWsInstance=void 0,this.heartbeatIntervalId&&(clearInterval(this.heartbeatIntervalId),this.heartbeatIntervalId=void 0),this.visibilityChangeHandler&&document.removeEventListener("visibilitychange",this.visibilityChangeHandler),this.focusHandler&&window.removeEventListener("focus",this.focusHandler),this.ws.disconnect(),"cleanup"in this.ws&&typeof this.ws.cleanup=="function"&&this.ws.cleanup(),this.listener=void 0)}get connected(){return this._connected}set connected(t){this._connected=t}get linked(){return this._linked}set linked(t){var n,r;this._linked=t,t&&((n=this.onceLinked)===null||n===void 0||n.call(this)),(r=this.listener)===null||r===void 0||r.linkedUpdated(t)}setOnceLinked(t){return new Promise(n=>{this.linked?t().then(n):this.onceLinked=()=>{t().then(n),this.onceLinked=void 0}})}async handleIncomingEvent(t){var n;if(!(t.type!=="Event"||t.event!=="Web3Response"))try{const r=await this.cipher.decrypt(t.data),a=JSON.parse(r);if(a.type!=="WEB3_RESPONSE")return;(n=this.listener)===null||n===void 0||n.handleWeb3ResponseMessage(a.id,a.response)}catch{}}async checkUnseenEvents(){await new Promise(t=>setTimeout(t,250));try{await this.fetchUnseenEventsAPI()}catch(t){console.error("Unable to check for unseen events",t)}}async fetchUnseenEventsAPI(){try{(await this.http.fetchUnseenEvents()).forEach(n=>{this.handleIncomingEvent(n)})}catch{Wd()}}async publishEvent(t,n,r=!1){const a=await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({},n),{origin:location.origin,location:location.href,relaySource:"coinbaseWalletExtension"in window&&window.coinbaseWalletExtension?"injected_sdk":"sdk"}))),i={type:"PublishEvent",id:fe(this.nextReqId++),sessionId:this.session.id,event:t,data:a,callWebhook:r};return this.setOnceLinked(async()=>{const s=await this.makeRequest(i);if(s.type==="Fail")throw new Error(s.error||"failed to publish event");return s.eventId})}sendData(t){this.ws.sendData(JSON.stringify(t))}updateLastHeartbeat(){this.lastHeartbeatResponse=Date.now()}heartbeat(){if(Date.now()-this.lastHeartbeatResponse>jr*2){this.ws.disconnect();return}if(this.connected)try{this.ws.sendData("h")}catch{}}async makeRequest(t,n={timeout:vl}){const r=t.id;this.sendData(t);let a;return Promise.race([new Promise((i,s)=>{a=window.setTimeout(()=>{s(new Error(`request ${r} timed out`))},n.timeout)}),new Promise(i=>{this.requestResolutions.set(r,s=>{clearTimeout(a),i(s),this.requestResolutions.delete(r)})})])}async handleConnected(){return(await this.makeRequest({type:"HostSession",id:fe(this.nextReqId++),sessionId:this.session.id,sessionKey:this.session.key})).type==="Fail"?!1:(this.sendData({type:"IsLinked",id:fe(this.nextReqId++),sessionId:this.session.id}),this.sendData({type:"GetSessionConfig",id:fe(this.nextReqId++),sessionId:this.session.id}),!0)}}class xl{constructor(){this._nextRequestId=0,this.callbacks=new Map}makeRequestId(){this._nextRequestId=(this._nextRequestId+1)%2147483647;const t=this._nextRequestId,n=xa(t.toString(16));return this.callbacks.get(n)&&this.callbacks.delete(n),t}}function Sl(e){return e instanceof Uint8Array||e!=null&&typeof e=="object"&&e.constructor.name==="Uint8Array"}function Kn(e,...t){if(!Sl(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function Mr(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function _l(e,t){Kn(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const $t=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),ie=(e,t)=>e<<32-t|e>>>t;new Uint8Array(new Uint32Array([287454020]).buffer)[0];const El=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Il(e){Kn(e);let t="";for(let n=0;n<e.length;n++)t+=El[e[n]];return t}function Al(e){if(typeof e!="string")throw new Error(`utf8ToBytes expected string, got ${typeof e}`);return new Uint8Array(new TextEncoder().encode(e))}function Vi(e){return typeof e=="string"&&(e=Al(e)),Kn(e),e}class Pl{clone(){return this._cloneInto()}}function Ol(e){const t=r=>e().update(Vi(r)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t}function Cl(e,t,n,r){if(typeof e.setBigUint64=="function")return e.setBigUint64(t,n,r);const a=BigInt(32),i=BigInt(**********),s=Number(n>>a&i),o=Number(n&i),c=r?4:0,u=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+u,o,r)}const Tl=(e,t,n)=>e&t^~e&n,Ll=(e,t,n)=>e&t^e&n^t&n;class jl extends Pl{constructor(t,n,r,a){super(),this.blockLen=t,this.outputLen=n,this.padOffset=r,this.isLE=a,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=$t(this.buffer)}update(t){Mr(this);const{view:n,buffer:r,blockLen:a}=this;t=Vi(t);const i=t.length;for(let s=0;s<i;){const o=Math.min(a-this.pos,i-s);if(o===a){const c=$t(t);for(;a<=i-s;s+=a)this.process(c,s);continue}r.set(t.subarray(s,s+o),this.pos),this.pos+=o,s+=o,this.pos===a&&(this.process(n,0),this.pos=0)}return this.length+=t.length,this.roundClean(),this}digestInto(t){Mr(this),_l(t,this),this.finished=!0;const{buffer:n,view:r,blockLen:a,isLE:i}=this;let{pos:s}=this;n[s++]=128,this.buffer.subarray(s).fill(0),this.padOffset>a-s&&(this.process(r,0),s=0);for(let l=s;l<a;l++)n[l]=0;Cl(r,a-8,BigInt(this.length*8),i),this.process(r,0);const o=$t(t),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,d=this.get();if(u>d.length)throw new Error("_sha2: outputLen bigger than state");for(let l=0;l<u;l++)o.setUint32(4*l,d[l],i)}digest(){const{buffer:t,outputLen:n}=this;this.digestInto(t);const r=t.slice(0,n);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:n,buffer:r,length:a,finished:i,destroyed:s,pos:o}=this;return t.length=a,t.pos=o,t.finished=i,t.destroyed=s,a%n&&t.buffer.set(r),t}}const Ml=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),be=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),we=new Uint32Array(64);class Dl extends jl{constructor(){super(64,32,8,!1),this.A=be[0]|0,this.B=be[1]|0,this.C=be[2]|0,this.D=be[3]|0,this.E=be[4]|0,this.F=be[5]|0,this.G=be[6]|0,this.H=be[7]|0}get(){const{A:t,B:n,C:r,D:a,E:i,F:s,G:o,H:c}=this;return[t,n,r,a,i,s,o,c]}set(t,n,r,a,i,s,o,c){this.A=t|0,this.B=n|0,this.C=r|0,this.D=a|0,this.E=i|0,this.F=s|0,this.G=o|0,this.H=c|0}process(t,n){for(let l=0;l<16;l++,n+=4)we[l]=t.getUint32(n,!1);for(let l=16;l<64;l++){const p=we[l-15],f=we[l-2],m=ie(p,7)^ie(p,18)^p>>>3,y=ie(f,17)^ie(f,19)^f>>>10;we[l]=y+we[l-7]+m+we[l-16]|0}let{A:r,B:a,C:i,D:s,E:o,F:c,G:u,H:d}=this;for(let l=0;l<64;l++){const p=ie(o,6)^ie(o,11)^ie(o,25),f=d+p+Tl(o,c,u)+Ml[l]+we[l]|0,y=(ie(r,2)^ie(r,13)^ie(r,22))+Ll(r,a,i)|0;d=u,u=c,c=o,o=s+f|0,s=i,i=a,a=r,r=f+y|0}r=r+this.A|0,a=a+this.B|0,i=i+this.C|0,s=s+this.D|0,o=o+this.E|0,c=c+this.F|0,u=u+this.G|0,d=d+this.H|0,this.set(r,a,i,s,o,c,u,d)}roundClean(){we.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Nl=Ol(()=>new Dl),Dr="session:id",Nr="session:secret",Rr="session:linked";class Be{constructor(t,n,r,a=!1){this.storage=t,this.id=n,this.secret=r,this.key=Il(Nl(`${n}, ${r} WalletLink`)),this._linked=!!a}static create(t){const n=Ee(16),r=Ee(32);return new Be(t,n,r).save()}static load(t){const n=t.getItem(Dr),r=t.getItem(Rr),a=t.getItem(Nr);return n&&a?new Be(t,n,a,r==="1"):null}get linked(){return this._linked}set linked(t){this._linked=t,this.persistLinked()}save(){return this.storage.setItem(Dr,this.id),this.storage.setItem(Nr,this.secret),this.persistLinked(),this}persistLinked(){this.storage.setItem(Rr,this._linked?"1":"0")}}const Rl=".-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}";class Bl{constructor(){this.root=null,this.darkMode=Ia()}attach(){const t=document.documentElement;this.root=document.createElement("div"),this.root.className="-cbwsdk-css-reset",t.appendChild(this.root),_a()}present(t){this.render(t)}clear(){this.render(null)}render(t){this.root&&(Wt(null,this.root),t&&Wt(M(Ul,Object.assign({},t,{onDismiss:()=>{this.clear()},darkMode:this.darkMode})),this.root))}}const Ul=({title:e,buttonText:t,darkMode:n,onButtonClick:r,onDismiss:a})=>{const i=n?"dark":"light";return M(Pa,{darkMode:n},M("div",{class:"-cbwsdk-redirect-dialog"},M("style",null,Rl),M("div",{class:"-cbwsdk-redirect-dialog-backdrop",onClick:a}),M("div",{class:Xe("-cbwsdk-redirect-dialog-box",i)},M("p",null,e),M("button",{onClick:r},t))))};class Br{constructor(){this.attached=!1,this.redirectDialog=new Bl}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");this.redirectDialog.attach(),this.attached=!0}redirectToCoinbaseWallet(t){const n=new URL(cc);n.searchParams.append("redirect_url",hc().href),t&&n.searchParams.append("wl_url",t);const r=document.createElement("a");r.target="cbw-opener",r.href=n.href,r.rel="noreferrer noopener",r.click()}openCoinbaseWalletDeeplink(t){this.redirectToCoinbaseWallet(t),setTimeout(()=>{this.redirectDialog.present({title:"Redirecting to Coinbase Wallet...",buttonText:"Open",onButtonClick:()=>{this.redirectToCoinbaseWallet(t)}})},99)}showConnecting(t){return()=>{this.redirectDialog.clear()}}}class pe{constructor(t){this.chainCallbackParams={chainId:"",jsonRpcUrl:""},this.isMobileWeb=mc(),this.linkedUpdated=i=>{this.isLinked=i;const s=this.storage.getItem(Pn);if(i&&(this._session.linked=i),this.isUnlinkedErrorState=!1,s){const o=s.split(" "),c=this.storage.getItem("IsStandaloneSigning")==="true";o[0]!==""&&!i&&this._session.linked&&!c&&(this.isUnlinkedErrorState=!0)}},this.metadataUpdated=(i,s)=>{this.storage.setItem(i,s)},this.chainUpdated=(i,s)=>{this.chainCallbackParams.chainId===i&&this.chainCallbackParams.jsonRpcUrl===s||(this.chainCallbackParams={chainId:i,jsonRpcUrl:s},this.chainCallback&&this.chainCallback(s,Number.parseInt(i,10)))},this.accountUpdated=i=>{this.accountsCallback&&this.accountsCallback([i]),pe.accountRequestCallbackIds.size>0&&(Array.from(pe.accountRequestCallbackIds.values()).forEach(s=>{this.invokeCallback(s,{method:"requestEthereumAccounts",result:[i]})}),pe.accountRequestCallbackIds.clear())},this.resetAndReload=this.resetAndReload.bind(this),this.linkAPIUrl=t.linkAPIUrl,this.storage=t.storage,this.metadata=t.metadata,this.accountsCallback=t.accountsCallback,this.chainCallback=t.chainCallback;const{session:n,ui:r,connection:a}=this.subscribe();this._session=n,this.connection=a,this.relayEventManager=new xl,this.ui=r,this.ui.attach()}subscribe(){const t=Be.load(this.storage)||Be.create(this.storage),{linkAPIUrl:n}=this,r=new kl({session:t,linkAPIUrl:n,listener:this}),a=this.isMobileWeb?new Br:new vc;return r.connect(),{session:t,ui:a,connection:r}}resetAndReload(){this.connection.destroy().then(()=>{const t=Be.load(this.storage);(t==null?void 0:t.id)===this._session.id&&Oe.clearAll(),document.location.reload()}).catch(t=>{})}signEthereumTransaction(t){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:re(t.weiValue),data:Ye(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?re(t.gasPriceInWei):null,maxFeePerGas:t.gasPriceInWei?re(t.gasPriceInWei):null,maxPriorityFeePerGas:t.gasPriceInWei?re(t.gasPriceInWei):null,gasLimit:t.gasLimit?re(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!1}})}signAndSubmitEthereumTransaction(t){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:re(t.weiValue),data:Ye(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?re(t.gasPriceInWei):null,maxFeePerGas:t.maxFeePerGas?re(t.maxFeePerGas):null,maxPriorityFeePerGas:t.maxPriorityFeePerGas?re(t.maxPriorityFeePerGas):null,gasLimit:t.gasLimit?re(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!0}})}submitEthereumTransaction(t,n){return this.sendRequest({method:"submitEthereumTransaction",params:{signedTransaction:Ye(t,!0),chainId:n}})}getWalletLinkSession(){return this._session}sendRequest(t){let n=null;const r=Ee(8),a=i=>{this.publishWeb3RequestCanceledEvent(r),this.handleErrorResponse(r,t.method,i),n==null||n()};return new Promise((i,s)=>{n=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:a,onResetConnection:this.resetAndReload}),this.relayEventManager.callbacks.set(r,o=>{if(n==null||n(),$(o))return s(new Error(o.errorMessage));i(o)}),this.publishWeb3RequestEvent(r,t)})}publishWeb3RequestEvent(t,n){const r={type:"WEB3_REQUEST",id:t,request:n};this.publishEvent("Web3Request",r,!0).then(a=>{}).catch(a=>{this.handleWeb3ResponseMessage(r.id,{method:n.method,errorMessage:a.message})}),this.isMobileWeb&&this.openCoinbaseWalletDeeplink(n.method)}openCoinbaseWalletDeeplink(t){if(this.ui instanceof Br)switch(t){case"requestEthereumAccounts":case"switchEthereumChain":return;default:window.addEventListener("blur",()=>{window.addEventListener("focus",()=>{this.connection.checkUnseenEvents()},{once:!0})},{once:!0}),this.ui.openCoinbaseWalletDeeplink();break}}publishWeb3RequestCanceledEvent(t){const n={type:"WEB3_REQUEST_CANCELED",id:t};this.publishEvent("Web3RequestCanceled",n,!1).then()}publishEvent(t,n,r){return this.connection.publishEvent(t,n,r)}handleWeb3ResponseMessage(t,n){if(n.method==="requestEthereumAccounts"){pe.accountRequestCallbackIds.forEach(r=>this.invokeCallback(r,n)),pe.accountRequestCallbackIds.clear();return}this.invokeCallback(t,n)}handleErrorResponse(t,n,r){var a;const i=(a=r==null?void 0:r.message)!==null&&a!==void 0?a:"Unspecified error message.";this.handleWeb3ResponseMessage(t,{method:n,errorMessage:i})}invokeCallback(t,n){const r=this.relayEventManager.callbacks.get(t);r&&(r(n),this.relayEventManager.callbacks.delete(t))}requestEthereumAccounts(){const{appName:t,appLogoUrl:n}=this.metadata,r={method:"requestEthereumAccounts",params:{appName:t,appLogoUrl:n}},a=Ee(8);return new Promise((i,s)=>{this.relayEventManager.callbacks.set(a,o=>{if($(o))return s(new Error(o.errorMessage));i(o)}),pe.accountRequestCallbackIds.add(a),this.publishWeb3RequestEvent(a,r)})}watchAsset(t,n,r,a,i,s){const o={method:"watchAsset",params:{type:t,options:{address:n,symbol:r,decimals:a,image:i},chainId:s}};let c=null;const u=Ee(8),d=l=>{this.publishWeb3RequestCanceledEvent(u),this.handleErrorResponse(u,o.method,l),c==null||c()};return c=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:d,onResetConnection:this.resetAndReload}),new Promise((l,p)=>{this.relayEventManager.callbacks.set(u,f=>{if(c==null||c(),$(f))return p(new Error(f.errorMessage));l(f)}),this.publishWeb3RequestEvent(u,o)})}addEthereumChain(t,n,r,a,i,s){const o={method:"addEthereumChain",params:{chainId:t,rpcUrls:n,blockExplorerUrls:a,chainName:i,iconUrls:r,nativeCurrency:s}};let c=null;const u=Ee(8),d=l=>{this.publishWeb3RequestCanceledEvent(u),this.handleErrorResponse(u,o.method,l),c==null||c()};return c=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:d,onResetConnection:this.resetAndReload}),new Promise((l,p)=>{this.relayEventManager.callbacks.set(u,f=>{if(c==null||c(),$(f))return p(new Error(f.errorMessage));l(f)}),this.publishWeb3RequestEvent(u,o)})}switchEthereumChain(t,n){const r={method:"switchEthereumChain",params:Object.assign({chainId:t},{address:n})};let a=null;const i=Ee(8),s=o=>{this.publishWeb3RequestCanceledEvent(i),this.handleErrorResponse(i,r.method,o),a==null||a()};return a=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:s,onResetConnection:this.resetAndReload}),new Promise((o,c)=>{this.relayEventManager.callbacks.set(i,u=>{if(a==null||a(),$(u)&&u.errorCode)return c(b.provider.custom({code:u.errorCode,message:"Unrecognized chain ID. Try adding the chain using addEthereumChain first."}));if($(u))return c(new Error(u.errorMessage));o(u)}),this.publishWeb3RequestEvent(i,r)})}}pe.accountRequestCallbackIds=new Set;const Ur="DefaultChainId",Gr="DefaultJsonRpcUrl";class Ki{constructor(t){this._relay=null,this._addresses=[],this.metadata=t.metadata,this._storage=new Oe("walletlink",lr),this.callback=t.callback||null;const n=this._storage.getItem(Pn);if(n){const r=n.split(" ");r[0]!==""&&(this._addresses=r.map(a=>me(a)))}this.initializeRelay()}getSession(){const t=this.initializeRelay(),{id:n,secret:r}=t.getWalletLinkSession();return{id:n,secret:r}}async handshake(t){const n="eth_requestAccounts",r=ee.get(t);Ud({method:n,correlationId:r});try{await this._eth_requestAccounts(),Fd({method:n,correlationId:r})}catch(a){throw Gd({method:n,correlationId:r,errorMessage:Ie(a)}),a}}get selectedAddress(){return this._addresses[0]||void 0}get jsonRpcUrl(){var t;return(t=this._storage.getItem(Gr))!==null&&t!==void 0?t:void 0}set jsonRpcUrl(t){this._storage.setItem(Gr,t)}updateProviderInfo(t,n){var r;this.jsonRpcUrl=t;const a=this.getChainId();this._storage.setItem(Ur,n.toString(10)),Qe(n)!==a&&((r=this.callback)===null||r===void 0||r.call(this,"chainChanged",je(n)))}async watchAsset(t){const n=Array.isArray(t)?t[0]:t;if(!n.type)throw b.rpc.invalidParams("Type is required");if((n==null?void 0:n.type)!=="ERC20")throw b.rpc.invalidParams(`Asset of type '${n.type}' is not supported`);if(!(n!=null&&n.options))throw b.rpc.invalidParams("Options are required");if(!(n!=null&&n.options.address))throw b.rpc.invalidParams("Address is required");const r=this.getChainId(),{address:a,symbol:i,image:s,decimals:o}=n.options,u=await this.initializeRelay().watchAsset(n.type,a,i,o,s,r==null?void 0:r.toString());return $(u)?!1:!!u.result}async addEthereumChain(t){var n,r;const a=t[0];if(((n=a.rpcUrls)===null||n===void 0?void 0:n.length)===0)throw b.rpc.invalidParams("please pass in at least 1 rpcUrl");if(!a.chainName||a.chainName.trim()==="")throw b.rpc.invalidParams("chainName is a required field");if(!a.nativeCurrency)throw b.rpc.invalidParams("nativeCurrency is a required field");const i=Number.parseInt(a.chainId,16);if(i===this.getChainId())return!1;const s=this.initializeRelay(),{rpcUrls:o=[],blockExplorerUrls:c=[],chainName:u,iconUrls:d=[],nativeCurrency:l}=a,p=await s.addEthereumChain(i.toString(),o,d,c,u,l);if($(p))return!1;if(((r=p.result)===null||r===void 0?void 0:r.isApproved)===!0)return this.updateProviderInfo(o[0],i),null;throw b.rpc.internal("unable to add ethereum chain")}async switchEthereumChain(t){const n=t[0],r=Number.parseInt(n.chainId,16),i=await this.initializeRelay().switchEthereumChain(r.toString(10),this.selectedAddress||void 0);if($(i))throw i;const s=i.result;return s.isApproved&&s.rpcUrl.length>0&&this.updateProviderInfo(s.rpcUrl,r),null}async cleanup(){this.callback=null,this._relay&&this._relay.resetAndReload(),this._storage.clear()}_setAddresses(t,n){var r;if(!Array.isArray(t))throw new Error("addresses is not an array");const a=t.map(i=>me(i));JSON.stringify(a)!==JSON.stringify(this._addresses)&&(this._addresses=a,(r=this.callback)===null||r===void 0||r.call(this,"accountsChanged",a),this._storage.setItem(Pn,a.join(" ")))}async request(t){const n=ee.get(t);Hd({method:t.method,correlationId:n});try{const r=await this._request(t);return $d({method:t.method,correlationId:n}),r}catch(r){throw zd({method:t.method,correlationId:n,errorMessage:Ie(r)}),r}}async _request(t){const n=t.params||[];switch(t.method){case"eth_accounts":return[...this._addresses];case"eth_coinbase":return this.selectedAddress||null;case"net_version":return this.getChainId().toString(10);case"eth_chainId":return je(this.getChainId());case"eth_requestAccounts":return this._eth_requestAccounts();case"eth_ecRecover":case"personal_ecRecover":return this.ecRecover(t);case"personal_sign":return this.personalSign(t);case"eth_signTransaction":return this._eth_signTransaction(n);case"eth_sendRawTransaction":return this._eth_sendRawTransaction(n);case"eth_sendTransaction":return this._eth_sendTransaction(n);case"eth_signTypedData_v1":case"eth_signTypedData_v3":case"eth_signTypedData_v4":case"eth_signTypedData":return this.signTypedData(t);case"wallet_addEthereumChain":return this.addEthereumChain(n);case"wallet_switchEthereumChain":return this.switchEthereumChain(n);case"wallet_watchAsset":return this.watchAsset(n);default:if(!this.jsonRpcUrl)throw b.rpc.internal("No RPC URL set for chain");return Ze(t,this.jsonRpcUrl)}}_ensureKnownAddress(t){const n=me(t);if(!this._addresses.map(a=>me(a)).includes(n))throw new Error("Unknown Ethereum address")}_prepareTransactionParams(t){const n=t.from?me(t.from):this.selectedAddress;if(!n)throw new Error("Ethereum address is unavailable");this._ensureKnownAddress(n);const r=t.to?me(t.to):null,a=t.value!=null?$e(t.value):BigInt(0),i=t.data?_n(t.data):Buffer.alloc(0),s=t.nonce!=null?Qe(t.nonce):null,o=t.gasPrice!=null?$e(t.gasPrice):null,c=t.maxFeePerGas!=null?$e(t.maxFeePerGas):null,u=t.maxPriorityFeePerGas!=null?$e(t.maxPriorityFeePerGas):null,d=t.gas!=null?$e(t.gas):null,l=t.chainId?Qe(t.chainId):this.getChainId();return{fromAddress:n,toAddress:r,weiValue:a,data:i,nonce:s,gasPriceInWei:o,maxFeePerGas:c,maxPriorityFeePerGas:u,gasLimit:d,chainId:l}}async ecRecover(t){const{method:n,params:r}=t;if(!Array.isArray(r))throw b.rpc.invalidParams();const i=await this.initializeRelay().sendRequest({method:"ethereumAddressFromSignedMessage",params:{message:Bt(r[0]),signature:Bt(r[1]),addPrefix:n==="personal_ecRecover"}});if($(i))throw i;return i.result}getChainId(){var t;return Number.parseInt((t=this._storage.getItem(Ur))!==null&&t!==void 0?t:"1",10)}async _eth_requestAccounts(){var t,n;if(this._addresses.length>0)return(t=this.callback)===null||t===void 0||t.call(this,"connect",{chainId:je(this.getChainId())}),this._addresses;const a=await this.initializeRelay().requestEthereumAccounts();if($(a))throw a;if(!a.result)throw new Error("accounts received is empty");return this._setAddresses(a.result),(n=this.callback)===null||n===void 0||n.call(this,"connect",{chainId:je(this.getChainId())}),this._addresses}async personalSign({params:t}){if(!Array.isArray(t))throw b.rpc.invalidParams();const n=t[1],r=t[0];this._ensureKnownAddress(n);const i=await this.initializeRelay().sendRequest({method:"signEthereumMessage",params:{address:me(n),message:Bt(r),addPrefix:!0,typedDataJson:null}});if($(i))throw i;return i.result}async _eth_signTransaction(t){const n=this._prepareTransactionParams(t[0]||{}),a=await this.initializeRelay().signEthereumTransaction(n);if($(a))throw a;return a.result}async _eth_sendRawTransaction(t){const n=_n(t[0]),a=await this.initializeRelay().submitEthereumTransaction(n,this.getChainId());if($(a))throw a;return a.result}async _eth_sendTransaction(t){const n=this._prepareTransactionParams(t[0]||{}),a=await this.initializeRelay().signAndSubmitEthereumTransaction(n);if($(a))throw a;return a.result}async signTypedData(t){const{method:n,params:r}=t;if(!Array.isArray(r))throw b.rpc.invalidParams();const a=u=>{const d={eth_signTypedData_v1:yt.hashForSignTypedDataLegacy,eth_signTypedData_v3:yt.hashForSignTypedData_v3,eth_signTypedData_v4:yt.hashForSignTypedData_v4,eth_signTypedData:yt.hashForSignTypedData_v4};return Ye(d[n]({data:Yo(u)}),!0)},i=r[n==="eth_signTypedData_v1"?1:0],s=r[n==="eth_signTypedData_v1"?0:1];this._ensureKnownAddress(i);const c=await this.initializeRelay().sendRequest({method:"signEthereumMessage",params:{address:me(i),message:a(s),typedDataJson:JSON.stringify(s,null,2),addPrefix:!1}});if($(c))throw c;return c.result}initializeRelay(){return this._relay||(this._relay=new pe({linkAPIUrl:lr,storage:this._storage,metadata:this.metadata,accountsCallback:this._setAddresses.bind(this),chainCallback:this.updateProviderInfo.bind(this)})),this._relay}}const Ji="SignerType",Yi=new Oe("CBWSDK","SignerConfigurator");function Gl(){return Yi.getItem(Ji)}function Fl(e){Yi.setItem(Ji,e)}function Fr(e){if(e)return e instanceof ri?"scw":"walletlink"}async function Hl(e){const{communicator:t,metadata:n,handshakeRequest:r,callback:a}=e;$l(t,n,a,r).catch(()=>{});const i={id:crypto.randomUUID(),event:"selectSignerType",data:Object.assign(Object.assign({},e.preference),{handshakeRequest:r})},{data:s}=await t.postRequestAndWaitForResponse(i);return s}function zl(e){const{signerType:t,metadata:n,communicator:r,callback:a}=e;switch(t){case"scw":return new ri({metadata:n,callback:a,communicator:r});case"walletlink":return new Ki({metadata:n,callback:a})}}async function $l(e,t,n,r){await e.onMessage(({event:i})=>i==="WalletLinkSessionRequest");const a=new Ki({metadata:t,callback:n});e.postMessage({event:"WalletLinkUpdate",data:{session:a.getSession()}}),await a.handshake(r),e.postMessage({event:"WalletLinkUpdate",data:{connected:!0}})}var Wl=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};class ql extends Tc{constructor(t){var{metadata:n}=t,r=t.preference,{keysUrl:a}=r,i=Wl(r,["keysUrl"]);super(),this.signer=null,this.isCoinbaseWallet=!0,this.metadata=n,this.preference=i,this.communicator=new Ic({url:a,metadata:n,preference:i});const s=Gl();s&&(this.signer=this.initSigner(s),Lc({signerType:s}))}async request(t){const n=crypto.randomUUID();ee.set(t,n),jc({method:t.method,correlationId:n});try{const r=await this._request(t);return Dc({method:t.method,signerType:Fr(this.signer),correlationId:n}),r}catch(r){throw Mc({method:t.method,correlationId:n,signerType:Fr(this.signer),errorMessage:r instanceof Error?r.message:""}),r}finally{ee.delete(t)}}async _request(t){try{if(ic(t),!this.signer)switch(t.method){case"eth_requestAccounts":{let r;const a=w.subAccountsConfig.get();a!=null&&a.enableAutoSubAccounts?r="scw":r=await this.requestSignerSelection(t);const i=this.initSigner(r);r==="scw"&&(a!=null&&a.enableAutoSubAccounts)?(await i.handshake({method:"handshake"}),await i.request(t)):await i.handshake(t),this.signer=i,Fl(r);break}case"wallet_connect":{const r=this.initSigner("scw");await r.handshake({method:"handshake"});const a=await r.request(t);return this.signer=r,a}case"wallet_sendCalls":case"wallet_sign":{const r=this.initSigner("scw");await r.handshake({method:"handshake"});const a=await r.request(t);return await r.cleanup(),a}case"wallet_getCallsStatus":return await Ze(t,Sa);case"net_version":return 1;case"eth_chainId":return je(1);default:throw b.provider.unauthorized("Must call 'eth_requestAccounts' before other methods")}return await this.signer.request(t)}catch(n){const{code:r}=n;return r===G.provider.unauthorized&&this.disconnect(),Promise.reject(Ac(n))}}async enable(){return console.warn('.enable() has been deprecated. Please use .request({ method: "eth_requestAccounts" }) instead.'),Nc(),await this.request({method:"eth_requestAccounts"})}async disconnect(){var t;await((t=this.signer)===null||t===void 0?void 0:t.cleanup()),this.signer=null,Oe.clearAll(),ee.clear(),this.emit("disconnect",b.provider.disconnected("User initiated disconnection"))}async requestSignerSelection(t){Rc();const n=await Hl({communicator:this.communicator,preference:this.preference,metadata:this.metadata,handshakeRequest:t,callback:this.emit.bind(this)});return Bc(n),n}initSigner(t){return zl({signerType:t,metadata:this.metadata,communicator:this.communicator,callback:this.emit.bind(this)})}}function Vl(e){var t;const n={metadata:e.metadata,preference:e.preference};return(t=ac(n))!==null&&t!==void 0?t:new ql(n)}const Kl={options:"all"};function af(e){var t,n,r,a;const i={metadata:{appName:e.appName||"Dapp",appLogoUrl:e.appLogoUrl||"",appChainIds:e.appChainIds||[]},preference:Object.assign(Kl,(t=e.preference)!==null&&t!==void 0?t:{}),paymasterUrls:e.paymasterUrls};!((n=e.subAccounts)===null||n===void 0)&&n.toOwnerAccount&&dr(e.subAccounts.toOwnerAccount),w.subAccountsConfig.set({toOwnerAccount:(r=e.subAccounts)===null||r===void 0?void 0:r.toOwnerAccount,enableAutoSubAccounts:(a=e.subAccounts)===null||a===void 0?void 0:a.enableAutoSubAccounts}),w.config.set(i),w.persist.rehydrate(),ec(),i.preference.telemetry!==!1&&Ho(),sc(i.preference);let s=null;const o={getProvider(){return s||(s=Vl(i)),s.sdk=o,s},subAccount:{async create(c){var u,d;const l=w.getState();return J((u=l.subAccount)===null||u===void 0?void 0:u.address,new Error("subaccount already exists")),await((d=o.getProvider())===null||d===void 0?void 0:d.request({method:"wallet_addSubAccount",params:[{version:"1",account:c}]}))},async get(){var c,u;const d=w.subAccounts.get();if(d!=null&&d.address)return d;const p=(u=(await((c=o.getProvider())===null||c===void 0?void 0:c.request({method:"wallet_connect",params:[{version:"1",capabilities:{}}]}))).accounts[0].capabilities)===null||u===void 0?void 0:u.subAccounts;return Array.isArray(p)?p[0]:null},async addOwner({address:c,publicKey:u,chainId:d}){var l,p;const f=w.subAccounts.get(),m=w.account.get();J(m,new Error("account does not exist")),J(f==null?void 0:f.address,new Error("subaccount does not exist"));const y=[];if(u){const[h,v]=Xr([{type:"bytes32"},{type:"bytes32"}],u);y.push({to:f.address,data:he({abi:ne,functionName:"addOwnerPublicKey",args:[h,v]}),value:De(0)})}return c&&y.push({to:f.address,data:he({abi:ne,functionName:"addOwnerAddress",args:[c]}),value:De(0)}),await((l=o.getProvider())===null||l===void 0?void 0:l.request({method:"wallet_sendCalls",params:[{calls:y,chainId:De(d),from:(p=m.accounts)===null||p===void 0?void 0:p[0],version:"1"}]}))},setToOwnerAccount(c){dr(c),w.subAccountsConfig.set({toOwnerAccount:c})}}};return o}export{af as createCoinbaseWalletSDK,In as getCryptoKeyAccount};
//# sourceMappingURL=index-DB754suD.js.map
