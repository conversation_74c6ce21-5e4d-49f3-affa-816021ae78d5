{"version": 3, "file": "w3m-modal-CGM7s3iG.js", "sources": ["../../node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TooltipController.js", "../../node_modules/@reown/appkit-controllers/dist/esm/src/utils/ModalUtil.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/index.js"], "sourcesContent": ["import { proxy, subscribe as sub } from 'valtio/vanilla';\nimport { subscribe<PERSON>ey as subKey } from 'valtio/vanilla/utils';\nimport { withErrorBoundary } from '../utils/withErrorBoundary.js';\n// -- State --------------------------------------------- //\nconst state = proxy({\n    message: '',\n    open: false,\n    triggerRect: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n    },\n    variant: 'shade'\n});\n// -- Controller ---------------------------------------- //\nconst controller = {\n    state,\n    subscribe(callback) {\n        return sub(state, () => callback(state));\n    },\n    subscribeKey(key, callback) {\n        return subKey(state, key, callback);\n    },\n    showTooltip({ message, triggerRect, variant }) {\n        state.open = true;\n        state.message = message;\n        state.triggerRect = triggerRect;\n        state.variant = variant;\n    },\n    hide() {\n        state.open = false;\n        state.message = '';\n        state.triggerRect = {\n            width: 0,\n            height: 0,\n            top: 0,\n            left: 0\n        };\n    }\n};\n// Export the controller wrapped with our error boundary\nexport const TooltipController = withErrorBoundary(controller);\n//# sourceMappingURL=TooltipController.js.map", "import { ModalController } from '../controllers/ModalController.js';\nimport { RouterController } from '../controllers/RouterController.js';\nimport { SIWXUtil } from './SIWXUtil.js';\nexport const ModalUtil = {\n    isUnsupportedChainView() {\n        return (RouterController.state.view === 'UnsupportedChain' ||\n            (RouterController.state.view === 'SwitchNetwork' &&\n                RouterController.state.history.includes('UnsupportedChain')));\n    },\n    async safeClose() {\n        if (this.isUnsupportedChainView()) {\n            ModalController.shake();\n            return;\n        }\n        const isSIWXCloseDisabled = await SIWXUtil.isSIWXCloseDisabled();\n        if (isSIWXCloseDisabled) {\n            ModalController.shake();\n            return;\n        }\n        ModalController.close();\n    }\n};\n//# sourceMappingURL=ModalUtil.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    border-radius: clamp(0px, var(--wui-border-radius-l), 44px);\n    box-shadow: 0 0 0 1px var(--wui-color-gray-glass-005);\n    background-color: var(--wui-color-modal-bg);\n    overflow: hidden;\n  }\n\n  :host([data-embedded='true']) {\n    box-shadow:\n      0 0 0 1px var(--wui-color-gray-glass-005),\n      0px 4px 12px 4px var(--w3m-card-embedded-shadow-color);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiCard = class WuiCard extends LitElement {\n    render() {\n        return html `<slot></slot>`;\n    }\n};\nWuiCard.styles = [resetStyles, styles];\nWuiCard = __decorate([\n    customElement('wui-card')\n], WuiCard);\nexport { WuiCard };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: var(--wui-spacing-s);\n    border-radius: var(--wui-border-radius-s);\n    border: 1px solid var(--wui-color-dark-glass-100);\n    box-sizing: border-box;\n    background-color: var(--wui-color-bg-325);\n    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.25);\n  }\n\n  wui-flex {\n    width: 100%;\n  }\n\n  wui-text {\n    word-break: break-word;\n    flex: 1;\n  }\n\n  .close {\n    cursor: pointer;\n  }\n\n  .icon-box {\n    height: 40px;\n    width: 40px;\n    border-radius: var(--wui-border-radius-3xs);\n    background-color: var(--local-icon-bg-value);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { AlertController } from '@reown/appkit-controllers';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-text/index.js';\nimport '../../layout/wui-flex/index.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiAlertBar = class WuiAlertBar extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.message = '';\n        this.backgroundColor = 'accent-100';\n        this.iconColor = 'accent-100';\n        this.icon = 'info';\n    }\n    render() {\n        this.style.cssText = `\n      --local-icon-bg-value: var(--wui-color-${this.backgroundColor});\n   `;\n        return html `\n      <wui-flex flexDirection=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n        <wui-flex columnGap=\"xs\" flexDirection=\"row\" alignItems=\"center\">\n          <wui-flex\n            flexDirection=\"row\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            class=\"icon-box\"\n          >\n            <wui-icon color=${this.iconColor} size=\"md\" name=${this.icon}></wui-icon>\n          </wui-flex>\n          <wui-text variant=\"small-500\" color=\"bg-350\" data-testid=\"wui-alertbar-text\"\n            >${this.message}</wui-text\n          >\n        </wui-flex>\n        <wui-icon\n          class=\"close\"\n          color=\"bg-350\"\n          size=\"sm\"\n          name=\"close\"\n          @click=${this.onClose}\n        ></wui-icon>\n      </wui-flex>\n    `;\n    }\n    onClose() {\n        AlertController.close();\n    }\n};\nWuiAlertBar.styles = [resetStyles, styles];\n__decorate([\n    property()\n], WuiAlertBar.prototype, \"message\", void 0);\n__decorate([\n    property()\n], WuiAlertBar.prototype, \"backgroundColor\", void 0);\n__decorate([\n    property()\n], WuiAlertBar.prototype, \"iconColor\", void 0);\n__decorate([\n    property()\n], WuiAlertBar.prototype, \"icon\", void 0);\nWuiAlertBar = __decorate([\n    customElement('wui-alertbar')\n], WuiAlertBar);\nexport { WuiAlertBar };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    position: absolute;\n    top: var(--wui-spacing-s);\n    left: var(--wui-spacing-l);\n    right: var(--wui-spacing-l);\n    opacity: 0;\n    pointer-events: none;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { AlertController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-alertbar';\nimport styles from './styles.js';\nexport const presets = {\n    info: {\n        backgroundColor: 'fg-350',\n        iconColor: 'fg-325',\n        icon: 'info'\n    },\n    success: {\n        backgroundColor: 'success-glass-reown-020',\n        iconColor: 'success-125',\n        icon: 'checkmark'\n    },\n    warning: {\n        backgroundColor: 'warning-glass-reown-020',\n        iconColor: 'warning-100',\n        icon: 'warningCircle'\n    },\n    error: {\n        backgroundColor: 'error-glass-reown-020',\n        iconColor: 'error-125',\n        icon: 'exclamationTriangle'\n    }\n};\nlet W3mAlertBar = class W3mAlertBar extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.open = AlertController.state.open;\n        this.onOpen(true);\n        this.unsubscribe.push(AlertController.subscribeKey('open', val => {\n            this.open = val;\n            this.onOpen(false);\n        }));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const { message, variant } = AlertController.state;\n        const preset = presets[variant];\n        return html `\n      <wui-alertbar\n        message=${message}\n        backgroundColor=${preset?.backgroundColor}\n        iconColor=${preset?.iconColor}\n        icon=${preset?.icon}\n      ></wui-alertbar>\n    `;\n    }\n    onOpen(isMounted) {\n        if (this.open) {\n            this.animate([\n                { opacity: 0, transform: 'scale(0.85)' },\n                { opacity: 1, transform: 'scale(1)' }\n            ], {\n                duration: 150,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n            this.style.cssText = `pointer-events: auto`;\n        }\n        else if (!isMounted) {\n            this.animate([\n                { opacity: 1, transform: 'scale(1)' },\n                { opacity: 0, transform: 'scale(0.85)' }\n            ], {\n                duration: 150,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n            this.style.cssText = `pointer-events: none`;\n        }\n    }\n};\nW3mAlertBar.styles = styles;\n__decorate([\n    state()\n], W3mAlertBar.prototype, \"open\", void 0);\nW3mAlertBar = __decorate([\n    customElement('w3m-alertbar')\n], W3mAlertBar);\nexport { W3mAlertBar };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    border-radius: var(--local-border-radius);\n    color: var(--wui-color-fg-100);\n    padding: var(--local-padding);\n  }\n\n  @media (max-width: 700px) {\n    button {\n      padding: var(--wui-spacing-s);\n    }\n  }\n\n  button > wui-icon {\n    pointer-events: none;\n  }\n\n  button:disabled > wui-icon {\n    color: var(--wui-color-bg-300) !important;\n  }\n\n  button:disabled {\n    background-color: transparent;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport { colorStyles, elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiIconLink = class WuiIconLink extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.size = 'md';\n        this.disabled = false;\n        this.icon = 'copy';\n        this.iconColor = 'inherit';\n    }\n    render() {\n        const borderRadius = this.size === 'lg' ? '--wui-border-radius-xs' : '--wui-border-radius-xxs';\n        const padding = this.size === 'lg' ? '--wui-spacing-1xs' : '--wui-spacing-2xs';\n        this.style.cssText = `\n    --local-border-radius: var(${borderRadius});\n    --local-padding: var(${padding});\n`;\n        return html `\n      <button ?disabled=${this.disabled}>\n        <wui-icon color=${this.iconColor} size=${this.size} name=${this.icon}></wui-icon>\n      </button>\n    `;\n    }\n};\nWuiIconLink.styles = [resetStyles, elementStyles, colorStyles, styles];\n__decorate([\n    property()\n], WuiIconLink.prototype, \"size\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiIconLink.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiIconLink.prototype, \"icon\", void 0);\n__decorate([\n    property()\n], WuiIconLink.prototype, \"iconColor\", void 0);\nWuiIconLink = __decorate([\n    customElement('wui-icon-link')\n], WuiIconLink);\nexport { WuiIconLink };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    display: block;\n    display: flex;\n    align-items: center;\n    padding: var(--wui-spacing-xxs);\n    gap: var(--wui-spacing-xxs);\n    transition: all var(--wui-ease-out-power-1) var(--wui-duration-md);\n    border-radius: var(--wui-border-radius-xxs);\n  }\n\n  wui-image {\n    border-radius: 100%;\n    width: var(--wui-spacing-xl);\n    height: var(--wui-spacing-xl);\n  }\n\n  wui-icon-box {\n    width: var(--wui-spacing-xl);\n    height: var(--wui-spacing-xl);\n  }\n\n  button:hover {\n    background-color: var(--wui-color-gray-glass-002);\n  }\n\n  button:active {\n    background-color: var(--wui-color-gray-glass-005);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-image/index.js';\nimport '../../composites/wui-icon-box/index.js';\nimport { colorStyles, elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiSelect = class WuiSelect extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.imageSrc = '';\n    }\n    render() {\n        return html `<button>\n      ${this.imageTemplate()}\n      <wui-icon size=\"xs\" color=\"fg-200\" name=\"chevronBottom\"></wui-icon>\n    </button>`;\n    }\n    imageTemplate() {\n        if (this.imageSrc) {\n            return html `<wui-image src=${this.imageSrc} alt=\"select visual\"></wui-image>`;\n        }\n        return html `<wui-icon-box\n      size=\"xxs\"\n      iconColor=\"fg-200\"\n      backgroundColor=\"fg-100\"\n      background=\"opaque\"\n      icon=\"networkPlaceholder\"\n    ></wui-icon-box>`;\n    }\n};\nWuiSelect.styles = [resetStyles, elementStyles, colorStyles, styles];\n__decorate([\n    property()\n], WuiSelect.prototype, \"imageSrc\", void 0);\nWuiSelect = __decorate([\n    customElement('wui-select')\n], WuiSelect);\nexport { WuiSelect };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    height: 64px;\n  }\n\n  wui-text {\n    text-transform: capitalize;\n  }\n\n  wui-flex.w3m-header-title {\n    transform: translateY(0);\n    opacity: 1;\n  }\n\n  wui-flex.w3m-header-title[view-direction='prev'] {\n    animation:\n      slide-down-out 120ms forwards var(--wui-ease-out-power-2),\n      slide-down-in 120ms forwards var(--wui-ease-out-power-2);\n    animation-delay: 0ms, 200ms;\n  }\n\n  wui-flex.w3m-header-title[view-direction='next'] {\n    animation:\n      slide-up-out 120ms forwards var(--wui-ease-out-power-2),\n      slide-up-in 120ms forwards var(--wui-ease-out-power-2);\n    animation-delay: 0ms, 200ms;\n  }\n\n  wui-icon-link[data-hidden='true'] {\n    opacity: 0 !important;\n    pointer-events: none;\n  }\n\n  @keyframes slide-up-out {\n    from {\n      transform: translateY(0px);\n      opacity: 1;\n    }\n    to {\n      transform: translateY(3px);\n      opacity: 0;\n    }\n  }\n\n  @keyframes slide-up-in {\n    from {\n      transform: translateY(-3px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-down-out {\n    from {\n      transform: translateY(0px);\n      opacity: 1;\n    }\n    to {\n      transform: translateY(-3px);\n      opacity: 0;\n    }\n  }\n\n  @keyframes slide-down-in {\n    from {\n      transform: translateY(3px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AccountController, AssetController, AssetUtil, ChainController, ConnectorController, EventsController, <PERSON>dal<PERSON><PERSON>, OptionsController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon-link';\nimport '@reown/appkit-ui/wui-select';\nimport '@reown/appkit-ui/wui-tag';\nimport '@reown/appkit-ui/wui-text';\nimport { ConstantsUtil } from '../../utils/ConstantsUtil.js';\nimport styles from './styles.js';\nconst BETA_SCREENS = ['SmartSessionList'];\nfunction headings() {\n    const connectorName = RouterController.state.data?.connector?.name;\n    const walletName = RouterController.state.data?.wallet?.name;\n    const networkName = RouterController.state.data?.network?.name;\n    const name = walletName ?? connectorName;\n    const connectors = ConnectorController.getConnectors();\n    const isEmail = connectors.length === 1 && connectors[0]?.id === 'w3m-email';\n    return {\n        Connect: `Connect ${isEmail ? 'Email' : ''} Wallet`,\n        Create: 'Create Wallet',\n        ChooseAccountName: undefined,\n        Account: undefined,\n        AccountSettings: undefined,\n        AllWallets: 'All Wallets',\n        ApproveTransaction: 'Approve Transaction',\n        BuyInProgress: 'Buy',\n        ConnectingExternal: name ?? 'Connect Wallet',\n        ConnectingWalletConnect: name ?? 'WalletConnect',\n        ConnectingWalletConnectBasic: 'WalletConnect',\n        ConnectingSiwe: 'Sign In',\n        Convert: 'Convert',\n        ConvertSelectToken: 'Select token',\n        ConvertPreview: 'Preview convert',\n        Downloads: name ? `Get ${name}` : 'Downloads',\n        EmailLogin: 'Email Login',\n        EmailVerifyOtp: 'Confirm Email',\n        EmailVerifyDevice: 'Register Device',\n        GetWallet: 'Get a wallet',\n        Networks: 'Choose Network',\n        OnRampProviders: 'Choose Provider',\n        OnRampActivity: 'Activity',\n        OnRampTokenSelect: 'Select Token',\n        OnRampFiatSelect: 'Select Currency',\n        Pay: 'How you pay',\n        Profile: undefined,\n        SwitchNetwork: networkName ?? 'Switch Network',\n        SwitchAddress: 'Switch Address',\n        Transactions: 'Activity',\n        UnsupportedChain: 'Switch Network',\n        UpgradeEmailWallet: 'Upgrade your Wallet',\n        UpdateEmailWallet: 'Edit Email',\n        UpdateEmailPrimaryOtp: 'Confirm Current Email',\n        UpdateEmailSecondaryOtp: 'Confirm New Email',\n        WhatIsABuy: 'What is Buy?',\n        RegisterAccountName: 'Choose name',\n        RegisterAccountNameSuccess: '',\n        WalletReceive: 'Receive',\n        WalletCompatibleNetworks: 'Compatible Networks',\n        Swap: 'Swap',\n        SwapSelectToken: 'Select token',\n        SwapPreview: 'Preview swap',\n        WalletSend: 'Send',\n        WalletSendPreview: 'Review send',\n        WalletSendSelectToken: 'Select Token',\n        WhatIsANetwork: 'What is a network?',\n        WhatIsAWallet: 'What is a wallet?',\n        ConnectWallets: 'Connect wallet',\n        ConnectSocials: 'All socials',\n        ConnectingSocial: AccountController.state.socialProvider\n            ? AccountController.state.socialProvider\n            : 'Connect Social',\n        ConnectingMultiChain: 'Select chain',\n        ConnectingFarcaster: 'Farcaster',\n        SwitchActiveChain: 'Switch chain',\n        SmartSessionCreated: undefined,\n        SmartSessionList: 'Smart Sessions',\n        SIWXSignMessage: 'Sign In',\n        PayLoading: 'Payment in progress'\n    };\n}\nlet W3mHeader = class W3mHeader extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.heading = headings()[RouterController.state.view];\n        this.network = ChainController.state.activeCaipNetwork;\n        this.networkImage = AssetUtil.getNetworkImage(this.network);\n        this.showBack = false;\n        this.prevHistoryLength = 1;\n        this.view = RouterController.state.view;\n        this.viewDirection = '';\n        this.headerText = headings()[RouterController.state.view];\n        this.unsubscribe.push(AssetController.subscribeNetworkImages(() => {\n            this.networkImage = AssetUtil.getNetworkImage(this.network);\n        }), RouterController.subscribeKey('view', val => {\n            setTimeout(() => {\n                this.view = val;\n                this.headerText = headings()[val];\n            }, ConstantsUtil.ANIMATION_DURATIONS.HeaderText);\n            this.onViewChange();\n            this.onHistoryChange();\n        }), ChainController.subscribeKey('activeCaipNetwork', val => {\n            this.network = val;\n            this.networkImage = AssetUtil.getNetworkImage(this.network);\n        }));\n    }\n    disconnectCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        return html `\n      <wui-flex .padding=${this.getPadding()} justifyContent=\"space-between\" alignItems=\"center\">\n        ${this.leftHeaderTemplate()} ${this.titleTemplate()} ${this.rightHeaderTemplate()}\n      </wui-flex>\n    `;\n    }\n    onWalletHelp() {\n        EventsController.sendEvent({ type: 'track', event: 'CLICK_WALLET_HELP' });\n        RouterController.push('WhatIsAWallet');\n    }\n    async onClose() {\n        await ModalUtil.safeClose();\n    }\n    rightHeaderTemplate() {\n        const isSmartSessionsEnabled = OptionsController?.state?.features?.smartSessions;\n        if (RouterController.state.view !== 'Account' || !isSmartSessionsEnabled) {\n            return this.closeButtonTemplate();\n        }\n        return html `<wui-flex>\n      <wui-icon-link\n        icon=\"clock\"\n        @click=${() => RouterController.push('SmartSessionList')}\n        data-testid=\"w3m-header-smart-sessions\"\n      ></wui-icon-link>\n      ${this.closeButtonTemplate()}\n    </wui-flex> `;\n    }\n    closeButtonTemplate() {\n        return html `\n      <wui-icon-link\n        icon=\"close\"\n        @click=${this.onClose.bind(this)}\n        data-testid=\"w3m-header-close\"\n      ></wui-icon-link>\n    `;\n    }\n    titleTemplate() {\n        const isBeta = BETA_SCREENS.includes(this.view);\n        return html `\n      <wui-flex\n        view-direction=\"${this.viewDirection}\"\n        class=\"w3m-header-title\"\n        alignItems=\"center\"\n        gap=\"xs\"\n      >\n        <wui-text variant=\"paragraph-700\" color=\"fg-100\" data-testid=\"w3m-header-text\"\n          >${this.headerText}</wui-text\n        >\n        ${isBeta ? html `<wui-tag variant=\"main\">Beta</wui-tag>` : null}\n      </wui-flex>\n    `;\n    }\n    leftHeaderTemplate() {\n        const { view } = RouterController.state;\n        const isConnectHelp = view === 'Connect';\n        const isEmbeddedEnable = OptionsController.state.enableEmbedded;\n        const isApproveTransaction = view === 'ApproveTransaction';\n        const isConnectingSIWEView = view === 'ConnectingSiwe';\n        const isAccountView = view === 'Account';\n        const enableNetworkSwitch = OptionsController.state.enableNetworkSwitch;\n        const shouldHideBack = isApproveTransaction || isConnectingSIWEView || (isConnectHelp && isEmbeddedEnable);\n        if (isAccountView && enableNetworkSwitch) {\n            return html `<wui-select\n        id=\"dynamic\"\n        data-testid=\"w3m-account-select-network\"\n        active-network=${ifDefined(this.network?.name)}\n        @click=${this.onNetworks.bind(this)}\n        imageSrc=${ifDefined(this.networkImage)}\n      ></wui-select>`;\n        }\n        if (this.showBack && !shouldHideBack) {\n            return html `<wui-icon-link\n        data-testid=\"header-back\"\n        id=\"dynamic\"\n        icon=\"chevronLeft\"\n        @click=${this.onGoBack.bind(this)}\n      ></wui-icon-link>`;\n        }\n        return html `<wui-icon-link\n      data-hidden=${!isConnectHelp}\n      id=\"dynamic\"\n      icon=\"helpCircle\"\n      @click=${this.onWalletHelp.bind(this)}\n    ></wui-icon-link>`;\n    }\n    onNetworks() {\n        if (this.isAllowedNetworkSwitch()) {\n            EventsController.sendEvent({ type: 'track', event: 'CLICK_NETWORKS' });\n            RouterController.push('Networks');\n        }\n    }\n    isAllowedNetworkSwitch() {\n        const requestedCaipNetworks = ChainController.getAllRequestedCaipNetworks();\n        const isMultiNetwork = requestedCaipNetworks ? requestedCaipNetworks.length > 1 : false;\n        const isValidNetwork = requestedCaipNetworks?.find(({ id }) => id === this.network?.id);\n        return isMultiNetwork || !isValidNetwork;\n    }\n    getPadding() {\n        if (this.heading) {\n            return ['l', '2l', 'l', '2l'];\n        }\n        return ['0', '2l', '0', '2l'];\n    }\n    onViewChange() {\n        const { history } = RouterController.state;\n        let direction = ConstantsUtil.VIEW_DIRECTION.Next;\n        if (history.length < this.prevHistoryLength) {\n            direction = ConstantsUtil.VIEW_DIRECTION.Prev;\n        }\n        this.prevHistoryLength = history.length;\n        this.viewDirection = direction;\n    }\n    async onHistoryChange() {\n        const { history } = RouterController.state;\n        const buttonEl = this.shadowRoot?.querySelector('#dynamic');\n        if (history.length > 1 && !this.showBack && buttonEl) {\n            await buttonEl.animate([{ opacity: 1 }, { opacity: 0 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            }).finished;\n            this.showBack = true;\n            buttonEl.animate([{ opacity: 0 }, { opacity: 1 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n        else if (history.length <= 1 && this.showBack && buttonEl) {\n            await buttonEl.animate([{ opacity: 1 }, { opacity: 0 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            }).finished;\n            this.showBack = false;\n            buttonEl.animate([{ opacity: 0 }, { opacity: 1 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n    }\n    onGoBack() {\n        RouterController.goBack();\n    }\n};\nW3mHeader.styles = styles;\n__decorate([\n    state()\n], W3mHeader.prototype, \"heading\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"network\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"networkImage\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"showBack\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"prevHistoryLength\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"view\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"viewDirection\", void 0);\n__decorate([\n    state()\n], W3mHeader.prototype, \"headerText\", void 0);\nW3mHeader = __decorate([\n    customElement('w3m-header')\n], W3mHeader);\nexport { W3mHeader };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: flex;\n    column-gap: var(--wui-spacing-s);\n    align-items: center;\n    padding: var(--wui-spacing-xs) var(--wui-spacing-m) var(--wui-spacing-xs) var(--wui-spacing-xs);\n    border-radius: var(--wui-border-radius-s);\n    border: 1px solid var(--wui-color-gray-glass-005);\n    box-sizing: border-box;\n    background-color: var(--wui-color-bg-175);\n    box-shadow:\n      0px 14px 64px -4px rgba(0, 0, 0, 0.15),\n      0px 8px 22px -6px rgba(0, 0, 0, 0.15);\n\n    max-width: 300px;\n  }\n\n  :host wui-loading-spinner {\n    margin-left: var(--wui-spacing-3xs);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-loading-spinner/index.js';\nimport '../../components/wui-text/index.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-icon-box/index.js';\nimport styles from './styles.js';\nlet WuiSnackbar = class WuiSnackbar extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.backgroundColor = 'accent-100';\n        this.iconColor = 'accent-100';\n        this.icon = 'checkmark';\n        this.message = '';\n        this.loading = false;\n        this.iconType = 'default';\n    }\n    render() {\n        return html `\n      ${this.templateIcon()}\n      <wui-text variant=\"paragraph-500\" color=\"fg-100\" data-testid=\"wui-snackbar-message\"\n        >${this.message}</wui-text\n      >\n    `;\n    }\n    templateIcon() {\n        if (this.loading) {\n            return html `<wui-loading-spinner size=\"md\" color=\"accent-100\"></wui-loading-spinner>`;\n        }\n        if (this.iconType === 'default') {\n            return html `<wui-icon size=\"xl\" color=${this.iconColor} name=${this.icon}></wui-icon>`;\n        }\n        return html `<wui-icon-box\n      size=\"sm\"\n      iconSize=\"xs\"\n      iconColor=${this.iconColor}\n      backgroundColor=${this.backgroundColor}\n      icon=${this.icon}\n      background=\"opaque\"\n    ></wui-icon-box>`;\n    }\n};\nWuiSnackbar.styles = [resetStyles, styles];\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"backgroundColor\", void 0);\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"iconColor\", void 0);\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"icon\", void 0);\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"message\", void 0);\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"loading\", void 0);\n__decorate([\n    property()\n], WuiSnackbar.prototype, \"iconType\", void 0);\nWuiSnackbar = __decorate([\n    customElement('wui-snackbar')\n], WuiSnackbar);\nexport { WuiSnackbar };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    position: absolute;\n    opacity: 0;\n    pointer-events: none;\n    top: 11px;\n    left: 50%;\n    width: max-content;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { SnackController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-snackbar';\nimport styles from './styles.js';\nconst presets = {\n    loading: undefined,\n    success: {\n        backgroundColor: 'success-100',\n        iconColor: 'success-100',\n        icon: 'checkmark'\n    },\n    error: {\n        backgroundColor: 'error-100',\n        iconColor: 'error-100',\n        icon: 'close'\n    }\n};\nlet W3mSnackBar = class W3mSnackBar extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.timeout = undefined;\n        this.open = SnackController.state.open;\n        this.unsubscribe.push(SnackController.subscribeKey('open', val => {\n            this.open = val;\n            this.onOpen();\n        }));\n    }\n    disconnectedCallback() {\n        clearTimeout(this.timeout);\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const { message, variant, svg } = SnackController.state;\n        const preset = presets[variant];\n        const { icon, iconColor } = svg ?? preset ?? {};\n        return html `\n      <wui-snackbar\n        message=${message}\n        backgroundColor=${preset?.backgroundColor}\n        iconColor=${iconColor}\n        icon=${icon}\n        .loading=${variant === 'loading'}\n      ></wui-snackbar>\n    `;\n    }\n    onOpen() {\n        clearTimeout(this.timeout);\n        if (this.open) {\n            this.animate([\n                { opacity: 0, transform: 'translateX(-50%) scale(0.85)' },\n                { opacity: 1, transform: 'translateX(-50%) scale(1)' }\n            ], {\n                duration: 150,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n            if (this.timeout) {\n                clearTimeout(this.timeout);\n            }\n            if (SnackController.state.autoClose) {\n                this.timeout = setTimeout(() => SnackController.hide(), 2500);\n            }\n        }\n        else {\n            this.animate([\n                { opacity: 1, transform: 'translateX(-50%) scale(1)' },\n                { opacity: 0, transform: 'translateX(-50%) scale(0.85)' }\n            ], {\n                duration: 150,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n    }\n};\nW3mSnackBar.styles = styles;\n__decorate([\n    state()\n], W3mSnackBar.prototype, \"open\", void 0);\nW3mSnackBar = __decorate([\n    customElement('w3m-snackbar')\n], W3mSnackBar);\nexport { W3mSnackBar };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    pointer-events: none;\n  }\n\n  :host > wui-flex {\n    display: var(--w3m-tooltip-display);\n    opacity: var(--w3m-tooltip-opacity);\n    padding: 9px var(--wui-spacing-s) 10px var(--wui-spacing-s);\n    border-radius: var(--wui-border-radius-xxs);\n    color: var(--wui-color-bg-100);\n    position: fixed;\n    top: var(--w3m-tooltip-top);\n    left: var(--w3m-tooltip-left);\n    transform: translate(calc(-50% + var(--w3m-tooltip-parent-width)), calc(-100% - 8px));\n    max-width: calc(var(--w3m-modal-width) - var(--wui-spacing-xl));\n    transition: opacity 0.2s var(--wui-ease-out-power-2);\n    will-change: opacity;\n  }\n\n  :host([data-variant='shade']) > wui-flex {\n    background-color: var(--wui-color-bg-150);\n    border: 1px solid var(--wui-color-gray-glass-005);\n  }\n\n  :host([data-variant='shade']) > wui-flex > wui-text {\n    color: var(--wui-color-fg-150);\n  }\n\n  :host([data-variant='fill']) > wui-flex {\n    background-color: var(--wui-color-fg-100);\n    border: none;\n  }\n\n  wui-icon {\n    position: absolute;\n    width: 12px !important;\n    height: 4px !important;\n    color: var(--wui-color-bg-150);\n  }\n\n  wui-icon[data-placement='top'] {\n    bottom: 0px;\n    left: 50%;\n    transform: translate(-50%, 95%);\n  }\n\n  wui-icon[data-placement='bottom'] {\n    top: 0;\n    left: 50%;\n    transform: translate(-50%, -95%) rotate(180deg);\n  }\n\n  wui-icon[data-placement='right'] {\n    top: 50%;\n    left: 0;\n    transform: translate(-65%, -50%) rotate(90deg);\n  }\n\n  wui-icon[data-placement='left'] {\n    top: 50%;\n    right: 0%;\n    transform: translate(65%, -50%) rotate(270deg);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { TooltipController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon';\nimport '@reown/appkit-ui/wui-text';\nimport styles from './styles.js';\nlet W3mTooltip = class W3mTooltip extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.open = TooltipController.state.open;\n        this.message = TooltipController.state.message;\n        this.triggerRect = TooltipController.state.triggerRect;\n        this.variant = TooltipController.state.variant;\n        this.unsubscribe.push(...[\n            TooltipController.subscribe(newState => {\n                this.open = newState.open;\n                this.message = newState.message;\n                this.triggerRect = newState.triggerRect;\n                this.variant = newState.variant;\n            })\n        ]);\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        this.dataset['variant'] = this.variant;\n        const topValue = this.triggerRect.top;\n        const leftValue = this.triggerRect.left;\n        this.style.cssText = `\n    --w3m-tooltip-top: ${topValue}px;\n    --w3m-tooltip-left: ${leftValue}px;\n    --w3m-tooltip-parent-width: ${this.triggerRect.width / 2}px;\n    --w3m-tooltip-display: ${this.open ? 'flex' : 'none'};\n    --w3m-tooltip-opacity: ${this.open ? 1 : 0};\n    `;\n        return html `<wui-flex>\n      <wui-icon data-placement=\"top\" color=\"fg-100\" size=\"inherit\" name=\"cursor\"></wui-icon>\n      <wui-text color=\"inherit\" variant=\"small-500\">${this.message}</wui-text>\n    </wui-flex>`;\n    }\n};\nW3mTooltip.styles = [styles];\n__decorate([\n    state()\n], W3mTooltip.prototype, \"open\", void 0);\n__decorate([\n    state()\n], W3mTooltip.prototype, \"message\", void 0);\n__decorate([\n    state()\n], W3mTooltip.prototype, \"triggerRect\", void 0);\n__decorate([\n    state()\n], W3mTooltip.prototype, \"variant\", void 0);\nW3mTooltip = __decorate([\n    customElement('w3m-tooltip'),\n    customElement('w3m-tooltip')\n], W3mTooltip);\nexport { W3mTooltip };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    --prev-height: 0px;\n    --new-height: 0px;\n    display: block;\n  }\n\n  div.w3m-router-container {\n    transform: translateY(0);\n    opacity: 1;\n  }\n\n  div.w3m-router-container[view-direction='prev'] {\n    animation:\n      slide-left-out 150ms forwards ease,\n      slide-left-in 150ms forwards ease;\n    animation-delay: 0ms, 200ms;\n  }\n\n  div.w3m-router-container[view-direction='next'] {\n    animation:\n      slide-right-out 150ms forwards ease,\n      slide-right-in 150ms forwards ease;\n    animation-delay: 0ms, 200ms;\n  }\n\n  @keyframes slide-left-out {\n    from {\n      transform: translateX(0px);\n      opacity: 1;\n    }\n    to {\n      transform: translateX(10px);\n      opacity: 0;\n    }\n  }\n\n  @keyframes slide-left-in {\n    from {\n      transform: translateX(-10px);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-right-out {\n    from {\n      transform: translateX(0px);\n      opacity: 1;\n    }\n    to {\n      transform: translateX(-10px);\n      opacity: 0;\n    }\n  }\n\n  @keyframes slide-right-in {\n    from {\n      transform: translateX(10px);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { RouterController, TooltipController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport { ConstantsUtil } from '../../utils/ConstantsUtil.js';\nimport styles from './styles.js';\nlet W3mRouter = class W3mRouter extends LitElement {\n    constructor() {\n        super();\n        this.resizeObserver = undefined;\n        this.prevHeight = '0px';\n        this.prevHistoryLength = 1;\n        this.unsubscribe = [];\n        this.view = RouterController.state.view;\n        this.viewDirection = '';\n        this.unsubscribe.push(RouterController.subscribeKey('view', val => this.onViewChange(val)));\n    }\n    firstUpdated() {\n        this.resizeObserver = new ResizeObserver(([content]) => {\n            const height = `${content?.contentRect.height}px`;\n            if (this.prevHeight !== '0px') {\n                this.style.setProperty('--prev-height', this.prevHeight);\n                this.style.setProperty('--new-height', height);\n                this.style.animation = 'w3m-view-height 150ms forwards ease';\n                this.style.height = 'auto';\n            }\n            setTimeout(() => {\n                this.prevHeight = height;\n                this.style.animation = 'unset';\n            }, ConstantsUtil.ANIMATION_DURATIONS.ModalHeight);\n        });\n        this.resizeObserver?.observe(this.getWrapper());\n    }\n    disconnectedCallback() {\n        this.resizeObserver?.unobserve(this.getWrapper());\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        return html `<div class=\"w3m-router-container\" view-direction=\"${this.viewDirection}\">\n      ${this.viewTemplate()}\n    </div>`;\n    }\n    viewTemplate() {\n        switch (this.view) {\n            case 'AccountSettings':\n                return html `<w3m-account-settings-view></w3m-account-settings-view>`;\n            case 'Account':\n                return html `<w3m-account-view></w3m-account-view>`;\n            case 'AllWallets':\n                return html `<w3m-all-wallets-view></w3m-all-wallets-view>`;\n            case 'ApproveTransaction':\n                return html `<w3m-approve-transaction-view></w3m-approve-transaction-view>`;\n            case 'BuyInProgress':\n                return html `<w3m-buy-in-progress-view></w3m-buy-in-progress-view>`;\n            case 'ChooseAccountName':\n                return html `<w3m-choose-account-name-view></w3m-choose-account-name-view>`;\n            case 'Connect':\n                return html `<w3m-connect-view></w3m-connect-view>`;\n            case 'Create':\n                return html `<w3m-connect-view walletGuide=\"explore\"></w3m-connect-view>`;\n            case 'ConnectingWalletConnect':\n                return html `<w3m-connecting-wc-view></w3m-connecting-wc-view>`;\n            case 'ConnectingWalletConnectBasic':\n                return html `<w3m-connecting-wc-basic-view></w3m-connecting-wc-basic-view>`;\n            case 'ConnectingExternal':\n                return html `<w3m-connecting-external-view></w3m-connecting-external-view>`;\n            case 'ConnectingSiwe':\n                return html `<w3m-connecting-siwe-view></w3m-connecting-siwe-view>`;\n            case 'ConnectWallets':\n                return html `<w3m-connect-wallets-view></w3m-connect-wallets-view>`;\n            case 'ConnectSocials':\n                return html `<w3m-connect-socials-view></w3m-connect-socials-view>`;\n            case 'ConnectingSocial':\n                return html `<w3m-connecting-social-view></w3m-connecting-social-view>`;\n            case 'Downloads':\n                return html `<w3m-downloads-view></w3m-downloads-view>`;\n            case 'EmailLogin':\n                return html `<w3m-email-login-view></w3m-email-login-view>`;\n            case 'EmailVerifyOtp':\n                return html `<w3m-email-verify-otp-view></w3m-email-verify-otp-view>`;\n            case 'EmailVerifyDevice':\n                return html `<w3m-email-verify-device-view></w3m-email-verify-device-view>`;\n            case 'GetWallet':\n                return html `<w3m-get-wallet-view></w3m-get-wallet-view>`;\n            case 'Networks':\n                return html `<w3m-networks-view></w3m-networks-view>`;\n            case 'SwitchNetwork':\n                return html `<w3m-network-switch-view></w3m-network-switch-view>`;\n            case 'Profile':\n                return html `<w3m-profile-view></w3m-profile-view>`;\n            case 'SwitchAddress':\n                return html `<w3m-switch-address-view></w3m-switch-address-view>`;\n            case 'Transactions':\n                return html `<w3m-transactions-view></w3m-transactions-view>`;\n            case 'OnRampProviders':\n                return html `<w3m-onramp-providers-view></w3m-onramp-providers-view>`;\n            case 'OnRampActivity':\n                return html `<w3m-onramp-activity-view></w3m-onramp-activity-view>`;\n            case 'OnRampTokenSelect':\n                return html `<w3m-onramp-token-select-view></w3m-onramp-token-select-view>`;\n            case 'OnRampFiatSelect':\n                return html `<w3m-onramp-fiat-select-view></w3m-onramp-fiat-select-view>`;\n            case 'UpgradeEmailWallet':\n                return html `<w3m-upgrade-wallet-view></w3m-upgrade-wallet-view>`;\n            case 'UpdateEmailWallet':\n                return html `<w3m-update-email-wallet-view></w3m-update-email-wallet-view>`;\n            case 'UpdateEmailPrimaryOtp':\n                return html `<w3m-update-email-primary-otp-view></w3m-update-email-primary-otp-view>`;\n            case 'UpdateEmailSecondaryOtp':\n                return html `<w3m-update-email-secondary-otp-view></w3m-update-email-secondary-otp-view>`;\n            case 'UnsupportedChain':\n                return html `<w3m-unsupported-chain-view></w3m-unsupported-chain-view>`;\n            case 'Swap':\n                return html `<w3m-swap-view></w3m-swap-view>`;\n            case 'SwapSelectToken':\n                return html `<w3m-swap-select-token-view></w3m-swap-select-token-view>`;\n            case 'SwapPreview':\n                return html `<w3m-swap-preview-view></w3m-swap-preview-view>`;\n            case 'WalletSend':\n                return html `<w3m-wallet-send-view></w3m-wallet-send-view>`;\n            case 'WalletSendSelectToken':\n                return html `<w3m-wallet-send-select-token-view></w3m-wallet-send-select-token-view>`;\n            case 'WalletSendPreview':\n                return html `<w3m-wallet-send-preview-view></w3m-wallet-send-preview-view>`;\n            case 'WhatIsABuy':\n                return html `<w3m-what-is-a-buy-view></w3m-what-is-a-buy-view>`;\n            case 'WalletReceive':\n                return html `<w3m-wallet-receive-view></w3m-wallet-receive-view>`;\n            case 'WalletCompatibleNetworks':\n                return html `<w3m-wallet-compatible-networks-view></w3m-wallet-compatible-networks-view>`;\n            case 'WhatIsAWallet':\n                return html `<w3m-what-is-a-wallet-view></w3m-what-is-a-wallet-view>`;\n            case 'ConnectingMultiChain':\n                return html `<w3m-connecting-multi-chain-view></w3m-connecting-multi-chain-view>`;\n            case 'WhatIsANetwork':\n                return html `<w3m-what-is-a-network-view></w3m-what-is-a-network-view>`;\n            case 'ConnectingFarcaster':\n                return html `<w3m-connecting-farcaster-view></w3m-connecting-farcaster-view>`;\n            case 'SwitchActiveChain':\n                return html `<w3m-switch-active-chain-view></w3m-switch-active-chain-view>`;\n            case 'RegisterAccountName':\n                return html `<w3m-register-account-name-view></w3m-register-account-name-view>`;\n            case 'RegisterAccountNameSuccess':\n                return html `<w3m-register-account-name-success-view></w3m-register-account-name-success-view>`;\n            case 'SmartSessionCreated':\n                return html `<w3m-smart-session-created-view></w3m-smart-session-created-view>`;\n            case 'SmartSessionList':\n                return html `<w3m-smart-session-list-view></w3m-smart-session-list-view>`;\n            case 'SIWXSignMessage':\n                return html `<w3m-siwx-sign-message-view></w3m-siwx-sign-message-view>`;\n            case 'Pay':\n                return html `<w3m-pay-view></w3m-pay-view>`;\n            case 'PayLoading':\n                return html `<w3m-pay-loading-view></w3m-pay-loading-view>`;\n            default:\n                return html `<w3m-connect-view></w3m-connect-view>`;\n        }\n    }\n    onViewChange(newView) {\n        TooltipController.hide();\n        let direction = ConstantsUtil.VIEW_DIRECTION.Next;\n        const { history } = RouterController.state;\n        if (history.length < this.prevHistoryLength) {\n            direction = ConstantsUtil.VIEW_DIRECTION.Prev;\n        }\n        this.prevHistoryLength = history.length;\n        this.viewDirection = direction;\n        setTimeout(() => {\n            this.view = newView;\n        }, ConstantsUtil.ANIMATION_DURATIONS.ViewTransition);\n    }\n    getWrapper() {\n        return this.shadowRoot?.querySelector('div');\n    }\n};\nW3mRouter.styles = styles;\n__decorate([\n    state()\n], W3mRouter.prototype, \"view\", void 0);\n__decorate([\n    state()\n], W3mRouter.prototype, \"viewDirection\", void 0);\nW3mRouter = __decorate([\n    customElement('w3m-router')\n], W3mRouter);\nexport { W3mRouter };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    z-index: var(--w3m-z-index);\n    display: block;\n    backface-visibility: hidden;\n    will-change: opacity;\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    opacity: 0;\n    background-color: var(--wui-cover);\n    transition: opacity 0.2s var(--wui-ease-out-power-2);\n    will-change: opacity;\n  }\n\n  :host(.open) {\n    opacity: 1;\n  }\n\n  :host(.appkit-modal) {\n    position: relative;\n    pointer-events: unset;\n    background: none;\n    width: 100%;\n    opacity: 1;\n  }\n\n  wui-card {\n    max-width: var(--w3m-modal-width);\n    width: 100%;\n    position: relative;\n    animation: zoom-in 0.2s var(--wui-ease-out-power-2);\n    animation-fill-mode: backwards;\n    outline: none;\n    transition:\n      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1),\n      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1);\n    will-change: border-radius, background-color;\n  }\n\n  :host(.appkit-modal) wui-card {\n    max-width: 400px;\n  }\n\n  wui-card[shake='true'] {\n    animation:\n      zoom-in 0.2s var(--wui-ease-out-power-2),\n      w3m-shake 0.5s var(--wui-ease-out-power-2);\n  }\n\n  wui-flex {\n    overflow-x: hidden;\n    overflow-y: auto;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    height: 100%;\n  }\n\n  @media (max-height: 700px) and (min-width: 431px) {\n    wui-flex {\n      align-items: flex-start;\n    }\n\n    wui-card {\n      margin: var(--wui-spacing-xxl) 0px;\n    }\n  }\n\n  @media (max-width: 430px) {\n    wui-flex {\n      align-items: flex-end;\n    }\n\n    wui-card {\n      max-width: 100%;\n      border-bottom-left-radius: var(--local-border-bottom-mobile-radius);\n      border-bottom-right-radius: var(--local-border-bottom-mobile-radius);\n      border-bottom: none;\n      animation: slide-in 0.2s var(--wui-ease-out-power-2);\n    }\n\n    wui-card[shake='true'] {\n      animation:\n        slide-in 0.2s var(--wui-ease-out-power-2),\n        w3m-shake 0.5s var(--wui-ease-out-power-2);\n    }\n  }\n\n  @keyframes zoom-in {\n    0% {\n      transform: scale(0.95) translateY(0);\n    }\n    100% {\n      transform: scale(1) translateY(0);\n    }\n  }\n\n  @keyframes slide-in {\n    0% {\n      transform: scale(1) translateY(50px);\n    }\n    100% {\n      transform: scale(1) translateY(0);\n    }\n  }\n\n  @keyframes w3m-shake {\n    0% {\n      transform: scale(1) rotate(0deg);\n    }\n    20% {\n      transform: scale(1) rotate(-1deg);\n    }\n    40% {\n      transform: scale(1) rotate(1.5deg);\n    }\n    60% {\n      transform: scale(1) rotate(-1.5deg);\n    }\n    80% {\n      transform: scale(1) rotate(1deg);\n    }\n    100% {\n      transform: scale(1) rotate(0deg);\n    }\n  }\n\n  @keyframes w3m-view-height {\n    from {\n      height: var(--prev-height);\n    }\n    to {\n      height: var(--new-height);\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { ConstantsUtil as CommonConstantsUtil } from '@reown/appkit-common';\nimport { ApiController, ChainController, Connector<PERSON>ontroller, CoreHelperUtil, ModalController, Modal<PERSON>til, OptionsController, RouterController, SIWXUtil, SnackController, ThemeController } from '@reown/appkit-controllers';\nimport { UiHelperUtil, customElement, initializeTheming } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-card';\nimport '@reown/appkit-ui/wui-flex';\nimport '../../partials/w3m-alertbar/index.js';\nimport '../../partials/w3m-header/index.js';\nimport '../../partials/w3m-snackbar/index.js';\nimport '../../partials/w3m-tooltip/index.js';\nimport '../w3m-router/index.js';\nimport styles from './styles.js';\nconst SCROLL_LOCK = 'scroll-lock';\nexport class W3mModalBase extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.abortController = undefined;\n        this.hasPrefetched = false;\n        this.enableEmbedded = OptionsController.state.enableEmbedded;\n        this.open = ModalController.state.open;\n        this.caipAddress = ChainController.state.activeCaipAddress;\n        this.caipNetwork = ChainController.state.activeCaipNetwork;\n        this.shake = ModalController.state.shake;\n        this.filterByNamespace = ConnectorController.state.filterByNamespace;\n        this.initializeTheming();\n        ApiController.prefetchAnalyticsConfig();\n        this.unsubscribe.push(...[\n            ModalController.subscribeKey('open', val => (val ? this.onOpen() : this.onClose())),\n            ModalController.subscribeKey('shake', val => (this.shake = val)),\n            ChainController.subscribeKey('activeCaipNetwork', val => this.onNewNetwork(val)),\n            ChainController.subscribeKey('activeCaipAddress', val => this.onNewAddress(val)),\n            OptionsController.subscribeKey('enableEmbedded', val => (this.enableEmbedded = val)),\n            ConnectorController.subscribeKey('filterByNamespace', val => {\n                if (this.filterByNamespace !== val && !ChainController.getAccountData(val)?.caipAddress) {\n                    ApiController.fetchRecommendedWallets();\n                    this.filterByNamespace = val;\n                }\n            })\n        ]);\n    }\n    firstUpdated() {\n        if (this.caipAddress) {\n            if (this.enableEmbedded) {\n                ModalController.close();\n                this.prefetch();\n                return;\n            }\n            this.onNewAddress(this.caipAddress);\n        }\n        if (this.open) {\n            this.onOpen();\n        }\n        if (this.enableEmbedded) {\n            this.prefetch();\n        }\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n        this.onRemoveKeyboardListener();\n    }\n    render() {\n        this.style.cssText = `\n      --local-border-bottom-mobile-radius: ${this.enableEmbedded ? 'clamp(0px, var(--wui-border-radius-l), 44px)' : '0px'};\n    `;\n        if (this.enableEmbedded) {\n            return html `${this.contentTemplate()}\n        <w3m-tooltip></w3m-tooltip> `;\n        }\n        return this.open\n            ? html `\n          <wui-flex @click=${this.onOverlayClick.bind(this)} data-testid=\"w3m-modal-overlay\">\n            ${this.contentTemplate()}\n          </wui-flex>\n          <w3m-tooltip></w3m-tooltip>\n        `\n            : null;\n    }\n    contentTemplate() {\n        return html ` <wui-card\n      shake=\"${this.shake}\"\n      data-embedded=\"${ifDefined(this.enableEmbedded)}\"\n      role=\"alertdialog\"\n      aria-modal=\"true\"\n      tabindex=\"0\"\n      data-testid=\"w3m-modal-card\"\n    >\n      <w3m-header></w3m-header>\n      <w3m-router></w3m-router>\n      <w3m-snackbar></w3m-snackbar>\n      <w3m-alertbar></w3m-alertbar>\n    </wui-card>`;\n    }\n    async onOverlayClick(event) {\n        if (event.target === event.currentTarget) {\n            await this.handleClose();\n        }\n    }\n    async handleClose() {\n        await ModalUtil.safeClose();\n    }\n    initializeTheming() {\n        const { themeVariables, themeMode } = ThemeController.state;\n        const defaultThemeMode = UiHelperUtil.getColorTheme(themeMode);\n        initializeTheming(themeVariables, defaultThemeMode);\n    }\n    onClose() {\n        this.open = false;\n        this.classList.remove('open');\n        this.onScrollUnlock();\n        SnackController.hide();\n        this.onRemoveKeyboardListener();\n    }\n    onOpen() {\n        this.open = true;\n        this.classList.add('open');\n        this.onScrollLock();\n        this.onAddKeyboardListener();\n    }\n    onScrollLock() {\n        const styleTag = document.createElement('style');\n        styleTag.dataset['w3m'] = SCROLL_LOCK;\n        styleTag.textContent = `\n      body {\n        touch-action: none;\n        overflow: hidden;\n        overscroll-behavior: contain;\n      }\n      w3m-modal {\n        pointer-events: auto;\n      }\n    `;\n        document.head.appendChild(styleTag);\n    }\n    onScrollUnlock() {\n        const styleTag = document.head.querySelector(`style[data-w3m=\"${SCROLL_LOCK}\"]`);\n        if (styleTag) {\n            styleTag.remove();\n        }\n    }\n    onAddKeyboardListener() {\n        this.abortController = new AbortController();\n        const card = this.shadowRoot?.querySelector('wui-card');\n        card?.focus();\n        window.addEventListener('keydown', event => {\n            if (event.key === 'Escape') {\n                this.handleClose();\n            }\n            else if (event.key === 'Tab') {\n                const { tagName } = event.target;\n                if (tagName && !tagName.includes('W3M-') && !tagName.includes('WUI-')) {\n                    card?.focus();\n                }\n            }\n        }, this.abortController);\n    }\n    onRemoveKeyboardListener() {\n        this.abortController?.abort();\n        this.abortController = undefined;\n    }\n    async onNewAddress(caipAddress) {\n        const isSwitchingNamespace = ChainController.state.isSwitchingNamespace;\n        const nextConnected = CoreHelperUtil.getPlainAddress(caipAddress);\n        const isDisconnectedInSameNamespace = !nextConnected && !isSwitchingNamespace;\n        const isSwitchingNamespaceAndConnected = isSwitchingNamespace && nextConnected;\n        if (isDisconnectedInSameNamespace) {\n            ModalController.close();\n        }\n        else if (isSwitchingNamespaceAndConnected) {\n            RouterController.goBack();\n        }\n        await SIWXUtil.initializeIfEnabled();\n        this.caipAddress = caipAddress;\n        ChainController.setIsSwitchingNamespace(false);\n    }\n    onNewNetwork(nextCaipNetwork) {\n        const prevCaipNetwork = this.caipNetwork;\n        const prevCaipNetworkId = prevCaipNetwork?.caipNetworkId?.toString();\n        const prevChainNamespace = prevCaipNetwork?.chainNamespace;\n        const nextNetworkId = nextCaipNetwork?.caipNetworkId?.toString();\n        const nextChainNamespace = nextCaipNetwork?.chainNamespace;\n        const networkIdChanged = prevCaipNetworkId !== nextNetworkId;\n        const namespaceChanged = prevChainNamespace !== nextChainNamespace;\n        const isNetworkChangedInSameNamespace = networkIdChanged && !namespaceChanged;\n        const wasUnsupportedNetwork = prevCaipNetwork?.name === CommonConstantsUtil.UNSUPPORTED_NETWORK_NAME;\n        const isConnectingExternal = RouterController.state.view === 'ConnectingExternal';\n        const isNotConnected = !ChainController.getAccountData(nextCaipNetwork?.chainNamespace)\n            ?.caipAddress;\n        const isUnsupportedNetworkScreen = RouterController.state.view === 'UnsupportedChain';\n        const isModalOpen = ModalController.state.open;\n        let shouldGoBack = false;\n        if (isModalOpen && !isConnectingExternal) {\n            if (isNotConnected) {\n                if (networkIdChanged) {\n                    shouldGoBack = true;\n                }\n            }\n            else if (isUnsupportedNetworkScreen) {\n                shouldGoBack = true;\n            }\n            else if (isNetworkChangedInSameNamespace && !wasUnsupportedNetwork) {\n                shouldGoBack = true;\n            }\n        }\n        if (shouldGoBack && RouterController.state.view !== 'SIWXSignMessage') {\n            RouterController.goBack();\n        }\n        this.caipNetwork = nextCaipNetwork;\n    }\n    prefetch() {\n        if (!this.hasPrefetched) {\n            ApiController.prefetch();\n            ApiController.fetchWalletsByPage({ page: 1 });\n            this.hasPrefetched = true;\n        }\n    }\n}\nW3mModalBase.styles = styles;\n__decorate([\n    property({ type: Boolean })\n], W3mModalBase.prototype, \"enableEmbedded\", void 0);\n__decorate([\n    state()\n], W3mModalBase.prototype, \"open\", void 0);\n__decorate([\n    state()\n], W3mModalBase.prototype, \"caipAddress\", void 0);\n__decorate([\n    state()\n], W3mModalBase.prototype, \"caipNetwork\", void 0);\n__decorate([\n    state()\n], W3mModalBase.prototype, \"shake\", void 0);\n__decorate([\n    state()\n], W3mModalBase.prototype, \"filterByNamespace\", void 0);\nlet W3mModal = class W3mModal extends W3mModalBase {\n};\nW3mModal = __decorate([\n    customElement('w3m-modal')\n], W3mModal);\nexport { W3mModal };\nlet AppKitModal = class AppKitModal extends W3mModalBase {\n};\nAppKitModal = __decorate([\n    customElement('appkit-modal')\n], AppKitModal);\nexport { AppKitModal };\n//# sourceMappingURL=index.js.map"], "names": ["state", "proxy", "controller", "callback", "sub", "key", "subKey", "message", "triggerRect", "variant", "TooltipController", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "RouterController", "ModalController", "SIWXUtil", "styles$a", "css", "__decorate", "decorators", "target", "desc", "c", "r", "d", "i", "WuiCard", "LitElement", "html", "resetStyles", "styles", "customElement", "styles$9", "WuiAlertBar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "property", "styles$8", "presets", "W3mAlertBar", "val", "unsubscribe", "preset", "isMounted", "styles$7", "WuiIconLink", "borderRadius", "padding", "elementStyles", "colorStyles", "styles$6", "WuiSelect", "styles$5", "BETA_SCREENS", "headings", "connectorName", "_b", "_a", "walletName", "_d", "_c", "networkName", "_f", "_e", "name", "connectors", "ConnectorController", "_g", "AccountController", "W3mHeader", "ChainController", "<PERSON><PERSON><PERSON><PERSON>", "AssetController", "Constants<PERSON><PERSON>", "EventsController", "isSmartSessionsEnabled", "OptionsController", "isBeta", "view", "isConnectHelp", "isEmbeddedEnable", "isApproveTransaction", "isConnectingSIWEView", "isAccountView", "enableNetworkSwitch", "shouldHideBack", "ifDefined", "requestedCaipNetworks", "isMultiNetwork", "isValidNetwork", "id", "history", "direction", "buttonEl", "styles$4", "WuiSnackbar", "styles$3", "W3mSnackBar", "SnackController", "svg", "icon", "iconColor", "styles$2", "W3mTooltip", "newState", "topValue", "leftValue", "styles$1", "W3m<PERSON><PERSON><PERSON>", "content", "height", "newView", "SCROLL_LOCK", "W3mModalBase", "ApiController", "event", "themeVariables", "themeMode", "ThemeController", "defaultThemeMode", "UiHelperUtil", "initializeTheming", "styleTag", "card", "tagName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSwitchingNamespace", "nextConnected", "CoreHelperUtil", "nextCaipNetwork", "prevCaipNetwork", "prevCaipNetworkId", "prevChainNamespace", "nextNetworkId", "nextChainNamespace", "networkIdChanged", "isNetworkChangedInSameNamespace", "wasUnsupportedNetwork", "CommonConstantsUtil", "isConnectingExternal", "isNotConnected", "isUnsupportedNetworkScreen", "isModalOpen", "shouldGoBack", "W3mModal", "AppKitModal"], "mappings": "2aAIA,MAAMA,EAAQC,GAAM,CAChB,QAAS,GACT,KAAM,GACN,YAAa,CACT,MAAO,EACP,OAAQ,EACR,IAAK,EACL,KAAM,CAAA,EAEV,QAAS,OACb,CAAC,EAEKC,GAAa,CACf,MAAAF,EACA,UAAUG,EAAU,CAChB,OAAOC,GAAIJ,EAAO,IAAMG,EAASH,CAAK,CAAC,CAC3C,EACA,aAAaK,EAAKF,EAAU,CACxB,OAAOG,GAAON,EAAOK,EAAKF,CAAQ,CACtC,EACA,YAAY,CAAE,QAAAI,EAAS,YAAAC,EAAa,QAAAC,GAAW,CAC3CT,EAAM,KAAO,GACbA,EAAM,QAAUO,EAChBP,EAAM,YAAcQ,EACpBR,EAAM,QAAUS,CACpB,EACA,MAAO,CACHT,EAAM,KAAO,GACbA,EAAM,QAAU,GAChBA,EAAM,YAAc,CAChB,MAAO,EACP,OAAQ,EACR,IAAK,EACL,KAAM,CAAA,CAEd,CACJ,EAEaU,EAAoBC,GAAkBT,EAAU,ECvChDU,GAAY,CACrB,wBAAyB,CACrB,OAAQC,EAAiB,MAAM,OAAS,oBACnCA,EAAiB,MAAM,OAAS,iBAC7BA,EAAiB,MAAM,QAAQ,SAAS,kBAAkB,CACtE,EACA,MAAM,WAAY,CACd,GAAI,KAAK,yBAA0B,CAC/BC,EAAgB,MAAA,EAChB,MACJ,CAEA,GAD4B,MAAMC,GAAS,oBAAA,EAClB,CACrBD,EAAgB,MAAA,EAChB,MACJ,CACAA,EAAgB,MAAA,CACpB,CACJ,ECpBAE,GAAeC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAASC,IAAS,KAAOA,EAAO,OAAO,yBAAyBD,EAAQf,CAAG,EAAIgB,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAKA,IAAIG,EAAU,cAAsBC,CAAW,CAC3C,QAAS,CACL,OAAOC,gBACX,CACJ,EACAF,EAAQ,OAAS,CAACG,EAAaC,EAAM,EACrCJ,EAAUR,GAAW,CACjBa,EAAc,UAAU,CAC5B,EAAGL,CAAO,ECjBV,MAAAM,GAAef;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAUA,IAAIU,EAAc,cAA0BN,CAAW,CACnD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,QAAU,GACf,KAAK,gBAAkB,aACvB,KAAK,UAAY,aACjB,KAAK,KAAO,MAChB,CACA,QAAS,CACL,YAAK,MAAM,QAAU;AAAA,+CACkB,KAAK,eAAe;AAAA,KAEpDC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,8BASe,KAAK,SAAS,mBAAmB,KAAK,IAAI;AAAA;AAAA;AAAA,eAGzD,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQR,KAAK,OAAO;AAAA;AAAA;AAAA,KAI3B,CACA,SAAU,CACNM,EAAgB,MAAA,CACpB,CACJ,EACAD,EAAY,OAAS,CAACJ,EAAaC,EAAM,EACzCZ,EAAW,CACPiB,EAAA,CACJ,EAAGF,EAAY,UAAW,UAAW,MAAM,EAC3Cf,EAAW,CACPiB,EAAA,CACJ,EAAGF,EAAY,UAAW,kBAAmB,MAAM,EACnDf,EAAW,CACPiB,EAAA,CACJ,EAAGF,EAAY,UAAW,YAAa,MAAM,EAC7Cf,EAAW,CACPiB,EAAA,CACJ,EAAGF,EAAY,UAAW,OAAQ,MAAM,EACxCA,EAAcf,EAAW,CACrBa,EAAc,cAAc,CAChC,EAAGE,CAAW,ECtEd,MAAAG,GAAenB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAOO,MAAMc,GAAU,CACnB,KAAM,CACF,gBAAiB,SACjB,UAAW,SACX,KAAM,MAAA,EAEV,QAAS,CACL,gBAAiB,0BACjB,UAAW,cACX,KAAM,WAAA,EAEV,QAAS,CACL,gBAAiB,0BACjB,UAAW,cACX,KAAM,eAAA,EAEV,MAAO,CACH,gBAAiB,wBACjB,UAAW,YACX,KAAM,qBAAA,CAEd,EACA,IAAIC,EAAc,cAA0BX,CAAW,CACnD,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,KAAOO,EAAgB,MAAM,KAClC,KAAK,OAAO,EAAI,EAChB,KAAK,YAAY,KAAKA,EAAgB,aAAa,OAAQK,GAAO,CAC9D,KAAK,KAAOA,EACZ,KAAK,OAAO,EAAK,CACrB,CAAC,CAAC,CACN,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,KAAM,CAAE,QAAAjC,EAAS,QAAAE,CAAA,EAAYyB,EAAgB,MACvCO,EAASJ,GAAQ5B,CAAO,EAC9B,OAAOmB;AAAAA;AAAAA,kBAEGrB,CAAO;AAAA,0BACCkC,GAAA,YAAAA,EAAQ,eAAe;AAAA,oBAC7BA,GAAA,YAAAA,EAAQ,SAAS;AAAA,eACtBA,GAAA,YAAAA,EAAQ,IAAI;AAAA;AAAA,KAGvB,CACA,OAAOC,EAAW,CACV,KAAK,MACL,KAAK,QAAQ,CACT,CAAE,QAAS,EAAG,UAAW,aAAA,EACzB,CAAE,QAAS,EAAG,UAAW,UAAA,CAAW,EACrC,CACC,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EACD,KAAK,MAAM,QAAU,wBAEfA,IACN,KAAK,QAAQ,CACT,CAAE,QAAS,EAAG,UAAW,UAAA,EACzB,CAAE,QAAS,EAAG,UAAW,aAAA,CAAc,EACxC,CACC,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EACD,KAAK,MAAM,QAAU,uBAE7B,CACJ,EACAJ,EAAY,OAASR,GACrBZ,GAAW,CACPlB,EAAA,CACJ,EAAGsC,EAAY,UAAW,OAAQ,MAAM,EACxCA,EAAcpB,GAAW,CACrBa,EAAc,cAAc,CAChC,EAAGO,CAAW,EC1Fd,MAAAK,GAAe1B;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAOA,IAAIqB,EAAc,cAA0BjB,CAAW,CACnD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,KACZ,KAAK,SAAW,GAChB,KAAK,KAAO,OACZ,KAAK,UAAY,SACrB,CACA,QAAS,CACL,MAAMkB,EAAe,KAAK,OAAS,KAAO,yBAA2B,0BAC/DC,EAAU,KAAK,OAAS,KAAO,oBAAsB,oBAC3D,YAAK,MAAM,QAAU;AAAA,iCACID,CAAY;AAAA,2BAClBC,CAAO;AAAA,EAEnBlB;AAAAA,0BACW,KAAK,QAAQ;AAAA,0BACb,KAAK,SAAS,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AAAA;AAAA,KAGxE,CACJ,EACAgB,EAAY,OAAS,CAACf,EAAakB,GAAeC,GAAalB,EAAM,EACrEZ,EAAW,CACPiB,EAAA,CACJ,EAAGS,EAAY,UAAW,OAAQ,MAAM,EACxC1B,EAAW,CACPiB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGS,EAAY,UAAW,WAAY,MAAM,EAC5C1B,EAAW,CACPiB,EAAA,CACJ,EAAGS,EAAY,UAAW,OAAQ,MAAM,EACxC1B,EAAW,CACPiB,EAAA,CACJ,EAAGS,EAAY,UAAW,YAAa,MAAM,EAC7CA,EAAc1B,EAAW,CACrBa,EAAc,eAAe,CACjC,EAAGa,CAAW,EChDd,MAAAK,GAAehC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EASA,IAAI2B,EAAY,cAAwBvB,CAAW,CAC/C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,SAAW,EACpB,CACA,QAAS,CACL,OAAOC;AAAAA,QACP,KAAK,eAAe;AAAA;AAAA,cAGxB,CACA,eAAgB,CACZ,OAAI,KAAK,SACEA,mBAAuB,KAAK,QAAQ,oCAExCA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,qBAOX,CACJ,EACAsB,EAAU,OAAS,CAACrB,EAAakB,GAAeC,GAAalB,EAAM,EACnEZ,GAAW,CACPiB,EAAA,CACJ,EAAGe,EAAU,UAAW,WAAY,MAAM,EAC1CA,EAAYhC,GAAW,CACnBa,EAAc,YAAY,CAC9B,EAAGmB,CAAS,EC3CZ,MAAAC,GAAelC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAaA,MAAM6B,GAAe,CAAC,kBAAkB,EACxC,SAASC,GAAW,mBAChB,MAAMC,GAAgBC,GAAAC,EAAA3C,EAAiB,MAAM,OAAvB,YAAA2C,EAA6B,YAA7B,YAAAD,EAAwC,KACxDE,GAAaC,GAAAC,EAAA9C,EAAiB,MAAM,OAAvB,YAAA8C,EAA6B,SAA7B,YAAAD,EAAqC,KAClDE,GAAcC,GAAAC,EAAAjD,EAAiB,MAAM,OAAvB,YAAAiD,EAA6B,UAA7B,YAAAD,EAAsC,KACpDE,EAAON,GAAcH,EACrBU,EAAaC,EAAoB,cAAA,EAEvC,MAAO,CACH,QAAS,WAFGD,EAAW,SAAW,KAAKE,EAAAF,EAAW,CAAC,IAAZ,YAAAE,EAAe,MAAO,YAE/B,QAAU,EAAE,UAC1C,OAAQ,gBACR,kBAAmB,OACnB,QAAS,OACT,gBAAiB,OACjB,WAAY,cACZ,mBAAoB,sBACpB,cAAe,MACf,mBAAoBH,GAAQ,iBAC5B,wBAAyBA,GAAQ,gBACjC,6BAA8B,gBAC9B,eAAgB,UAChB,QAAS,UACT,mBAAoB,eACpB,eAAgB,kBAChB,UAAWA,EAAO,OAAOA,CAAI,GAAK,YAClC,WAAY,cACZ,eAAgB,gBAChB,kBAAmB,kBACnB,UAAW,eACX,SAAU,iBACV,gBAAiB,kBACjB,eAAgB,WAChB,kBAAmB,eACnB,iBAAkB,kBAClB,IAAK,cACL,QAAS,OACT,cAAeH,GAAe,iBAC9B,cAAe,iBACf,aAAc,WACd,iBAAkB,iBAClB,mBAAoB,sBACpB,kBAAmB,aACnB,sBAAuB,wBACvB,wBAAyB,oBACzB,WAAY,eACZ,oBAAqB,cACrB,2BAA4B,GAC5B,cAAe,UACf,yBAA0B,sBAC1B,KAAM,OACN,gBAAiB,eACjB,YAAa,eACb,WAAY,OACZ,kBAAmB,cACnB,sBAAuB,eACvB,eAAgB,qBAChB,cAAe,oBACf,eAAgB,iBAChB,eAAgB,cAChB,iBAAkBO,GAAkB,MAAM,eACpCA,GAAkB,MAAM,eACxB,iBACN,qBAAsB,eACtB,oBAAqB,YACrB,kBAAmB,eACnB,oBAAqB,OACrB,iBAAkB,iBAClB,gBAAiB,UACjB,WAAY,qBAAA,CAEpB,CACA,IAAIC,EAAY,cAAwBzC,CAAW,CAC/C,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,QAAU0B,EAAA,EAAWxC,EAAiB,MAAM,IAAI,EACrD,KAAK,QAAUwD,EAAgB,MAAM,kBACrC,KAAK,aAAeC,EAAU,gBAAgB,KAAK,OAAO,EAC1D,KAAK,SAAW,GAChB,KAAK,kBAAoB,EACzB,KAAK,KAAOzD,EAAiB,MAAM,KACnC,KAAK,cAAgB,GACrB,KAAK,WAAawC,EAAA,EAAWxC,EAAiB,MAAM,IAAI,EACxD,KAAK,YAAY,KAAK0D,GAAgB,uBAAuB,IAAM,CAC/D,KAAK,aAAeD,EAAU,gBAAgB,KAAK,OAAO,CAC9D,CAAC,EAAGzD,EAAiB,aAAa,OAAQ0B,GAAO,CAC7C,WAAW,IAAM,CACb,KAAK,KAAOA,EACZ,KAAK,WAAac,EAAA,EAAWd,CAAG,CACpC,EAAGiC,EAAc,oBAAoB,UAAU,EAC/C,KAAK,aAAA,EACL,KAAK,gBAAA,CACT,CAAC,EAAGH,EAAgB,aAAa,oBAAqB9B,GAAO,CACzD,KAAK,QAAUA,EACf,KAAK,aAAe+B,EAAU,gBAAgB,KAAK,OAAO,CAC9D,CAAC,CAAC,CACN,CACA,oBAAqB,CACjB,KAAK,YAAY,QAAQ9B,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,OAAOZ;AAAAA,2BACY,KAAK,YAAY;AAAA,UAClC,KAAK,mBAAA,CAAoB,IAAI,KAAK,eAAe,IAAI,KAAK,oBAAA,CAAqB;AAAA;AAAA,KAGrF,CACA,cAAe,CACX6C,GAAiB,UAAU,CAAE,KAAM,QAAS,MAAO,oBAAqB,EACxE5D,EAAiB,KAAK,eAAe,CACzC,CACA,MAAM,SAAU,CACZ,MAAMD,GAAU,UAAA,CACpB,CACA,qBAAsB,WAClB,MAAM8D,GAAyBf,GAAAJ,GAAAC,EAAAmB,IAAA,YAAAnB,EAAmB,QAAnB,YAAAD,EAA0B,WAA1B,YAAAI,EAAoC,cACnE,OAAI9C,EAAiB,MAAM,OAAS,WAAa,CAAC6D,EACvC,KAAK,oBAAA,EAET9C;AAAAA;AAAAA;AAAAA,iBAGE,IAAMf,EAAiB,KAAK,kBAAkB,CAAC;AAAA;AAAA;AAAA,QAGxD,KAAK,qBAAqB;AAAA,iBAE9B,CACA,qBAAsB,CAClB,OAAOe;AAAAA;AAAAA;AAAAA,iBAGE,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,KAIpC,CACA,eAAgB,CACZ,MAAMgD,EAASxB,GAAa,SAAS,KAAK,IAAI,EAC9C,OAAOxB;AAAAA;AAAAA,0BAEW,KAAK,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAM/B,KAAK,UAAU;AAAA;AAAA,UAElBgD,EAAShD,0CAAgD,IAAI;AAAA;AAAA,KAGnE,CACA,oBAAqB,OACjB,KAAM,CAAE,KAAAiD,GAAShE,EAAiB,MAC5BiE,EAAgBD,IAAS,UACzBE,EAAmBJ,EAAkB,MAAM,eAC3CK,EAAuBH,IAAS,qBAChCI,EAAuBJ,IAAS,iBAChCK,EAAgBL,IAAS,UACzBM,EAAsBR,EAAkB,MAAM,oBAC9CS,EAAiBJ,GAAwBC,GAAyBH,GAAiBC,EACzF,OAAIG,GAAiBC,EACVvD;AAAAA;AAAAA;AAAAA,yBAGMyD,GAAU7B,EAAA,KAAK,UAAL,YAAAA,EAAc,IAAI,CAAC;AAAA,iBACrC,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,mBACxB6B,EAAU,KAAK,YAAY,CAAC;AAAA,sBAGnC,KAAK,UAAY,CAACD,EACXxD;AAAAA;AAAAA;AAAAA;AAAAA,iBAIF,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,yBAG1BA;AAAAA,oBACK,CAACkD,CAAa;AAAA;AAAA;AAAA,eAGnB,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,sBAEvC,CACA,YAAa,CACL,KAAK,2BACLL,GAAiB,UAAU,CAAE,KAAM,QAAS,MAAO,iBAAkB,EACrE5D,EAAiB,KAAK,UAAU,EAExC,CACA,wBAAyB,CACrB,MAAMyE,EAAwBjB,EAAgB,4BAAA,EACxCkB,EAAiBD,EAAwBA,EAAsB,OAAS,EAAI,GAC5EE,EAAiBF,GAAA,YAAAA,EAAuB,KAAK,CAAC,CAAE,GAAAG,KAAG,OAAM,OAAAA,MAAOjC,EAAA,KAAK,UAAL,YAAAA,EAAc,MACpF,OAAO+B,GAAkB,CAACC,CAC9B,CACA,YAAa,CACT,OAAI,KAAK,QACE,CAAC,IAAK,KAAM,IAAK,IAAI,EAEzB,CAAC,IAAK,KAAM,IAAK,IAAI,CAChC,CACA,cAAe,CACX,KAAM,CAAE,QAAAE,GAAY7E,EAAiB,MACrC,IAAI8E,EAAYnB,EAAc,eAAe,KACzCkB,EAAQ,OAAS,KAAK,oBACtBC,EAAYnB,EAAc,eAAe,MAE7C,KAAK,kBAAoBkB,EAAQ,OACjC,KAAK,cAAgBC,CACzB,CACA,MAAM,iBAAkB,OACpB,KAAM,CAAE,QAAAD,GAAY7E,EAAiB,MAC/B+E,GAAWpC,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,YAC5CkC,EAAQ,OAAS,GAAK,CAAC,KAAK,UAAYE,GACxC,MAAMA,EAAS,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CACrD,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAAE,SACH,KAAK,SAAW,GAChBA,EAAS,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CAC/C,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,GAEIF,EAAQ,QAAU,GAAK,KAAK,UAAYE,IAC7C,MAAMA,EAAS,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CACrD,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAAE,SACH,KAAK,SAAW,GAChBA,EAAS,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CAC/C,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAET,CACA,UAAW,CACP/E,EAAiB,OAAA,CACrB,CACJ,EACAuD,EAAU,OAAStC,GACnBZ,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,UAAW,MAAM,EACzClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,UAAW,MAAM,EACzClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,eAAgB,MAAM,EAC9ClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,WAAY,MAAM,EAC1ClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,oBAAqB,MAAM,EACnDlD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,OAAQ,MAAM,EACtClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,gBAAiB,MAAM,EAC/ClD,EAAW,CACPlB,EAAA,CACJ,EAAGoE,EAAU,UAAW,aAAc,MAAM,EAC5CA,EAAYlD,EAAW,CACnBa,EAAc,YAAY,CAC9B,EAAGqC,CAAS,ECnSZ,MAAAyB,GAAe5E;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAUA,IAAIuE,EAAc,cAA0BnE,CAAW,CACnD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,gBAAkB,aACvB,KAAK,UAAY,aACjB,KAAK,KAAO,YACZ,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,SAAW,SACpB,CACA,QAAS,CACL,OAAOC;AAAAA,QACP,KAAK,cAAc;AAAA;AAAA,WAEhB,KAAK,OAAO;AAAA;AAAA,KAGnB,CACA,cAAe,CACX,OAAI,KAAK,QACEA,4EAEP,KAAK,WAAa,UACXA,8BAAkC,KAAK,SAAS,SAAS,KAAK,IAAI,eAEtEA;AAAAA;AAAAA;AAAAA,kBAGG,KAAK,SAAS;AAAA,wBACR,KAAK,eAAe;AAAA,aAC/B,KAAK,IAAI;AAAA;AAAA,qBAGlB,CACJ,EACAkE,EAAY,OAAS,CAACjE,EAAaC,EAAM,EACzCZ,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,kBAAmB,MAAM,EACnD5E,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,YAAa,MAAM,EAC7C5E,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,OAAQ,MAAM,EACxC5E,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,UAAW,MAAM,EAC3C5E,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,UAAW,MAAM,EAC3C5E,EAAW,CACPiB,EAAA,CACJ,EAAG2D,EAAY,UAAW,WAAY,MAAM,EAC5CA,EAAc5E,EAAW,CACrBa,EAAc,cAAc,CAChC,EAAG+D,CAAW,ECtEd,MAAAC,GAAe9E;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAOA,MAAMc,GAAU,CACZ,QAAS,OACT,QAAS,CACL,gBAAiB,cACjB,UAAW,cACX,KAAM,WAAA,EAEV,MAAO,CACH,gBAAiB,YACjB,UAAW,YACX,KAAM,OAAA,CAEd,EACA,IAAI2D,EAAc,cAA0BrE,CAAW,CACnD,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,QAAU,OACf,KAAK,KAAOsE,EAAgB,MAAM,KAClC,KAAK,YAAY,KAAKA,EAAgB,aAAa,OAAQ1D,GAAO,CAC9D,KAAK,KAAOA,EACZ,KAAK,OAAA,CACT,CAAC,CAAC,CACN,CACA,sBAAuB,CACnB,aAAa,KAAK,OAAO,EACzB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,KAAM,CAAE,QAAAjC,EAAS,QAAAE,EAAS,IAAAyF,CAAA,EAAQD,EAAgB,MAC5CxD,EAASJ,GAAQ5B,CAAO,EACxB,CAAE,KAAA0F,EAAM,UAAAC,CAAA,EAAcF,GAAOzD,GAAU,CAAA,EAC7C,OAAOb;AAAAA;AAAAA,kBAEGrB,CAAO;AAAA,0BACCkC,GAAA,YAAAA,EAAQ,eAAe;AAAA,oBAC7B2D,CAAS;AAAA,eACdD,CAAI;AAAA,mBACA1F,IAAY,SAAS;AAAA;AAAA,KAGpC,CACA,QAAS,CACL,aAAa,KAAK,OAAO,EACrB,KAAK,MACL,KAAK,QAAQ,CACT,CAAE,QAAS,EAAG,UAAW,8BAAA,EACzB,CAAE,QAAS,EAAG,UAAW,2BAAA,CAA4B,EACtD,CACC,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EACG,KAAK,SACL,aAAa,KAAK,OAAO,EAEzBwF,EAAgB,MAAM,YACtB,KAAK,QAAU,WAAW,IAAMA,EAAgB,KAAA,EAAQ,IAAI,IAIhE,KAAK,QAAQ,CACT,CAAE,QAAS,EAAG,UAAW,2BAAA,EACzB,CAAE,QAAS,EAAG,UAAW,8BAAA,CAA+B,EACzD,CACC,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,CAET,CACJ,EACAD,EAAY,OAASlE,GACrBZ,GAAW,CACPlB,EAAA,CACJ,EAAGgG,EAAY,UAAW,OAAQ,MAAM,EACxCA,EAAc9E,GAAW,CACrBa,EAAc,cAAc,CAChC,EAAGiE,CAAW,ECzFd,MAAAK,GAAepF;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EASA,IAAI+E,EAAa,cAAyB3E,CAAW,CACjD,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,KAAOjB,EAAkB,MAAM,KACpC,KAAK,QAAUA,EAAkB,MAAM,QACvC,KAAK,YAAcA,EAAkB,MAAM,YAC3C,KAAK,QAAUA,EAAkB,MAAM,QACvC,KAAK,YAAY,KACbA,EAAkB,UAAU6F,GAAY,CACpC,KAAK,KAAOA,EAAS,KACrB,KAAK,QAAUA,EAAS,QACxB,KAAK,YAAcA,EAAS,YAC5B,KAAK,QAAUA,EAAS,OAC5B,CAAC,CACJ,CACL,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQ/D,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,KAAK,QAAQ,QAAa,KAAK,QAC/B,MAAMgE,EAAW,KAAK,YAAY,IAC5BC,EAAY,KAAK,YAAY,KACnC,YAAK,MAAM,QAAU;AAAA,yBACJD,CAAQ;AAAA,0BACPC,CAAS;AAAA,kCACD,KAAK,YAAY,MAAQ,CAAC;AAAA,6BAC/B,KAAK,KAAO,OAAS,MAAM;AAAA,6BAC3B,KAAK,KAAO,EAAI,CAAC;AAAA,MAE/B7E;AAAAA;AAAAA,sDAEuC,KAAK,OAAO;AAAA,gBAE9D,CACJ,EACA0E,EAAW,OAAS,CAACxE,EAAM,EAC3BZ,EAAW,CACPlB,EAAA,CACJ,EAAGsG,EAAW,UAAW,OAAQ,MAAM,EACvCpF,EAAW,CACPlB,EAAA,CACJ,EAAGsG,EAAW,UAAW,UAAW,MAAM,EAC1CpF,EAAW,CACPlB,EAAA,CACJ,EAAGsG,EAAW,UAAW,cAAe,MAAM,EAC9CpF,EAAW,CACPlB,EAAA,CACJ,EAAGsG,EAAW,UAAW,UAAW,MAAM,EAC1CA,EAAapF,EAAW,CACpBa,EAAc,aAAa,EAC3BA,EAAc,aAAa,CAC/B,EAAGuE,CAAU,EClEb,MAAAI,GAAezF;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAOA,IAAIoF,EAAY,cAAwBhF,CAAW,CAC/C,aAAc,CACV,MAAA,EACA,KAAK,eAAiB,OACtB,KAAK,WAAa,MAClB,KAAK,kBAAoB,EACzB,KAAK,YAAc,CAAA,EACnB,KAAK,KAAOd,EAAiB,MAAM,KACnC,KAAK,cAAgB,GACrB,KAAK,YAAY,KAAKA,EAAiB,aAAa,UAAe,KAAK,aAAa0B,CAAG,CAAC,CAAC,CAC9F,CACA,cAAe,OACX,KAAK,eAAiB,IAAI,eAAe,CAAC,CAACqE,CAAO,IAAM,CACpD,MAAMC,EAAS,GAAGD,GAAA,YAAAA,EAAS,YAAY,MAAM,KACzC,KAAK,aAAe,QACpB,KAAK,MAAM,YAAY,gBAAiB,KAAK,UAAU,EACvD,KAAK,MAAM,YAAY,eAAgBC,CAAM,EAC7C,KAAK,MAAM,UAAY,sCACvB,KAAK,MAAM,OAAS,QAExB,WAAW,IAAM,CACb,KAAK,WAAaA,EAClB,KAAK,MAAM,UAAY,OAC3B,EAAGrC,EAAc,oBAAoB,WAAW,CACpD,CAAC,GACDhB,EAAA,KAAK,iBAAL,MAAAA,EAAqB,QAAQ,KAAK,WAAA,EACtC,CACA,sBAAuB,QACnBA,EAAA,KAAK,iBAAL,MAAAA,EAAqB,UAAU,KAAK,WAAA,GACpC,KAAK,YAAY,QAAQhB,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,OAAOZ,sDAA0D,KAAK,aAAa;AAAA,QACnF,KAAK,cAAc;AAAA,WAEvB,CACA,cAAe,CACX,OAAQ,KAAK,KAAA,CACT,IAAK,kBACD,OAAOA,2DACX,IAAK,UACD,OAAOA,yCACX,IAAK,aACD,OAAOA,iDACX,IAAK,qBACD,OAAOA,iEACX,IAAK,gBACD,OAAOA,yDACX,IAAK,oBACD,OAAOA,iEACX,IAAK,UACD,OAAOA,yCACX,IAAK,SACD,OAAOA,+DACX,IAAK,0BACD,OAAOA,qDACX,IAAK,+BACD,OAAOA,iEACX,IAAK,qBACD,OAAOA,iEACX,IAAK,iBACD,OAAOA,yDACX,IAAK,iBACD,OAAOA,yDACX,IAAK,iBACD,OAAOA,yDACX,IAAK,mBACD,OAAOA,6DACX,IAAK,YACD,OAAOA,6CACX,IAAK,aACD,OAAOA,iDACX,IAAK,iBACD,OAAOA,2DACX,IAAK,oBACD,OAAOA,iEACX,IAAK,YACD,OAAOA,+CACX,IAAK,WACD,OAAOA,2CACX,IAAK,gBACD,OAAOA,uDACX,IAAK,UACD,OAAOA,yCACX,IAAK,gBACD,OAAOA,uDACX,IAAK,eACD,OAAOA,mDACX,IAAK,kBACD,OAAOA,2DACX,IAAK,iBACD,OAAOA,yDACX,IAAK,oBACD,OAAOA,iEACX,IAAK,mBACD,OAAOA,+DACX,IAAK,qBACD,OAAOA,uDACX,IAAK,oBACD,OAAOA,iEACX,IAAK,wBACD,OAAOA,2EACX,IAAK,0BACD,OAAOA,+EACX,IAAK,mBACD,OAAOA,6DACX,IAAK,OACD,OAAOA,mCACX,IAAK,kBACD,OAAOA,6DACX,IAAK,cACD,OAAOA,mDACX,IAAK,aACD,OAAOA,iDACX,IAAK,wBACD,OAAOA,2EACX,IAAK,oBACD,OAAOA,iEACX,IAAK,aACD,OAAOA,qDACX,IAAK,gBACD,OAAOA,uDACX,IAAK,2BACD,OAAOA,+EACX,IAAK,gBACD,OAAOA,2DACX,IAAK,uBACD,OAAOA,uEACX,IAAK,iBACD,OAAOA,6DACX,IAAK,sBACD,OAAOA,mEACX,IAAK,oBACD,OAAOA,iEACX,IAAK,sBACD,OAAOA,qEACX,IAAK,6BACD,OAAOA,qFACX,IAAK,sBACD,OAAOA,qEACX,IAAK,mBACD,OAAOA,+DACX,IAAK,kBACD,OAAOA,6DACX,IAAK,MACD,OAAOA,iCACX,IAAK,aACD,OAAOA,iDACX,QACI,OAAOA,wCAAA,CAEnB,CACA,aAAakF,EAAS,CAClBpG,EAAkB,KAAA,EAClB,IAAIiF,EAAYnB,EAAc,eAAe,KAC7C,KAAM,CAAE,QAAAkB,GAAY7E,EAAiB,MACjC6E,EAAQ,OAAS,KAAK,oBACtBC,EAAYnB,EAAc,eAAe,MAE7C,KAAK,kBAAoBkB,EAAQ,OACjC,KAAK,cAAgBC,EACrB,WAAW,IAAM,CACb,KAAK,KAAOmB,CAChB,EAAGtC,EAAc,oBAAoB,cAAc,CACvD,CACA,YAAa,OACT,OAAOhB,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,MAC1C,CACJ,EACAmD,EAAU,OAAS7E,GACnBZ,EAAW,CACPlB,EAAA,CACJ,EAAG2G,EAAU,UAAW,OAAQ,MAAM,EACtCzF,EAAW,CACPlB,EAAA,CACJ,EAAG2G,EAAU,UAAW,gBAAiB,MAAM,EAC/CA,EAAYzF,EAAW,CACnBa,EAAc,YAAY,CAC9B,EAAG4E,CAAS,EC7LZ,MAAA7E,GAAeb;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQf,EAAKgB,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIF,EAA+EC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASJ,EAAYC,EAAQf,EAAKgB,CAAI,MACxH,SAASI,EAAIN,EAAW,OAAS,EAAGM,GAAK,EAAGA,KAASD,EAAIL,EAAWM,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEJ,EAAQf,EAAKkB,CAAC,EAAIC,EAAEJ,EAAQf,CAAG,IAAMkB,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeH,EAAQf,EAAKkB,CAAC,EAAGA,CAChE,EAeA,MAAMwF,GAAc,cACb,MAAMC,UAAqBrF,CAAW,CACzC,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,gBAAkB,OACvB,KAAK,cAAgB,GACrB,KAAK,eAAiBgD,EAAkB,MAAM,eAC9C,KAAK,KAAO7D,EAAgB,MAAM,KAClC,KAAK,YAAcuD,EAAgB,MAAM,kBACzC,KAAK,YAAcA,EAAgB,MAAM,kBACzC,KAAK,MAAQvD,EAAgB,MAAM,MACnC,KAAK,kBAAoBmD,EAAoB,MAAM,kBACnD,KAAK,kBAAA,EACLgD,EAAc,wBAAA,EACd,KAAK,YAAY,KACbnG,EAAgB,aAAa,OAAQyB,GAAQA,EAAM,KAAK,OAAA,EAAW,KAAK,SAAU,EAClFzB,EAAgB,aAAa,QAASyB,GAAQ,KAAK,MAAQA,CAAI,EAC/D8B,EAAgB,aAAa,uBAA4B,KAAK,aAAa9B,CAAG,CAAC,EAC/E8B,EAAgB,aAAa,uBAA4B,KAAK,aAAa9B,CAAG,CAAC,EAC/EoC,EAAkB,aAAa,iBAAkBpC,GAAQ,KAAK,eAAiBA,CAAI,EACnF0B,EAAoB,aAAa,oBAAqB1B,GAAO,OACrD,KAAK,oBAAsBA,GAAO,GAACiB,EAAAa,EAAgB,eAAe9B,CAAG,IAAlC,MAAAiB,EAAqC,eACxEyD,EAAc,wBAAA,EACd,KAAK,kBAAoB1E,EAEjC,CAAC,CACJ,CACL,CACA,cAAe,CACX,GAAI,KAAK,YAAa,CAClB,GAAI,KAAK,eAAgB,CACrBzB,EAAgB,MAAA,EAChB,KAAK,SAAA,EACL,MACJ,CACA,KAAK,aAAa,KAAK,WAAW,CACtC,CACI,KAAK,MACL,KAAK,OAAA,EAEL,KAAK,gBACL,KAAK,SAAA,CAEb,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQ0B,GAAeA,EAAA,CAAa,EACrD,KAAK,yBAAA,CACT,CACA,QAAS,CAIL,OAHA,KAAK,MAAM,QAAU;AAAA,6CACgB,KAAK,eAAiB,+CAAiD,KAAK;AAAA,MAE7G,KAAK,eACEZ,IAAQ,KAAK,gBAAA,CAAiB;AAAA,sCAGlC,KAAK,KACNA;AAAAA,6BACe,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,cAC7C,KAAK,iBAAiB;AAAA;AAAA;AAAA,UAItB,IACV,CACA,iBAAkB,CACd,OAAOA;AAAAA,eACA,KAAK,KAAK;AAAA,uBACFyD,EAAU,KAAK,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAWjD,CACA,MAAM,eAAe6B,EAAO,CACpBA,EAAM,SAAWA,EAAM,eACvB,MAAM,KAAK,YAAA,CAEnB,CACA,MAAM,aAAc,CAChB,MAAMtG,GAAU,UAAA,CACpB,CACA,mBAAoB,CAChB,KAAM,CAAE,eAAAuG,EAAgB,UAAAC,CAAA,EAAcC,GAAgB,MAChDC,EAAmBC,GAAa,cAAcH,CAAS,EAC7DI,GAAkBL,EAAgBG,CAAgB,CACtD,CACA,SAAU,CACN,KAAK,KAAO,GACZ,KAAK,UAAU,OAAO,MAAM,EAC5B,KAAK,eAAA,EACLrB,EAAgB,KAAA,EAChB,KAAK,yBAAA,CACT,CACA,QAAS,CACL,KAAK,KAAO,GACZ,KAAK,UAAU,IAAI,MAAM,EACzB,KAAK,aAAA,EACL,KAAK,sBAAA,CACT,CACA,cAAe,CACX,MAAMwB,EAAW,SAAS,cAAc,OAAO,EAC/CA,EAAS,QAAQ,IAASV,GAC1BU,EAAS,YAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUvB,SAAS,KAAK,YAAYA,CAAQ,CACtC,CACA,gBAAiB,CACb,MAAMA,EAAW,SAAS,KAAK,cAAc,mBAAmBV,EAAW,IAAI,EAC3EU,GACAA,EAAS,OAAA,CAEjB,CACA,uBAAwB,OACpB,KAAK,gBAAkB,IAAI,gBAC3B,MAAMC,GAAOlE,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,YAC5CkE,GAAA,MAAAA,EAAM,QACN,OAAO,iBAAiB,UAAWR,GAAS,CACxC,GAAIA,EAAM,MAAQ,SACd,KAAK,YAAA,UAEAA,EAAM,MAAQ,MAAO,CAC1B,KAAM,CAAE,QAAAS,GAAYT,EAAM,OACtBS,GAAW,CAACA,EAAQ,SAAS,MAAM,GAAK,CAACA,EAAQ,SAAS,MAAM,IAChED,GAAA,MAAAA,EAAM,QAEd,CACJ,EAAG,KAAK,eAAe,CAC3B,CACA,0BAA2B,QACvBlE,EAAA,KAAK,kBAAL,MAAAA,EAAsB,QACtB,KAAK,gBAAkB,MAC3B,CACA,MAAM,aAAaoE,EAAa,CAC5B,MAAMC,EAAuBxD,EAAgB,MAAM,qBAC7CyD,EAAgBC,GAAe,gBAAgBH,CAAW,EAC1B,CAACE,GAAiB,CAACD,EAGrD/G,EAAgB,MAAA,EAFqB+G,GAAwBC,GAK7DjH,EAAiB,OAAA,EAErB,MAAME,GAAS,oBAAA,EACf,KAAK,YAAc6G,EACnBvD,EAAgB,wBAAwB,EAAK,CACjD,CACA,aAAa2D,EAAiB,aAC1B,MAAMC,EAAkB,KAAK,YACvBC,GAAoB1E,EAAAyE,GAAA,YAAAA,EAAiB,gBAAjB,YAAAzE,EAAgC,WACpD2E,EAAqBF,GAAA,YAAAA,EAAiB,eACtCG,GAAgB7E,GAAAyE,GAAA,YAAAA,EAAiB,gBAAjB,YAAAzE,GAAgC,WAChD8E,EAAqBL,GAAA,YAAAA,EAAiB,eACtCM,EAAmBJ,IAAsBE,EAEzCG,EAAkCD,GAAoB,EADnCH,IAAuBE,GAE1CG,GAAwBP,GAAA,YAAAA,EAAiB,QAASQ,GAAoB,yBACtEC,EAAuB7H,EAAiB,MAAM,OAAS,qBACvD8H,EAAiB,GAAChF,GAAAU,EAAgB,eAAe2D,GAAA,YAAAA,EAAiB,cAAc,IAA9D,MAAArE,GAClB,aACAiF,GAA6B/H,EAAiB,MAAM,OAAS,mBAC7DgI,GAAc/H,EAAgB,MAAM,KAC1C,IAAIgI,EAAe,GACfD,IAAe,CAACH,IACZC,EACIL,IACAQ,EAAe,KAGdF,IAGAL,GAAmC,CAACC,KACzCM,EAAe,KAGnBA,GAAgBjI,EAAiB,MAAM,OAAS,mBAChDA,EAAiB,OAAA,EAErB,KAAK,YAAcmH,CACvB,CACA,UAAW,CACF,KAAK,gBACNf,EAAc,SAAA,EACdA,EAAc,mBAAmB,CAAE,KAAM,CAAA,CAAG,EAC5C,KAAK,cAAgB,GAE7B,CACJ,CACAD,EAAa,OAASlF,GACtBZ,EAAW,CACPiB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAG6E,EAAa,UAAW,iBAAkB,MAAM,EACnD9F,EAAW,CACPlB,EAAA,CACJ,EAAGgH,EAAa,UAAW,OAAQ,MAAM,EACzC9F,EAAW,CACPlB,EAAA,CACJ,EAAGgH,EAAa,UAAW,cAAe,MAAM,EAChD9F,EAAW,CACPlB,EAAA,CACJ,EAAGgH,EAAa,UAAW,cAAe,MAAM,EAChD9F,EAAW,CACPlB,EAAA,CACJ,EAAGgH,EAAa,UAAW,QAAS,MAAM,EAC1C9F,EAAW,CACPlB,EAAA,CACJ,EAAGgH,EAAa,UAAW,oBAAqB,MAAM,EACtD,IAAI+B,GAAW,cAAuB/B,CAAa,CACnD,EACA+B,GAAW7H,EAAW,CAClBa,EAAc,WAAW,CAC7B,EAAGgH,EAAQ,EAEX,IAAIC,GAAc,cAA0BhC,CAAa,CACzD,EACAgC,GAAc9H,EAAW,CACrBa,EAAc,cAAc,CAChC,EAAGiH,EAAW", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}