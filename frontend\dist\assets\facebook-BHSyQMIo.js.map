{"version": 3, "file": "facebook-BHSyQMIo.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const facebookSvg = svg `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#1877F2\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M26 12.38h-2.89c-.92 0-1.61.38-1.61 1.34v1.66H26l-.36 4.5H21.5v12H17v-12h-3v-4.5h3V12.5c0-3.03 1.6-4.62 5.2-4.62H26v4.5Z\"\n        />\n      </g>\n    </g>\n    <path\n      fill=\"#1877F2\"\n      d=\"M40 20a20 20 0 1 0-23.13 19.76V25.78H11.8V20h5.07v-4.4c0-5.02 3-7.79 7.56-7.79 2.19 0 4.48.4 4.48.4v4.91h-2.53c-2.48 0-3.25 1.55-3.25 3.13V20h5.54l-.88 5.78h-4.66v13.98A20 20 0 0 0 40 20Z\"\n    />\n    <path\n      fill=\"#fff\"\n      d=\"m27.79 25.78.88-5.78h-5.55v-3.75c0-1.58.78-3.13 3.26-3.13h2.53V8.2s-2.3-.39-4.48-.39c-4.57 0-7.55 2.77-7.55 7.78V20H11.8v5.78h5.07v13.98a20.15 20.15 0 0 0 6.25 0V25.78h4.67Z\"\n    />\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=facebook.js.map"], "names": ["facebookSvg", "svg"], "mappings": "2JACO,MAAMA,EAAcC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}