{"version": 3, "file": "checkmark-C6RfSvaW.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const checkmarkSvg = svg `<svg\n  width=\"28\"\n  height=\"28\"\n  viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n  <path\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M25.5297 4.92733C26.1221 5.4242 26.1996 6.30724 25.7027 6.89966L12.2836 22.8997C12.0316 23.2001 11.6652 23.3811 11.2735 23.3986C10.8817 23.4161 10.5006 23.2686 10.2228 22.9919L2.38218 15.1815C1.83439 14.6358 1.83268 13.7494 2.37835 13.2016C2.92403 12.6538 3.81046 12.6521 4.35825 13.1978L11.1183 19.9317L23.5573 5.10036C24.0542 4.50794 24.9372 4.43047 25.5297 4.92733Z\"\n    fill=\"currentColor\"/>\n</svg>\n`;\n//# sourceMappingURL=checkmark.js.map"], "names": ["checkmarkSvg", "svg"], "mappings": "2JACO,MAAMA,EAAeC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}