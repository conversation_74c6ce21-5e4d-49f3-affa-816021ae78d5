{"version": 3, "file": "secp256k1-vwi1ufCg.js", "sources": ["../../node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js", "../../node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js"], "sourcesContent": ["/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\nimport { aexists, aoutput } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nexport function Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport class HashMD extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        aexists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        aexists(this);\n        aoutput(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake<PERSON>.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { Chi, HashMD, Maj } from './_md.js';\nimport { rotr, wrapConstructor } from './utils.js';\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = SHA256_IV[0] | 0;\n        this.B = SHA256_IV[1] | 0;\n        this.C = SHA256_IV[2] | 0;\n        this.D = SHA256_IV[3] | 0;\n        this.E = SHA256_IV[4] | 0;\n        this.F = SHA256_IV[5] | 0;\n        this.G = SHA256_IV[6] | 0;\n        this.H = SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/** SHA2-256 hash function */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n/** SHA2-224 hash function */\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nexport function isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nexport function abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nexport function abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nexport function numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nexport function inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nexport const notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map", "/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash } from './_assert.js';\nimport { Hash, toBytes } from './utils.js';\nexport class HMAC extends Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        ahash(hash);\n        const key = toBytes(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        aexists(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        aexists(this);\n        abytes(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map", "/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitMask, bytesToNumberBE, bytesToNumberLE, ensureBytes, numberToBytesBE, numberToBytesLE, validateObject, } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nexport function validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: bitMask(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = ensureBytes('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map", "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { nLength, validateField } from './modular.js';\nimport { bitLen, validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport function wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nexport function pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = bitLen(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nexport function validateBasic(curve) {\n    validateField(curve.Fp);\n    validateObject(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...nLength(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map", "/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { pippenger, validateBasic, wNAF, } from './curve.js';\nimport { Field, getMinHashLength, invert, mapHashToField, mod, validateField, } from './modular.js';\nimport * as ut from './utils.js';\nimport { abool, ensureBytes, memoized } from './utils.js';\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        abool('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        abool('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\nexport class DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = ut.numberToHexUnpadded(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? ut.numberToHexUnpadded((len.length / 2) | 128) : '';\n            const t = ut.numberToHexUnpadded(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = ut.numberToHexUnpadded(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return b2n(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        ut.abytes(data);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nexport function weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = Field(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return ut.inRange(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (ut.isBytes(key))\n                key = ut.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = mod(num, N); // disabled by default, enabled for BLS\n        ut.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    const toAffineMemo = memoized((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = memoized((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        if (!Fp.eql(left, right))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return pippenger(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            ut.aInRange('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            ut.aInRange('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            abool('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            abool('isCompressed', isCompressed);\n            return ut.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nexport function weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = ut.concatBytes;\n            abool('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = ut.bytesToNumberBE(tail);\n                if (!ut.inRange(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    const numToNByteStr = (num) => ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => ut.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = ensureBytes('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig(ensureBytes('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            ut.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n            ut.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return ut.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return ut.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = getMinHashLength(CURVE.n);\n            return mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = ut.isBytes(item);\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\"\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        ut.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return ut.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = ensureBytes('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n        }\n        const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = ut.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = ensureBytes('msgHash', msgHash);\n        publicKey = ensureBytes('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || ut.isBytes(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n    validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map", "/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass } from './abstract/weierstrass.js';\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),\n        randomBytes,\n    };\n}\nexport function createCurve(curveDef, defHash) {\n    const create = (hash) => weierstrass({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map", "/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { createCurve } from './_shortw_utils.js';\nimport { createHasher, isogenyMap } from './abstract/hash-to-curve.js';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport { aInRange, bytesToNumberBE, concatBytes, ensureBytes, inRange, numberToBytesBE, } from './abstract/utils.js';\nimport { mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = (pow2(b3, _3n, P) * b3) % P;\n    const b9 = (pow2(b6, _3n, P) * b3) % P;\n    const b11 = (pow2(b9, _2n, P) * b2) % P;\n    const b22 = (pow2(b11, _11n, P) * b11) % P;\n    const b44 = (pow2(b22, _22n, P) * b22) % P;\n    const b88 = (pow2(b44, _44n, P) * b44) % P;\n    const b176 = (pow2(b88, _88n, P) * b88) % P;\n    const b220 = (pow2(b176, _44n, P) * b44) % P;\n    const b223 = (pow2(b220, _3n, P) * b3) % P;\n    const t1 = (pow2(b223, _23n, P) * b22) % P;\n    const t2 = (pow2(t1, _6n, P) * b2) % P;\n    const root = pow2(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = Field(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 short weierstrass curve and ECDSA signatures over it.\n *\n * @example\n * import { secp256k1 } from '@noble/curves/secp256k1';\n *\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n */\nexport const secp256k1 = createCurve({\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7),\n    Fp: Fpk1, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = mod(k - c1 * a1 - c2 * a2, n);\n            let k2 = mod(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = concatBytes(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return sha256(concatBytes(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => numberToBytesBE(n, 32);\nconst modP = (x) => mod(x, secp256k1P);\nconst modN = (x) => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    aInRange('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = randomBytes(32)) {\n    const m = ensureBytes('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = ensureBytes('signature', signature, 64);\n    const m = ensureBytes('message', message);\n    const pub = ensureBytes('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!inRange(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!inRange(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n */\nexport const schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE,\n        bytesToNumberBE,\n        taggedHash,\n        mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => isogenyMap(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => mapToCurveSimpleSWU(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => createHasher(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha256,\n}))();\n/** secp256k1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\n/** secp256k1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map"], "names": ["setBigUint64", "view", "byteOffset", "value", "isLE", "_32n", "_u32_max", "wh", "wl", "h", "l", "<PERSON>", "a", "b", "c", "Maj", "HashMD", "Hash", "blockLen", "outputLen", "padOffset", "createView", "data", "aexists", "buffer", "toBytes", "len", "pos", "take", "dataView", "out", "aoutput", "i", "oview", "outLen", "state", "res", "to", "length", "finished", "destroyed", "SHA256_K", "SHA256_IV", "SHA256_W", "SHA256", "A", "B", "C", "D", "E", "F", "G", "H", "offset", "W15", "W2", "s0", "rotr", "s1", "sigma1", "T1", "T2", "sha256", "wrapConstructor", "_0n", "_1n", "_2n", "isBytes", "abytes", "item", "abool", "title", "hexes", "_", "bytesToHex", "bytes", "hex", "numberToHexUnpadded", "num", "hexToNumber", "asciis", "asciiToBase16", "ch", "hexToBytes", "hl", "al", "array", "ai", "hi", "n1", "n2", "char", "bytesToNumberBE", "bytesToNumberLE", "numberToBytesBE", "numberToBytesLE", "numberToVarBytesBE", "ensureBytes", "<PERSON><PERSON><PERSON><PERSON>", "e", "concatBytes", "arrays", "sum", "pad", "equalBytes", "diff", "utf8ToBytes", "str", "isPosBig", "inRange", "min", "max", "aInRange", "n", "bitLen", "bitGet", "bitSet", "bitMask", "u8n", "u8fr", "arr", "createHmacDrbg", "hashLen", "qByteLen", "hmacFn", "v", "k", "reset", "reseed", "seed", "gen", "sl", "pred", "validatorFns", "val", "object", "validateObject", "validators", "optValidators", "checkField", "fieldName", "type", "isOptional", "checkVal", "notImplemented", "memoized", "fn", "map", "arg", "args", "computed", "HMAC", "hash", "_key", "ahash", "key", "buf", "oHash", "iHash", "hmac", "message", "_3n", "_4n", "_5n", "_8n", "mod", "result", "pow", "power", "modulo", "pow2", "x", "invert", "number", "u", "q", "r", "m", "tonelliShanks", "P", "legendreC", "Q", "S", "Z", "p1div4", "Fp", "root", "Q1div2", "g", "t2", "ge", "FpSqrt", "c1", "nv", "FIELD_FIELDS", "validateField", "field", "initial", "opts", "FpPow", "f", "p", "d", "FpInvertBatch", "nums", "tmp", "lastMultiplied", "acc", "inverted", "nLength", "nBitLength", "_nBitLength", "nByteLength", "Field", "ORDER", "redef", "BITS", "BYTES", "sqrtP", "lhs", "rhs", "lst", "getFieldBytesLength", "fieldOrder", "bitLength", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "mapHashToField", "fieldLen", "minLen", "reduced", "constTimeNegate", "condition", "neg", "validateW", "W", "bits", "calcWOpts", "windows", "windowSize", "validateMSMPoints", "points", "validateMSMScalars", "scalars", "s", "pointPrecomputes", "pointWindowSizes", "getW", "wNAF", "elm", "base", "window", "precomputes", "mask", "maxNumber", "shiftBy", "wbits", "offset1", "offset2", "cond1", "cond2", "curr", "transform", "comp", "prev", "pippenger", "fieldN", "zero", "MASK", "buckets", "lastBits", "j", "scalar", "resI", "sumI", "validateBasic", "curve", "validateSigVerOpts", "validatePointOpts", "ut.validateObject", "endo", "b2n", "h2b", "ut", "DERErr", "DER", "tag", "dataLen", "ut.numberToHexUnpadded", "lenLen", "first", "isLong", "lengthBytes", "int", "tlv", "ut.abytes", "seqBytes", "seqLeftBytes", "rBytes", "rLeftBytes", "sBytes", "sLeftBytes", "sig", "rs", "ss", "seq", "weierstrassPoints", "CURVE", "Fn", "_c", "point", "_isCompressed", "ut.concatBytes", "fromBytes", "tail", "y", "weierstrassEquation", "x2", "x3", "isWithinCurveOrder", "ut.inRange", "normPrivateKeyToScalar", "lengths", "wrapPrivateKey", "N", "ut.isBytes", "ut.bytesToHex", "ut.bytesToNumberBE", "ut.aInRange", "assertPrjPoint", "other", "Point", "toAffineMemo", "iz", "z", "is0", "ax", "ay", "zz", "assertValidMemo", "left", "right", "px", "py", "pz", "toInv", "privateKey", "wnaf", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "U1", "U2", "b3", "X3", "Y3", "Z3", "t0", "t1", "t3", "t4", "t5", "sc", "I", "k1neg", "k1", "k2neg", "k2", "k1p", "k2p", "fake", "f1p", "f2p", "mul", "cofactor", "isTorsionFree", "clearCofactor", "isCompressed", "_bits", "validateOpts", "<PERSON><PERSON><PERSON><PERSON>", "curveDef", "CURVE_ORDER", "compressedLen", "uncompressedLen", "modN", "invN", "cat", "head", "y2", "sqrtError", "suffix", "isYOdd", "cl", "ul", "numToNByteStr", "ut.numberToBytesBE", "isBiggerThanHalfOrder", "HALF", "normalizeS", "slcNum", "from", "Signature", "recovery", "msgHash", "rec", "bits2int_modN", "radj", "prefix", "R", "ir", "u1", "u2", "ut.hexToBytes", "utils", "getPublicKey", "isProbPub", "getSharedSecret", "privateA", "publicB", "bits2int", "delta", "ORDER_MASK", "ut.bitMask", "int2octets", "prepSig", "defaultSigOpts", "randomBytes", "lowS", "prehash", "ent", "h1int", "seedArgs", "k2sig", "kBytes", "ik", "normS", "defaultVerOpts", "sign", "privKey", "ut.createHmacDrbg", "verify", "signature", "public<PERSON>ey", "sg", "format", "isHex", "isObj", "_sig", "<PERSON><PERSON><PERSON><PERSON>", "is", "_a", "getHash", "msgs", "createCurve", "defHash", "create", "secp256k1P", "secp256k1N", "divNearest", "sqrtMod", "_6n", "_11n", "_22n", "_23n", "_44n", "_88n", "b2", "b6", "b9", "b11", "b22", "b44", "b88", "b176", "b220", "b223", "Fpk1", "secp256k1", "a1", "b1", "a2", "POW_2_128", "c2"], "mappings": "gNAOO,SAASA,GAAaC,EAAMC,EAAYC,EAAOC,EAAM,CACxD,GAAI,OAAOH,EAAK,cAAiB,WAC7B,OAAOA,EAAK,aAAaC,EAAYC,EAAOC,CAAI,EACpD,MAAMC,EAAO,OAAO,EAAE,EAChBC,EAAW,OAAO,UAAU,EAC5BC,EAAK,OAAQJ,GAASE,EAAQC,CAAQ,EACtCE,EAAK,OAAOL,EAAQG,CAAQ,EAC5BG,EAAIL,EAAO,EAAI,EACfM,EAAIN,EAAO,EAAI,EACrBH,EAAK,UAAUC,EAAaO,EAAGF,EAAIH,CAAI,EACvCH,EAAK,UAAUC,EAAaQ,EAAGF,EAAIJ,CAAI,CAC3C,CAEO,SAASO,GAAIC,EAAGC,EAAGC,EAAG,CACzB,OAAQF,EAAIC,EAAM,CAACD,EAAIE,CAC3B,CAEO,SAASC,GAAIH,EAAGC,EAAGC,EAAG,CACzB,OAAQF,EAAIC,EAAMD,EAAIE,EAAMD,EAAIC,CACpC,CAKO,MAAME,WAAeC,EAAK,CAC7B,YAAYC,EAAUC,EAAWC,EAAWhB,EAAM,CAC9C,MAAK,EACL,KAAK,SAAWc,EAChB,KAAK,UAAYC,EACjB,KAAK,UAAYC,EACjB,KAAK,KAAOhB,EACZ,KAAK,SAAW,GAChB,KAAK,OAAS,EACd,KAAK,IAAM,EACX,KAAK,UAAY,GACjB,KAAK,OAAS,IAAI,WAAWc,CAAQ,EACrC,KAAK,KAAOG,GAAW,KAAK,MAAM,CACtC,CACA,OAAOC,EAAM,CACTC,GAAQ,IAAI,EACZ,KAAM,CAAE,KAAAtB,EAAM,OAAAuB,EAAQ,SAAAN,CAAQ,EAAK,KACnCI,EAAOG,GAAQH,CAAI,EACnB,MAAMI,EAAMJ,EAAK,OACjB,QAASK,EAAM,EAAGA,EAAMD,GAAM,CAC1B,MAAME,EAAO,KAAK,IAAIV,EAAW,KAAK,IAAKQ,EAAMC,CAAG,EAEpD,GAAIC,IAASV,EAAU,CACnB,MAAMW,EAAWR,GAAWC,CAAI,EAChC,KAAOJ,GAAYQ,EAAMC,EAAKA,GAAOT,EACjC,KAAK,QAAQW,EAAUF,CAAG,EAC9B,QACJ,CACAH,EAAO,IAAIF,EAAK,SAASK,EAAKA,EAAMC,CAAI,EAAG,KAAK,GAAG,EACnD,KAAK,KAAOA,EACZD,GAAOC,EACH,KAAK,MAAQV,IACb,KAAK,QAAQjB,EAAM,CAAC,EACpB,KAAK,IAAM,EAEnB,CACA,YAAK,QAAUqB,EAAK,OACpB,KAAK,WAAU,EACR,IACX,CACA,WAAWQ,EAAK,CACZP,GAAQ,IAAI,EACZQ,GAAQD,EAAK,IAAI,EACjB,KAAK,SAAW,GAIhB,KAAM,CAAE,OAAAN,EAAQ,KAAAvB,EAAM,SAAAiB,EAAU,KAAAd,CAAI,EAAK,KACzC,GAAI,CAAE,IAAAuB,CAAG,EAAK,KAEdH,EAAOG,GAAK,EAAI,IAChB,KAAK,OAAO,SAASA,CAAG,EAAE,KAAK,CAAC,EAG5B,KAAK,UAAYT,EAAWS,IAC5B,KAAK,QAAQ1B,EAAM,CAAC,EACpB0B,EAAM,GAGV,QAASK,EAAIL,EAAKK,EAAId,EAAUc,IAC5BR,EAAOQ,CAAC,EAAI,EAIhBhC,GAAaC,EAAMiB,EAAW,EAAG,OAAO,KAAK,OAAS,CAAC,EAAGd,CAAI,EAC9D,KAAK,QAAQH,EAAM,CAAC,EACpB,MAAMgC,EAAQZ,GAAWS,CAAG,EACtBJ,EAAM,KAAK,UAEjB,GAAIA,EAAM,EACN,MAAM,IAAI,MAAM,6CAA6C,EACjE,MAAMQ,EAASR,EAAM,EACfS,EAAQ,KAAK,IAAG,EACtB,GAAID,EAASC,EAAM,OACf,MAAM,IAAI,MAAM,oCAAoC,EACxD,QAASH,EAAI,EAAGA,EAAIE,EAAQF,IACxBC,EAAM,UAAU,EAAID,EAAGG,EAAMH,CAAC,EAAG5B,CAAI,CAC7C,CACA,QAAS,CACL,KAAM,CAAE,OAAAoB,EAAQ,UAAAL,CAAS,EAAK,KAC9B,KAAK,WAAWK,CAAM,EACtB,MAAMY,EAAMZ,EAAO,MAAM,EAAGL,CAAS,EACrC,YAAK,QAAO,EACLiB,CACX,CACA,WAAWC,EAAI,CACXA,IAAOA,EAAK,IAAI,KAAK,aACrBA,EAAG,IAAI,GAAG,KAAK,IAAG,CAAE,EACpB,KAAM,CAAE,SAAAnB,EAAU,OAAAM,EAAQ,OAAAc,EAAQ,SAAAC,EAAU,UAAAC,EAAW,IAAAb,CAAG,EAAK,KAC/D,OAAAU,EAAG,OAASC,EACZD,EAAG,IAAMV,EACTU,EAAG,SAAWE,EACdF,EAAG,UAAYG,EACXF,EAASpB,GACTmB,EAAG,OAAO,IAAIb,CAAM,EACjBa,CACX,CACJ,CCnHA,MAAMI,GAA2B,IAAI,YAAY,CAC7C,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WACpF,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACpF,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACpF,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACxF,CAAC,EAGKC,EAA4B,IAAI,YAAY,CAC9C,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACxF,CAAC,EAKKC,EAA2B,IAAI,YAAY,EAAE,EAC5C,MAAMC,WAAe5B,EAAO,CAC/B,aAAc,CACV,MAAM,GAAI,GAAI,EAAG,EAAK,EAGtB,KAAK,EAAI0B,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,EACxB,KAAK,EAAIA,EAAU,CAAC,EAAI,CAC5B,CACA,KAAM,CACF,KAAM,CAAE,EAAAG,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,CAAC,EAAK,KACnC,MAAO,CAACP,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,CAAC,CAClC,CAEA,IAAIP,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG,CACxB,KAAK,EAAIP,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,EACb,KAAK,EAAIC,EAAI,CACjB,CACA,QAAQnD,EAAMoD,EAAQ,CAElB,QAASrB,EAAI,EAAGA,EAAI,GAAIA,IAAKqB,GAAU,EACnCV,EAASX,CAAC,EAAI/B,EAAK,UAAUoD,EAAQ,EAAK,EAC9C,QAASrB,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAC1B,MAAMsB,EAAMX,EAASX,EAAI,EAAE,EACrBuB,EAAKZ,EAASX,EAAI,CAAC,EACnBwB,EAAKC,EAAKH,EAAK,CAAC,EAAIG,EAAKH,EAAK,EAAE,EAAKA,IAAQ,EAC7CI,EAAKD,EAAKF,EAAI,EAAE,EAAIE,EAAKF,EAAI,EAAE,EAAKA,IAAO,GACjDZ,EAASX,CAAC,EAAK0B,EAAKf,EAASX,EAAI,CAAC,EAAIwB,EAAKb,EAASX,EAAI,EAAE,EAAK,CACnE,CAEA,GAAI,CAAE,EAAAa,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,EAAAC,CAAC,EAAK,KACjC,QAASpB,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MAAM2B,EAASF,EAAKR,EAAG,CAAC,EAAIQ,EAAKR,EAAG,EAAE,EAAIQ,EAAKR,EAAG,EAAE,EAC9CW,EAAMR,EAAIO,EAAShD,GAAIsC,EAAGC,EAAGC,CAAC,EAAIV,GAAST,CAAC,EAAIW,EAASX,CAAC,EAAK,EAE/D6B,GADSJ,EAAKZ,EAAG,CAAC,EAAIY,EAAKZ,EAAG,EAAE,EAAIY,EAAKZ,EAAG,EAAE,GAC/B9B,GAAI8B,EAAGC,EAAGC,CAAC,EAAK,EACrCK,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKD,EAAIY,EAAM,EACfZ,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKe,EAAKC,EAAM,CACpB,CAEAhB,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnBC,EAAKA,EAAI,KAAK,EAAK,EACnB,KAAK,IAAIP,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,CAAC,CACnC,CACA,YAAa,CACTT,EAAS,KAAK,CAAC,CACnB,CACA,SAAU,CACN,KAAK,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAC/B,KAAK,OAAO,KAAK,CAAC,CACtB,CACJ,CAmBO,MAAMmB,GAAyBC,GAAgB,IAAM,IAAInB,EAAQ,EC1HxE,sEAKA,MAAMoB,GAAsB,OAAO,CAAC,EAC9BC,GAAsB,OAAO,CAAC,EAC9BC,GAAsB,OAAO,CAAC,EAC7B,SAASC,GAAQvD,EAAG,CACvB,OAAOA,aAAa,YAAe,YAAY,OAAOA,CAAC,GAAKA,EAAE,YAAY,OAAS,YACvF,CACO,SAASwD,GAAOC,EAAM,CACzB,GAAI,CAACF,GAAQE,CAAI,EACb,MAAM,IAAI,MAAM,qBAAqB,CAC7C,CACO,SAASC,GAAMC,EAAOpE,EAAO,CAChC,GAAI,OAAOA,GAAU,UACjB,MAAM,IAAI,MAAMoE,EAAQ,0BAA4BpE,CAAK,CACjE,CAEA,MAAMqE,GAAwB,MAAM,KAAK,CAAE,OAAQ,GAAG,EAAI,CAACC,EAAGzC,IAAMA,EAAE,SAAS,EAAE,EAAE,SAAS,EAAG,GAAG,CAAC,EAI5F,SAAS0C,GAAWC,EAAO,CAC9BP,GAAOO,CAAK,EAEZ,IAAIC,EAAM,GACV,QAAS5C,EAAI,EAAGA,EAAI2C,EAAM,OAAQ3C,IAC9B4C,GAAOJ,GAAMG,EAAM3C,CAAC,CAAC,EAEzB,OAAO4C,CACX,CACO,SAASC,GAAoBC,EAAK,CACrC,MAAMF,EAAME,EAAI,SAAS,EAAE,EAC3B,OAAOF,EAAI,OAAS,EAAI,IAAMA,EAAMA,CACxC,CACO,SAASG,GAAYH,EAAK,CAC7B,GAAI,OAAOA,GAAQ,SACf,MAAM,IAAI,MAAM,4BAA8B,OAAOA,CAAG,EAC5D,OAAOA,IAAQ,GAAKZ,GAAM,OAAO,KAAOY,CAAG,CAC/C,CAEA,MAAMI,EAAS,CAAE,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAG,EAC5D,SAASC,GAAcC,EAAI,CACvB,GAAIA,GAAMF,EAAO,IAAME,GAAMF,EAAO,GAChC,OAAOE,EAAKF,EAAO,GACvB,GAAIE,GAAMF,EAAO,GAAKE,GAAMF,EAAO,EAC/B,OAAOE,GAAMF,EAAO,EAAI,IAC5B,GAAIE,GAAMF,EAAO,GAAKE,GAAMF,EAAO,EAC/B,OAAOE,GAAMF,EAAO,EAAI,GAEhC,CAIO,SAASG,GAAWP,EAAK,CAC5B,GAAI,OAAOA,GAAQ,SACf,MAAM,IAAI,MAAM,4BAA8B,OAAOA,CAAG,EAC5D,MAAMQ,EAAKR,EAAI,OACTS,EAAKD,EAAK,EAChB,GAAIA,EAAK,EACL,MAAM,IAAI,MAAM,mDAAqDA,CAAE,EAC3E,MAAME,EAAQ,IAAI,WAAWD,CAAE,EAC/B,QAASE,EAAK,EAAGC,EAAK,EAAGD,EAAKF,EAAIE,IAAMC,GAAM,EAAG,CAC7C,MAAMC,EAAKR,GAAcL,EAAI,WAAWY,CAAE,CAAC,EACrCE,EAAKT,GAAcL,EAAI,WAAWY,EAAK,CAAC,CAAC,EAC/C,GAAIC,IAAO,QAAaC,IAAO,OAAW,CACtC,MAAMC,EAAOf,EAAIY,CAAE,EAAIZ,EAAIY,EAAK,CAAC,EACjC,MAAM,IAAI,MAAM,+CAAiDG,EAAO,cAAgBH,CAAE,CAC9F,CACAF,EAAMC,CAAE,EAAIE,EAAK,GAAKC,CAC1B,CACA,OAAOJ,CACX,CAEO,SAASM,GAAgBjB,EAAO,CACnC,OAAOI,GAAYL,GAAWC,CAAK,CAAC,CACxC,CACO,SAASkB,GAAgBlB,EAAO,CACnC,OAAAP,GAAOO,CAAK,EACLI,GAAYL,GAAW,WAAW,KAAKC,CAAK,EAAE,QAAO,CAAE,CAAC,CACnE,CACO,SAASmB,GAAgB,EAAGpE,EAAK,CACpC,OAAOyD,GAAW,EAAE,SAAS,EAAE,EAAE,SAASzD,EAAM,EAAG,GAAG,CAAC,CAC3D,CACO,SAASqE,GAAgB,EAAGrE,EAAK,CACpC,OAAOoE,GAAgB,EAAGpE,CAAG,EAAE,QAAO,CAC1C,CAEO,SAASsE,GAAmB,EAAG,CAClC,OAAOb,GAAWN,GAAoB,CAAC,CAAC,CAC5C,CAUO,SAASoB,EAAY1B,EAAOK,EAAKsB,EAAgB,CACpD,IAAI9D,EACJ,GAAI,OAAOwC,GAAQ,SACf,GAAI,CACAxC,EAAM+C,GAAWP,CAAG,CACxB,OACOuB,EAAG,CACN,MAAM,IAAI,MAAM5B,EAAQ,6CAA+C4B,CAAC,CAC5E,SAEKhC,GAAQS,CAAG,EAGhBxC,EAAM,WAAW,KAAKwC,CAAG,MAGzB,OAAM,IAAI,MAAML,EAAQ,mCAAmC,EAE/D,MAAM7C,EAAMU,EAAI,OAChB,GAAI,OAAO8D,GAAmB,UAAYxE,IAAQwE,EAC9C,MAAM,IAAI,MAAM3B,EAAQ,cAAgB2B,EAAiB,kBAAoBxE,CAAG,EACpF,OAAOU,CACX,CAIO,SAASgE,MAAeC,EAAQ,CACnC,IAAIC,EAAM,EACV,QAAStE,EAAI,EAAGA,EAAIqE,EAAO,OAAQrE,IAAK,CACpC,MAAMpB,EAAIyF,EAAOrE,CAAC,EAClBoC,GAAOxD,CAAC,EACR0F,GAAO1F,EAAE,MACb,CACA,MAAMwB,EAAM,IAAI,WAAWkE,CAAG,EAC9B,QAAStE,EAAI,EAAGuE,EAAM,EAAGvE,EAAIqE,EAAO,OAAQrE,IAAK,CAC7C,MAAMpB,EAAIyF,EAAOrE,CAAC,EAClBI,EAAI,IAAIxB,EAAG2F,CAAG,EACdA,GAAO3F,EAAE,MACb,CACA,OAAOwB,CACX,CAEO,SAASoE,GAAW5F,EAAGC,EAAG,CAC7B,GAAID,EAAE,SAAWC,EAAE,OACf,MAAO,GACX,IAAI4F,EAAO,EACX,QAASzE,EAAI,EAAGA,EAAIpB,EAAE,OAAQoB,IAC1ByE,GAAQ7F,EAAEoB,CAAC,EAAInB,EAAEmB,CAAC,EACtB,OAAOyE,IAAS,CACpB,CAIO,SAASC,GAAYC,EAAK,CAC7B,GAAI,OAAOA,GAAQ,SACf,MAAM,IAAI,MAAM,iBAAiB,EACrC,OAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAOA,CAAG,CAAC,CACvD,CAEA,MAAMC,GAAY,GAAM,OAAO,GAAM,UAAY5C,IAAO,EACjD,SAAS6C,GAAQ,EAAGC,EAAKC,EAAK,CACjC,OAAOH,GAAS,CAAC,GAAKA,GAASE,CAAG,GAAKF,GAASG,CAAG,GAAKD,GAAO,GAAK,EAAIC,CAC5E,CAMO,SAASC,GAASzC,EAAO0C,EAAGH,EAAKC,EAAK,CAMzC,GAAI,CAACF,GAAQI,EAAGH,EAAKC,CAAG,EACpB,MAAM,IAAI,MAAM,kBAAoBxC,EAAQ,KAAOuC,EAAM,WAAaC,EAAM,SAAWE,CAAC,CAChG,CAMO,SAASC,GAAO,EAAG,CACtB,IAAIxF,EACJ,IAAKA,EAAM,EAAG,EAAIsC,GAAK,IAAMC,GAAKvC,GAAO,EACrC,CACJ,OAAOA,CACX,CAMO,SAASyF,GAAO,EAAGxF,EAAK,CAC3B,OAAQ,GAAK,OAAOA,CAAG,EAAKsC,EAChC,CAIO,SAASmD,GAAO,EAAGzF,EAAKxB,EAAO,CAClC,OAAO,GAAMA,EAAQ8D,GAAMD,KAAQ,OAAOrC,CAAG,CACjD,CAKO,MAAM0F,GAAW,IAAOnD,IAAO,OAAO,EAAI,CAAC,GAAKD,GAEjDqD,GAAOhG,GAAS,IAAI,WAAWA,CAAI,EACnCiG,GAAQC,GAAQ,WAAW,KAAKA,CAAG,EAQlC,SAASC,GAAeC,EAASC,EAAUC,EAAQ,CACtD,GAAI,OAAOF,GAAY,UAAYA,EAAU,EACzC,MAAM,IAAI,MAAM,0BAA0B,EAC9C,GAAI,OAAOC,GAAa,UAAYA,EAAW,EAC3C,MAAM,IAAI,MAAM,2BAA2B,EAC/C,GAAI,OAAOC,GAAW,WAClB,MAAM,IAAI,MAAM,2BAA2B,EAE/C,IAAIC,EAAIP,GAAII,CAAO,EACfI,EAAIR,GAAII,CAAO,EACf1F,EAAI,EACR,MAAM+F,EAAQ,IAAM,CAChBF,EAAE,KAAK,CAAC,EACRC,EAAE,KAAK,CAAC,EACR9F,EAAI,CACR,EACMvB,EAAI,IAAII,IAAM+G,EAAOE,EAAGD,EAAG,GAAGhH,CAAC,EAC/BmH,EAAS,CAACC,EAAOX,OAAU,CAE7BQ,EAAIrH,EAAE8G,GAAK,CAAC,CAAI,CAAC,EAAGU,CAAI,EACxBJ,EAAIpH,EAAC,EACDwH,EAAK,SAAW,IAEpBH,EAAIrH,EAAE8G,GAAK,CAAC,CAAI,CAAC,EAAGU,CAAI,EACxBJ,EAAIpH,EAAC,EACT,EACMyH,EAAM,IAAM,CAEd,GAAIlG,KAAO,IACP,MAAM,IAAI,MAAM,yBAAyB,EAC7C,IAAIN,EAAM,EACV,MAAMI,EAAM,CAAA,EACZ,KAAOJ,EAAMiG,GAAU,CACnBE,EAAIpH,EAAC,EACL,MAAM0H,EAAKN,EAAE,MAAK,EAClB/F,EAAI,KAAKqG,CAAE,EACXzG,GAAOmG,EAAE,MACb,CACA,OAAOzB,GAAY,GAAGtE,CAAG,CAC7B,EAUA,MATiB,CAACmG,EAAMG,IAAS,CAC7BL,EAAK,EACLC,EAAOC,CAAI,EACX,IAAI7F,EACJ,KAAO,EAAEA,EAAMgG,EAAKF,EAAG,CAAE,IACrBF,EAAM,EACV,OAAAD,EAAK,EACE3F,CACX,CAEJ,CAEA,MAAMiG,GAAe,CACjB,OAASC,GAAQ,OAAOA,GAAQ,SAChC,SAAWA,GAAQ,OAAOA,GAAQ,WAClC,QAAUA,GAAQ,OAAOA,GAAQ,UACjC,OAASA,GAAQ,OAAOA,GAAQ,SAChC,mBAAqBA,GAAQ,OAAOA,GAAQ,UAAYnE,GAAQmE,CAAG,EACnE,cAAgBA,GAAQ,OAAO,cAAcA,CAAG,EAChD,MAAQA,GAAQ,MAAM,QAAQA,CAAG,EACjC,MAAO,CAACA,EAAKC,IAAWA,EAAO,GAAG,QAAQD,CAAG,EAC7C,KAAOA,GAAQ,OAAOA,GAAQ,YAAc,OAAO,cAAcA,EAAI,SAAS,CAClF,EAEO,SAASE,GAAeD,EAAQE,EAAYC,EAAgB,CAAA,EAAI,CACnE,MAAMC,EAAa,CAACC,EAAWC,EAAMC,IAAe,CAChD,MAAMC,EAAWV,GAAaQ,CAAI,EAClC,GAAI,OAAOE,GAAa,WACpB,MAAM,IAAI,MAAM,4BAA4B,EAChD,MAAMT,EAAMC,EAAOK,CAAS,EAC5B,GAAI,EAAAE,GAAcR,IAAQ,SAEtB,CAACS,EAAST,EAAKC,CAAM,EACrB,MAAM,IAAI,MAAM,SAAW,OAAOK,CAAS,EAAI,yBAA2BC,EAAO,SAAWP,CAAG,CAEvG,EACA,SAAW,CAACM,EAAWC,CAAI,IAAK,OAAO,QAAQJ,CAAU,EACrDE,EAAWC,EAAWC,EAAM,EAAK,EACrC,SAAW,CAACD,EAAWC,CAAI,IAAK,OAAO,QAAQH,CAAa,EACxDC,EAAWC,EAAWC,EAAM,EAAI,EACpC,OAAON,CACX,CAYO,MAAMS,GAAiB,IAAM,CAChC,MAAM,IAAI,MAAM,iBAAiB,CACrC,EAKO,SAASC,GAASC,EAAI,CACzB,MAAMC,EAAM,IAAI,QAChB,MAAO,CAACC,KAAQC,IAAS,CACrB,MAAMf,EAAMa,EAAI,IAAIC,CAAG,EACvB,GAAId,IAAQ,OACR,OAAOA,EACX,MAAMgB,EAAWJ,EAAGE,EAAK,GAAGC,CAAI,EAChC,OAAAF,EAAI,IAAIC,EAAKE,CAAQ,EACdA,CACX,CACJ,meCvUO,MAAMC,WAAatI,EAAK,CAC3B,YAAYuI,EAAMC,EAAM,CACpB,MAAK,EACL,KAAK,SAAW,GAChB,KAAK,UAAY,GACjBC,GAAMF,CAAI,EACV,MAAMG,EAAMlI,GAAQgI,CAAI,EAExB,GADA,KAAK,MAAQD,EAAK,OAAM,EACpB,OAAO,KAAK,MAAM,QAAW,WAC7B,MAAM,IAAI,MAAM,qDAAqD,EACzE,KAAK,SAAW,KAAK,MAAM,SAC3B,KAAK,UAAY,KAAK,MAAM,UAC5B,MAAMtI,EAAW,KAAK,SAChBqF,EAAM,IAAI,WAAWrF,CAAQ,EAEnCqF,EAAI,IAAIoD,EAAI,OAASzI,EAAWsI,EAAK,OAAM,EAAG,OAAOG,CAAG,EAAE,OAAM,EAAKA,CAAG,EACxE,QAAS3H,EAAI,EAAGA,EAAIuE,EAAI,OAAQvE,IAC5BuE,EAAIvE,CAAC,GAAK,GACd,KAAK,MAAM,OAAOuE,CAAG,EAErB,KAAK,MAAQiD,EAAK,OAAM,EAExB,QAASxH,EAAI,EAAGA,EAAIuE,EAAI,OAAQvE,IAC5BuE,EAAIvE,CAAC,GAAK,IACd,KAAK,MAAM,OAAOuE,CAAG,EACrBA,EAAI,KAAK,CAAC,CACd,CACA,OAAOqD,EAAK,CACR,OAAArI,GAAQ,IAAI,EACZ,KAAK,MAAM,OAAOqI,CAAG,EACd,IACX,CACA,WAAW9H,EAAK,CACZP,GAAQ,IAAI,EACZ6C,GAAOtC,EAAK,KAAK,SAAS,EAC1B,KAAK,SAAW,GAChB,KAAK,MAAM,WAAWA,CAAG,EACzB,KAAK,MAAM,OAAOA,CAAG,EACrB,KAAK,MAAM,WAAWA,CAAG,EACzB,KAAK,QAAO,CAChB,CACA,QAAS,CACL,MAAMA,EAAM,IAAI,WAAW,KAAK,MAAM,SAAS,EAC/C,YAAK,WAAWA,CAAG,EACZA,CACX,CACA,WAAWO,EAAI,CAEXA,IAAOA,EAAK,OAAO,OAAO,OAAO,eAAe,IAAI,EAAG,CAAA,CAAE,GACzD,KAAM,CAAE,MAAAwH,EAAO,MAAAC,EAAO,SAAAvH,EAAU,UAAAC,EAAW,SAAAtB,EAAU,UAAAC,CAAS,EAAK,KACnE,OAAAkB,EAAKA,EACLA,EAAG,SAAWE,EACdF,EAAG,UAAYG,EACfH,EAAG,SAAWnB,EACdmB,EAAG,UAAYlB,EACfkB,EAAG,MAAQwH,EAAM,WAAWxH,EAAG,KAAK,EACpCA,EAAG,MAAQyH,EAAM,WAAWzH,EAAG,KAAK,EAC7BA,CACX,CACA,SAAU,CACN,KAAK,UAAY,GACjB,KAAK,MAAM,QAAO,EAClB,KAAK,MAAM,QAAO,CACtB,CACJ,CAWO,MAAM0H,GAAO,CAACP,EAAMG,EAAKK,IAAY,IAAIT,GAAKC,EAAMG,CAAG,EAAE,OAAOK,CAAO,EAAE,OAAM,EACtFD,GAAK,OAAS,CAACP,EAAMG,IAAQ,IAAIJ,GAAKC,EAAMG,CAAG,EC5E/C,sEAGA,MAAM3F,EAAM,OAAO,CAAC,EAAGC,EAAM,OAAO,CAAC,EAAGC,GAAsB,OAAO,CAAC,EAAG+F,GAAsB,OAAO,CAAC,EAEjGC,GAAsB,OAAO,CAAC,EAAGC,GAAsB,OAAO,CAAC,EAAGC,GAAsB,OAAO,CAAC,EAI/F,SAASC,EAAIzJ,EAAGC,EAAG,CACtB,MAAMyJ,EAAS1J,EAAIC,EACnB,OAAOyJ,GAAUtG,EAAMsG,EAASzJ,EAAIyJ,CACxC,CAQO,SAASC,GAAIzF,EAAK0F,EAAOC,EAAQ,CACpC,GAAID,EAAQxG,EACR,MAAM,IAAI,MAAM,yCAAyC,EAC7D,GAAIyG,GAAUzG,EACV,MAAM,IAAI,MAAM,iBAAiB,EACrC,GAAIyG,IAAWxG,EACX,OAAOD,EACX,IAAI5B,EAAM6B,EACV,KAAOuG,EAAQxG,GACPwG,EAAQvG,IACR7B,EAAOA,EAAM0C,EAAO2F,GACxB3F,EAAOA,EAAMA,EAAO2F,EACpBD,IAAUvG,EAEd,OAAO7B,CACX,CAEO,SAASsI,EAAKC,EAAGH,EAAOC,EAAQ,CACnC,IAAIrI,EAAMuI,EACV,KAAOH,KAAUxG,GACb5B,GAAOA,EACPA,GAAOqI,EAEX,OAAOrI,CACX,CAKO,SAASwI,GAAOC,EAAQJ,EAAQ,CACnC,GAAII,IAAW7G,EACX,MAAM,IAAI,MAAM,kCAAkC,EACtD,GAAIyG,GAAUzG,EACV,MAAM,IAAI,MAAM,0CAA4CyG,CAAM,EAEtE,IAAI7J,EAAIyJ,EAAIQ,EAAQJ,CAAM,EACtB5J,EAAI4J,EAEJE,EAAI3G,EAAc8G,EAAI7G,EAC1B,KAAOrD,IAAMoD,GAAK,CAEd,MAAM+G,EAAIlK,EAAID,EACRoK,EAAInK,EAAID,EACRqK,EAAIN,EAAIG,EAAIC,EAGlBlK,EAAID,EAAGA,EAAIoK,EAAGL,EAAIG,EAAUA,EAAIG,CACpC,CAEA,GADYpK,IACAoD,EACR,MAAM,IAAI,MAAM,wBAAwB,EAC5C,OAAOoG,EAAIM,EAAGF,CAAM,CACxB,CASO,SAASS,GAAcC,EAAG,CAM7B,MAAMC,GAAaD,EAAIlH,GAAOC,GAC9B,IAAImH,EAAGC,EAAGC,EAGV,IAAKF,EAAIF,EAAIlH,EAAKqH,EAAI,EAAGD,EAAInH,KAAQF,EAAKqH,GAAKnH,GAAKoH,IAChD,CAEJ,IAAKC,EAAIrH,GAAKqH,EAAIJ,GAAKZ,GAAIgB,EAAGH,EAAWD,CAAC,IAAMA,EAAIlH,EAAKsH,IAErD,GAAIA,EAAI,IACJ,MAAM,IAAI,MAAM,6CAA6C,EAGrE,GAAID,IAAM,EAAG,CACT,MAAME,GAAUL,EAAIlH,GAAOiG,GAC3B,OAAO,SAAqBuB,EAAIxE,EAAG,CAC/B,MAAMyE,EAAOD,EAAG,IAAIxE,EAAGuE,CAAM,EAC7B,GAAI,CAACC,EAAG,IAAIA,EAAG,IAAIC,CAAI,EAAGzE,CAAC,EACvB,MAAM,IAAI,MAAM,yBAAyB,EAC7C,OAAOyE,CACX,CACJ,CAEA,MAAMC,GAAUN,EAAIpH,GAAOC,GAC3B,OAAO,SAAqBuH,EAAIxE,EAAG,CAE/B,GAAIwE,EAAG,IAAIxE,EAAGmE,CAAS,IAAMK,EAAG,IAAIA,EAAG,GAAG,EACtC,MAAM,IAAI,MAAM,yBAAyB,EAC7C,IAAIT,EAAIM,EAEJM,EAAIH,EAAG,IAAIA,EAAG,IAAIA,EAAG,IAAKF,CAAC,EAAGF,CAAC,EAC/BV,EAAIc,EAAG,IAAIxE,EAAG0E,CAAM,EACpB9K,EAAI4K,EAAG,IAAIxE,EAAGoE,CAAC,EACnB,KAAO,CAACI,EAAG,IAAI5K,EAAG4K,EAAG,GAAG,GAAG,CACvB,GAAIA,EAAG,IAAI5K,EAAG4K,EAAG,IAAI,EACjB,OAAOA,EAAG,KAEd,IAAI,EAAI,EACR,QAASI,EAAKJ,EAAG,IAAI5K,CAAC,EAAG,EAAImK,GACrB,CAAAS,EAAG,IAAII,EAAIJ,EAAG,GAAG,EADO,IAG5BI,EAAKJ,EAAG,IAAII,CAAE,EAGlB,MAAMC,EAAKL,EAAG,IAAIG,EAAG3H,GAAO,OAAO+G,EAAI,EAAI,CAAC,CAAC,EAC7CY,EAAIH,EAAG,IAAIK,CAAE,EACbnB,EAAIc,EAAG,IAAId,EAAGmB,CAAE,EAChBjL,EAAI4K,EAAG,IAAI5K,EAAG+K,CAAC,EACfZ,EAAI,CACR,CACA,OAAOL,CACX,CACJ,CAYO,SAASoB,GAAOZ,EAAG,CAGtB,GAAIA,EAAIjB,KAAQD,GAAK,CAKjB,MAAMuB,GAAUL,EAAIlH,GAAOiG,GAC3B,OAAO,SAAmBuB,EAAIxE,EAAG,CAC7B,MAAMyE,EAAOD,EAAG,IAAIxE,EAAGuE,CAAM,EAE7B,GAAI,CAACC,EAAG,IAAIA,EAAG,IAAIC,CAAI,EAAGzE,CAAC,EACvB,MAAM,IAAI,MAAM,yBAAyB,EAC7C,OAAOyE,CACX,CACJ,CAEA,GAAIP,EAAIf,KAAQD,GAAK,CACjB,MAAM6B,GAAMb,EAAIhB,IAAOC,GACvB,OAAO,SAAmBqB,EAAIxE,EAAG,CAC7B,MAAMvB,EAAK+F,EAAG,IAAIxE,EAAG/C,EAAG,EAClB2D,EAAI4D,EAAG,IAAI/F,EAAIsG,CAAE,EACjBC,EAAKR,EAAG,IAAIxE,EAAGY,CAAC,EAChB,EAAI4D,EAAG,IAAIA,EAAG,IAAIQ,EAAI/H,EAAG,EAAG2D,CAAC,EAC7B6D,EAAOD,EAAG,IAAIQ,EAAIR,EAAG,IAAI,EAAGA,EAAG,GAAG,CAAC,EACzC,GAAI,CAACA,EAAG,IAAIA,EAAG,IAAIC,CAAI,EAAGzE,CAAC,EACvB,MAAM,IAAI,MAAM,yBAAyB,EAC7C,OAAOyE,CACX,CACJ,CAwBA,OAAOR,GAAcC,CAAC,CAC1B,CAIA,MAAMe,GAAe,CACjB,SAAU,UAAW,MAAO,MAAO,MAAO,OAAQ,MAClD,MAAO,MAAO,MAAO,MAAO,MAAO,MACnC,OAAQ,OAAQ,OAAQ,MAC5B,EACO,SAASC,GAAcC,EAAO,CACjC,MAAMC,EAAU,CACZ,MAAO,SACP,KAAM,SACN,MAAO,gBACP,KAAM,eACd,EACUC,EAAOJ,GAAa,OAAO,CAAC/C,EAAKb,KACnCa,EAAIb,CAAG,EAAI,WACJa,GACRkD,CAAO,EACV,OAAO7D,GAAe4D,EAAOE,CAAI,CACrC,CAMO,SAASC,GAAMC,EAAG1H,EAAK0F,EAAO,CAGjC,GAAIA,EAAQxG,EACR,MAAM,IAAI,MAAM,yCAAyC,EAC7D,GAAIwG,IAAUxG,EACV,OAAOwI,EAAE,IACb,GAAIhC,IAAUvG,EACV,OAAOa,EACX,IAAI2H,EAAID,EAAE,IACNE,EAAI5H,EACR,KAAO0F,EAAQxG,GACPwG,EAAQvG,IACRwI,EAAID,EAAE,IAAIC,EAAGC,CAAC,GAClBA,EAAIF,EAAE,IAAIE,CAAC,EACXlC,IAAUvG,EAEd,OAAOwI,CACX,CAKO,SAASE,GAAcH,EAAGI,EAAM,CACnC,MAAMC,EAAM,IAAI,MAAMD,EAAK,MAAM,EAE3BE,EAAiBF,EAAK,OAAO,CAACG,EAAKjI,EAAK9C,IACtCwK,EAAE,IAAI1H,CAAG,EACFiI,GACXF,EAAI7K,CAAC,EAAI+K,EACFP,EAAE,IAAIO,EAAKjI,CAAG,GACtB0H,EAAE,GAAG,EAEFQ,EAAWR,EAAE,IAAIM,CAAc,EAErC,OAAAF,EAAK,YAAY,CAACG,EAAKjI,EAAK9C,IACpBwK,EAAE,IAAI1H,CAAG,EACFiI,GACXF,EAAI7K,CAAC,EAAIwK,EAAE,IAAIO,EAAKF,EAAI7K,CAAC,CAAC,EACnBwK,EAAE,IAAIO,EAAKjI,CAAG,GACtBkI,CAAQ,EACJH,CACX,CAuBO,SAASI,GAAQ,EAAGC,EAAY,CAEnC,MAAMC,EAAcD,IAAe,OAAYA,EAAa,EAAE,SAAS,CAAC,EAAE,OACpEE,EAAc,KAAK,KAAKD,EAAc,CAAC,EAC7C,MAAO,CAAE,WAAYA,EAAa,YAAAC,CAAW,CACjD,CAgBO,SAASC,GAAMC,EAAOpG,EAAQ9G,EAAO,GAAOmN,EAAQ,GAAI,CAC3D,GAAID,GAAStJ,EACT,MAAM,IAAI,MAAM,0CAA4CsJ,CAAK,EACrE,KAAM,CAAE,WAAYE,EAAM,YAAaC,CAAK,EAAKR,GAAQK,EAAOpG,CAAM,EACtE,GAAIuG,EAAQ,KACR,MAAM,IAAI,MAAM,gDAAgD,EACpE,IAAIC,EACJ,MAAM,EAAI,OAAO,OAAO,CACpB,MAAAJ,EACA,KAAAlN,EACA,KAAAoN,EACA,MAAAC,EACA,KAAMpG,GAAQmG,CAAI,EAClB,KAAMxJ,EACN,IAAKC,EACL,OAASa,GAAQuF,EAAIvF,EAAKwI,CAAK,EAC/B,QAAUxI,GAAQ,CACd,GAAI,OAAOA,GAAQ,SACf,MAAM,IAAI,MAAM,+CAAiD,OAAOA,CAAG,EAC/E,OAAOd,GAAOc,GAAOA,EAAMwI,CAC/B,EACA,IAAMxI,GAAQA,IAAQd,EACtB,MAAQc,IAASA,EAAMb,KAASA,EAChC,IAAMa,GAAQuF,EAAI,CAACvF,EAAKwI,CAAK,EAC7B,IAAK,CAACK,EAAKC,IAAQD,IAAQC,EAC3B,IAAM9I,GAAQuF,EAAIvF,EAAMA,EAAKwI,CAAK,EAClC,IAAK,CAACK,EAAKC,IAAQvD,EAAIsD,EAAMC,EAAKN,CAAK,EACvC,IAAK,CAACK,EAAKC,IAAQvD,EAAIsD,EAAMC,EAAKN,CAAK,EACvC,IAAK,CAACK,EAAKC,IAAQvD,EAAIsD,EAAMC,EAAKN,CAAK,EACvC,IAAK,CAACxI,EAAK0F,IAAU+B,GAAM,EAAGzH,EAAK0F,CAAK,EACxC,IAAK,CAACmD,EAAKC,IAAQvD,EAAIsD,EAAM/C,GAAOgD,EAAKN,CAAK,EAAGA,CAAK,EAEtD,KAAOxI,GAAQA,EAAMA,EACrB,KAAM,CAAC6I,EAAKC,IAAQD,EAAMC,EAC1B,KAAM,CAACD,EAAKC,IAAQD,EAAMC,EAC1B,KAAM,CAACD,EAAKC,IAAQD,EAAMC,EAC1B,IAAM9I,GAAQ8F,GAAO9F,EAAKwI,CAAK,EAC/B,KAAMC,EAAM,OACNtG,IACOyG,IACDA,EAAQ3B,GAAOuB,CAAK,GACjBI,EAAM,EAAGzG,CAAC,IAEzB,YAAc4G,GAAQlB,GAAc,EAAGkB,CAAG,EAG1C,KAAM,CAACjN,EAAGC,EAAGC,IAAOA,EAAID,EAAID,EAC5B,QAAUkE,GAAS1E,EAAO2F,GAAgBjB,EAAK2I,CAAK,EAAI3H,GAAgBhB,EAAK2I,CAAK,EAClF,UAAY9I,GAAU,CAClB,GAAIA,EAAM,SAAW8I,EACjB,MAAM,IAAI,MAAM,6BAA+BA,EAAQ,eAAiB9I,EAAM,MAAM,EACxF,OAAOvE,EAAOyF,GAAgBlB,CAAK,EAAIiB,GAAgBjB,CAAK,CAChE,CACR,CAAK,EACD,OAAO,OAAO,OAAO,CAAC,CAC1B,CAkCO,SAASmJ,GAAoBC,EAAY,CAC5C,GAAI,OAAOA,GAAe,SACtB,MAAM,IAAI,MAAM,4BAA4B,EAChD,MAAMC,EAAYD,EAAW,SAAS,CAAC,EAAE,OACzC,OAAO,KAAK,KAAKC,EAAY,CAAC,CAClC,CAQO,SAASC,GAAiBF,EAAY,CACzC,MAAMzL,EAASwL,GAAoBC,CAAU,EAC7C,OAAOzL,EAAS,KAAK,KAAKA,EAAS,CAAC,CACxC,CAcO,SAAS4L,GAAevE,EAAKoE,EAAY3N,EAAO,GAAO,CAC1D,MAAMsB,EAAMiI,EAAI,OACVwE,EAAWL,GAAoBC,CAAU,EACzCK,EAASH,GAAiBF,CAAU,EAE1C,GAAIrM,EAAM,IAAMA,EAAM0M,GAAU1M,EAAM,KAClC,MAAM,IAAI,MAAM,YAAc0M,EAAS,6BAA+B1M,CAAG,EAC7E,MAAMoD,EAAM1E,EAAOyF,GAAgB8D,CAAG,EAAI/D,GAAgB+D,CAAG,EAEvD0E,EAAUhE,EAAIvF,EAAKiJ,EAAa9J,CAAG,EAAIA,EAC7C,OAAO7D,EAAO2F,GAAgBsI,EAASF,CAAQ,EAAIrI,GAAgBuI,EAASF,CAAQ,CACxF,CClcA,sEAGA,MAAMnK,GAAM,OAAO,CAAC,EACdC,GAAM,OAAO,CAAC,EACpB,SAASqK,GAAgBC,EAAWlK,EAAM,CACtC,MAAMmK,EAAMnK,EAAK,OAAM,EACvB,OAAOkK,EAAYC,EAAMnK,CAC7B,CACA,SAASoK,GAAUC,EAAGC,EAAM,CACxB,GAAI,CAAC,OAAO,cAAcD,CAAC,GAAKA,GAAK,GAAKA,EAAIC,EAC1C,MAAM,IAAI,MAAM,qCAAuCA,EAAO,YAAcD,CAAC,CACrF,CACA,SAASE,GAAUF,EAAGC,EAAM,CACxBF,GAAUC,EAAGC,CAAI,EACjB,MAAME,EAAU,KAAK,KAAKF,EAAOD,CAAC,EAAI,EAChCI,EAAa,IAAMJ,EAAI,GAC7B,MAAO,CAAE,QAAAG,EAAS,WAAAC,CAAU,CAChC,CACA,SAASC,GAAkBC,EAAQlO,EAAG,CAClC,GAAI,CAAC,MAAM,QAAQkO,CAAM,EACrB,MAAM,IAAI,MAAM,gBAAgB,EACpCA,EAAO,QAAQ,CAACvC,EAAGzK,IAAM,CACrB,GAAI,EAAEyK,aAAa3L,GACf,MAAM,IAAI,MAAM,0BAA4BkB,CAAC,CACrD,CAAC,CACL,CACA,SAASiN,GAAmBC,EAAS9C,EAAO,CACxC,GAAI,CAAC,MAAM,QAAQ8C,CAAO,EACtB,MAAM,IAAI,MAAM,2BAA2B,EAC/CA,EAAQ,QAAQ,CAACC,EAAGnN,IAAM,CACtB,GAAI,CAACoK,EAAM,QAAQ+C,CAAC,EAChB,MAAM,IAAI,MAAM,2BAA6BnN,CAAC,CACtD,CAAC,CACL,CAGA,MAAMoN,GAAmB,IAAI,QACvBC,GAAmB,IAAI,QAC7B,SAASC,GAAKnE,EAAG,CACb,OAAOkE,GAAiB,IAAIlE,CAAC,GAAK,CACtC,CAeO,SAASoE,GAAKzO,EAAG6N,EAAM,CAC1B,MAAO,CACH,gBAAAL,GACA,eAAekB,EAAK,CAChB,OAAOF,GAAKE,CAAG,IAAM,CACzB,EAEA,aAAaA,EAAKvI,EAAGwF,EAAI3L,EAAE,KAAM,CAC7B,IAAI4L,EAAI8C,EACR,KAAOvI,EAAIjD,IACHiD,EAAIhD,KACJwI,EAAIA,EAAE,IAAIC,CAAC,GACfA,EAAIA,EAAE,OAAM,EACZzF,IAAMhD,GAEV,OAAOwI,CACX,EAaA,iBAAiB+C,EAAKd,EAAG,CACrB,KAAM,CAAE,QAAAG,EAAS,WAAAC,CAAU,EAAKF,GAAUF,EAAGC,CAAI,EAC3CK,EAAS,CAAA,EACf,IAAIvC,EAAI+C,EACJC,EAAOhD,EACX,QAASiD,EAAS,EAAGA,EAASb,EAASa,IAAU,CAC7CD,EAAOhD,EACPuC,EAAO,KAAKS,CAAI,EAEhB,QAASzN,EAAI,EAAGA,EAAI8M,EAAY9M,IAC5ByN,EAAOA,EAAK,IAAIhD,CAAC,EACjBuC,EAAO,KAAKS,CAAI,EAEpBhD,EAAIgD,EAAK,OAAM,CACnB,CACA,OAAOT,CACX,EAQA,KAAKN,EAAGiB,EAAa1I,EAAG,CAGpB,KAAM,CAAE,QAAA4H,EAAS,WAAAC,CAAU,EAAKF,GAAUF,EAAGC,CAAI,EACjD,IAAIlC,EAAI3L,EAAE,KACN0L,EAAI1L,EAAE,KACV,MAAM8O,EAAO,OAAO,GAAKlB,EAAI,CAAC,EACxBmB,EAAY,GAAKnB,EACjBoB,EAAU,OAAOpB,CAAC,EACxB,QAASgB,EAAS,EAAGA,EAASb,EAASa,IAAU,CAC7C,MAAMrM,EAASqM,EAASZ,EAExB,IAAIiB,EAAQ,OAAO9I,EAAI2I,CAAI,EAE3B3I,IAAM6I,EAGFC,EAAQjB,IACRiB,GAASF,EACT5I,GAAKhD,IAST,MAAM+L,EAAU3M,EACV4M,EAAU5M,EAAS,KAAK,IAAI0M,CAAK,EAAI,EACrCG,EAAQR,EAAS,IAAM,EACvBS,EAAQJ,EAAQ,EAClBA,IAAU,EAEVvD,EAAIA,EAAE,IAAI8B,GAAgB4B,EAAOP,EAAYK,CAAO,CAAC,CAAC,EAGtDvD,EAAIA,EAAE,IAAI6B,GAAgB6B,EAAOR,EAAYM,CAAO,CAAC,CAAC,CAE9D,CAMA,MAAO,CAAE,EAAAxD,EAAG,EAAAD,CAAC,CACjB,EASA,WAAWkC,EAAGiB,EAAa1I,EAAG8F,EAAMjM,EAAE,KAAM,CACxC,KAAM,CAAE,QAAA+N,EAAS,WAAAC,CAAU,EAAKF,GAAUF,EAAGC,CAAI,EAC3CiB,EAAO,OAAO,GAAKlB,EAAI,CAAC,EACxBmB,EAAY,GAAKnB,EACjBoB,EAAU,OAAOpB,CAAC,EACxB,QAASgB,EAAS,EAAGA,EAASb,EAASa,IAAU,CAC7C,MAAMrM,EAASqM,EAASZ,EACxB,GAAI7H,IAAMjD,GACN,MAEJ,IAAI+L,EAAQ,OAAO9I,EAAI2I,CAAI,EAS3B,GAPA3I,IAAM6I,EAGFC,EAAQjB,IACRiB,GAASF,EACT5I,GAAKhD,IAEL8L,IAAU,EACV,SACJ,IAAIK,EAAOT,EAAYtM,EAAS,KAAK,IAAI0M,CAAK,EAAI,CAAC,EAC/CA,EAAQ,IACRK,EAAOA,EAAK,OAAM,GAEtBrD,EAAMA,EAAI,IAAIqD,CAAI,CACtB,CACA,OAAOrD,CACX,EACA,eAAe2B,EAAGvD,EAAGkF,EAAW,CAE5B,IAAIC,EAAOlB,GAAiB,IAAIjE,CAAC,EACjC,OAAKmF,IACDA,EAAO,KAAK,iBAAiBnF,EAAGuD,CAAC,EAC7BA,IAAM,GACNU,GAAiB,IAAIjE,EAAGkF,EAAUC,CAAI,CAAC,GAExCA,CACX,EACA,WAAWnF,EAAGlE,EAAGoJ,EAAW,CACxB,MAAM3B,EAAIY,GAAKnE,CAAC,EAChB,OAAO,KAAK,KAAKuD,EAAG,KAAK,eAAeA,EAAGvD,EAAGkF,CAAS,EAAGpJ,CAAC,CAC/D,EACA,iBAAiBkE,EAAGlE,EAAGoJ,EAAWE,EAAM,CACpC,MAAM7B,EAAIY,GAAKnE,CAAC,EAChB,OAAIuD,IAAM,EACC,KAAK,aAAavD,EAAGlE,EAAGsJ,CAAI,EAChC,KAAK,WAAW7B,EAAG,KAAK,eAAeA,EAAGvD,EAAGkF,CAAS,EAAGpJ,EAAGsJ,CAAI,CAC3E,EAIA,cAAcpF,EAAGuD,EAAG,CAChBD,GAAUC,EAAGC,CAAI,EACjBU,GAAiB,IAAIlE,EAAGuD,CAAC,EACzBU,GAAiB,OAAOjE,CAAC,CAC7B,CACR,CACA,CAWO,SAASqF,GAAU1P,EAAG2P,EAAQzB,EAAQE,EAAS,CASlD,GAFAH,GAAkBC,EAAQlO,CAAC,EAC3BmO,GAAmBC,EAASuB,CAAM,EAC9BzB,EAAO,SAAWE,EAAQ,OAC1B,MAAM,IAAI,MAAM,qDAAqD,EACzE,MAAMwB,EAAO5P,EAAE,KACTiP,EAAQ7I,GAAO,OAAO8H,EAAO,MAAM,CAAC,EACpCF,EAAaiB,EAAQ,GAAKA,EAAQ,EAAIA,EAAQ,EAAIA,EAAQ,EAAIA,EAAQ,EAAI,EAC1EY,GAAQ,GAAK7B,GAAc,EAC3B8B,EAAU,IAAI,MAAMD,EAAO,CAAC,EAAE,KAAKD,CAAI,EACvCG,EAAW,KAAK,OAAOJ,EAAO,KAAO,GAAK3B,CAAU,EAAIA,EAC9D,IAAIxI,EAAMoK,EACV,QAAS1O,EAAI6O,EAAU7O,GAAK,EAAGA,GAAK8M,EAAY,CAC5C8B,EAAQ,KAAKF,CAAI,EACjB,QAASI,EAAI,EAAGA,EAAI5B,EAAQ,OAAQ4B,IAAK,CACrC,MAAMC,EAAS7B,EAAQ4B,CAAC,EAClBf,EAAQ,OAAQgB,GAAU,OAAO/O,CAAC,EAAK,OAAO2O,CAAI,CAAC,EACzDC,EAAQb,CAAK,EAAIa,EAAQb,CAAK,EAAE,IAAIf,EAAO8B,CAAC,CAAC,CACjD,CACA,IAAIE,EAAON,EAEX,QAASI,EAAIF,EAAQ,OAAS,EAAGK,EAAOP,EAAMI,EAAI,EAAGA,IACjDG,EAAOA,EAAK,IAAIL,EAAQE,CAAC,CAAC,EAC1BE,EAAOA,EAAK,IAAIC,CAAI,EAGxB,GADA3K,EAAMA,EAAI,IAAI0K,CAAI,EACdhP,IAAM,EACN,QAAS8O,EAAI,EAAGA,EAAIhC,EAAYgC,IAC5BxK,EAAMA,EAAI,OAAM,CAC5B,CACA,OAAOA,CACX,CAgFO,SAAS4K,GAAcC,EAAO,CACjC,OAAAhF,GAAcgF,EAAM,EAAE,EACtB3I,GAAe2I,EAAO,CAClB,EAAG,SACH,EAAG,SACH,GAAI,QACJ,GAAI,OACZ,EAAO,CACC,WAAY,gBACZ,YAAa,eACrB,CAAK,EAEM,OAAO,OAAO,CACjB,GAAGlE,GAAQkE,EAAM,EAAGA,EAAM,UAAU,EACpC,GAAGA,EACE,EAAGA,EAAM,GAAG,KACzB,CAAK,CACL,CC1VA,sEAKA,SAASC,GAAmB9E,EAAM,CAC1BA,EAAK,OAAS,QACdhI,GAAM,OAAQgI,EAAK,IAAI,EACvBA,EAAK,UAAY,QACjBhI,GAAM,UAAWgI,EAAK,OAAO,CACrC,CACA,SAAS+E,GAAkBF,EAAO,CAC9B,MAAM7E,EAAO4E,GAAcC,CAAK,EAChCG,GAAkBhF,EAAM,CACpB,EAAG,QACH,EAAG,OACX,EAAO,CACC,yBAA0B,QAC1B,eAAgB,UAChB,cAAe,WACf,cAAe,WACf,mBAAoB,UACpB,UAAW,WACX,QAAS,UACjB,CAAK,EACD,KAAM,CAAE,KAAAiF,EAAM,GAAA9F,EAAI,EAAA7K,CAAC,EAAK0L,EACxB,GAAIiF,EAAM,CACN,GAAI,CAAC9F,EAAG,IAAI7K,EAAG6K,EAAG,IAAI,EAClB,MAAM,IAAI,MAAM,4EAA4E,EAEhG,GAAI,OAAO8F,GAAS,UAChB,OAAOA,EAAK,MAAS,UACrB,OAAOA,EAAK,aAAgB,WAC5B,MAAM,IAAI,MAAM,uEAAuE,CAE/F,CACA,OAAO,OAAO,OAAO,CAAE,GAAGjF,CAAI,CAAE,CACpC,CACA,KAAM,CAAE,gBAAiBkF,GAAK,WAAYC,EAAG,EAAKC,GAC3C,MAAMC,WAAe,KAAM,CAC9B,YAAY1G,EAAI,GAAI,CAChB,MAAMA,CAAC,CACX,CACJ,CAQO,MAAM2G,EAAM,CAEf,IAAKD,GAEL,KAAM,CACF,OAAQ,CAACE,EAAKvQ,IAAS,CACnB,KAAM,CAAE,IAAK2B,CAAC,EAAK2O,EACnB,GAAIC,EAAM,GAAKA,EAAM,IACjB,MAAM,IAAI5O,EAAE,uBAAuB,EACvC,GAAI3B,EAAK,OAAS,EACd,MAAM,IAAI2B,EAAE,2BAA2B,EAC3C,MAAM6O,EAAUxQ,EAAK,OAAS,EACxBI,EAAMqQ,GAAuBD,CAAO,EAC1C,GAAKpQ,EAAI,OAAS,EAAK,IACnB,MAAM,IAAIuB,EAAE,sCAAsC,EAEtD,MAAM+O,EAASF,EAAU,IAAMC,GAAwBrQ,EAAI,OAAS,EAAK,GAAG,EAAI,GAEhF,OADUqQ,GAAuBF,CAAG,EACzBG,EAAStQ,EAAMJ,CAC9B,EAEA,OAAOuQ,EAAKvQ,EAAM,CACd,KAAM,CAAE,IAAK2B,CAAC,EAAK2O,EACnB,IAAIjQ,EAAM,EACV,GAAIkQ,EAAM,GAAKA,EAAM,IACjB,MAAM,IAAI5O,EAAE,uBAAuB,EACvC,GAAI3B,EAAK,OAAS,GAAKA,EAAKK,GAAK,IAAMkQ,EACnC,MAAM,IAAI5O,EAAE,uBAAuB,EACvC,MAAMgP,EAAQ3Q,EAAKK,GAAK,EAClBuQ,EAAS,CAAC,EAAED,EAAQ,KAC1B,IAAI3P,EAAS,EACb,GAAI,CAAC4P,EACD5P,EAAS2P,MACR,CAED,MAAMD,EAASC,EAAQ,IACvB,GAAI,CAACD,EACD,MAAM,IAAI/O,EAAE,mDAAmD,EACnE,GAAI+O,EAAS,EACT,MAAM,IAAI/O,EAAE,0CAA0C,EAC1D,MAAMkP,EAAc7Q,EAAK,SAASK,EAAKA,EAAMqQ,CAAM,EACnD,GAAIG,EAAY,SAAWH,EACvB,MAAM,IAAI/O,EAAE,uCAAuC,EACvD,GAAIkP,EAAY,CAAC,IAAM,EACnB,MAAM,IAAIlP,EAAE,sCAAsC,EACtD,UAAWpC,KAAKsR,EACZ7P,EAAUA,GAAU,EAAKzB,EAE7B,GADAc,GAAOqQ,EACH1P,EAAS,IACT,MAAM,IAAIW,EAAE,wCAAwC,CAC5D,CACA,MAAM4E,EAAIvG,EAAK,SAASK,EAAKA,EAAMW,CAAM,EACzC,GAAIuF,EAAE,SAAWvF,EACb,MAAM,IAAIW,EAAE,gCAAgC,EAChD,MAAO,CAAE,EAAA4E,EAAG,EAAGvG,EAAK,SAASK,EAAMW,CAAM,CAAC,CAC9C,CACR,EAKI,KAAM,CACF,OAAOwC,EAAK,CACR,KAAM,CAAE,IAAK7B,CAAC,EAAK2O,EACnB,GAAI9M,EAAMd,EACN,MAAM,IAAIf,EAAE,4CAA4C,EAC5D,IAAI2B,EAAMmN,GAAuBjN,CAAG,EAIpC,GAFI,OAAO,SAASF,EAAI,CAAC,EAAG,EAAE,EAAI,IAC9BA,EAAM,KAAOA,GACbA,EAAI,OAAS,EACb,MAAM,IAAI3B,EAAE,gDAAgD,EAChE,OAAO2B,CACX,EACA,OAAOtD,EAAM,CACT,KAAM,CAAE,IAAK2B,CAAC,EAAK2O,EACnB,GAAItQ,EAAK,CAAC,EAAI,IACV,MAAM,IAAI2B,EAAE,qCAAqC,EACrD,GAAI3B,EAAK,CAAC,IAAM,GAAQ,EAAEA,EAAK,CAAC,EAAI,KAChC,MAAM,IAAI2B,EAAE,qDAAqD,EACrE,OAAOuO,GAAIlQ,CAAI,CACnB,CACR,EACI,MAAMsD,EAAK,CAEP,KAAM,CAAE,IAAK3B,EAAG,KAAMmP,EAAK,KAAMC,CAAG,EAAKT,EACnCtQ,EAAO,OAAOsD,GAAQ,SAAW6M,GAAI7M,CAAG,EAAIA,EAClD0N,GAAUhR,CAAI,EACd,KAAM,CAAE,EAAGiR,EAAU,EAAGC,CAAY,EAAKH,EAAI,OAAO,GAAM/Q,CAAI,EAC9D,GAAIkR,EAAa,OACb,MAAM,IAAIvP,EAAE,6CAA6C,EAC7D,KAAM,CAAE,EAAGwP,EAAQ,EAAGC,CAAU,EAAKL,EAAI,OAAO,EAAME,CAAQ,EACxD,CAAE,EAAGI,EAAQ,EAAGC,CAAU,EAAKP,EAAI,OAAO,EAAMK,CAAU,EAChE,GAAIE,EAAW,OACX,MAAM,IAAI3P,EAAE,6CAA6C,EAC7D,MAAO,CAAE,EAAGmP,EAAI,OAAOK,CAAM,EAAG,EAAGL,EAAI,OAAOO,CAAM,CAAC,CACzD,EACA,WAAWE,EAAK,CACZ,KAAM,CAAE,KAAMR,EAAK,KAAMD,CAAG,EAAKR,EAC3BkB,EAAKT,EAAI,OAAO,EAAMD,EAAI,OAAOS,EAAI,CAAC,CAAC,EACvCE,EAAKV,EAAI,OAAO,EAAMD,EAAI,OAAOS,EAAI,CAAC,CAAC,EACvCG,EAAMF,EAAKC,EACjB,OAAOV,EAAI,OAAO,GAAMW,CAAG,CAC/B,CACJ,EAGMhP,EAAM,OAAO,CAAC,EAAGC,EAAM,OAAO,CAAC,EAAS,OAAO,CAAC,EAAE,MAACgG,GAAM,OAAO,CAAC,EAAS,OAAO,CAAC,EACjF,SAASgJ,GAAkB3G,EAAM,CACpC,MAAM4G,EAAQ7B,GAAkB/E,CAAI,EAC9B,CAAE,GAAAb,CAAE,EAAKyH,EACTC,EAAK9F,GAAM6F,EAAM,EAAGA,EAAM,UAAU,EACpCzR,EAAUyR,EAAM,UACjB,CAACE,EAAIC,EAAOC,IAAkB,CAC3B,MAAM1S,EAAIyS,EAAM,SAAQ,EACxB,OAAOE,GAAe,WAAW,KAAK,CAAC,CAAI,CAAC,EAAG9H,EAAG,QAAQ7K,EAAE,CAAC,EAAG6K,EAAG,QAAQ7K,EAAE,CAAC,CAAC,CACnF,GACE4S,EAAYN,EAAM,YAClBvO,GAAU,CAER,MAAM8O,EAAO9O,EAAM,SAAS,CAAC,EAEvBgG,EAAIc,EAAG,UAAUgI,EAAK,SAAS,EAAGhI,EAAG,KAAK,CAAC,EAC3CiI,EAAIjI,EAAG,UAAUgI,EAAK,SAAShI,EAAG,MAAO,EAAIA,EAAG,KAAK,CAAC,EAC5D,MAAO,CAAE,EAAAd,EAAG,EAAA+I,CAAC,CACjB,GAKJ,SAASC,EAAoBhJ,EAAG,CAC5B,KAAM,CAAE,EAAG,EAAA9J,CAAC,EAAKqS,EACXU,EAAKnI,EAAG,IAAId,CAAC,EACbkJ,EAAKpI,EAAG,IAAImI,EAAIjJ,CAAC,EACvB,OAAOc,EAAG,IAAIA,EAAG,IAAIoI,EAAIpI,EAAG,IAAId,EAAG,CAAC,CAAC,EAAG9J,CAAC,CAC7C,CAKA,GAAI,CAAC4K,EAAG,IAAIA,EAAG,IAAIyH,EAAM,EAAE,EAAGS,EAAoBT,EAAM,EAAE,CAAC,EACvD,MAAM,IAAI,MAAM,6CAA6C,EAEjE,SAASY,EAAmBhP,EAAK,CAC7B,OAAOiP,GAAWjP,EAAKb,EAAKiP,EAAM,CAAC,CACvC,CAGA,SAASc,EAAuBrK,EAAK,CACjC,KAAM,CAAE,yBAA0BsK,EAAS,YAAA7G,EAAa,eAAA8G,EAAgB,EAAGC,CAAC,EAAKjB,EACjF,GAAIe,GAAW,OAAOtK,GAAQ,SAAU,CAIpC,GAHIyK,GAAWzK,CAAG,IACdA,EAAM0K,GAAc1K,CAAG,GAEvB,OAAOA,GAAQ,UAAY,CAACsK,EAAQ,SAAStK,EAAI,MAAM,EACvD,MAAM,IAAI,MAAM,qBAAqB,EACzCA,EAAMA,EAAI,SAASyD,EAAc,EAAG,GAAG,CAC3C,CACA,IAAItI,EACJ,GAAI,CACAA,EACI,OAAO6E,GAAQ,SACTA,EACA2K,GAAmBrO,EAAY,cAAe0D,EAAKyD,CAAW,CAAC,CAC7E,MACc,CACV,MAAM,IAAI,MAAM,wCAA0CA,EAAc,eAAiB,OAAOzD,CAAG,CACvG,CACA,OAAIuK,IACApP,EAAMuF,EAAIvF,EAAKqP,CAAC,GACpBI,GAAY,cAAezP,EAAKb,EAAKkQ,CAAC,EAC/BrP,CACX,CACA,SAAS0P,EAAeC,EAAO,CAC3B,GAAI,EAAEA,aAAiBC,GACnB,MAAM,IAAI,MAAM,0BAA0B,CAClD,CAKA,MAAMC,EAAe1L,GAAS,CAACwD,EAAGmI,IAAO,CACrC,KAAM,CAAE,GAAIjK,EAAG,GAAI+I,EAAG,GAAImB,CAAC,EAAKpI,EAEhC,GAAIhB,EAAG,IAAIoJ,EAAGpJ,EAAG,GAAG,EAChB,MAAO,CAAE,EAAAd,EAAG,EAAA+I,CAAC,EACjB,MAAMoB,EAAMrI,EAAE,IAAG,EAGbmI,GAAM,OACNA,EAAKE,EAAMrJ,EAAG,IAAMA,EAAG,IAAIoJ,CAAC,GAChC,MAAME,EAAKtJ,EAAG,IAAId,EAAGiK,CAAE,EACjBI,EAAKvJ,EAAG,IAAIiI,EAAGkB,CAAE,EACjBK,EAAKxJ,EAAG,IAAIoJ,EAAGD,CAAE,EACvB,GAAIE,EACA,MAAO,CAAE,EAAGrJ,EAAG,KAAM,EAAGA,EAAG,IAAI,EACnC,GAAI,CAACA,EAAG,IAAIwJ,EAAIxJ,EAAG,GAAG,EAClB,MAAM,IAAI,MAAM,kBAAkB,EACtC,MAAO,CAAE,EAAGsJ,EAAI,EAAGC,CAAE,CACzB,CAAC,EAGKE,EAAkBjM,GAAUwD,GAAM,CACpC,GAAIA,EAAE,MAAO,CAIT,GAAIyG,EAAM,oBAAsB,CAACzH,EAAG,IAAIgB,EAAE,EAAE,EACxC,OACJ,MAAM,IAAI,MAAM,iBAAiB,CACrC,CAEA,KAAM,CAAE,EAAA9B,EAAG,EAAA+I,GAAMjH,EAAE,SAAQ,EAE3B,GAAI,CAAChB,EAAG,QAAQd,CAAC,GAAK,CAACc,EAAG,QAAQiI,CAAC,EAC/B,MAAM,IAAI,MAAM,0BAA0B,EAC9C,MAAMyB,EAAO1J,EAAG,IAAIiI,CAAC,EACf0B,EAAQzB,EAAoBhJ,CAAC,EACnC,GAAI,CAACc,EAAG,IAAI0J,EAAMC,CAAK,EACnB,MAAM,IAAI,MAAM,mCAAmC,EACvD,GAAI,CAAC3I,EAAE,cAAa,EAChB,MAAM,IAAI,MAAM,wCAAwC,EAC5D,MAAO,EACX,CAAC,EAMD,MAAMiI,CAAM,CACR,YAAYW,EAAIC,EAAIC,EAAI,CAIpB,GAHA,KAAK,GAAKF,EACV,KAAK,GAAKC,EACV,KAAK,GAAKC,EACNF,GAAM,MAAQ,CAAC5J,EAAG,QAAQ4J,CAAE,EAC5B,MAAM,IAAI,MAAM,YAAY,EAChC,GAAIC,GAAM,MAAQ,CAAC7J,EAAG,QAAQ6J,CAAE,EAC5B,MAAM,IAAI,MAAM,YAAY,EAChC,GAAIC,GAAM,MAAQ,CAAC9J,EAAG,QAAQ8J,CAAE,EAC5B,MAAM,IAAI,MAAM,YAAY,EAChC,OAAO,OAAO,IAAI,CACtB,CAGA,OAAO,WAAW9I,EAAG,CACjB,KAAM,CAAE,EAAA9B,EAAG,EAAA+I,CAAC,EAAKjH,GAAK,CAAA,EACtB,GAAI,CAACA,GAAK,CAAChB,EAAG,QAAQd,CAAC,GAAK,CAACc,EAAG,QAAQiI,CAAC,EACrC,MAAM,IAAI,MAAM,sBAAsB,EAC1C,GAAIjH,aAAaiI,EACb,MAAM,IAAI,MAAM,8BAA8B,EAClD,MAAMI,EAAO9S,GAAMyJ,EAAG,IAAIzJ,EAAGyJ,EAAG,IAAI,EAEpC,OAAIqJ,EAAInK,CAAC,GAAKmK,EAAIpB,CAAC,EACRgB,EAAM,KACV,IAAIA,EAAM/J,EAAG+I,EAAGjI,EAAG,GAAG,CACjC,CACA,IAAI,GAAI,CACJ,OAAO,KAAK,SAAQ,EAAG,CAC3B,CACA,IAAI,GAAI,CACJ,OAAO,KAAK,SAAQ,EAAG,CAC3B,CAOA,OAAO,WAAWuD,EAAQ,CACtB,MAAMwG,EAAQ/J,EAAG,YAAYuD,EAAO,IAAKvC,GAAMA,EAAE,EAAE,CAAC,EACpD,OAAOuC,EAAO,IAAI,CAACvC,EAAGzK,IAAMyK,EAAE,SAAS+I,EAAMxT,CAAC,CAAC,CAAC,EAAE,IAAI0S,EAAM,UAAU,CAC1E,CAKA,OAAO,QAAQ9P,EAAK,CAChB,MAAMuG,EAAIuJ,EAAM,WAAWlB,EAAUvN,EAAY,WAAYrB,CAAG,CAAC,CAAC,EAClE,OAAAuG,EAAE,eAAc,EACTA,CACX,CAEA,OAAO,eAAesK,EAAY,CAC9B,OAAOf,EAAM,KAAK,SAASV,EAAuByB,CAAU,CAAC,CACjE,CAEA,OAAO,IAAIzG,EAAQE,EAAS,CACxB,OAAOsB,GAAUkE,EAAOvB,EAAInE,EAAQE,CAAO,CAC/C,CAEA,eAAeJ,EAAY,CACvB4G,EAAK,cAAc,KAAM5G,CAAU,CACvC,CAEA,gBAAiB,CACboG,EAAgB,IAAI,CACxB,CACA,UAAW,CACP,KAAM,CAAE,EAAAxB,CAAC,EAAK,KAAK,SAAQ,EAC3B,GAAIjI,EAAG,MACH,MAAO,CAACA,EAAG,MAAMiI,CAAC,EACtB,MAAM,IAAI,MAAM,6BAA6B,CACjD,CAIA,OAAOe,EAAO,CACVD,EAAeC,CAAK,EACpB,KAAM,CAAE,GAAIkB,EAAI,GAAIC,EAAI,GAAIC,CAAE,EAAK,KAC7B,CAAE,GAAIC,EAAI,GAAIC,EAAI,GAAIC,CAAE,EAAKvB,EAC7BwB,EAAKxK,EAAG,IAAIA,EAAG,IAAIkK,EAAIK,CAAE,EAAGvK,EAAG,IAAIqK,EAAID,CAAE,CAAC,EAC1CK,EAAKzK,EAAG,IAAIA,EAAG,IAAImK,EAAII,CAAE,EAAGvK,EAAG,IAAIsK,EAAIF,CAAE,CAAC,EAChD,OAAOI,GAAMC,CACjB,CAIA,QAAS,CACL,OAAO,IAAIxB,EAAM,KAAK,GAAIjJ,EAAG,IAAI,KAAK,EAAE,EAAG,KAAK,EAAE,CACtD,CAKA,QAAS,CACL,KAAM,CAAE,EAAG,EAAA5K,CAAC,EAAKqS,EACXiD,EAAK1K,EAAG,IAAI5K,EAAGoJ,EAAG,EAClB,CAAE,GAAI0L,EAAI,GAAIC,EAAI,GAAIC,CAAE,EAAK,KACnC,IAAIO,EAAK3K,EAAG,KAAM4K,EAAK5K,EAAG,KAAM6K,EAAK7K,EAAG,KACpC8K,EAAK9K,EAAG,IAAIkK,EAAIA,CAAE,EAClBa,EAAK/K,EAAG,IAAImK,EAAIA,CAAE,EAClB/J,EAAKJ,EAAG,IAAIoK,EAAIA,CAAE,EAClBY,EAAKhL,EAAG,IAAIkK,EAAIC,CAAE,EACtB,OAAAa,EAAKhL,EAAG,IAAIgL,EAAIA,CAAE,EAClBH,EAAK7K,EAAG,IAAIkK,EAAIE,CAAE,EAClBS,EAAK7K,EAAG,IAAI6K,EAAIA,CAAE,EAClBF,EAAK3K,EAAG,IAAI,EAAG6K,CAAE,EACjBD,EAAK5K,EAAG,IAAI0K,EAAItK,CAAE,EAClBwK,EAAK5K,EAAG,IAAI2K,EAAIC,CAAE,EAClBD,EAAK3K,EAAG,IAAI+K,EAAIH,CAAE,EAClBA,EAAK5K,EAAG,IAAI+K,EAAIH,CAAE,EAClBA,EAAK5K,EAAG,IAAI2K,EAAIC,CAAE,EAClBD,EAAK3K,EAAG,IAAIgL,EAAIL,CAAE,EAClBE,EAAK7K,EAAG,IAAI0K,EAAIG,CAAE,EAClBzK,EAAKJ,EAAG,IAAI,EAAGI,CAAE,EACjB4K,EAAKhL,EAAG,IAAI8K,EAAI1K,CAAE,EAClB4K,EAAKhL,EAAG,IAAI,EAAGgL,CAAE,EACjBA,EAAKhL,EAAG,IAAIgL,EAAIH,CAAE,EAClBA,EAAK7K,EAAG,IAAI8K,EAAIA,CAAE,EAClBA,EAAK9K,EAAG,IAAI6K,EAAIC,CAAE,EAClBA,EAAK9K,EAAG,IAAI8K,EAAI1K,CAAE,EAClB0K,EAAK9K,EAAG,IAAI8K,EAAIE,CAAE,EAClBJ,EAAK5K,EAAG,IAAI4K,EAAIE,CAAE,EAClB1K,EAAKJ,EAAG,IAAImK,EAAIC,CAAE,EAClBhK,EAAKJ,EAAG,IAAII,EAAIA,CAAE,EAClB0K,EAAK9K,EAAG,IAAII,EAAI4K,CAAE,EAClBL,EAAK3K,EAAG,IAAI2K,EAAIG,CAAE,EAClBD,EAAK7K,EAAG,IAAII,EAAI2K,CAAE,EAClBF,EAAK7K,EAAG,IAAI6K,EAAIA,CAAE,EAClBA,EAAK7K,EAAG,IAAI6K,EAAIA,CAAE,EACX,IAAI5B,EAAM0B,EAAIC,EAAIC,CAAE,CAC/B,CAKA,IAAI7B,EAAO,CACPD,EAAeC,CAAK,EACpB,KAAM,CAAE,GAAIkB,EAAI,GAAIC,EAAI,GAAIC,CAAE,EAAK,KAC7B,CAAE,GAAIC,EAAI,GAAIC,EAAI,GAAIC,CAAE,EAAKvB,EACnC,IAAI2B,EAAK3K,EAAG,KAAM4K,EAAK5K,EAAG,KAAM6K,EAAK7K,EAAG,KACxC,MAAM7K,EAAIsS,EAAM,EACViD,EAAK1K,EAAG,IAAIyH,EAAM,EAAGjJ,EAAG,EAC9B,IAAIsM,EAAK9K,EAAG,IAAIkK,EAAIG,CAAE,EAClBU,EAAK/K,EAAG,IAAImK,EAAIG,CAAE,EAClBlK,EAAKJ,EAAG,IAAIoK,EAAIG,CAAE,EAClBS,EAAKhL,EAAG,IAAIkK,EAAIC,CAAE,EAClBc,EAAKjL,EAAG,IAAIqK,EAAIC,CAAE,EACtBU,EAAKhL,EAAG,IAAIgL,EAAIC,CAAE,EAClBA,EAAKjL,EAAG,IAAI8K,EAAIC,CAAE,EAClBC,EAAKhL,EAAG,IAAIgL,EAAIC,CAAE,EAClBA,EAAKjL,EAAG,IAAIkK,EAAIE,CAAE,EAClB,IAAIc,EAAKlL,EAAG,IAAIqK,EAAIE,CAAE,EACtB,OAAAU,EAAKjL,EAAG,IAAIiL,EAAIC,CAAE,EAClBA,EAAKlL,EAAG,IAAI8K,EAAI1K,CAAE,EAClB6K,EAAKjL,EAAG,IAAIiL,EAAIC,CAAE,EAClBA,EAAKlL,EAAG,IAAImK,EAAIC,CAAE,EAClBO,EAAK3K,EAAG,IAAIsK,EAAIC,CAAE,EAClBW,EAAKlL,EAAG,IAAIkL,EAAIP,CAAE,EAClBA,EAAK3K,EAAG,IAAI+K,EAAI3K,CAAE,EAClB8K,EAAKlL,EAAG,IAAIkL,EAAIP,CAAE,EAClBE,EAAK7K,EAAG,IAAI7K,EAAG8V,CAAE,EACjBN,EAAK3K,EAAG,IAAI0K,EAAItK,CAAE,EAClByK,EAAK7K,EAAG,IAAI2K,EAAIE,CAAE,EAClBF,EAAK3K,EAAG,IAAI+K,EAAIF,CAAE,EAClBA,EAAK7K,EAAG,IAAI+K,EAAIF,CAAE,EAClBD,EAAK5K,EAAG,IAAI2K,EAAIE,CAAE,EAClBE,EAAK/K,EAAG,IAAI8K,EAAIA,CAAE,EAClBC,EAAK/K,EAAG,IAAI+K,EAAID,CAAE,EAClB1K,EAAKJ,EAAG,IAAI7K,EAAGiL,CAAE,EACjB6K,EAAKjL,EAAG,IAAI0K,EAAIO,CAAE,EAClBF,EAAK/K,EAAG,IAAI+K,EAAI3K,CAAE,EAClBA,EAAKJ,EAAG,IAAI8K,EAAI1K,CAAE,EAClBA,EAAKJ,EAAG,IAAI7K,EAAGiL,CAAE,EACjB6K,EAAKjL,EAAG,IAAIiL,EAAI7K,CAAE,EAClB0K,EAAK9K,EAAG,IAAI+K,EAAIE,CAAE,EAClBL,EAAK5K,EAAG,IAAI4K,EAAIE,CAAE,EAClBA,EAAK9K,EAAG,IAAIkL,EAAID,CAAE,EAClBN,EAAK3K,EAAG,IAAIgL,EAAIL,CAAE,EAClBA,EAAK3K,EAAG,IAAI2K,EAAIG,CAAE,EAClBA,EAAK9K,EAAG,IAAIgL,EAAID,CAAE,EAClBF,EAAK7K,EAAG,IAAIkL,EAAIL,CAAE,EAClBA,EAAK7K,EAAG,IAAI6K,EAAIC,CAAE,EACX,IAAI7B,EAAM0B,EAAIC,EAAIC,CAAE,CAC/B,CACA,SAAS7B,EAAO,CACZ,OAAO,KAAK,IAAIA,EAAM,OAAM,CAAE,CAClC,CACA,KAAM,CACF,OAAO,KAAK,OAAOC,EAAM,IAAI,CACjC,CACA,KAAKzN,EAAG,CACJ,OAAOyO,EAAK,WAAW,KAAMzO,EAAGyN,EAAM,UAAU,CACpD,CAMA,eAAekC,EAAI,CACf,KAAM,CAAE,KAAArF,EAAM,EAAG4C,CAAC,EAAKjB,EACvBqB,GAAY,SAAUqC,EAAI5S,EAAKmQ,CAAC,EAChC,MAAM0C,EAAInC,EAAM,KAChB,GAAIkC,IAAO5S,EACP,OAAO6S,EACX,GAAI,KAAK,OAASD,IAAO3S,EACrB,OAAO,KAEX,GAAI,CAACsN,GAAQmE,EAAK,eAAe,IAAI,EACjC,OAAOA,EAAK,iBAAiB,KAAMkB,EAAIlC,EAAM,UAAU,EAE3D,GAAI,CAAE,MAAAoC,EAAO,GAAAC,EAAI,MAAAC,EAAO,GAAAC,CAAE,EAAK1F,EAAK,YAAYqF,CAAE,EAC9CM,EAAML,EACNM,EAAMN,EACNnK,EAAI,KACR,KAAOqK,EAAK/S,GAAOiT,EAAKjT,GAChB+S,EAAK9S,IACLiT,EAAMA,EAAI,IAAIxK,CAAC,GACfuK,EAAKhT,IACLkT,EAAMA,EAAI,IAAIzK,CAAC,GACnBA,EAAIA,EAAE,OAAM,EACZqK,IAAO9S,EACPgT,IAAOhT,EAEX,OAAI6S,IACAI,EAAMA,EAAI,OAAM,GAChBF,IACAG,EAAMA,EAAI,OAAM,GACpBA,EAAM,IAAIzC,EAAMjJ,EAAG,IAAI0L,EAAI,GAAI5F,EAAK,IAAI,EAAG4F,EAAI,GAAIA,EAAI,EAAE,EAClDD,EAAI,IAAIC,CAAG,CACtB,CAUA,SAASpG,EAAQ,CACb,KAAM,CAAE,KAAAQ,EAAM,EAAG4C,CAAC,EAAKjB,EACvBqB,GAAY,SAAUxD,EAAQ9M,EAAKkQ,CAAC,EACpC,IAAId,EAAO+D,EACX,GAAI7F,EAAM,CACN,KAAM,CAAE,MAAAuF,EAAO,GAAAC,EAAI,MAAAC,EAAO,GAAAC,CAAE,EAAK1F,EAAK,YAAYR,CAAM,EACxD,GAAI,CAAE,EAAGmG,EAAK,EAAGG,CAAG,EAAK,KAAK,KAAKN,CAAE,EACjC,CAAE,EAAGI,EAAK,EAAGG,CAAG,EAAK,KAAK,KAAKL,CAAE,EACrCC,EAAMxB,EAAK,gBAAgBoB,EAAOI,CAAG,EACrCC,EAAMzB,EAAK,gBAAgBsB,EAAOG,CAAG,EACrCA,EAAM,IAAIzC,EAAMjJ,EAAG,IAAI0L,EAAI,GAAI5F,EAAK,IAAI,EAAG4F,EAAI,GAAIA,EAAI,EAAE,EACzD9D,EAAQ6D,EAAI,IAAIC,CAAG,EACnBC,EAAOC,EAAI,IAAIC,CAAG,CACtB,KACK,CACD,KAAM,CAAE,EAAA7K,EAAG,EAAAD,CAAC,EAAK,KAAK,KAAKuE,CAAM,EACjCsC,EAAQ5G,EACR2K,EAAO5K,CACX,CAEA,OAAOkI,EAAM,WAAW,CAACrB,EAAO+D,CAAI,CAAC,EAAE,CAAC,CAC5C,CAOA,qBAAqB/L,EAAGzK,EAAGC,EAAG,CAC1B,MAAMsC,EAAIuR,EAAM,KACV6C,EAAM,CAACpM,EAAGvK,IACVA,IAAMoD,GAAOpD,IAAMqD,GAAO,CAACkH,EAAE,OAAOhI,CAAC,EAAIgI,EAAE,eAAevK,CAAC,EAAIuK,EAAE,SAASvK,CAAC,EAC3E0F,EAAMiR,EAAI,KAAM3W,CAAC,EAAE,IAAI2W,EAAIlM,EAAGxK,CAAC,CAAC,EACtC,OAAOyF,EAAI,MAAQ,OAAYA,CACnC,CAIA,SAASsO,EAAI,CACT,OAAOD,EAAa,KAAMC,CAAE,CAChC,CACA,eAAgB,CACZ,KAAM,CAAE,EAAG4C,EAAU,cAAAC,CAAa,EAAKvE,EACvC,GAAIsE,IAAavT,EACb,MAAO,GACX,GAAIwT,EACA,OAAOA,EAAc/C,EAAO,IAAI,EACpC,MAAM,IAAI,MAAM,8DAA8D,CAClF,CACA,eAAgB,CACZ,KAAM,CAAE,EAAG8C,EAAU,cAAAE,CAAa,EAAKxE,EACvC,OAAIsE,IAAavT,EACN,KACPyT,EACOA,EAAchD,EAAO,IAAI,EAC7B,KAAK,eAAexB,EAAM,CAAC,CACtC,CACA,WAAWyE,EAAe,GAAM,CAC5B,OAAArT,GAAM,eAAgBqT,CAAY,EAClC,KAAK,eAAc,EACZlW,EAAQiT,EAAO,KAAMiD,CAAY,CAC5C,CACA,MAAMA,EAAe,GAAM,CACvB,OAAArT,GAAM,eAAgBqT,CAAY,EAC3BtD,GAAc,KAAK,WAAWsD,CAAY,CAAC,CACtD,CACR,CACIjD,EAAM,KAAO,IAAIA,EAAMxB,EAAM,GAAIA,EAAM,GAAIzH,EAAG,GAAG,EACjDiJ,EAAM,KAAO,IAAIA,EAAMjJ,EAAG,KAAMA,EAAG,IAAKA,EAAG,IAAI,EAC/C,MAAMmM,EAAQ1E,EAAM,WACdwC,EAAOnG,GAAKmF,EAAOxB,EAAM,KAAO,KAAK,KAAK0E,EAAQ,CAAC,EAAIA,CAAK,EAElE,MAAO,CACH,MAAA1E,EACA,gBAAiBwB,EACjB,uBAAAV,EACA,oBAAAL,EACA,mBAAAG,CACR,CACA,CACA,SAAS+D,GAAa1G,EAAO,CACzB,MAAM7E,EAAO4E,GAAcC,CAAK,EAChCG,OAAAA,GAAkBhF,EAAM,CACpB,KAAM,OACN,KAAM,WACN,YAAa,UACrB,EAAO,CACC,SAAU,WACV,cAAe,WACf,KAAM,SACd,CAAK,EACM,OAAO,OAAO,CAAE,KAAM,GAAM,GAAGA,EAAM,CAChD,CAQO,SAASwL,GAAYC,EAAU,CAClC,MAAM7E,EAAQ2E,GAAaE,CAAQ,EAC7B,CAAE,GAAAtM,EAAI,EAAGuM,CAAW,EAAK9E,EACzB+E,EAAgBxM,EAAG,MAAQ,EAC3ByM,EAAkB,EAAIzM,EAAG,MAAQ,EACvC,SAAS0M,EAAKvX,EAAG,CACb,OAAOyJ,EAAIzJ,EAAGoX,CAAW,CAC7B,CACA,SAASI,EAAKxX,EAAG,CACb,OAAOgK,GAAOhK,EAAGoX,CAAW,CAChC,CACA,KAAM,CAAE,gBAAiBtD,EAAO,uBAAAV,EAAwB,oBAAAL,EAAqB,mBAAAG,CAAkB,EAAMb,GAAkB,CACnH,GAAGC,EACH,QAAQE,EAAIC,EAAOsE,EAAc,CAC7B,MAAM/W,EAAIyS,EAAM,SAAQ,EAClB1I,EAAIc,EAAG,QAAQ7K,EAAE,CAAC,EAClByX,EAAM9E,GAEZ,OADAjP,GAAM,eAAgBqT,CAAY,EAC9BA,EACOU,EAAI,WAAW,KAAK,CAAChF,EAAM,SAAQ,EAAK,EAAO,CAAI,CAAC,EAAG1I,CAAC,EAGxD0N,EAAI,WAAW,KAAK,CAAC,CAAI,CAAC,EAAG1N,EAAGc,EAAG,QAAQ7K,EAAE,CAAC,CAAC,CAE9D,EACA,UAAU+D,EAAO,CACb,MAAMjD,EAAMiD,EAAM,OACZ2T,EAAO3T,EAAM,CAAC,EACd8O,EAAO9O,EAAM,SAAS,CAAC,EAE7B,GAAIjD,IAAQuW,IAAkBK,IAAS,GAAQA,IAAS,GAAO,CAC3D,MAAM3N,EAAI2J,GAAmBb,CAAI,EACjC,GAAI,CAACM,GAAWpJ,EAAG1G,EAAKwH,EAAG,KAAK,EAC5B,MAAM,IAAI,MAAM,uBAAuB,EAC3C,MAAM8M,EAAK5E,EAAoBhJ,CAAC,EAChC,IAAI+I,EACJ,GAAI,CACAA,EAAIjI,EAAG,KAAK8M,CAAE,CAClB,OACOC,EAAW,CACd,MAAMC,EAASD,aAAqB,MAAQ,KAAOA,EAAU,QAAU,GACvE,MAAM,IAAI,MAAM,wBAA0BC,CAAM,CACpD,CACA,MAAMC,GAAUhF,EAAIzP,KAASA,EAG7B,OADmBqU,EAAO,KAAO,IACfI,IACdhF,EAAIjI,EAAG,IAAIiI,CAAC,GACT,CAAE,EAAA/I,EAAG,EAAA+I,CAAC,CACjB,SACShS,IAAQwW,GAAmBI,IAAS,EAAM,CAC/C,MAAM3N,EAAIc,EAAG,UAAUgI,EAAK,SAAS,EAAGhI,EAAG,KAAK,CAAC,EAC3CiI,EAAIjI,EAAG,UAAUgI,EAAK,SAAShI,EAAG,MAAO,EAAIA,EAAG,KAAK,CAAC,EAC5D,MAAO,CAAE,EAAAd,EAAG,EAAA+I,CAAC,CACjB,KACK,CACD,MAAMiF,EAAKV,EACLW,EAAKV,EACX,MAAM,IAAI,MAAM,qCAAuCS,EAAK,qBAAuBC,EAAK,SAAWlX,CAAG,CAC1G,CACJ,CACR,CAAK,EACKmX,EAAiB/T,GAAQuP,GAAcyE,GAAmBhU,EAAKoO,EAAM,WAAW,CAAC,EACvF,SAAS6F,EAAsBlO,EAAQ,CACnC,MAAMmO,EAAOhB,GAAe/T,EAC5B,OAAO4G,EAASmO,CACpB,CACA,SAASC,EAAW9J,EAAG,CACnB,OAAO4J,EAAsB5J,CAAC,EAAIgJ,EAAK,CAAChJ,CAAC,EAAIA,CACjD,CAEA,MAAM+J,EAAS,CAACrY,EAAGsY,EAAM9W,IAAOiS,GAAmBzT,EAAE,MAAMsY,EAAM9W,CAAE,CAAC,EAIpE,MAAM+W,CAAU,CACZ,YAAYpO,EAAGmE,EAAGkK,EAAU,CACxB,KAAK,EAAIrO,EACT,KAAK,EAAImE,EACT,KAAK,SAAWkK,EAChB,KAAK,eAAc,CACvB,CAEA,OAAO,YAAYzU,EAAK,CACpB,MAAMlE,EAAIwS,EAAM,YAChB,OAAAtO,EAAMqB,EAAY,mBAAoBrB,EAAKlE,EAAI,CAAC,EACzC,IAAI0Y,EAAUF,EAAOtU,EAAK,EAAGlE,CAAC,EAAGwY,EAAOtU,EAAKlE,EAAG,EAAIA,CAAC,CAAC,CACjE,CAGA,OAAO,QAAQkE,EAAK,CAChB,KAAM,CAAE,EAAAoG,EAAG,EAAAmE,GAAMyC,EAAI,MAAM3L,EAAY,MAAOrB,CAAG,CAAC,EAClD,OAAO,IAAIwU,EAAUpO,EAAGmE,CAAC,CAC7B,CACA,gBAAiB,CACboF,GAAY,IAAK,KAAK,EAAGtQ,EAAK+T,CAAW,EACzCzD,GAAY,IAAK,KAAK,EAAGtQ,EAAK+T,CAAW,CAC7C,CACA,eAAeqB,EAAU,CACrB,OAAO,IAAID,EAAU,KAAK,EAAG,KAAK,EAAGC,CAAQ,CACjD,CACA,iBAAiBC,EAAS,CACtB,KAAM,CAAE,EAAAtO,EAAG,EAAAmE,EAAG,SAAUoK,CAAG,EAAK,KAC1B9Y,EAAI+Y,EAAcvT,EAAY,UAAWqT,CAAO,CAAC,EACvD,GAAIC,GAAO,MAAQ,CAAC,CAAC,EAAG,EAAG,EAAG,CAAC,EAAE,SAASA,CAAG,EACzC,MAAM,IAAI,MAAM,qBAAqB,EACzC,MAAME,EAAOF,IAAQ,GAAKA,IAAQ,EAAIvO,EAAIkI,EAAM,EAAIlI,EACpD,GAAIyO,GAAQhO,EAAG,MACX,MAAM,IAAI,MAAM,4BAA4B,EAChD,MAAMiO,EAAUH,EAAM,EAAkB,KAAP,KAC3BI,EAAIjF,EAAM,QAAQgF,EAASb,EAAcY,CAAI,CAAC,EAC9CG,EAAKxB,EAAKqB,CAAI,EACdI,EAAK1B,EAAK,CAAC1X,EAAImZ,CAAE,EACjBE,GAAK3B,EAAKhJ,EAAIyK,CAAE,EAChBvO,EAAIqJ,EAAM,KAAK,qBAAqBiF,EAAGE,EAAIC,EAAE,EACnD,GAAI,CAACzO,EACD,MAAM,IAAI,MAAM,mBAAmB,EACvC,OAAAA,EAAE,eAAc,EACTA,CACX,CAEA,UAAW,CACP,OAAO0N,EAAsB,KAAK,CAAC,CACvC,CACA,YAAa,CACT,OAAO,KAAK,SAAQ,EAAK,IAAIK,EAAU,KAAK,EAAGjB,EAAK,CAAC,KAAK,CAAC,EAAG,KAAK,QAAQ,EAAI,IACnF,CAEA,eAAgB,CACZ,OAAO4B,GAAc,KAAK,UAAU,CACxC,CACA,UAAW,CACP,OAAOnI,EAAI,WAAW,CAAE,EAAG,KAAK,EAAG,EAAG,KAAK,EAAG,CAClD,CAEA,mBAAoB,CAChB,OAAOmI,GAAc,KAAK,cAAc,CAC5C,CACA,cAAe,CACX,OAAOlB,EAAc,KAAK,CAAC,EAAIA,EAAc,KAAK,CAAC,CACvD,CACR,CACI,MAAMmB,EAAQ,CACV,kBAAkBvE,EAAY,CAC1B,GAAI,CACA,OAAAzB,EAAuByB,CAAU,EAC1B,EACX,MACc,CACV,MAAO,EACX,CACJ,EACA,uBAAwBzB,EAKxB,iBAAkB,IAAM,CACpB,MAAM1R,EAAS2L,GAAiBiF,EAAM,CAAC,EACvC,OAAOhF,GAAegF,EAAM,YAAY5Q,CAAM,EAAG4Q,EAAM,CAAC,CAC5D,EASA,WAAWpE,EAAa,EAAGuE,EAAQqB,EAAM,KAAM,CAC3C,OAAArB,EAAM,eAAevE,CAAU,EAC/BuE,EAAM,SAAS,OAAO,CAAC,CAAC,EACjBA,CACX,CACR,EAOI,SAAS4G,EAAaxE,EAAYkC,EAAe,GAAM,CACnD,OAAOjD,EAAM,eAAee,CAAU,EAAE,WAAWkC,CAAY,CACnE,CAIA,SAASuC,EAAU7V,EAAM,CACrB,MAAMmD,EAAM4M,GAAW/P,CAAI,EACrBsC,EAAM,OAAOtC,GAAS,SACtB3C,GAAO8F,GAAOb,IAAQtC,EAAK,OACjC,OAAImD,EACO9F,IAAQuW,GAAiBvW,IAAQwW,EACxCvR,EACOjF,IAAQ,EAAIuW,GAAiBvW,IAAQ,EAAIwW,EAChD7T,aAAgBqQ,CAGxB,CAWA,SAASyF,EAAgBC,EAAUC,EAAS1C,EAAe,GAAM,CAC7D,GAAIuC,EAAUE,CAAQ,EAClB,MAAM,IAAI,MAAM,+BAA+B,EACnD,GAAI,CAACF,EAAUG,CAAO,EAClB,MAAM,IAAI,MAAM,+BAA+B,EAEnD,OADU3F,EAAM,QAAQ2F,CAAO,EACtB,SAASrG,EAAuBoG,CAAQ,CAAC,EAAE,WAAWzC,CAAY,CAC/E,CAKA,MAAM2C,EAAWpH,EAAM,UACnB,SAAUvO,EAAO,CAEb,GAAIA,EAAM,OAAS,KACf,MAAM,IAAI,MAAM,oBAAoB,EAGxC,MAAMG,EAAMwP,GAAmB3P,CAAK,EAC9B4V,EAAQ5V,EAAM,OAAS,EAAIuO,EAAM,WACvC,OAAOqH,EAAQ,EAAIzV,GAAO,OAAOyV,CAAK,EAAIzV,CAC9C,EACE0U,EAAgBtG,EAAM,eACxB,SAAUvO,EAAO,CACb,OAAOwT,EAAKmC,EAAS3V,CAAK,CAAC,CAC/B,EAEE6V,EAAaC,GAAWvH,EAAM,UAAU,EAI9C,SAASwH,EAAW5V,EAAK,CACrByP,OAAAA,GAAY,WAAarB,EAAM,WAAYpO,EAAKd,EAAKwW,CAAU,EAExD1B,GAAmBhU,EAAKoO,EAAM,WAAW,CACpD,CAMA,SAASyH,EAAQrB,EAAS7D,EAAYnJ,EAAOsO,EAAgB,CACzD,GAAI,CAAC,YAAa,WAAW,EAAE,KAAM9S,IAAMA,MAAKwE,CAAI,EAChD,MAAM,IAAI,MAAM,qCAAqC,EACzD,KAAM,CAAE,KAAA9C,EAAM,YAAAqR,CAAW,EAAK3H,EAC9B,GAAI,CAAE,KAAA4H,EAAM,QAAAC,EAAS,aAAcC,CAAG,EAAK1O,EACvCwO,GAAQ,OACRA,EAAO,IACXxB,EAAUrT,EAAY,UAAWqT,CAAO,EACxClI,GAAmB9E,CAAI,EACnByO,IACAzB,EAAUrT,EAAY,oBAAqBuD,EAAK8P,CAAO,CAAC,GAI5D,MAAM2B,EAAQzB,EAAcF,CAAO,EAC7B5M,EAAIsH,EAAuByB,CAAU,EACrCyF,EAAW,CAACR,EAAWhO,CAAC,EAAGgO,EAAWO,CAAK,CAAC,EAElD,GAAID,GAAO,MAAQA,IAAQ,GAAO,CAE9B,MAAM7U,GAAI6U,IAAQ,GAAOH,EAAYpP,EAAG,KAAK,EAAIuP,EACjDE,EAAS,KAAKjV,EAAY,eAAgBE,EAAC,CAAC,CAChD,CACA,MAAM8B,GAAOsL,GAAe,GAAG2H,CAAQ,EACjCjQ,EAAIgQ,EAEV,SAASE,GAAMC,GAAQ,CAEnB,MAAMtT,GAAIwS,EAASc,EAAM,EACzB,GAAI,CAACtH,EAAmBhM,EAAC,EACrB,OACJ,MAAMuT,GAAKjD,EAAKtQ,EAAC,EACXiD,GAAI2J,EAAM,KAAK,SAAS5M,EAAC,EAAE,WAC3BkD,GAAImN,EAAKpN,GAAE,CAAC,EAClB,GAAIC,KAAMhH,EACN,OAIJ,MAAMmL,GAAIgJ,EAAKkD,GAAKlD,EAAKlN,EAAID,GAAI0B,CAAC,CAAC,EACnC,GAAIyC,KAAMnL,EACN,OACJ,IAAIqV,IAAYtO,GAAE,IAAMC,GAAI,EAAI,GAAK,OAAOD,GAAE,EAAI9G,CAAG,EACjDqX,GAAQnM,GACZ,OAAI2L,GAAQ/B,EAAsB5J,EAAC,IAC/BmM,GAAQrC,EAAW9J,EAAC,EACpBkK,IAAY,GAET,IAAID,EAAUpO,GAAGsQ,GAAOjC,EAAQ,CAC3C,CACA,MAAO,CAAE,KAAApR,GAAM,MAAAkT,EAAK,CACxB,CACA,MAAMP,EAAiB,CAAE,KAAM1H,EAAM,KAAM,QAAS,EAAK,EACnDqI,EAAiB,CAAE,KAAMrI,EAAM,KAAM,QAAS,EAAK,EAczD,SAASsI,EAAKlC,EAASmC,EAASnP,EAAOsO,EAAgB,CACnD,KAAM,CAAE,KAAA3S,EAAM,MAAAkT,CAAK,EAAKR,EAAQrB,EAASmC,EAASnP,CAAI,EAChDvJ,EAAImQ,EAEV,OADawI,GAAkB3Y,EAAE,KAAK,UAAWA,EAAE,YAAaA,EAAE,IAAI,EAC1DkF,EAAMkT,CAAK,CAC3B,CAEAzG,EAAM,KAAK,eAAe,CAAC,EAe3B,SAASiH,EAAOC,EAAWtC,EAASuC,EAAWvP,EAAOiP,EAAgB,QAClE,MAAMO,EAAKF,EACXtC,EAAUrT,EAAY,UAAWqT,CAAO,EACxCuC,EAAY5V,EAAY,YAAa4V,CAAS,EAC9C,KAAM,CAAE,KAAAf,EAAM,QAAAC,EAAS,OAAAgB,CAAM,EAAKzP,EAGlC,GADA8E,GAAmB9E,CAAI,EACnB,WAAYA,EACZ,MAAM,IAAI,MAAM,oCAAoC,EACxD,GAAIyP,IAAW,QAAaA,IAAW,WAAaA,IAAW,MAC3D,MAAM,IAAI,MAAM,+BAA+B,EACnD,MAAMC,EAAQ,OAAOF,GAAO,UAAY1H,GAAW0H,CAAE,EAC/CG,EAAQ,CAACD,GACX,CAACD,GACD,OAAOD,GAAO,UACdA,IAAO,MACP,OAAOA,EAAG,GAAM,UAChB,OAAOA,EAAG,GAAM,SACpB,GAAI,CAACE,GAAS,CAACC,EACX,MAAM,IAAI,MAAM,0EAA0E,EAC9F,IAAIC,EACA/Q,GACJ,GAAI,CAGA,GAFI8Q,IACAC,EAAO,IAAI9C,EAAU0C,EAAG,EAAGA,EAAG,CAAC,GAC/BE,EAAO,CAGP,GAAI,CACID,IAAW,YACXG,EAAO9C,EAAU,QAAQ0C,CAAE,EACnC,OACOK,GAAU,CACb,GAAI,EAAEA,cAAoBvK,EAAI,KAC1B,MAAMuK,EACd,CACI,CAACD,GAAQH,IAAW,QACpBG,EAAO9C,EAAU,YAAY0C,CAAE,EACvC,CACA3Q,GAAIuJ,EAAM,QAAQmH,CAAS,CAC/B,MACc,CACV,MAAO,EACX,CAGA,GAFI,CAACK,GAEDpB,GAAQoB,EAAK,SAAQ,EACrB,MAAO,GACPnB,IACAzB,EAAUpG,EAAM,KAAKoG,CAAO,GAChC,KAAM,CAAE,EAAAtO,EAAG,EAAAmE,EAAC,EAAK+M,EACXzb,GAAI+Y,EAAcF,CAAO,EACzB8C,GAAKhE,EAAKjJ,EAAC,EACX0K,GAAK1B,EAAK1X,GAAI2b,EAAE,EAChBtC,GAAK3B,EAAKnN,EAAIoR,EAAE,EAChBzC,IAAI0C,GAAA3H,EAAM,KAAK,qBAAqBvJ,GAAG0O,GAAIC,EAAE,IAAzC,YAAAuC,GAA4C,WACtD,OAAK1C,GAEKxB,EAAKwB,GAAE,CAAC,IACL3O,EAFF,EAGf,CACA,MAAO,CACH,MAAAkI,EACA,aAAA+G,EACA,gBAAAE,EACA,KAAAqB,EACA,OAAAG,EACA,gBAAiBjH,EACjB,UAAA0E,EACA,MAAAY,CACR,CACA,CC9hCA,sEAKO,SAASsC,GAAQ9S,EAAM,CAC1B,MAAO,CACH,KAAAA,EACA,KAAM,CAACG,KAAQ4S,IAASxS,GAAKP,EAAMG,EAAKvD,GAAY,GAAGmW,CAAI,CAAC,EAC5D,YAAA1B,EACR,CACA,CACO,SAAS2B,GAAYzE,EAAU0E,EAAS,CAC3C,MAAMC,EAAUlT,GAASsO,GAAY,CAAE,GAAGC,EAAU,GAAGuE,GAAQ9S,CAAI,EAAG,EACtE,MAAO,CAAE,GAAGkT,EAAOD,CAAO,EAAG,OAAAC,CAAM,CACvC,CCPA,sEAQA,MAAMC,GAAa,OAAO,oEAAoE,EACxFC,GAAa,OAAO,oEAAoE,EACxF3Y,GAAM,OAAO,CAAC,EACdC,GAAM,OAAO,CAAC,EACd2Y,GAAa,CAACjc,EAAGC,KAAOD,EAAIC,EAAIqD,IAAOrD,EAK7C,SAASic,GAAQpJ,EAAG,CAChB,MAAMvI,EAAIwR,GAEJ1S,EAAM,OAAO,CAAC,EAAG8S,EAAM,OAAO,CAAC,EAAGC,EAAO,OAAO,EAAE,EAAGC,EAAO,OAAO,EAAE,EAErEC,EAAO,OAAO,EAAE,EAAGC,EAAO,OAAO,EAAE,EAAGC,EAAO,OAAO,EAAE,EACtDC,EAAM3J,EAAIA,EAAIA,EAAKvI,EACnBgL,EAAMkH,EAAKA,EAAK3J,EAAKvI,EACrBmS,EAAM5S,EAAKyL,EAAIlM,EAAKkB,CAAC,EAAIgL,EAAMhL,EAC/BoS,EAAM7S,EAAK4S,EAAIrT,EAAKkB,CAAC,EAAIgL,EAAMhL,EAC/BqS,EAAO9S,EAAK6S,EAAIrZ,GAAKiH,CAAC,EAAIkS,EAAMlS,EAChCsS,EAAO/S,EAAK8S,EAAKR,EAAM7R,CAAC,EAAIqS,EAAOrS,EACnCuS,EAAOhT,EAAK+S,EAAKR,EAAM9R,CAAC,EAAIsS,EAAOtS,EACnCwS,EAAOjT,EAAKgT,EAAKP,EAAMhS,CAAC,EAAIuS,EAAOvS,EACnCyS,EAAQlT,EAAKiT,EAAKP,EAAMjS,CAAC,EAAIwS,EAAOxS,EACpC0S,EAAQnT,EAAKkT,EAAMT,EAAMhS,CAAC,EAAIuS,EAAOvS,EACrC2S,EAAQpT,EAAKmT,EAAM5T,EAAKkB,CAAC,EAAIgL,EAAMhL,EACnCqL,EAAM9L,EAAKoT,EAAMZ,EAAM/R,CAAC,EAAIsS,EAAOtS,EACnCU,EAAMnB,EAAK8L,EAAIuG,EAAK5R,CAAC,EAAIkS,EAAMlS,EAC/BO,EAAOhB,EAAKmB,EAAI3H,GAAKiH,CAAC,EAC5B,GAAI,CAAC4S,GAAK,IAAIA,GAAK,IAAIrS,CAAI,EAAGgI,CAAC,EAC3B,MAAM,IAAI,MAAM,yBAAyB,EAC7C,OAAOhI,CACX,CACA,MAAMqS,GAAO1Q,GAAMsP,GAAY,OAAW,OAAW,CAAE,KAAMG,GAAS,EAazDkB,GAAYxB,GAAY,CACjC,EAAG,OAAO,CAAC,EACX,EAAG,OAAO,CAAC,EACX,GAAIuB,GACJ,EAAGnB,GAEH,GAAI,OAAO,+EAA+E,EAC1F,GAAI,OAAO,+EAA+E,EAC1F,EAAG,OAAO,CAAC,EACX,KAAM,GACN,KAAM,CAEF,KAAM,OAAO,oEAAoE,EACjF,YAAc9U,GAAM,CAChB,MAAMb,EAAI2V,GACJqB,EAAK,OAAO,oCAAoC,EAChDC,EAAK,CAACja,GAAM,OAAO,oCAAoC,EACvDka,EAAK,OAAO,qCAAqC,EACjDd,EAAKY,EACLG,EAAY,OAAO,qCAAqC,EACxDpS,EAAK6Q,GAAWQ,EAAKvV,EAAGb,CAAC,EACzBoX,EAAKxB,GAAW,CAACqB,EAAKpW,EAAGb,CAAC,EAChC,IAAI8P,EAAK1M,EAAIvC,EAAIkE,EAAKiS,EAAKI,EAAKF,EAAIlX,CAAC,EACjCgQ,EAAK5M,EAAI,CAAC2B,EAAKkS,EAAKG,EAAKhB,EAAIpW,CAAC,EAClC,MAAM6P,EAAQC,EAAKqH,EACbpH,EAAQC,EAAKmH,EAKnB,GAJItH,IACAC,EAAK9P,EAAI8P,GACTC,IACAC,EAAKhQ,EAAIgQ,GACTF,EAAKqH,GAAanH,EAAKmH,EACvB,MAAM,IAAI,MAAM,uCAAyCtW,CAAC,EAE9D,MAAO,CAAE,MAAAgP,EAAO,GAAAC,EAAI,MAAAC,EAAO,GAAAC,CAAE,CACjC,CACR,CACA,EAAGnT,EAAM,EAGG,OAAO,CAAC,EAiBNka,GAAU", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}