{"version": 3, "file": "etherscan-lC29LaWs.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const etherscanSvg = svg `<svg fill=\"none\" viewBox=\"0 0 16 16\">\n  <path\n    fill=\"currentColor\"\n    d=\"M4.25 7a.63.63 0 0 0-.63.63v3.97c0 .28-.2.51-.47.54l-.75.07a.93.93 0 0 1-.9-.47A7.51 7.51 0 0 1 5.54.92a7.5 7.5 0 0 1 9.54 4.62c.12.35.06.72-.16 1-.74.97-1.68 1.78-2.6 2.44V4.44a.64.64 0 0 0-.63-.64h-1.06c-.35 0-.63.3-.63.64v5.5c0 .23-.12.42-.32.5l-.52.23V6.05c0-.36-.3-.64-.64-.64H7.45c-.35 0-.64.3-.64.64v4.97c0 .25-.17.46-.4.52a5.8 5.8 0 0 0-.45.11v-4c0-.36-.3-.65-.64-.65H4.25ZM14.07 12.4A7.49 7.49 0 0 1 3.6 14.08c4.09-.58 9.14-2.5 11.87-6.6v.03a7.56 7.56 0 0 1-1.41 4.91Z\"\n  />\n</svg>`;\n//# sourceMappingURL=etherscan.js.map"], "names": ["etherscanSvg", "svg"], "mappings": "2JACO,MAAMA,EAAeC;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}