import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'
import { Button } from './Button'

// 模态框遮罩层
const ModalOverlay = React.forwardRef(({ className, ...props }, ref) => (
  <motion.div
    ref={ref}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3, ease: 'easeOut' }}
    className={cn(
      'fixed inset-0 z-50 bg-bg-primary/90 backdrop-blur-md',
      'bg-gradient-to-br from-bg-primary/95 via-bg-secondary/90 to-bg-primary/95',
      className
    )}
    {...props}
  />
))
ModalOverlay.displayName = 'ModalOverlay'

// 模态框内容
const ModalContent = React.forwardRef(({ className, children, variant = 'default', ...props }, ref) => {
  const contentVariants = {
    default: 'bg-bg-secondary border-border-primary shadow-tech',
    tech: 'bg-gradient-tech border-border-accent shadow-tech-lg',
    glass: 'bg-bg-primary/80 backdrop-blur-xl border-border-accent/50 shadow-glow'
  }

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.9, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.9, y: 20 }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration: 0.4
      }}
      className={cn(
        // 基础定位和层级
        'fixed left-[50%] top-[50%] z-50 grid translate-x-[-50%] translate-y-[-50%]',
        // 响应式宽度 - 移动端优先设计
        'w-[calc(100vw-2rem)] max-w-md',
        'sm:w-[calc(100vw-4rem)] sm:max-w-lg',
        'md:w-full md:max-w-xl',
        'lg:max-w-2xl',
        // 响应式间距和布局
        'gap-3 sm:gap-4',
        'p-4 sm:p-5 md:p-6',
        // 边框和圆角
        'border rounded-tech-lg',
        // 溢出处理
        'relative overflow-hidden',
        // 最大高度限制，防止内容过长
        'max-h-[calc(100vh-4rem)] sm:max-h-[calc(100vh-6rem)]',
        contentVariants[variant],
        className
      )}
      {...props}
    >
      {/* 科技风背景效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-primary/5 via-transparent to-brand-secondary/5 pointer-events-none" />
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-brand-primary/50 to-transparent" />

      <div className="relative z-10 overflow-y-auto">
        {children}
      </div>
    </motion.div>
  )
})
ModalContent.displayName = 'ModalContent'

// 模态框头部
const ModalHeader = React.forwardRef(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'flex flex-col space-y-2 text-center sm:text-left',
      // 响应式间距
      'pb-2 sm:pb-3',
      'border-b border-border-primary/30',
      className
    )}
    {...props}
  />
))
ModalHeader.displayName = 'ModalHeader'

// 模态框标题
const ModalTitle = React.forwardRef(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      // 响应式字体大小
      'text-lg sm:text-xl font-bold leading-tight tracking-tight',
      'text-text-primary bg-gradient-to-r from-text-primary to-text-accent bg-clip-text',
      // 移动端优化
      'break-words',
      className
    )}
    {...props}
  />
))
ModalTitle.displayName = 'ModalTitle'

// 模态框描述
const ModalDescription = React.forwardRef(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      'text-sm text-text-secondary leading-relaxed',
      // 移动端优化
      'break-words',
      className
    )}
    {...props}
  />
))
ModalDescription.displayName = 'ModalDescription'

// 模态框底部
const ModalFooter = React.forwardRef(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      // 移动端垂直布局，桌面端水平布局
      'flex flex-col sm:flex-row sm:justify-end',
      // 响应式间距
      'gap-2 sm:gap-3',
      'pt-2 sm:pt-3',
      'border-t border-border-primary/30',
      className
    )}
    {...props}
  />
))
ModalFooter.displayName = 'ModalFooter'

// 主模态框组件
const Modal = ({ 
  open, 
  onOpenChange, 
  children, 
  className,
  variant = 'default',
  showCloseButton = true,
  closeOnOverlayClick = true,
  ...props 
}) => {
  const [isOpen, setIsOpen] = React.useState(open)
  
  React.useEffect(() => {
    setIsOpen(open)
  }, [open])
  
  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onOpenChange?.(false)
      }
    }
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onOpenChange])
  
  const handleOverlayClick = (e) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onOpenChange?.(false)
    }
  }
  
  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50">
          <ModalOverlay onClick={handleOverlayClick} />
          <ModalContent className={className} variant={variant} {...props}>
            {showCloseButton && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className={cn(
                  // 响应式定位 - 移动端更容易点击
                  "absolute right-4 top-4 sm:right-5 sm:top-5 md:right-6 md:top-6 z-20",
                  // 响应式尺寸 - 移动端更大的点击区域
                  "flex items-center justify-center",
                  "w-9 h-9 sm:w-8 sm:h-8",
                  // 样式
                  "rounded-tech bg-bg-primary/80 backdrop-blur-sm",
                  "border border-border-accent/50",
                  "text-text-muted hover:text-text-primary",
                  "transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-brand-primary/50",
                  // 移动端触摸优化
                  "touch-manipulation"
                )}
                onClick={() => onOpenChange?.(false)}
              >
                <X className="h-4 w-4 sm:h-4 sm:w-4" />
                <span className="sr-only">Close</span>
              </motion.button>
            )}
            {children}
          </ModalContent>
        </div>
      )}
    </AnimatePresence>
  )
}

// 确认对话框
const ConfirmDialog = ({ 
  open, 
  onOpenChange, 
  title, 
  description, 
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  variant = 'default',
  modalVariant = 'tech',
  loading = false
}) => {
  const handleConfirm = () => {
    onConfirm?.()
  }
  
  const handleCancel = () => {
    onCancel?.()
    onOpenChange?.(false)
  }
  
  const getButtonVariant = () => {
    switch (variant) {
      case 'destructive': return 'destructive'
      case 'warning': return 'warning'
      case 'success': return 'success'
      default: return 'tech'
    }
  }
  
  return (
    <Modal 
      open={open} 
      onOpenChange={onOpenChange} 
      variant={modalVariant}
      className="max-w-md"
    >
      <ModalHeader>
        <ModalTitle className="flex items-center gap-2">
          {variant === 'destructive' && (
            <div className="w-5 h-5 rounded-full bg-error/20 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-error" />
            </div>
          )}
          {variant === 'warning' && (
            <div className="w-5 h-5 rounded-full bg-warning/20 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-warning" />
            </div>
          )}
          {variant === 'success' && (
            <div className="w-5 h-5 rounded-full bg-success/20 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-success" />
            </div>
          )}
          {title}
        </ModalTitle>
        {description && (
          <ModalDescription>{description}</ModalDescription>
        )}
      </ModalHeader>
      <ModalFooter>
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button
          variant={getButtonVariant()}
          onClick={handleConfirm}
          loading={loading}
        >
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  )
}

// 表单模态框
const FormModal = ({ 
  open, 
  onOpenChange, 
  title, 
  description,
  children,
  onSubmit,
  submitText = 'Submit',
  cancelText = 'Cancel',
  variant = 'glass',
  loading = false,
  className
}) => {
  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit?.(e)
  }
  
  return (
    <Modal 
      open={open} 
      onOpenChange={onOpenChange} 
      variant={variant}
      className={cn('max-w-2xl', className)}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <ModalHeader>
          <ModalTitle className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-full bg-brand-primary/20 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-brand-primary animate-pulse" />
            </div>
            {title}
          </ModalTitle>
          {description && (
            <ModalDescription>{description}</ModalDescription>
          )}
        </ModalHeader>
        
        <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
          {children}
        </div>
        
        <ModalFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange?.(false)}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            type="submit"
            variant="tech"
            loading={loading}
          >
            {submitText}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  )
}

export {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalDescription,
  ModalFooter,
  ConfirmDialog,
  FormModal
}