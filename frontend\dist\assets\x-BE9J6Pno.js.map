{"version": 3, "file": "x-BE9J6Pno.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const xSvg = svg `<svg fill=\"none\" viewBox=\"0 0 41 40\">\n  <g clip-path=\"url(#a)\">\n    <path fill=\"#000\" d=\"M.8 0h40v40H.8z\" />\n    <path\n      fill=\"#fff\"\n      d=\"m22.63 18.46 7.14-8.3h-1.69l-6.2 7.2-4.96-7.2H11.2l7.5 10.9-7.5 8.71h1.7l6.55-7.61 5.23 7.61h5.72l-7.77-11.31Zm-9.13-7.03h2.6l11.98 17.13h-2.6L13.5 11.43Z\"\n    />\n  </g>\n  <defs>\n    <clipPath id=\"a\"><path fill=\"#fff\" d=\"M.8 20a20 20 0 1 1 40 0 20 20 0 0 1-40 0Z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=x.js.map"], "names": ["xSvg", "svg"], "mappings": "2JACO,MAAMA,EAAOC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}