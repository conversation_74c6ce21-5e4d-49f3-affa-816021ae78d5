import{b as r}from"./core-C0olQNtY.js";import"./index-2wea5Wgv.js";import"./events-B2jzgt6q.js";import"./index.es-CAssemqx.js";import"./index-nibyPLVP.js";const n=r`<svg fill="none" viewBox="0 0 20 20">
  <path
    fill="currentColor"
    d="M11 6.67a1 1 0 1 0-2 0v2.66a1 1 0 0 0 2 0V6.67ZM10 14.5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z"
  />
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M10 1a9 9 0 1 0 0 18 9 9 0 0 0 0-18Zm-7 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0Z"
    clip-rule="evenodd"
  />
</svg>`;export{n as warningCircleSvg};
//# sourceMappingURL=warning-circle-Dn7tmkxe.js.map
