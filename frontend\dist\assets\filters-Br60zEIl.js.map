{"version": 3, "file": "filters-Br60zEIl.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const filtersSvg = svg `<svg fill=\"none\" viewBox=\"0 0 16 16\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M0 3a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1Zm2.63 5.25a1 1 0 0 1 1-1h8.75a1 1 0 1 1 0 2H3.63a1 1 0 0 1-1-1Zm2.62 5.25a1 1 0 0 1 1-1h3.5a1 1 0 0 1 0 2h-3.5a1 1 0 0 1-1-1Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=filters.js.map"], "names": ["filtersSvg", "svg"], "mappings": "2JACO,MAAMA,EAAaC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}