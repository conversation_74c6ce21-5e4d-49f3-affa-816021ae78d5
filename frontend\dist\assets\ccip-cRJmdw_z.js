import{B as p,a as m,s as w,d as k,i as b,l as L,b as O,c as E,e as x,f as R,H as y,h as M}from"./index-2wea5Wgv.js";class S extends p{constructor({callbackSelector:r,cause:a,data:n,extraData:i,sender:f,urls:t}){var o;super(a.shortMessage||"An error occurred while fetching for an offchain result.",{cause:a,metaMessages:[...a.metaMessages||[],(o=a.metaMessages)!=null&&o.length?"":[],"Offchain Gateway Call:",t&&["  Gateway URL(s):",...t.map(d=>`    ${m(d)}`)],`  Sender: ${f}`,`  Data: ${n}`,`  Callback selector: ${r}`,`  Extra data: ${i}`].flat(),name:"OffchainLookupError"})}}class $ extends p{constructor({result:r,url:a}){super("Offchain gateway response is malformed. Response data must be a hex value.",{metaMessages:[`Gateway URL: ${m(a)}`,`Response: ${w(r)}`],name:"OffchainLookupResponseMalformedError"})}}class q extends p{constructor({sender:r,to:a}){super("Reverted sender address does not match target contract address (`to`).",{metaMessages:[`Contract address: ${a}`,`OffchainLookup sender address: ${r}`],name:"OffchainLookupSenderMismatchError"})}}const A="0x556f1830",T={name:"OffchainLookup",type:"error",inputs:[{name:"sender",type:"address"},{name:"urls",type:"string[]"},{name:"callData",type:"bytes"},{name:"callbackFunction",type:"bytes4"},{name:"extraData",type:"bytes"}]};async function D(c,{blockNumber:r,blockTag:a,data:n,to:i}){const{args:f}=k({data:n,abi:[T]}),[t,o,d,l,s]=f,{ccipRead:e}=c,h=e&&typeof(e==null?void 0:e.request)=="function"?e.request:C;try{if(!b(i,t))throw new q({sender:t,to:i});const u=o.includes(L)?await O({data:d,ccipRequest:h}):await h({data:d,sender:t,urls:o}),{data:g}=await E(c,{blockNumber:r,blockTag:a,data:x([l,R([{type:"bytes"},{type:"bytes"}],[u,s])]),to:i});return g}catch(u){throw new S({callbackSelector:l,cause:u,data:n,extraData:s,sender:t,urls:o})}}async function C({data:c,sender:r,urls:a}){var i;let n=new Error("An unknown error occurred.");for(let f=0;f<a.length;f++){const t=a[f],o=t.includes("{data}")?"GET":"POST",d=o==="POST"?{data:c,sender:r}:void 0,l=o==="POST"?{"Content-Type":"application/json"}:{};try{const s=await fetch(t.replace("{sender}",r.toLowerCase()).replace("{data}",c),{body:JSON.stringify(d),headers:l,method:o});let e;if((i=s.headers.get("Content-Type"))!=null&&i.startsWith("application/json")?e=(await s.json()).data:e=await s.text(),!s.ok){n=new y({body:d,details:e!=null&&e.error?w(e.error):s.statusText,headers:s.headers,status:s.status,url:t});continue}if(!M(e)){n=new $({result:e,url:t});continue}return e}catch(s){n=new y({body:d,details:s.message,url:t})}}throw n}export{C as ccipRequest,D as offchainLookup,T as offchainLookupAbiItem,A as offchainLookupSignature};
//# sourceMappingURL=ccip-cRJmdw_z.js.map
