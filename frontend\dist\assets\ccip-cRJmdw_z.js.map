{"version": 3, "file": "ccip-cRJmdw_z.js", "sources": ["../../node_modules/viem/_esm/errors/ccip.js", "../../node_modules/viem/_esm/utils/ccip.js"], "sourcesContent": ["import { stringify } from '../utils/stringify.js';\nimport { BaseError } from './base.js';\nimport { getUrl } from './utils.js';\nexport class OffchainLookupError extends BaseError {\n    constructor({ callbackSelector, cause, data, extraData, sender, urls, }) {\n        super(cause.shortMessage ||\n            'An error occurred while fetching for an offchain result.', {\n            cause,\n            metaMessages: [\n                ...(cause.metaMessages || []),\n                cause.metaMessages?.length ? '' : [],\n                'Offchain Gateway Call:',\n                urls && [\n                    '  Gateway URL(s):',\n                    ...urls.map((url) => `    ${getUrl(url)}`),\n                ],\n                `  Sender: ${sender}`,\n                `  Data: ${data}`,\n                `  Callback selector: ${callbackSelector}`,\n                `  Extra data: ${extraData}`,\n            ].flat(),\n            name: 'OffchainLookupError',\n        });\n    }\n}\nexport class OffchainLookupResponseMalformedError extends BaseError {\n    constructor({ result, url }) {\n        super('Offchain gateway response is malformed. Response data must be a hex value.', {\n            metaMessages: [\n                `Gateway URL: ${getUrl(url)}`,\n                `Response: ${stringify(result)}`,\n            ],\n            name: 'OffchainLookupResponseMalformedError',\n        });\n    }\n}\nexport class OffchainLookupSenderMismatchError extends BaseError {\n    constructor({ sender, to }) {\n        super('Reverted sender address does not match target contract address (`to`).', {\n            metaMessages: [\n                `Contract address: ${to}`,\n                `OffchainLookup sender address: ${sender}`,\n            ],\n            name: 'OffchainLookupSenderMismatchError',\n        });\n    }\n}\n//# sourceMappingURL=ccip.js.map", "import { call } from '../actions/public/call.js';\nimport { OffchainLookupError, OffchainLookupResponseMalformedError, OffchainLookupSenderMismatchError, } from '../errors/ccip.js';\nimport { HttpRequestError, } from '../errors/request.js';\nimport { decodeErrorResult } from './abi/decodeErrorResult.js';\nimport { encodeAbiParameters } from './abi/encodeAbiParameters.js';\nimport { isAddressEqual } from './address/isAddressEqual.js';\nimport { concat } from './data/concat.js';\nimport { isHex } from './data/isHex.js';\nimport { localBatchGatewayRequest, localBatchGatewayUrl, } from './ens/localBatchGatewayRequest.js';\nimport { stringify } from './stringify.js';\nexport const offchainLookupSignature = '0x556f1830';\nexport const offchainLookupAbiItem = {\n    name: 'OffchainLookup',\n    type: 'error',\n    inputs: [\n        {\n            name: 'sender',\n            type: 'address',\n        },\n        {\n            name: 'urls',\n            type: 'string[]',\n        },\n        {\n            name: 'callData',\n            type: 'bytes',\n        },\n        {\n            name: 'callbackFunction',\n            type: 'bytes4',\n        },\n        {\n            name: 'extraData',\n            type: 'bytes',\n        },\n    ],\n};\nexport async function offchainLookup(client, { blockNumber, blockTag, data, to, }) {\n    const { args } = decodeErrorResult({\n        data,\n        abi: [offchainLookupAbiItem],\n    });\n    const [sender, urls, callData, callbackSelector, extraData] = args;\n    const { ccipRead } = client;\n    const ccipRequest_ = ccipRead && typeof ccipRead?.request === 'function'\n        ? ccipRead.request\n        : ccipRequest;\n    try {\n        if (!isAddressEqual(to, sender))\n            throw new OffchainLookupSenderMismatchError({ sender, to });\n        const result = urls.includes(localBatchGatewayUrl)\n            ? await localBatchGatewayRequest({\n                data: callData,\n                ccipRequest: ccipRequest_,\n            })\n            : await ccipRequest_({ data: callData, sender, urls });\n        const { data: data_ } = await call(client, {\n            blockNumber,\n            blockTag,\n            data: concat([\n                callbackSelector,\n                encodeAbiParameters([{ type: 'bytes' }, { type: 'bytes' }], [result, extraData]),\n            ]),\n            to,\n        });\n        return data_;\n    }\n    catch (err) {\n        throw new OffchainLookupError({\n            callbackSelector,\n            cause: err,\n            data,\n            extraData,\n            sender,\n            urls,\n        });\n    }\n}\nexport async function ccipRequest({ data, sender, urls, }) {\n    let error = new Error('An unknown error occurred.');\n    for (let i = 0; i < urls.length; i++) {\n        const url = urls[i];\n        const method = url.includes('{data}') ? 'GET' : 'POST';\n        const body = method === 'POST' ? { data, sender } : undefined;\n        const headers = method === 'POST' ? { 'Content-Type': 'application/json' } : {};\n        try {\n            const response = await fetch(url.replace('{sender}', sender.toLowerCase()).replace('{data}', data), {\n                body: JSON.stringify(body),\n                headers,\n                method,\n            });\n            let result;\n            if (response.headers.get('Content-Type')?.startsWith('application/json')) {\n                result = (await response.json()).data;\n            }\n            else {\n                result = (await response.text());\n            }\n            if (!response.ok) {\n                error = new HttpRequestError({\n                    body,\n                    details: result?.error\n                        ? stringify(result.error)\n                        : response.statusText,\n                    headers: response.headers,\n                    status: response.status,\n                    url,\n                });\n                continue;\n            }\n            if (!isHex(result)) {\n                error = new OffchainLookupResponseMalformedError({\n                    result,\n                    url,\n                });\n                continue;\n            }\n            return result;\n        }\n        catch (err) {\n            error = new HttpRequestError({\n                body,\n                details: err.message,\n                url,\n            });\n        }\n    }\n    throw error;\n}\n//# sourceMappingURL=ccip.js.map"], "names": ["OffchainLookupError", "BaseError", "callbackSelector", "cause", "data", "extraData", "sender", "urls", "_a", "url", "getUrl", "OffchainLookupResponseMalformedError", "result", "stringify", "OffchainLookupSenderMismatchError", "to", "offchainLookupSignature", "offchainLookupAbiItem", "offchainLookup", "client", "blockNumber", "blockTag", "args", "decodeErrorResult", "callData", "ccipRead", "ccipRequest_", "ccipRequest", "isAddressEqual", "localBatchGatewayUrl", "localBatchGatewayRequest", "data_", "call", "concat", "encodeAbiParameters", "err", "error", "i", "method", "body", "headers", "response", "HttpRequestError", "isHex"], "mappings": "qHAGO,MAAMA,UAA4BC,CAAU,CAC/C,YAAY,CAAE,iBAAAC,EAAkB,MAAAC,EAAO,KAAAC,EAAM,UAAAC,EAAW,OAAAC,EAAQ,KAAAC,GAAS,OACrE,MAAMJ,EAAM,cACR,2DAA4D,CAC5D,MAAAA,EACA,aAAc,CACV,GAAIA,EAAM,cAAgB,IAC1BK,EAAAL,EAAM,eAAN,MAAAK,EAAoB,OAAS,GAAK,CAAA,EAClC,yBACAD,GAAQ,CACJ,oBACA,GAAGA,EAAK,IAAKE,GAAQ,OAAOC,EAAOD,CAAG,CAAC,EAAE,CAC7D,EACgB,aAAaH,CAAM,GACnB,WAAWF,CAAI,GACf,wBAAwBF,CAAgB,GACxC,iBAAiBG,CAAS,EAC1C,EAAc,KAAI,EACN,KAAM,qBAClB,CAAS,CACL,CACJ,CACO,MAAMM,UAA6CV,CAAU,CAChE,YAAY,CAAE,OAAAW,EAAQ,IAAAH,GAAO,CACzB,MAAM,6EAA8E,CAChF,aAAc,CACV,gBAAgBC,EAAOD,CAAG,CAAC,GAC3B,aAAaI,EAAUD,CAAM,CAAC,EAC9C,EACY,KAAM,sCAClB,CAAS,CACL,CACJ,CACO,MAAME,UAA0Cb,CAAU,CAC7D,YAAY,CAAE,OAAAK,EAAQ,GAAAS,GAAM,CACxB,MAAM,yEAA0E,CAC5E,aAAc,CACV,qBAAqBA,CAAE,GACvB,kCAAkCT,CAAM,EACxD,EACY,KAAM,mCAClB,CAAS,CACL,CACJ,CCpCY,MAACU,EAA0B,aAC1BC,EAAwB,CACjC,KAAM,iBACN,KAAM,QACN,OAAQ,CACJ,CACI,KAAM,SACN,KAAM,SAClB,EACQ,CACI,KAAM,OACN,KAAM,UAClB,EACQ,CACI,KAAM,WACN,KAAM,OAClB,EACQ,CACI,KAAM,mBACN,KAAM,QAClB,EACQ,CACI,KAAM,YACN,KAAM,OAClB,CACA,CACA,EACO,eAAeC,EAAeC,EAAQ,CAAE,YAAAC,EAAa,SAAAC,EAAU,KAAAjB,EAAM,GAAAW,GAAO,CAC/E,KAAM,CAAE,KAAAO,CAAI,EAAKC,EAAkB,CAC/B,KAAAnB,EACA,IAAK,CAACa,CAAqB,CACnC,CAAK,EACK,CAACX,EAAQC,EAAMiB,EAAUtB,EAAkBG,CAAS,EAAIiB,EACxD,CAAE,SAAAG,CAAQ,EAAKN,EACfO,EAAeD,GAAY,OAAOA,GAAA,YAAAA,EAAU,UAAY,WACxDA,EAAS,QACTE,EACN,GAAI,CACA,GAAI,CAACC,EAAeb,EAAIT,CAAM,EAC1B,MAAM,IAAIQ,EAAkC,CAAE,OAAAR,EAAQ,GAAAS,CAAE,CAAE,EAC9D,MAAMH,EAASL,EAAK,SAASsB,CAAoB,EAC3C,MAAMC,EAAyB,CAC7B,KAAMN,EACN,YAAaE,CAC7B,CAAa,EACC,MAAMA,EAAa,CAAE,KAAMF,EAAU,OAAAlB,EAAQ,KAAAC,CAAI,CAAE,EACnD,CAAE,KAAMwB,CAAK,EAAK,MAAMC,EAAKb,EAAQ,CACvC,YAAAC,EACA,SAAAC,EACA,KAAMY,EAAO,CACT/B,EACAgC,EAAoB,CAAC,CAAE,KAAM,SAAW,CAAE,KAAM,QAAS,EAAG,CAACtB,EAAQP,CAAS,CAAC,CAC/F,CAAa,EACD,GAAAU,CACZ,CAAS,EACD,OAAOgB,CACX,OACOI,EAAK,CACR,MAAM,IAAInC,EAAoB,CAC1B,iBAAAE,EACA,MAAOiC,EACP,KAAA/B,EACA,UAAAC,EACA,OAAAC,EACA,KAAAC,CACZ,CAAS,CACL,CACJ,CACO,eAAeoB,EAAY,CAAE,KAAAvB,EAAM,OAAAE,EAAQ,KAAAC,CAAI,EAAK,OACvD,IAAI6B,EAAQ,IAAI,MAAM,4BAA4B,EAClD,QAASC,EAAI,EAAGA,EAAI9B,EAAK,OAAQ8B,IAAK,CAClC,MAAM5B,EAAMF,EAAK8B,CAAC,EACZC,EAAS7B,EAAI,SAAS,QAAQ,EAAI,MAAQ,OAC1C8B,EAAOD,IAAW,OAAS,CAAE,KAAAlC,EAAM,OAAAE,CAAM,EAAK,OAC9CkC,EAAUF,IAAW,OAAS,CAAE,eAAgB,kBAAkB,EAAK,CAAA,EAC7E,GAAI,CACA,MAAMG,EAAW,MAAM,MAAMhC,EAAI,QAAQ,WAAYH,EAAO,YAAW,CAAE,EAAE,QAAQ,SAAUF,CAAI,EAAG,CAChG,KAAM,KAAK,UAAUmC,CAAI,EACzB,QAAAC,EACA,OAAAF,CAChB,CAAa,EACD,IAAI1B,EAOJ,IANIJ,EAAAiC,EAAS,QAAQ,IAAI,cAAc,IAAnC,MAAAjC,EAAsC,WAAW,oBACjDI,GAAU,MAAM6B,EAAS,KAAI,GAAI,KAGjC7B,EAAU,MAAM6B,EAAS,OAEzB,CAACA,EAAS,GAAI,CACdL,EAAQ,IAAIM,EAAiB,CACzB,KAAAH,EACA,QAAS3B,GAAA,MAAAA,EAAQ,MACXC,EAAUD,EAAO,KAAK,EACtB6B,EAAS,WACf,QAASA,EAAS,QAClB,OAAQA,EAAS,OACjB,IAAAhC,CACpB,CAAiB,EACD,QACJ,CACA,GAAI,CAACkC,EAAM/B,CAAM,EAAG,CAChBwB,EAAQ,IAAIzB,EAAqC,CAC7C,OAAAC,EACA,IAAAH,CACpB,CAAiB,EACD,QACJ,CACA,OAAOG,CACX,OACOuB,EAAK,CACRC,EAAQ,IAAIM,EAAiB,CACzB,KAAAH,EACA,QAASJ,EAAI,QACb,IAAA1B,CAChB,CAAa,CACL,CACJ,CACA,MAAM2B,CACV", "x_google_ignoreList": [0, 1]}