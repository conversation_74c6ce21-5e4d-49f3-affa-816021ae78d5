import{b as l}from"./core-C0olQNtY.js";import"./index-2wea5Wgv.js";import"./events-B2jzgt6q.js";import"./index.es-CAssemqx.js";import"./index-nibyPLVP.js";const p=l`<svg fill="none" viewBox="0 0 14 16">
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M3.94 1.04a1 1 0 0 1 .7 1.23l-.48 1.68a5.85 5.85 0 0 1 8.53 4.32 5.86 5.86 0 0 1-11.4 2.56 1 1 0 0 1 1.9-.57 3.86 3.86 0 1 0 1.83-4.5l1.87.53a1 1 0 0 1-.55 1.92l-4.1-1.15a1 1 0 0 1-.69-1.23l1.16-4.1a1 1 0 0 1 1.23-.7Z"
    clip-rule="evenodd"
  />
</svg>`;export{p as refreshSvg};
//# sourceMappingURL=refresh-BeEvvjYN.js.map
