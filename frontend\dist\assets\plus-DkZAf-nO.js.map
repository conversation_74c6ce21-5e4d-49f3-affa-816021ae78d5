{"version": 3, "file": "plus-DkZAf-nO.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js"], "sourcesContent": ["import { svg } from 'lit';\nexport const plusSvg = svg `<svg\n  width=\"13\"\n  height=\"12\"\n  viewBox=\"0 0 13 12\"\n  fill=\"none\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M0.794373 5.99982C0.794373 5.52643 1.17812 5.14268 1.6515 5.14268H5.643V1.15109C5.643 0.677701 6.02675 0.293946 6.50012 0.293945C6.9735 0.293946 7.35725 0.677701 7.35725 1.15109V5.14268H11.3488C11.8221 5.14268 12.2059 5.52643 12.2059 5.99982C12.2059 6.47321 11.8221 6.85696 11.3488 6.85696H7.35725V10.8486C7.35725 11.3219 6.9735 11.7057 6.50012 11.7057C6.02675 11.7057 5.643 11.3219 5.643 10.8486V6.85696H1.6515C1.17812 6.85696 0.794373 6.47321 0.794373 5.99982Z\"\n  /></svg\n>`;\n//# sourceMappingURL=plus.js.map"], "names": ["plusSvg", "svg"], "mappings": "2JACO,MAAMA,EAAUC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;", "x_google_ignoreList": [0]}