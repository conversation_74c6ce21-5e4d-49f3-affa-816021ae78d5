{"version": 3, "file": "basic-D4Ate8cw.js", "sources": ["../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-wallet-image/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-wallet-image/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-all-wallets-image/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-all-wallets-image/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-list-wallet/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-list-wallet/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-announced-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-custom-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-external-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-featured-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-injected-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-multi-chain-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-recent-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-recommended-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connect-walletconnect-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connector-list/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connector-list/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tabs/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tabs/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-header/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-button/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-button/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-link/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-link/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-thumbnail/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-thumbnail/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-chip-button/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-chip-button/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-cta-button/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-cta-button/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-mobile-download-links/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-mobile-download-links/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/utils/w3m-connecting-widget/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/utils/w3m-connecting-widget/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-browser/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-desktop/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-mobile/index.js", "../../node_modules/qrcode/lib/can-promise.js", "../../node_modules/qrcode/lib/core/utils.js", "../../node_modules/qrcode/lib/core/error-correction-level.js", "../../node_modules/qrcode/lib/core/bit-buffer.js", "../../node_modules/qrcode/lib/core/bit-matrix.js", "../../node_modules/qrcode/lib/core/alignment-pattern.js", "../../node_modules/qrcode/lib/core/finder-pattern.js", "../../node_modules/qrcode/lib/core/mask-pattern.js", "../../node_modules/qrcode/lib/core/error-correction-code.js", "../../node_modules/qrcode/lib/core/galois-field.js", "../../node_modules/qrcode/lib/core/polynomial.js", "../../node_modules/qrcode/lib/core/reed-solomon-encoder.js", "../../node_modules/qrcode/lib/core/version-check.js", "../../node_modules/qrcode/lib/core/regex.js", "../../node_modules/qrcode/lib/core/mode.js", "../../node_modules/qrcode/lib/core/version.js", "../../node_modules/qrcode/lib/core/format-info.js", "../../node_modules/qrcode/lib/core/numeric-data.js", "../../node_modules/qrcode/lib/core/alphanumeric-data.js", "../../node_modules/encode-utf8/index.js", "../../node_modules/qrcode/lib/core/byte-data.js", "../../node_modules/qrcode/lib/core/kanji-data.js", "../../node_modules/dijkstrajs/dijkstra.js", "../../node_modules/qrcode/lib/core/segments.js", "../../node_modules/qrcode/lib/core/qrcode.js", "../../node_modules/qrcode/lib/renderer/utils.js", "../../node_modules/qrcode/lib/renderer/canvas.js", "../../node_modules/qrcode/lib/renderer/svg-tag.js", "../../node_modules/qrcode/lib/browser.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/utils/QrCode.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-qr-code/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-qr-code/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-shimmer/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/components/wui-shimmer/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/utils/ConstantsUtil.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-ux-by-reown/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-ux-by-reown/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-qrcode/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-qrcode/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-unsupported/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-connecting-wc-web/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/views/w3m-connecting-wc-view/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/views/w3m-connecting-wc-basic-view/index.js", "../../node_modules/lit-html/directives/ref.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-switch/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-switch/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-certified-switch/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-certified-switch/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-input-element/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-input-element/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-input-text/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-input-text/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-search-bar/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-search-bar/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/networkMd.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-card-select-loader/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-card-select-loader/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-grid/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-grid/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-list-item/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-list-item/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-list/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-list/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-search/styles.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-all-wallets-search/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/views/w3m-all-wallets-view/index.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-list-item/styles.js", "../../node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-list-item/index.js", "../../node_modules/@reown/appkit-scaffold-ui/dist/esm/src/views/w3m-downloads-view/index.js"], "sourcesContent": ["import { css } from 'lit';\nexport default css `\n  :host {\n    position: relative;\n    background-color: var(--wui-color-gray-glass-002);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: var(--local-size);\n    height: var(--local-size);\n    border-radius: inherit;\n    border-radius: var(--local-border-radius);\n  }\n\n  :host > wui-flex {\n    overflow: hidden;\n    border-radius: inherit;\n    border-radius: var(--local-border-radius);\n  }\n\n  :host::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    border-radius: inherit;\n    border: 1px solid var(--wui-color-gray-glass-010);\n    pointer-events: none;\n  }\n\n  :host([name='Extension'])::after {\n    border: 1px solid var(--wui-color-accent-glass-010);\n  }\n\n  :host([data-wallet-icon='allWallets']) {\n    background-color: var(--wui-all-wallets-bg-100);\n  }\n\n  :host([data-wallet-icon='allWallets'])::after {\n    border: 1px solid var(--wui-color-accent-glass-010);\n  }\n\n  wui-icon[data-parent-size='inherit'] {\n    width: 75%;\n    height: 75%;\n    align-items: center;\n  }\n\n  wui-icon[data-parent-size='sm'] {\n    width: 18px;\n    height: 18px;\n  }\n\n  wui-icon[data-parent-size='md'] {\n    width: 24px;\n    height: 24px;\n  }\n\n  wui-icon[data-parent-size='lg'] {\n    width: 42px;\n    height: 42px;\n  }\n\n  wui-icon[data-parent-size='full'] {\n    width: 100%;\n    height: 100%;\n  }\n\n  :host > wui-icon-box {\n    position: absolute;\n    overflow: hidden;\n    right: -1px;\n    bottom: -2px;\n    z-index: 1;\n    border: 2px solid var(--wui-color-bg-150, #1e1f1f);\n    padding: 1px;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-image/index.js';\nimport '../../layout/wui-flex/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-icon-box/index.js';\nimport styles from './styles.js';\nlet WuiWalletImage = class WuiWalletImage extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.size = 'md';\n        this.name = '';\n        this.installed = false;\n        this.badgeSize = 'xs';\n    }\n    render() {\n        let borderRadius = 'xxs';\n        if (this.size === 'lg') {\n            borderRadius = 'm';\n        }\n        else if (this.size === 'md') {\n            borderRadius = 'xs';\n        }\n        else {\n            borderRadius = 'xxs';\n        }\n        this.style.cssText = `\n       --local-border-radius: var(--wui-border-radius-${borderRadius});\n       --local-size: var(--wui-wallet-image-size-${this.size});\n   `;\n        if (this.walletIcon) {\n            this.dataset['walletIcon'] = this.walletIcon;\n        }\n        return html `\n      <wui-flex justifyContent=\"center\" alignItems=\"center\"> ${this.templateVisual()} </wui-flex>\n    `;\n    }\n    templateVisual() {\n        if (this.imageSrc) {\n            return html `<wui-image src=${this.imageSrc} alt=${this.name}></wui-image>`;\n        }\n        else if (this.walletIcon) {\n            return html `<wui-icon\n        data-parent-size=\"md\"\n        size=\"md\"\n        color=\"inherit\"\n        name=${this.walletIcon}\n      ></wui-icon>`;\n        }\n        return html `<wui-icon\n      data-parent-size=${this.size}\n      size=\"inherit\"\n      color=\"inherit\"\n      name=\"walletPlaceholder\"\n    ></wui-icon>`;\n    }\n};\nWuiWalletImage.styles = [elementStyles, resetStyles, styles];\n__decorate([\n    property()\n], WuiWalletImage.prototype, \"size\", void 0);\n__decorate([\n    property()\n], WuiWalletImage.prototype, \"name\", void 0);\n__decorate([\n    property()\n], WuiWalletImage.prototype, \"imageSrc\", void 0);\n__decorate([\n    property()\n], WuiWalletImage.prototype, \"walletIcon\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiWalletImage.prototype, \"installed\", void 0);\n__decorate([\n    property()\n], WuiWalletImage.prototype, \"badgeSize\", void 0);\nWuiWalletImage = __decorate([\n    customElement('wui-wallet-image')\n], WuiWalletImage);\nexport { WuiWalletImage };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    position: relative;\n    border-radius: var(--wui-border-radius-xxs);\n    width: 40px;\n    height: 40px;\n    overflow: hidden;\n    background: var(--wui-color-gray-glass-002);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: var(--wui-spacing-4xs);\n    padding: 3.75px !important;\n  }\n\n  :host::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    border-radius: inherit;\n    border: 1px solid var(--wui-color-gray-glass-010);\n    pointer-events: none;\n  }\n\n  :host > wui-wallet-image {\n    width: 14px;\n    height: 14px;\n    border-radius: var(--wui-border-radius-5xs);\n  }\n\n  :host > wui-flex {\n    padding: 2px;\n    position: fixed;\n    overflow: hidden;\n    left: 34px;\n    bottom: 8px;\n    background: var(--dark-background-150, #1e1f1f);\n    border-radius: 50%;\n    z-index: 2;\n    display: flex;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport '../../layout/wui-flex/index.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-icon-box/index.js';\nimport '../wui-wallet-image/index.js';\nimport styles from './styles.js';\nconst TOTAL_IMAGES = 4;\nlet WuiAllWalletsImage = class WuiAllWalletsImage extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.walletImages = [];\n    }\n    render() {\n        const isPlaceholders = this.walletImages.length < TOTAL_IMAGES;\n        return html `${this.walletImages\n            .slice(0, TOTAL_IMAGES)\n            .map(({ src, walletName }) => html `\n            <wui-wallet-image\n              size=\"inherit\"\n              imageSrc=${src}\n              name=${ifDefined(walletName)}\n            ></wui-wallet-image>\n          `)}\n      ${isPlaceholders\n            ? [...Array(TOTAL_IMAGES - this.walletImages.length)].map(() => html ` <wui-wallet-image size=\"inherit\" name=\"\"></wui-wallet-image>`)\n            : null}\n      <wui-flex>\n        <wui-icon-box\n          size=\"xxs\"\n          iconSize=\"xxs\"\n          iconcolor=\"success-100\"\n          backgroundcolor=\"success-100\"\n          icon=\"checkmark\"\n          background=\"opaque\"\n        ></wui-icon-box>\n      </wui-flex>`;\n    }\n};\nWuiAllWalletsImage.styles = [resetStyles, styles];\n__decorate([\n    property({ type: Array })\n], WuiAllWalletsImage.prototype, \"walletImages\", void 0);\nWuiAllWalletsImage = __decorate([\n    customElement('wui-all-wallets-image')\n], WuiAllWalletsImage);\nexport { WuiAllWalletsImage };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    column-gap: var(--wui-spacing-s);\n    padding: 7px var(--wui-spacing-l) 7px var(--wui-spacing-xs);\n    width: 100%;\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: var(--wui-border-radius-xs);\n    color: var(--wui-color-fg-100);\n  }\n\n  button > wui-text:nth-child(2) {\n    display: flex;\n    flex: 1;\n  }\n\n  button:disabled {\n    background-color: var(--wui-color-gray-glass-015);\n    color: var(--wui-color-gray-glass-015);\n  }\n\n  button:disabled > wui-tag {\n    background-color: var(--wui-color-gray-glass-010);\n    color: var(--wui-color-fg-300);\n  }\n\n  wui-icon {\n    color: var(--wui-color-fg-200) !important;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-text/index.js';\nimport '../../composites/wui-icon-box/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-all-wallets-image/index.js';\nimport '../wui-tag/index.js';\nimport '../wui-wallet-image/index.js';\nimport styles from './styles.js';\nlet WuiListWallet = class WuiListWallet extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.walletImages = [];\n        this.imageSrc = '';\n        this.name = '';\n        this.tabIdx = undefined;\n        this.installed = false;\n        this.disabled = false;\n        this.showAllWallets = false;\n        this.loading = false;\n        this.loadingSpinnerColor = 'accent-100';\n    }\n    render() {\n        return html `\n      <button ?disabled=${this.disabled} tabindex=${ifDefined(this.tabIdx)}>\n        ${this.templateAllWallets()} ${this.templateWalletImage()}\n        <wui-text variant=\"paragraph-500\" color=\"inherit\">${this.name}</wui-text>\n        ${this.templateStatus()}\n      </button>\n    `;\n    }\n    templateAllWallets() {\n        if (this.showAllWallets && this.imageSrc) {\n            return html ` <wui-all-wallets-image .imageeSrc=${this.imageSrc}> </wui-all-wallets-image> `;\n        }\n        else if (this.showAllWallets && this.walletIcon) {\n            return html ` <wui-wallet-image .walletIcon=${this.walletIcon} size=\"sm\"> </wui-wallet-image> `;\n        }\n        return null;\n    }\n    templateWalletImage() {\n        if (!this.showAllWallets && this.imageSrc) {\n            return html `<wui-wallet-image\n        size=\"sm\"\n        imageSrc=${this.imageSrc}\n        name=${this.name}\n        .installed=${this.installed}\n      ></wui-wallet-image>`;\n        }\n        else if (!this.showAllWallets && !this.imageSrc) {\n            return html `<wui-wallet-image size=\"sm\" name=${this.name}></wui-wallet-image>`;\n        }\n        return null;\n    }\n    templateStatus() {\n        if (this.loading) {\n            return html `<wui-loading-spinner\n        size=\"lg\"\n        color=${this.loadingSpinnerColor}\n      ></wui-loading-spinner>`;\n        }\n        else if (this.tagLabel && this.tagVariant) {\n            return html `<wui-tag variant=${this.tagVariant}>${this.tagLabel}</wui-tag>`;\n        }\n        else if (this.icon) {\n            return html `<wui-icon color=\"inherit\" size=\"sm\" name=${this.icon}></wui-icon>`;\n        }\n        return null;\n    }\n};\nWuiListWallet.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property({ type: Array })\n], WuiListWallet.prototype, \"walletImages\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"imageSrc\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"name\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"tagLabel\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"tagVariant\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"icon\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"walletIcon\", void 0);\n__decorate([\n    property()\n], WuiListWallet.prototype, \"tabIdx\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListWallet.prototype, \"installed\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListWallet.prototype, \"disabled\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListWallet.prototype, \"showAllWallets\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListWallet.prototype, \"loading\", void 0);\n__decorate([\n    property({ type: String })\n], WuiListWallet.prototype, \"loadingSpinnerColor\", void 0);\nWuiListWallet = __decorate([\n    customElement('wui-list-wallet')\n], WuiListWallet);\nexport { WuiListWallet };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { ApiController, ConnectorController, CoreHelperUtil, EventsController, OptionsController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-list-wallet';\nlet W3mAllWalletsWidget = class W3mAllWalletsWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.count = ApiController.state.count;\n        this.filteredCount = ApiController.state.filteredWallets.length;\n        this.isFetchingRecommendedWallets = ApiController.state.isFetchingRecommendedWallets;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)), ApiController.subscribeKey('count', val => (this.count = val)), ApiController.subscribeKey('filteredWallets', val => (this.filteredCount = val.length)), ApiController.subscribeKey('isFetchingRecommendedWallets', val => (this.isFetchingRecommendedWallets = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const wcConnector = this.connectors.find(c => c.id === 'walletConnect');\n        const { allWallets } = OptionsController.state;\n        if (!wcConnector || allWallets === 'HIDE') {\n            return null;\n        }\n        if (allWallets === 'ONLY_MOBILE' && !CoreHelperUtil.isMobile()) {\n            return null;\n        }\n        const featuredCount = ApiController.state.featured.length;\n        const rawCount = this.count + featuredCount;\n        const roundedCount = rawCount < 10 ? rawCount : Math.floor(rawCount / 10) * 10;\n        const count = this.filteredCount > 0 ? this.filteredCount : roundedCount;\n        let tagLabel = `${count}`;\n        if (this.filteredCount > 0) {\n            tagLabel = `${this.filteredCount}`;\n        }\n        else if (count < rawCount) {\n            tagLabel = `${count}+`;\n        }\n        return html `\n      <wui-list-wallet\n        name=\"All Wallets\"\n        walletIcon=\"allWallets\"\n        showAllWallets\n        @click=${this.onAllWallets.bind(this)}\n        tagLabel=${tagLabel}\n        tagVariant=\"shade\"\n        data-testid=\"all-wallets\"\n        tabIdx=${ifDefined(this.tabIdx)}\n        .loading=${this.isFetchingRecommendedWallets}\n        loadingSpinnerColor=${this.isFetchingRecommendedWallets ? 'fg-300' : 'accent-100'}\n      ></wui-list-wallet>\n    `;\n    }\n    onAllWallets() {\n        EventsController.sendEvent({ type: 'track', event: 'CLICK_ALL_WALLETS' });\n        RouterController.push('AllWallets');\n    }\n};\n__decorate([\n    property()\n], W3mAllWalletsWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsWidget.prototype, \"connectors\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsWidget.prototype, \"count\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsWidget.prototype, \"filteredCount\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsWidget.prototype, \"isFetchingRecommendedWallets\", void 0);\nW3mAllWalletsWidget = __decorate([\n    customElement('w3m-all-wallets-widget')\n], W3mAllWalletsWidget);\nexport { W3mAllWalletsWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectorController, CoreHelperUtil, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nimport { ConnectorUtil } from '../../utils/ConnectorUtil.js';\nlet W3mConnectAnnouncedWidget = class W3mConnectAnnouncedWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const announcedConnectors = this.connectors.filter(connector => connector.type === 'ANNOUNCED');\n        if (!announcedConnectors?.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${announcedConnectors\n            .filter(ConnectorUtil.showConnector)\n            .map(connector => html `\n              <wui-list-wallet\n                imageSrc=${ifDefined(AssetUtil.getConnectorImage(connector))}\n                name=${connector.name ?? 'Unknown'}\n                @click=${() => this.onConnector(connector)}\n                tagVariant=\"success\"\n                tagLabel=\"installed\"\n                data-testid=${`wallet-selector-${connector.id}`}\n                .installed=${true}\n                tabIdx=${ifDefined(this.tabIdx)}\n              >\n              </wui-list-wallet>\n            `)}\n      </wui-flex>\n    `;\n    }\n    onConnector(connector) {\n        if (connector.id === 'walletConnect') {\n            if (CoreHelperUtil.isMobile()) {\n                RouterController.push('AllWallets');\n            }\n            else {\n                RouterController.push('ConnectingWalletConnect');\n            }\n        }\n        else {\n            RouterController.push('ConnectingExternal', { connector });\n        }\n    }\n};\n__decorate([\n    property()\n], W3mConnectAnnouncedWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectAnnouncedWidget.prototype, \"connectors\", void 0);\nW3mConnectAnnouncedWidget = __decorate([\n    customElement('w3m-connect-announced-widget')\n], W3mConnectAnnouncedWidget);\nexport { W3mConnectAnnouncedWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectionController, ConnectorController, CoreHelperUtil, OptionsController, RouterController, StorageUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nlet W3mConnectCustomWidget = class W3mConnectCustomWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.loading = false;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)));\n        if (CoreHelperUtil.isTelegram() && CoreHelperUtil.isIos()) {\n            this.loading = !ConnectionController.state.wcUri;\n            this.unsubscribe.push(ConnectionController.subscribeKey('wcUri', val => (this.loading = !val)));\n        }\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const { customWallets } = OptionsController.state;\n        if (!customWallets?.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        const wallets = this.filterOutDuplicateWallets(customWallets);\n        return html `<wui-flex flexDirection=\"column\" gap=\"xs\">\n      ${wallets.map(wallet => html `\n          <wui-list-wallet\n            imageSrc=${ifDefined(AssetUtil.getWalletImage(wallet))}\n            name=${wallet.name ?? 'Unknown'}\n            @click=${() => this.onConnectWallet(wallet)}\n            data-testid=${`wallet-selector-${wallet.id}`}\n            tabIdx=${ifDefined(this.tabIdx)}\n            ?loading=${this.loading}\n          >\n          </wui-list-wallet>\n        `)}\n    </wui-flex>`;\n    }\n    filterOutDuplicateWallets(wallets) {\n        const recent = StorageUtil.getRecentWallets();\n        const connectorRDNSs = this.connectors\n            .map(connector => connector.info?.rdns)\n            .filter(Boolean);\n        const recentRDNSs = recent.map(wallet => wallet.rdns).filter(Boolean);\n        const allRDNSs = connectorRDNSs.concat(recentRDNSs);\n        if (allRDNSs.includes('io.metamask.mobile') && CoreHelperUtil.isMobile()) {\n            const index = allRDNSs.indexOf('io.metamask.mobile');\n            allRDNSs[index] = 'io.metamask';\n        }\n        const filtered = wallets.filter(wallet => !allRDNSs.includes(String(wallet?.rdns)));\n        return filtered;\n    }\n    onConnectWallet(wallet) {\n        if (this.loading) {\n            return;\n        }\n        RouterController.push('ConnectingWalletConnect', { wallet });\n    }\n};\n__decorate([\n    property()\n], W3mConnectCustomWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectCustomWidget.prototype, \"connectors\", void 0);\n__decorate([\n    state()\n], W3mConnectCustomWidget.prototype, \"loading\", void 0);\nW3mConnectCustomWidget = __decorate([\n    customElement('w3m-connect-custom-widget')\n], W3mConnectCustomWidget);\nexport { W3mConnectCustomWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { ConstantsUtil } from '@reown/appkit-common';\nimport { AssetUtil, ConnectorController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nimport { ConnectorUtil } from '../../utils/ConnectorUtil.js';\nlet W3mConnectExternalWidget = class W3mConnectExternalWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const externalConnectors = this.connectors.filter(connector => connector.type === 'EXTERNAL');\n        const filteredOutExcludedConnectors = externalConnectors.filter(ConnectorUtil.showConnector);\n        const filteredOutCoinbaseConnectors = filteredOutExcludedConnectors.filter(connector => connector.id !== ConstantsUtil.CONNECTOR_ID.COINBASE_SDK);\n        if (!filteredOutCoinbaseConnectors?.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${filteredOutCoinbaseConnectors.map(connector => html `\n            <wui-list-wallet\n              imageSrc=${ifDefined(AssetUtil.getConnectorImage(connector))}\n              .installed=${true}\n              name=${connector.name ?? 'Unknown'}\n              data-testid=${`wallet-selector-external-${connector.id}`}\n              @click=${() => this.onConnector(connector)}\n              tabIdx=${ifDefined(this.tabIdx)}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnector(connector) {\n        RouterController.push('ConnectingExternal', { connector });\n    }\n};\n__decorate([\n    property()\n], W3mConnectExternalWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectExternalWidget.prototype, \"connectors\", void 0);\nW3mConnectExternalWidget = __decorate([\n    customElement('w3m-connect-external-widget')\n], W3mConnectExternalWidget);\nexport { W3mConnectExternalWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectorController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nlet W3mConnectFeaturedWidget = class W3mConnectFeaturedWidget extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.tabIdx = undefined;\n        this.wallets = [];\n    }\n    render() {\n        if (!this.wallets.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${this.wallets.map(wallet => html `\n            <wui-list-wallet\n              data-testid=${`wallet-selector-featured-${wallet.id}`}\n              imageSrc=${ifDefined(AssetUtil.getWalletImage(wallet))}\n              name=${wallet.name ?? 'Unknown'}\n              @click=${() => this.onConnectWallet(wallet)}\n              tabIdx=${ifDefined(this.tabIdx)}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnectWallet(wallet) {\n        ConnectorController.selectWalletConnector(wallet);\n    }\n};\n__decorate([\n    property()\n], W3mConnectFeaturedWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    property()\n], W3mConnectFeaturedWidget.prototype, \"wallets\", void 0);\nW3mConnectFeaturedWidget = __decorate([\n    customElement('w3m-connect-featured-widget')\n], W3mConnectFeaturedWidget);\nexport { W3mConnectFeaturedWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectorController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nimport { ConnectorUtil } from '../../utils/ConnectorUtil.js';\nlet W3mConnectInjectedWidget = class W3mConnectInjectedWidget extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.tabIdx = undefined;\n        this.connectors = [];\n    }\n    render() {\n        const injectedConnectors = this.connectors.filter(ConnectorUtil.showConnector);\n        if (injectedConnectors.length === 0) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${injectedConnectors.map(connector => html `\n            <wui-list-wallet\n              imageSrc=${ifDefined(AssetUtil.getConnectorImage(connector))}\n              .installed=${true}\n              name=${connector.name ?? 'Unknown'}\n              tagVariant=\"success\"\n              tagLabel=\"installed\"\n              data-testid=${`wallet-selector-${connector.id}`}\n              @click=${() => this.onConnector(connector)}\n              tabIdx=${ifDefined(this.tabIdx)}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnector(connector) {\n        ConnectorController.setActiveConnector(connector);\n        RouterController.push('ConnectingExternal', { connector });\n    }\n};\n__decorate([\n    property()\n], W3mConnectInjectedWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    property()\n], W3mConnectInjectedWidget.prototype, \"connectors\", void 0);\nW3mConnectInjectedWidget = __decorate([\n    customElement('w3m-connect-injected-widget')\n], W3mConnectInjectedWidget);\nexport { W3mConnectInjectedWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectorController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nlet W3mConnectMultiChainWidget = class W3mConnectMultiChainWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const multiChainConnectors = this.connectors.filter(connector => connector.type === 'MULTI_CHAIN' && connector.name !== 'WalletConnect');\n        if (!multiChainConnectors?.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${multiChainConnectors.map(connector => html `\n            <wui-list-wallet\n              imageSrc=${ifDefined(AssetUtil.getConnectorImage(connector))}\n              .installed=${true}\n              name=${connector.name ?? 'Unknown'}\n              tagVariant=\"shade\"\n              tagLabel=\"multichain\"\n              data-testid=${`wallet-selector-${connector.id}`}\n              @click=${() => this.onConnector(connector)}\n              tabIdx=${ifDefined(this.tabIdx)}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnector(connector) {\n        ConnectorController.setActiveConnector(connector);\n        RouterController.push('ConnectingMultiChain');\n    }\n};\n__decorate([\n    property()\n], W3mConnectMultiChainWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectMultiChainWidget.prototype, \"connectors\", void 0);\nW3mConnectMultiChainWidget = __decorate([\n    customElement('w3m-connect-multi-chain-widget')\n], W3mConnectMultiChainWidget);\nexport { W3mConnectMultiChainWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ChainController, ConnectionController, ConnectorController, CoreHelperUtil, StorageUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nimport { WalletUtil } from '../../utils/WalletUtil.js';\nlet W3mConnectRecentWidget = class W3mConnectRecentWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.loading = false;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)));\n        if (CoreHelperUtil.isTelegram() && CoreHelperUtil.isIos()) {\n            this.loading = !ConnectionController.state.wcUri;\n            this.unsubscribe.push(ConnectionController.subscribeKey('wcUri', val => (this.loading = !val)));\n        }\n    }\n    render() {\n        const recentWallets = StorageUtil.getRecentWallets();\n        const filteredRecentWallets = recentWallets\n            .filter(wallet => !WalletUtil.isExcluded(wallet))\n            .filter(wallet => !this.hasWalletConnector(wallet))\n            .filter(wallet => this.isWalletCompatibleWithCurrentChain(wallet));\n        if (!filteredRecentWallets.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${filteredRecentWallets.map(wallet => html `\n            <wui-list-wallet\n              imageSrc=${ifDefined(AssetUtil.getWalletImage(wallet))}\n              name=${wallet.name ?? 'Unknown'}\n              @click=${() => this.onConnectWallet(wallet)}\n              tagLabel=\"recent\"\n              tagVariant=\"shade\"\n              tabIdx=${ifDefined(this.tabIdx)}\n              ?loading=${this.loading}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnectWallet(wallet) {\n        if (this.loading) {\n            return;\n        }\n        ConnectorController.selectWalletConnector(wallet);\n    }\n    hasWalletConnector(wallet) {\n        return this.connectors.some(connector => connector.id === wallet.id || connector.name === wallet.name);\n    }\n    isWalletCompatibleWithCurrentChain(wallet) {\n        const currentNamespace = ChainController.state.activeChain;\n        if (currentNamespace && wallet.chains) {\n            return wallet.chains.some(c => {\n                const chainNamespace = c.split(':')[0];\n                return currentNamespace === chainNamespace;\n            });\n        }\n        return true;\n    }\n};\n__decorate([\n    property()\n], W3mConnectRecentWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectRecentWidget.prototype, \"connectors\", void 0);\n__decorate([\n    state()\n], W3mConnectRecentWidget.prototype, \"loading\", void 0);\nW3mConnectRecentWidget = __decorate([\n    customElement('w3m-connect-recent-widget')\n], W3mConnectRecentWidget);\nexport { W3mConnectRecentWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectionController, ConnectorController, CoreHelperUtil, OptionsController, RouterController, StorageUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-wallet';\nimport { WalletUtil } from '../../utils/WalletUtil.js';\nlet W3mConnectRecommendedWidget = class W3mConnectRecommendedWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.wallets = [];\n        this.loading = false;\n        if (CoreHelperUtil.isTelegram() && CoreHelperUtil.isIos()) {\n            this.loading = !ConnectionController.state.wcUri;\n            this.unsubscribe.push(ConnectionController.subscribeKey('wcUri', val => (this.loading = !val)));\n        }\n    }\n    render() {\n        const { connectors } = ConnectorController.state;\n        const { customWallets, featuredWalletIds } = OptionsController.state;\n        const recentWallets = StorageUtil.getRecentWallets();\n        const wcConnector = connectors.find(c => c.id === 'walletConnect');\n        const injectedConnectors = connectors.filter(c => c.type === 'INJECTED' || c.type === 'ANNOUNCED' || c.type === 'MULTI_CHAIN');\n        const injectedWallets = injectedConnectors.filter(i => i.name !== 'Browser Wallet');\n        if (!wcConnector) {\n            return null;\n        }\n        if (featuredWalletIds || customWallets || !this.wallets.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        const overrideLength = injectedWallets.length + recentWallets.length;\n        const maxRecommended = Math.max(0, 2 - overrideLength);\n        const wallets = WalletUtil.filterOutDuplicateWallets(this.wallets).slice(0, maxRecommended);\n        if (!wallets.length) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\">\n        ${wallets.map(wallet => html `\n            <wui-list-wallet\n              imageSrc=${ifDefined(AssetUtil.getWalletImage(wallet))}\n              name=${wallet?.name ?? 'Unknown'}\n              @click=${() => this.onConnectWallet(wallet)}\n              tabIdx=${ifDefined(this.tabIdx)}\n              ?loading=${this.loading}\n            >\n            </wui-list-wallet>\n          `)}\n      </wui-flex>\n    `;\n    }\n    onConnectWallet(wallet) {\n        if (this.loading) {\n            return;\n        }\n        const connector = ConnectorController.getConnector(wallet.id, wallet.rdns);\n        if (connector) {\n            RouterController.push('ConnectingExternal', { connector });\n        }\n        else {\n            RouterController.push('ConnectingWalletConnect', { wallet });\n        }\n    }\n};\n__decorate([\n    property()\n], W3mConnectRecommendedWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    property()\n], W3mConnectRecommendedWidget.prototype, \"wallets\", void 0);\n__decorate([\n    state()\n], W3mConnectRecommendedWidget.prototype, \"loading\", void 0);\nW3mConnectRecommendedWidget = __decorate([\n    customElement('w3m-connect-recommended-widget')\n], W3mConnectRecommendedWidget);\nexport { W3mConnectRecommendedWidget };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetController, ConnectorController, CoreHelperUtil, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-list-wallet';\nlet W3mConnectWalletConnectWidget = class W3mConnectWalletConnectWidget extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.connectorImages = AssetController.state.connectorImages;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)), AssetController.subscribeKey('connectorImages', val => (this.connectorImages = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        if (CoreHelperUtil.isMobile()) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        const connector = this.connectors.find(c => c.id === 'walletConnect');\n        if (!connector) {\n            this.style.cssText = `display: none`;\n            return null;\n        }\n        const connectorImage = connector.imageUrl || this.connectorImages[connector?.imageId ?? ''];\n        return html `\n      <wui-list-wallet\n        imageSrc=${ifDefined(connectorImage)}\n        name=${connector.name ?? 'Unknown'}\n        @click=${() => this.onConnector(connector)}\n        tagLabel=\"qr code\"\n        tagVariant=\"main\"\n        tabIdx=${ifDefined(this.tabIdx)}\n        data-testid=\"wallet-selector-walletconnect\"\n      >\n      </wui-list-wallet>\n    `;\n    }\n    onConnector(connector) {\n        ConnectorController.setActiveConnector(connector);\n        RouterController.push('ConnectingWalletConnect');\n    }\n};\n__decorate([\n    property()\n], W3mConnectWalletConnectWidget.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectWalletConnectWidget.prototype, \"connectors\", void 0);\n__decorate([\n    state()\n], W3mConnectWalletConnectWidget.prototype, \"connectorImages\", void 0);\nW3mConnectWalletConnectWidget = __decorate([\n    customElement('w3m-connect-walletconnect-widget')\n], W3mConnectWalletConnectWidget);\nexport { W3mConnectWalletConnectWidget };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    margin-top: var(--wui-spacing-3xs);\n  }\n  wui-separator {\n    margin: var(--wui-spacing-m) calc(var(--wui-spacing-m) * -1) var(--wui-spacing-xs)\n      calc(var(--wui-spacing-m) * -1);\n    width: calc(100% + var(--wui-spacing-s) * 2);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { ApiController, ConnectorController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '../../partials/w3m-connect-announced-widget/index.js';\nimport '../../partials/w3m-connect-custom-widget/index.js';\nimport '../../partials/w3m-connect-external-widget/index.js';\nimport '../../partials/w3m-connect-featured-widget/index.js';\nimport '../../partials/w3m-connect-injected-widget/index.js';\nimport '../../partials/w3m-connect-multi-chain-widget/index.js';\nimport '../../partials/w3m-connect-recent-widget/index.js';\nimport '../../partials/w3m-connect-recommended-widget/index.js';\nimport '../../partials/w3m-connect-walletconnect-widget/index.js';\nimport { ConnectorUtil } from '../../utils/ConnectorUtil.js';\nimport styles from './styles.js';\nlet W3mConnectorList = class W3mConnectorList extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.tabIdx = undefined;\n        this.connectors = ConnectorController.state.connectors;\n        this.recommended = ApiController.state.recommended;\n        this.featured = ApiController.state.featured;\n        this.unsubscribe.push(ConnectorController.subscribeKey('connectors', val => (this.connectors = val)), ApiController.subscribeKey('recommended', val => (this.recommended = val)), ApiController.subscribeKey('featured', val => (this.featured = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        return html `\n      <wui-flex flexDirection=\"column\" gap=\"xs\"> ${this.connectorListTemplate()} </wui-flex>\n    `;\n    }\n    connectorListTemplate() {\n        const { custom, recent, announced, injected, multiChain, recommended, featured, external } = ConnectorUtil.getConnectorsByType(this.connectors, this.recommended, this.featured);\n        const connectorTypeOrder = ConnectorUtil.getConnectorTypeOrder({\n            custom,\n            recent,\n            announced,\n            injected,\n            multiChain,\n            recommended,\n            featured,\n            external\n        });\n        return connectorTypeOrder.map(type => {\n            switch (type) {\n                case 'injected':\n                    return html `\n            ${multiChain.length\n                        ? html `<w3m-connect-multi-chain-widget\n                  tabIdx=${ifDefined(this.tabIdx)}\n                ></w3m-connect-multi-chain-widget>`\n                        : null}\n            ${announced.length\n                        ? html `<w3m-connect-announced-widget\n                  tabIdx=${ifDefined(this.tabIdx)}\n                ></w3m-connect-announced-widget>`\n                        : null}\n            ${injected.length\n                        ? html `<w3m-connect-injected-widget\n                  .connectors=${injected}\n                  tabIdx=${ifDefined(this.tabIdx)}\n                ></w3m-connect-injected-widget>`\n                        : null}\n          `;\n                case 'walletConnect':\n                    return html `<w3m-connect-walletconnect-widget\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-walletconnect-widget>`;\n                case 'recent':\n                    return html `<w3m-connect-recent-widget\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-recent-widget>`;\n                case 'featured':\n                    return html `<w3m-connect-featured-widget\n            .wallets=${featured}\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-featured-widget>`;\n                case 'custom':\n                    return html `<w3m-connect-custom-widget\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-custom-widget>`;\n                case 'external':\n                    return html `<w3m-connect-external-widget\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-external-widget>`;\n                case 'recommended':\n                    return html `<w3m-connect-recommended-widget\n            .wallets=${recommended}\n            tabIdx=${ifDefined(this.tabIdx)}\n          ></w3m-connect-recommended-widget>`;\n                default:\n                    console.warn(`Unknown connector type: ${type}`);\n                    return null;\n            }\n        });\n    }\n};\nW3mConnectorList.styles = styles;\n__decorate([\n    property()\n], W3mConnectorList.prototype, \"tabIdx\", void 0);\n__decorate([\n    state()\n], W3mConnectorList.prototype, \"connectors\", void 0);\n__decorate([\n    state()\n], W3mConnectorList.prototype, \"recommended\", void 0);\n__decorate([\n    state()\n], W3mConnectorList.prototype, \"featured\", void 0);\nW3mConnectorList = __decorate([\n    customElement('w3m-connector-list')\n], W3mConnectorList);\nexport { W3mConnectorList };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: inline-flex;\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: var(--wui-border-radius-3xl);\n    padding: var(--wui-spacing-3xs);\n    position: relative;\n    height: 36px;\n    min-height: 36px;\n    overflow: hidden;\n  }\n\n  :host::before {\n    content: '';\n    position: absolute;\n    pointer-events: none;\n    top: 4px;\n    left: 4px;\n    display: block;\n    width: var(--local-tab-width);\n    height: 28px;\n    border-radius: var(--wui-border-radius-3xl);\n    background-color: var(--wui-color-gray-glass-002);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);\n    transform: translateX(calc(var(--local-tab) * var(--local-tab-width)));\n    transition: transform var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: background-color, opacity;\n  }\n\n  :host([data-type='flex'])::before {\n    left: 3px;\n    transform: translateX(calc((var(--local-tab) * 34px) + (var(--local-tab) * 4px)));\n  }\n\n  :host([data-type='flex']) {\n    display: flex;\n    padding: 0px 0px 0px 12px;\n    gap: 4px;\n  }\n\n  :host([data-type='flex']) > button > wui-text {\n    position: absolute;\n    left: 18px;\n    opacity: 0;\n  }\n\n  button[data-active='true'] > wui-icon,\n  button[data-active='true'] > wui-text {\n    color: var(--wui-color-fg-100);\n  }\n\n  button[data-active='false'] > wui-icon,\n  button[data-active='false'] > wui-text {\n    color: var(--wui-color-fg-200);\n  }\n\n  button[data-active='true']:disabled,\n  button[data-active='false']:disabled {\n    background-color: transparent;\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  button[data-active='true']:disabled > wui-text {\n    color: var(--wui-color-fg-200);\n  }\n\n  button[data-active='false']:disabled > wui-text {\n    color: var(--wui-color-fg-300);\n  }\n\n  button > wui-icon,\n  button > wui-text {\n    pointer-events: none;\n    transition: color var(--wui-e ase-out-power-1) var(--wui-duration-md);\n    will-change: color;\n  }\n\n  button {\n    width: var(--local-tab-width);\n    transition: background-color var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: background-color;\n  }\n\n  :host([data-type='flex']) > button {\n    width: 34px;\n    position: relative;\n    display: flex;\n    justify-content: flex-start;\n  }\n\n  button:hover:enabled,\n  button:active:enabled {\n    background-color: transparent !important;\n  }\n\n  button:hover:enabled > wui-icon,\n  button:active:enabled > wui-icon {\n    transition: all var(--wui-ease-out-power-1) var(--wui-duration-lg);\n    color: var(--wui-color-fg-125);\n  }\n\n  button:hover:enabled > wui-text,\n  button:active:enabled > wui-text {\n    transition: all var(--wui-ease-out-power-1) var(--wui-duration-lg);\n    color: var(--wui-color-fg-125);\n  }\n\n  button {\n    border-radius: var(--wui-border-radius-3xl);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-text/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiTabs = class WuiTabs extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.tabs = [];\n        this.onTabChange = () => null;\n        this.buttons = [];\n        this.disabled = false;\n        this.localTabWidth = '100px';\n        this.activeTab = 0;\n        this.isDense = false;\n    }\n    render() {\n        this.isDense = this.tabs.length > 3;\n        this.style.cssText = `\n      --local-tab: ${this.activeTab};\n      --local-tab-width: ${this.localTabWidth};\n    `;\n        this.dataset['type'] = this.isDense ? 'flex' : 'block';\n        return this.tabs.map((tab, index) => {\n            const isActive = index === this.activeTab;\n            return html `\n        <button\n          ?disabled=${this.disabled}\n          @click=${() => this.onTabClick(index)}\n          data-active=${isActive}\n          data-testid=\"tab-${tab.label?.toLowerCase()}\"\n        >\n          ${this.iconTemplate(tab)}\n          <wui-text variant=\"small-600\" color=\"inherit\"> ${tab.label} </wui-text>\n        </button>\n      `;\n        });\n    }\n    firstUpdated() {\n        if (this.shadowRoot && this.isDense) {\n            this.buttons = [...this.shadowRoot.querySelectorAll('button')];\n            setTimeout(() => {\n                this.animateTabs(0, true);\n            }, 0);\n        }\n    }\n    iconTemplate(tab) {\n        if (tab.icon) {\n            return html `<wui-icon size=\"xs\" color=\"inherit\" name=${tab.icon}></wui-icon>`;\n        }\n        return null;\n    }\n    onTabClick(index) {\n        if (this.buttons) {\n            this.animateTabs(index, false);\n        }\n        this.activeTab = index;\n        this.onTabChange(index);\n    }\n    animateTabs(index, initialAnimation) {\n        const passiveBtn = this.buttons[this.activeTab];\n        const activeBtn = this.buttons[index];\n        const passiveBtnText = passiveBtn?.querySelector('wui-text');\n        const activeBtnText = activeBtn?.querySelector('wui-text');\n        const activeBtnBounds = activeBtn?.getBoundingClientRect();\n        const activeBtnTextBounds = activeBtnText?.getBoundingClientRect();\n        if (passiveBtn && passiveBtnText && !initialAnimation && index !== this.activeTab) {\n            passiveBtnText.animate([{ opacity: 0 }], {\n                duration: 50,\n                easing: 'ease',\n                fill: 'forwards'\n            });\n            passiveBtn.animate([{ width: `34px` }], {\n                duration: 500,\n                easing: 'ease',\n                fill: 'forwards'\n            });\n        }\n        if (activeBtn && activeBtnBounds && activeBtnTextBounds && activeBtnText) {\n            if (index !== this.activeTab || initialAnimation) {\n                this.localTabWidth = `${Math.round(activeBtnBounds.width + activeBtnTextBounds.width) + 6}px`;\n                activeBtn.animate([{ width: `${activeBtnBounds.width + activeBtnTextBounds.width}px` }], {\n                    duration: initialAnimation ? 0 : 500,\n                    fill: 'forwards',\n                    easing: 'ease'\n                });\n                activeBtnText.animate([{ opacity: 1 }], {\n                    duration: initialAnimation ? 0 : 125,\n                    delay: initialAnimation ? 0 : 200,\n                    fill: 'forwards',\n                    easing: 'ease'\n                });\n            }\n        }\n    }\n};\nWuiTabs.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property({ type: Array })\n], WuiTabs.prototype, \"tabs\", void 0);\n__decorate([\n    property()\n], WuiTabs.prototype, \"onTabChange\", void 0);\n__decorate([\n    property({ type: Array })\n], WuiTabs.prototype, \"buttons\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiTabs.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiTabs.prototype, \"localTabWidth\", void 0);\n__decorate([\n    state()\n], WuiTabs.prototype, \"activeTab\", void 0);\n__decorate([\n    state()\n], WuiTabs.prototype, \"isDense\", void 0);\nWuiTabs = __decorate([\n    customElement('wui-tabs')\n], WuiTabs);\nexport { WuiTabs };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-tabs';\nlet W3mConnectingHeader = class W3mConnectingHeader extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.platformTabs = [];\n        this.unsubscribe = [];\n        this.platforms = [];\n        this.onSelectPlatfrom = undefined;\n    }\n    disconnectCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        const tabs = this.generateTabs();\n        return html `\n      <wui-flex justifyContent=\"center\" .padding=${['0', '0', 'l', '0']}>\n        <wui-tabs .tabs=${tabs} .onTabChange=${this.onTabChange.bind(this)}></wui-tabs>\n      </wui-flex>\n    `;\n    }\n    generateTabs() {\n        const tabs = this.platforms.map(platform => {\n            if (platform === 'browser') {\n                return { label: 'Browser', icon: 'extension', platform: 'browser' };\n            }\n            else if (platform === 'mobile') {\n                return { label: 'Mobile', icon: 'mobile', platform: 'mobile' };\n            }\n            else if (platform === 'qrcode') {\n                return { label: 'Mobile', icon: 'mobile', platform: 'qrcode' };\n            }\n            else if (platform === 'web') {\n                return { label: 'Webapp', icon: 'browser', platform: 'web' };\n            }\n            else if (platform === 'desktop') {\n                return { label: 'Desktop', icon: 'desktop', platform: 'desktop' };\n            }\n            return { label: 'Browser', icon: 'extension', platform: 'unsupported' };\n        });\n        this.platformTabs = tabs.map(({ platform }) => platform);\n        return tabs;\n    }\n    onTabChange(index) {\n        const tab = this.platformTabs[index];\n        if (tab) {\n            this.onSelectPlatfrom?.(tab);\n        }\n    }\n};\n__decorate([\n    property({ type: Array })\n], W3mConnectingHeader.prototype, \"platforms\", void 0);\n__decorate([\n    property()\n], W3mConnectingHeader.prototype, \"onSelectPlatfrom\", void 0);\nW3mConnectingHeader = __decorate([\n    customElement('w3m-connecting-header')\n], W3mConnectingHeader);\nexport { W3mConnectingHeader };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    width: var(--local-width);\n    position: relative;\n  }\n\n  button {\n    border: none;\n    border-radius: var(--local-border-radius);\n    width: var(--local-width);\n    white-space: nowrap;\n  }\n\n  /* -- Sizes --------------------------------------------------- */\n  button[data-size='md'] {\n    padding: 8.2px var(--wui-spacing-l) 9px var(--wui-spacing-l);\n    height: 36px;\n  }\n\n  button[data-size='md'][data-icon-left='true'][data-icon-right='false'] {\n    padding: 8.2px var(--wui-spacing-l) 9px var(--wui-spacing-s);\n  }\n\n  button[data-size='md'][data-icon-right='true'][data-icon-left='false'] {\n    padding: 8.2px var(--wui-spacing-s) 9px var(--wui-spacing-l);\n  }\n\n  button[data-size='lg'] {\n    padding: var(--wui-spacing-m) var(--wui-spacing-2l);\n    height: 48px;\n  }\n\n  /* -- Variants --------------------------------------------------------- */\n  button[data-variant='main'] {\n    background-color: var(--wui-color-accent-100);\n    color: var(--wui-color-inverse-100);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  button[data-variant='inverse'] {\n    background-color: var(--wui-color-inverse-100);\n    color: var(--wui-color-inverse-000);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  button[data-variant='accent'] {\n    background-color: var(--wui-color-accent-glass-010);\n    color: var(--wui-color-accent-100);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);\n  }\n\n  button[data-variant='accent-error'] {\n    background: var(--wui-color-error-glass-015);\n    color: var(--wui-color-error-100);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-error-glass-010);\n  }\n\n  button[data-variant='accent-success'] {\n    background: var(--wui-color-success-glass-015);\n    color: var(--wui-color-success-100);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-success-glass-010);\n  }\n\n  button[data-variant='neutral'] {\n    background: transparent;\n    color: var(--wui-color-fg-100);\n    border: none;\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);\n  }\n\n  /* -- Focus states --------------------------------------------------- */\n  button[data-variant='main']:focus-visible:enabled {\n    background-color: var(--wui-color-accent-090);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-accent-100),\n      0 0 0 4px var(--wui-color-accent-glass-020);\n  }\n  button[data-variant='inverse']:focus-visible:enabled {\n    background-color: var(--wui-color-inverse-100);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-gray-glass-010),\n      0 0 0 4px var(--wui-color-accent-glass-020);\n  }\n  button[data-variant='accent']:focus-visible:enabled {\n    background-color: var(--wui-color-accent-glass-010);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-accent-100),\n      0 0 0 4px var(--wui-color-accent-glass-020);\n  }\n  button[data-variant='accent-error']:focus-visible:enabled {\n    background: var(--wui-color-error-glass-015);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-error-100),\n      0 0 0 4px var(--wui-color-error-glass-020);\n  }\n  button[data-variant='accent-success']:focus-visible:enabled {\n    background: var(--wui-color-success-glass-015);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-success-100),\n      0 0 0 4px var(--wui-color-success-glass-020);\n  }\n  button[data-variant='neutral']:focus-visible:enabled {\n    background: var(--wui-color-gray-glass-005);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-gray-glass-010),\n      0 0 0 4px var(--wui-color-gray-glass-002);\n  }\n\n  /* -- Hover & Active states ----------------------------------------------------------- */\n  @media (hover: hover) and (pointer: fine) {\n    button[data-variant='main']:hover:enabled {\n      background-color: var(--wui-color-accent-090);\n    }\n\n    button[data-variant='main']:active:enabled {\n      background-color: var(--wui-color-accent-080);\n    }\n\n    button[data-variant='accent']:hover:enabled {\n      background-color: var(--wui-color-accent-glass-015);\n    }\n\n    button[data-variant='accent']:active:enabled {\n      background-color: var(--wui-color-accent-glass-020);\n    }\n\n    button[data-variant='accent-error']:hover:enabled {\n      background: var(--wui-color-error-glass-020);\n      color: var(--wui-color-error-100);\n    }\n\n    button[data-variant='accent-error']:active:enabled {\n      background: var(--wui-color-error-glass-030);\n      color: var(--wui-color-error-100);\n    }\n\n    button[data-variant='accent-success']:hover:enabled {\n      background: var(--wui-color-success-glass-020);\n      color: var(--wui-color-success-100);\n    }\n\n    button[data-variant='accent-success']:active:enabled {\n      background: var(--wui-color-success-glass-030);\n      color: var(--wui-color-success-100);\n    }\n\n    button[data-variant='neutral']:hover:enabled {\n      background: var(--wui-color-gray-glass-002);\n    }\n\n    button[data-variant='neutral']:active:enabled {\n      background: var(--wui-color-gray-glass-005);\n    }\n\n    button[data-size='lg'][data-icon-left='true'][data-icon-right='false'] {\n      padding-left: var(--wui-spacing-m);\n    }\n\n    button[data-size='lg'][data-icon-right='true'][data-icon-left='false'] {\n      padding-right: var(--wui-spacing-m);\n    }\n  }\n\n  /* -- Disabled state --------------------------------------------------- */\n  button:disabled {\n    background-color: var(--wui-color-gray-glass-002);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);\n    color: var(--wui-color-gray-glass-020);\n    cursor: not-allowed;\n  }\n\n  button > wui-text {\n    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: opacity;\n    opacity: var(--local-opacity-100);\n  }\n\n  ::slotted(*) {\n    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: opacity;\n    opacity: var(--local-opacity-100);\n  }\n\n  wui-loading-spinner {\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    transform: translate(-50%, -50%);\n    opacity: var(--local-opacity-000);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-loading-spinner/index.js';\nimport '../../components/wui-text/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nconst SPINNER_COLOR_BY_VARIANT = {\n    main: 'inverse-100',\n    inverse: 'inverse-000',\n    accent: 'accent-100',\n    'accent-error': 'error-100',\n    'accent-success': 'success-100',\n    neutral: 'fg-100',\n    disabled: 'gray-glass-020'\n};\nconst TEXT_VARIANT_BY_SIZE = {\n    lg: 'paragraph-600',\n    md: 'small-600'\n};\nconst SPINNER_SIZE_BY_SIZE = {\n    lg: 'md',\n    md: 'md'\n};\nlet WuiButton = class WuiButton extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.size = 'lg';\n        this.disabled = false;\n        this.fullWidth = false;\n        this.loading = false;\n        this.variant = 'main';\n        this.hasIconLeft = false;\n        this.hasIconRight = false;\n        this.borderRadius = 'm';\n    }\n    render() {\n        this.style.cssText = `\n    --local-width: ${this.fullWidth ? '100%' : 'auto'};\n    --local-opacity-100: ${this.loading ? 0 : 1};\n    --local-opacity-000: ${this.loading ? 1 : 0};\n    --local-border-radius: var(--wui-border-radius-${this.borderRadius});\n    `;\n        const textVariant = this.textVariant ?? TEXT_VARIANT_BY_SIZE[this.size];\n        return html `\n      <button\n        data-variant=${this.variant}\n        data-icon-left=${this.hasIconLeft}\n        data-icon-right=${this.hasIconRight}\n        data-size=${this.size}\n        ?disabled=${this.disabled}\n      >\n        ${this.loadingTemplate()}\n        <slot name=\"iconLeft\" @slotchange=${() => this.handleSlotLeftChange()}></slot>\n        <wui-text variant=${textVariant} color=\"inherit\">\n          <slot></slot>\n        </wui-text>\n        <slot name=\"iconRight\" @slotchange=${() => this.handleSlotRightChange()}></slot>\n      </button>\n    `;\n    }\n    handleSlotLeftChange() {\n        this.hasIconLeft = true;\n    }\n    handleSlotRightChange() {\n        this.hasIconRight = true;\n    }\n    loadingTemplate() {\n        if (this.loading) {\n            const size = SPINNER_SIZE_BY_SIZE[this.size];\n            const color = this.disabled\n                ? SPINNER_COLOR_BY_VARIANT['disabled']\n                : SPINNER_COLOR_BY_VARIANT[this.variant];\n            return html `<wui-loading-spinner color=${color} size=${size}></wui-loading-spinner>`;\n        }\n        return html ``;\n    }\n};\nWuiButton.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiButton.prototype, \"size\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiButton.prototype, \"disabled\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiButton.prototype, \"fullWidth\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiButton.prototype, \"loading\", void 0);\n__decorate([\n    property()\n], WuiButton.prototype, \"variant\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiButton.prototype, \"hasIconLeft\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiButton.prototype, \"hasIconRight\", void 0);\n__decorate([\n    property()\n], WuiButton.prototype, \"borderRadius\", void 0);\n__decorate([\n    property()\n], WuiButton.prototype, \"textVariant\", void 0);\nWuiButton = __decorate([\n    customElement('wui-button')\n], WuiButton);\nexport { WuiButton };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    padding: var(--wui-spacing-4xs) var(--wui-spacing-xxs);\n    border-radius: var(--wui-border-radius-3xs);\n    background-color: transparent;\n    color: var(--wui-color-accent-100);\n  }\n\n  button:disabled {\n    background-color: transparent;\n    color: var(--wui-color-gray-glass-015);\n  }\n\n  button:hover {\n    background-color: var(--wui-color-gray-glass-005);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport '../../components/wui-text/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiLink = class WuiLink extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.tabIdx = undefined;\n        this.disabled = false;\n        this.color = 'inherit';\n    }\n    render() {\n        return html `\n      <button ?disabled=${this.disabled} tabindex=${ifDefined(this.tabIdx)}>\n        <slot name=\"iconLeft\"></slot>\n        <wui-text variant=\"small-600\" color=${this.color}>\n          <slot></slot>\n        </wui-text>\n        <slot name=\"iconRight\"></slot>\n      </button>\n    `;\n    }\n};\nWuiLink.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiLink.prototype, \"tabIdx\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiLink.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiLink.prototype, \"color\", void 0);\nWuiLink = __decorate([\n    customElement('wui-link')\n], WuiLink);\nexport { WuiLink };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    width: var(--wui-box-size-md);\n    height: var(--wui-box-size-md);\n  }\n\n  svg {\n    width: var(--wui-box-size-md);\n    height: var(--wui-box-size-md);\n  }\n\n  rect {\n    fill: none;\n    stroke: var(--wui-color-accent-100);\n    stroke-width: 4px;\n    stroke-linecap: round;\n    animation: dash 1s linear infinite;\n  }\n\n  @keyframes dash {\n    to {\n      stroke-dashoffset: 0px;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiLoadingThumbnail = class WuiLoadingThumbnail extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.radius = 36;\n    }\n    render() {\n        return this.svgLoaderTemplate();\n    }\n    svgLoaderTemplate() {\n        const radius = this.radius > 50 ? 50 : this.radius;\n        const standardValue = 36;\n        const radiusFactor = standardValue - radius;\n        const dashArrayStart = 116 + radiusFactor;\n        const dashArrayEnd = 245 + radiusFactor;\n        const dashOffset = 360 + radiusFactor * 1.75;\n        return html `\n      <svg viewBox=\"0 0 110 110\" width=\"110\" height=\"110\">\n        <rect\n          x=\"2\"\n          y=\"2\"\n          width=\"106\"\n          height=\"106\"\n          rx=${radius}\n          stroke-dasharray=\"${dashArrayStart} ${dashArrayEnd}\"\n          stroke-dashoffset=${dashOffset}\n        />\n      </svg>\n    `;\n    }\n};\nWuiLoadingThumbnail.styles = [resetStyles, styles];\n__decorate([\n    property({ type: Number })\n], WuiLoadingThumbnail.prototype, \"radius\", void 0);\nWuiLoadingThumbnail = __decorate([\n    customElement('wui-loading-thumbnail')\n], WuiLoadingThumbnail);\nexport { WuiLoadingThumbnail };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    border: none;\n    border-radius: var(--wui-border-radius-3xl);\n  }\n\n  button[data-variant='main'] {\n    background-color: var(--wui-color-accent-100);\n    color: var(--wui-color-inverse-100);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  button[data-variant='accent'] {\n    background-color: var(--wui-color-accent-glass-010);\n    color: var(--wui-color-accent-100);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);\n  }\n\n  button[data-variant='gray'] {\n    background-color: transparent;\n    color: var(--wui-color-fg-200);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  button[data-variant='shade'] {\n    background-color: transparent;\n    color: var(--wui-color-accent-100);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  button[data-size='sm'] {\n    height: 32px;\n    padding: 0 var(--wui-spacing-s);\n  }\n\n  button[data-size='md'] {\n    height: 40px;\n    padding: 0 var(--wui-spacing-l);\n  }\n\n  button[data-size='sm'] > wui-image {\n    width: 16px;\n    height: 16px;\n  }\n\n  button[data-size='md'] > wui-image {\n    width: 24px;\n    height: 24px;\n  }\n\n  button[data-size='sm'] > wui-icon {\n    width: 12px;\n    height: 12px;\n  }\n\n  button[data-size='md'] > wui-icon {\n    width: 14px;\n    height: 14px;\n  }\n\n  wui-image {\n    border-radius: var(--wui-border-radius-3xl);\n    overflow: hidden;\n  }\n\n  button.disabled > wui-icon,\n  button.disabled > wui-image {\n    filter: grayscale(1);\n  }\n\n  button[data-variant='main'] > wui-image {\n    box-shadow: inset 0 0 0 1px var(--wui-color-accent-090);\n  }\n\n  button[data-variant='shade'] > wui-image,\n  button[data-variant='gray'] > wui-image {\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-010);\n  }\n\n  @media (hover: hover) and (pointer: fine) {\n    button[data-variant='main']:focus-visible {\n      background-color: var(--wui-color-accent-090);\n    }\n\n    button[data-variant='main']:hover:enabled {\n      background-color: var(--wui-color-accent-090);\n    }\n\n    button[data-variant='main']:active:enabled {\n      background-color: var(--wui-color-accent-080);\n    }\n\n    button[data-variant='accent']:hover:enabled {\n      background-color: var(--wui-color-accent-glass-015);\n    }\n\n    button[data-variant='accent']:active:enabled {\n      background-color: var(--wui-color-accent-glass-020);\n    }\n\n    button[data-variant='shade']:focus-visible,\n    button[data-variant='gray']:focus-visible,\n    button[data-variant='shade']:hover,\n    button[data-variant='gray']:hover {\n      background-color: var(--wui-color-gray-glass-002);\n    }\n\n    button[data-variant='gray']:active,\n    button[data-variant='shade']:active {\n      background-color: var(--wui-color-gray-glass-005);\n    }\n  }\n\n  button.disabled {\n    color: var(--wui-color-gray-glass-020);\n    background-color: var(--wui-color-gray-glass-002);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);\n    pointer-events: none;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-image/index.js';\nimport '../../components/wui-text/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiChipButton = class WuiChipButton extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.variant = 'accent';\n        this.imageSrc = '';\n        this.disabled = false;\n        this.icon = 'externalLink';\n        this.size = 'md';\n        this.text = '';\n    }\n    render() {\n        const textVariant = this.size === 'sm' ? 'small-600' : 'paragraph-600';\n        return html `\n      <button\n        class=${this.disabled ? 'disabled' : ''}\n        data-variant=${this.variant}\n        data-size=${this.size}\n      >\n        ${this.imageSrc ? html `<wui-image src=${this.imageSrc}></wui-image>` : null}\n        <wui-text variant=${textVariant} color=\"inherit\"> ${this.text} </wui-text>\n        <wui-icon name=${this.icon} color=\"inherit\" size=\"inherit\"></wui-icon>\n      </button>\n    `;\n    }\n};\nWuiChipButton.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiChipButton.prototype, \"variant\", void 0);\n__decorate([\n    property()\n], WuiChipButton.prototype, \"imageSrc\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiChipButton.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiChipButton.prototype, \"icon\", void 0);\n__decorate([\n    property()\n], WuiChipButton.prototype, \"size\", void 0);\n__decorate([\n    property()\n], WuiChipButton.prototype, \"text\", void 0);\nWuiChipButton = __decorate([\n    customElement('wui-chip-button')\n], WuiChipButton);\nexport { WuiChipButton };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  wui-flex {\n    width: 100%;\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: var(--wui-border-radius-xs);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-text/index.js';\nimport '../../composites/wui-chip-button/index.js';\nimport '../../layout/wui-flex/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiCtaButton = class WuiCtaButton extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.disabled = false;\n        this.label = '';\n        this.buttonLabel = '';\n    }\n    render() {\n        return html `\n      <wui-flex\n        justifyContent=\"space-between\"\n        alignItems=\"center\"\n        .padding=${['1xs', '2l', '1xs', '2l']}\n      >\n        <wui-text variant=\"paragraph-500\" color=\"fg-200\">${this.label}</wui-text>\n        <wui-chip-button size=\"sm\" variant=\"shade\" text=${this.buttonLabel} icon=\"chevronRight\">\n        </wui-chip-button>\n      </wui-flex>\n    `;\n    }\n};\nWuiCtaButton.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property({ type: Boolean })\n], WuiCtaButton.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiCtaButton.prototype, \"label\", void 0);\n__decorate([\n    property()\n], WuiCtaButton.prototype, \"buttonLabel\", void 0);\nWuiCtaButton = __decorate([\n    customElement('wui-cta-button')\n], WuiCtaButton);\nexport { WuiCtaButton };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    padding: 0 var(--wui-spacing-xl) var(--wui-spacing-xl);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { CoreHelperUtil, RouterController } from '@reown/appkit-controllers';\nimport { UiHelperUtil, customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-cta-button';\nimport styles from './styles.js';\nlet W3mMobileDownloadLinks = class W3mMobileDownloadLinks extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.wallet = undefined;\n    }\n    render() {\n        if (!this.wallet) {\n            this.style.display = 'none';\n            return null;\n        }\n        const { name, app_store, play_store, chrome_store, homepage } = this.wallet;\n        const isMobile = CoreHelperUtil.isMobile();\n        const isIos = CoreHelperUtil.isIos();\n        const isAndroid = CoreHelperUtil.isAndroid();\n        const isMultiple = [app_store, play_store, homepage, chrome_store].filter(Boolean).length > 1;\n        const shortName = UiHelperUtil.getTruncateString({\n            string: name,\n            charsStart: 12,\n            charsEnd: 0,\n            truncate: 'end'\n        });\n        if (isMultiple && !isMobile) {\n            return html `\n        <wui-cta-button\n          label=${`Don't have ${shortName}?`}\n          buttonLabel=\"Get\"\n          @click=${() => RouterController.push('Downloads', { wallet: this.wallet })}\n        ></wui-cta-button>\n      `;\n        }\n        if (!isMultiple && homepage) {\n            return html `\n        <wui-cta-button\n          label=${`Don't have ${shortName}?`}\n          buttonLabel=\"Get\"\n          @click=${this.onHomePage.bind(this)}\n        ></wui-cta-button>\n      `;\n        }\n        if (app_store && isIos) {\n            return html `\n        <wui-cta-button\n          label=${`Don't have ${shortName}?`}\n          buttonLabel=\"Get\"\n          @click=${this.onAppStore.bind(this)}\n        ></wui-cta-button>\n      `;\n        }\n        if (play_store && isAndroid) {\n            return html `\n        <wui-cta-button\n          label=${`Don't have ${shortName}?`}\n          buttonLabel=\"Get\"\n          @click=${this.onPlayStore.bind(this)}\n        ></wui-cta-button>\n      `;\n        }\n        this.style.display = 'none';\n        return null;\n    }\n    onAppStore() {\n        if (this.wallet?.app_store) {\n            CoreHelperUtil.openHref(this.wallet.app_store, '_blank');\n        }\n    }\n    onPlayStore() {\n        if (this.wallet?.play_store) {\n            CoreHelperUtil.openHref(this.wallet.play_store, '_blank');\n        }\n    }\n    onHomePage() {\n        if (this.wallet?.homepage) {\n            CoreHelperUtil.openHref(this.wallet.homepage, '_blank');\n        }\n    }\n};\nW3mMobileDownloadLinks.styles = [styles];\n__decorate([\n    property({ type: Object })\n], W3mMobileDownloadLinks.prototype, \"wallet\", void 0);\nW3mMobileDownloadLinks = __decorate([\n    customElement('w3m-mobile-download-links')\n], W3mMobileDownloadLinks);\nexport { W3mMobileDownloadLinks };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  @keyframes shake {\n    0% {\n      transform: translateX(0);\n    }\n    25% {\n      transform: translateX(3px);\n    }\n    50% {\n      transform: translateX(-3px);\n    }\n    75% {\n      transform: translateX(3px);\n    }\n    100% {\n      transform: translateX(0);\n    }\n  }\n\n  wui-flex:first-child:not(:only-child) {\n    position: relative;\n  }\n\n  wui-loading-thumbnail {\n    position: absolute;\n  }\n\n  wui-icon-box {\n    position: absolute;\n    right: calc(var(--wui-spacing-3xs) * -1);\n    bottom: calc(var(--wui-spacing-3xs) * -1);\n    opacity: 0;\n    transform: scale(0.5);\n    transition-property: opacity, transform;\n    transition-duration: var(--wui-duration-lg);\n    transition-timing-function: var(--wui-ease-out-power-2);\n    will-change: opacity, transform;\n  }\n\n  wui-text[align='center'] {\n    width: 100%;\n    padding: 0px var(--wui-spacing-l);\n  }\n\n  [data-error='true'] wui-icon-box {\n    opacity: 1;\n    transform: scale(1);\n  }\n\n  [data-error='true'] > wui-flex:first-child {\n    animation: shake 250ms cubic-bezier(0.36, 0.07, 0.19, 0.97) both;\n  }\n\n  [data-retry='false'] wui-link {\n    display: none;\n  }\n\n  [data-retry='true'] wui-link {\n    display: block;\n    opacity: 1;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectionController, CoreHelperUtil, RouterController, SnackController, ThemeController } from '@reown/appkit-controllers';\nimport '@reown/appkit-ui/wui-button';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon';\nimport '@reown/appkit-ui/wui-icon-box';\nimport '@reown/appkit-ui/wui-link';\nimport '@reown/appkit-ui/wui-loading-thumbnail';\nimport '@reown/appkit-ui/wui-text';\nimport '@reown/appkit-ui/wui-wallet-image';\nimport '../../partials/w3m-mobile-download-links/index.js';\nimport styles from './styles.js';\nexport class W3mConnectingWidget extends LitElement {\n    constructor() {\n        super();\n        this.wallet = RouterController.state.data?.wallet;\n        this.connector = RouterController.state.data?.connector;\n        this.timeout = undefined;\n        this.secondaryBtnIcon = 'refresh';\n        this.onConnect = undefined;\n        this.onRender = undefined;\n        this.onAutoConnect = undefined;\n        this.isWalletConnect = true;\n        this.unsubscribe = [];\n        this.imageSrc = AssetUtil.getWalletImage(this.wallet) ?? AssetUtil.getConnectorImage(this.connector);\n        this.name = this.wallet?.name ?? this.connector?.name ?? 'Wallet';\n        this.isRetrying = false;\n        this.uri = ConnectionController.state.wcUri;\n        this.error = ConnectionController.state.wcError;\n        this.ready = false;\n        this.showRetry = false;\n        this.secondaryBtnLabel = 'Try again';\n        this.secondaryLabel = 'Accept connection request in the wallet';\n        this.isLoading = false;\n        this.isMobile = false;\n        this.onRetry = undefined;\n        this.unsubscribe.push(...[\n            ConnectionController.subscribeKey('wcUri', val => {\n                this.uri = val;\n                if (this.isRetrying && this.onRetry) {\n                    this.isRetrying = false;\n                    this.onConnect?.();\n                }\n            }),\n            ConnectionController.subscribeKey('wcError', val => (this.error = val))\n        ]);\n        if ((CoreHelperUtil.isTelegram() || CoreHelperUtil.isSafari()) &&\n            CoreHelperUtil.isIos() &&\n            ConnectionController.state.wcUri) {\n            this.onConnect?.();\n        }\n    }\n    firstUpdated() {\n        this.onAutoConnect?.();\n        this.showRetry = !this.onAutoConnect;\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n        ConnectionController.setWcError(false);\n        clearTimeout(this.timeout);\n    }\n    render() {\n        this.onRender?.();\n        this.onShowRetry();\n        const subLabel = this.error\n            ? 'Connection can be declined if a previous request is still active'\n            : this.secondaryLabel;\n        let label = `Continue in ${this.name}`;\n        if (this.error) {\n            label = 'Connection declined';\n        }\n        return html `\n      <wui-flex\n        data-error=${ifDefined(this.error)}\n        data-retry=${this.showRetry}\n        flexDirection=\"column\"\n        alignItems=\"center\"\n        .padding=${['3xl', 'xl', 'xl', 'xl']}\n        gap=\"xl\"\n      >\n        <wui-flex justifyContent=\"center\" alignItems=\"center\">\n          <wui-wallet-image size=\"lg\" imageSrc=${ifDefined(this.imageSrc)}></wui-wallet-image>\n\n          ${this.error ? null : this.loaderTemplate()}\n\n          <wui-icon-box\n            backgroundColor=\"error-100\"\n            background=\"opaque\"\n            iconColor=\"error-100\"\n            icon=\"close\"\n            size=\"sm\"\n            border\n            borderColor=\"wui-color-bg-125\"\n          ></wui-icon-box>\n        </wui-flex>\n\n        <wui-flex flexDirection=\"column\" alignItems=\"center\" gap=\"xs\">\n          <wui-text variant=\"paragraph-500\" color=${this.error ? 'error-100' : 'fg-100'}>\n            ${label}\n          </wui-text>\n          <wui-text align=\"center\" variant=\"small-500\" color=\"fg-200\">${subLabel}</wui-text>\n        </wui-flex>\n\n        ${this.secondaryBtnLabel\n            ? html `\n              <wui-button\n                variant=\"accent\"\n                size=\"md\"\n                ?disabled=${this.isRetrying || this.isLoading}\n                @click=${this.onTryAgain.bind(this)}\n                data-testid=\"w3m-connecting-widget-secondary-button\"\n              >\n                <wui-icon color=\"inherit\" slot=\"iconLeft\" name=${this.secondaryBtnIcon}></wui-icon>\n                ${this.secondaryBtnLabel}\n              </wui-button>\n            `\n            : null}\n      </wui-flex>\n\n      ${this.isWalletConnect\n            ? html `\n            <wui-flex .padding=${['0', 'xl', 'xl', 'xl']} justifyContent=\"center\">\n              <wui-link @click=${this.onCopyUri} color=\"fg-200\" data-testid=\"wui-link-copy\">\n                <wui-icon size=\"xs\" color=\"fg-200\" slot=\"iconLeft\" name=\"copy\"></wui-icon>\n                Copy link\n              </wui-link>\n            </wui-flex>\n          `\n            : null}\n\n      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>\n    `;\n    }\n    onShowRetry() {\n        if (this.error && !this.showRetry) {\n            this.showRetry = true;\n            const retryButton = this.shadowRoot?.querySelector('wui-button');\n            retryButton?.animate([{ opacity: 0 }, { opacity: 1 }], {\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n    }\n    onTryAgain() {\n        ConnectionController.setWcError(false);\n        if (this.onRetry) {\n            this.isRetrying = true;\n            this.onRetry?.();\n        }\n        else {\n            this.onConnect?.();\n        }\n    }\n    loaderTemplate() {\n        const borderRadiusMaster = ThemeController.state.themeVariables['--w3m-border-radius-master'];\n        const radius = borderRadiusMaster ? parseInt(borderRadiusMaster.replace('px', ''), 10) : 4;\n        return html `<wui-loading-thumbnail radius=${radius * 9}></wui-loading-thumbnail>`;\n    }\n    onCopyUri() {\n        try {\n            if (this.uri) {\n                CoreHelperUtil.copyToClopboard(this.uri);\n                SnackController.showSuccess('Link copied');\n            }\n        }\n        catch {\n            SnackController.showError('Failed to copy');\n        }\n    }\n}\nW3mConnectingWidget.styles = styles;\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"isRetrying\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"uri\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"error\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"ready\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"showRetry\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"secondaryBtnLabel\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"secondaryLabel\", void 0);\n__decorate([\n    state()\n], W3mConnectingWidget.prototype, \"isLoading\", void 0);\n__decorate([\n    property({ type: Boolean })\n], W3mConnectingWidget.prototype, \"isMobile\", void 0);\n__decorate([\n    property()\n], W3mConnectingWidget.prototype, \"onRetry\", void 0);\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { ConnectionController, ConnectorController, EventsController, ModalController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport { W3mConnectingWidget } from '../../utils/w3m-connecting-widget/index.js';\nlet W3mConnectingWcBrowser = class W3mConnectingWcBrowser extends W3mConnectingWidget {\n    constructor() {\n        super();\n        if (!this.wallet) {\n            throw new Error('w3m-connecting-wc-browser: No wallet provided');\n        }\n        this.onConnect = this.onConnectProxy.bind(this);\n        this.onAutoConnect = this.onConnectProxy.bind(this);\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet.name, platform: 'browser' }\n        });\n    }\n    async onConnectProxy() {\n        try {\n            this.error = false;\n            const { connectors } = ConnectorController.state;\n            const connector = connectors.find(c => (c.type === 'ANNOUNCED' && c.info?.rdns === this.wallet?.rdns) ||\n                c.type === 'INJECTED' ||\n                c.name === this.wallet?.name);\n            if (connector) {\n                await ConnectionController.connectExternal(connector, connector.chain);\n            }\n            else {\n                throw new Error('w3m-connecting-wc-browser: No connector found');\n            }\n            ModalController.close();\n            EventsController.sendEvent({\n                type: 'track',\n                event: 'CONNECT_SUCCESS',\n                properties: { method: 'browser', name: this.wallet?.name || 'Unknown' }\n            });\n        }\n        catch (error) {\n            EventsController.sendEvent({\n                type: 'track',\n                event: 'CONNECT_ERROR',\n                properties: { message: error?.message ?? 'Unknown' }\n            });\n            this.error = true;\n        }\n    }\n};\nW3mConnectingWcBrowser = __decorate([\n    customElement('w3m-connecting-wc-browser')\n], W3mConnectingWcBrowser);\nexport { W3mConnectingWcBrowser };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { ConnectionController, CoreHelperUtil, EventsController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport { W3mConnectingWidget } from '../../utils/w3m-connecting-widget/index.js';\nlet W3mConnectingWcDesktop = class W3mConnectingWcDesktop extends W3mConnectingWidget {\n    constructor() {\n        super();\n        if (!this.wallet) {\n            throw new Error('w3m-connecting-wc-desktop: No wallet provided');\n        }\n        this.onConnect = this.onConnectProxy.bind(this);\n        this.onRender = this.onRenderProxy.bind(this);\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet.name, platform: 'desktop' }\n        });\n    }\n    onRenderProxy() {\n        if (!this.ready && this.uri) {\n            this.ready = true;\n            this.onConnect?.();\n        }\n    }\n    onConnectProxy() {\n        if (this.wallet?.desktop_link && this.uri) {\n            try {\n                this.error = false;\n                const { desktop_link, name } = this.wallet;\n                const { redirect, href } = CoreHelperUtil.formatNativeUrl(desktop_link, this.uri);\n                ConnectionController.setWcLinking({ name, href });\n                ConnectionController.setRecentWallet(this.wallet);\n                CoreHelperUtil.openHref(redirect, '_blank');\n            }\n            catch {\n                this.error = true;\n            }\n        }\n    }\n};\nW3mConnectingWcDesktop = __decorate([\n    customElement('w3m-connecting-wc-desktop')\n], W3mConnectingWcDesktop);\nexport { W3mConnectingWcDesktop };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { state } from 'lit/decorators.js';\nimport { ConnectionController, ConstantsUtil, CoreHelperUtil, EventsController, OptionsController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport { W3mConnectingWidget } from '../../utils/w3m-connecting-widget/index.js';\nlet W3mConnectingWcMobile = class W3mConnectingWcMobile extends W3mConnectingWidget {\n    constructor() {\n        super();\n        this.btnLabelTimeout = undefined;\n        this.redirectDeeplink = undefined;\n        this.redirectUniversalLink = undefined;\n        this.target = undefined;\n        this.preferUniversalLinks = OptionsController.state.experimental_preferUniversalLinks;\n        this.isLoading = true;\n        this.onConnect = () => {\n            if (this.wallet?.mobile_link && this.uri) {\n                try {\n                    this.error = false;\n                    const { mobile_link, link_mode, name } = this.wallet;\n                    const { redirect, redirectUniversalLink, href } = CoreHelperUtil.formatNativeUrl(mobile_link, this.uri, link_mode);\n                    this.redirectDeeplink = redirect;\n                    this.redirectUniversalLink = redirectUniversalLink;\n                    this.target = CoreHelperUtil.isIframe() ? '_top' : '_self';\n                    ConnectionController.setWcLinking({ name, href });\n                    ConnectionController.setRecentWallet(this.wallet);\n                    if (this.preferUniversalLinks && this.redirectUniversalLink) {\n                        CoreHelperUtil.openHref(this.redirectUniversalLink, this.target);\n                    }\n                    else {\n                        CoreHelperUtil.openHref(this.redirectDeeplink, this.target);\n                    }\n                }\n                catch (e) {\n                    EventsController.sendEvent({\n                        type: 'track',\n                        event: 'CONNECT_PROXY_ERROR',\n                        properties: {\n                            message: e instanceof Error ? e.message : 'Error parsing the deeplink',\n                            uri: this.uri,\n                            mobile_link: this.wallet.mobile_link,\n                            name: this.wallet.name\n                        }\n                    });\n                    this.error = true;\n                }\n            }\n        };\n        if (!this.wallet) {\n            throw new Error('w3m-connecting-wc-mobile: No wallet provided');\n        }\n        this.secondaryBtnLabel = 'Open';\n        this.secondaryLabel = ConstantsUtil.CONNECT_LABELS.MOBILE;\n        this.secondaryBtnIcon = 'externalLink';\n        this.onHandleURI();\n        this.unsubscribe.push(ConnectionController.subscribeKey('wcUri', () => {\n            this.onHandleURI();\n        }));\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet.name, platform: 'mobile' }\n        });\n    }\n    disconnectedCallback() {\n        super.disconnectedCallback();\n        clearTimeout(this.btnLabelTimeout);\n    }\n    onHandleURI() {\n        this.isLoading = !this.uri;\n        if (!this.ready && this.uri) {\n            this.ready = true;\n            this.onConnect?.();\n        }\n    }\n    onTryAgain() {\n        ConnectionController.setWcError(false);\n        this.onConnect?.();\n    }\n};\n__decorate([\n    state()\n], W3mConnectingWcMobile.prototype, \"redirectDeeplink\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcMobile.prototype, \"redirectUniversalLink\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcMobile.prototype, \"target\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcMobile.prototype, \"preferUniversalLinks\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcMobile.prototype, \"isLoading\", void 0);\nW3mConnectingWcMobile = __decorate([\n    customElement('w3m-connecting-wc-mobile')\n], W3mConnectingWcMobile);\nexport { W3mConnectingWcMobile };\n//# sourceMappingURL=index.js.map", "// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n", "let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n", "exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n", "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n", "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n", "const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n", "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n", "const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n", "const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n", "const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n", "const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n", "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n", "const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n", "const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n", "const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n", "const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n", "const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n", "'use strict'\n\nmodule.exports = function encodeUtf8 (input) {\n  var result = []\n  var size = input.length\n\n  for (var index = 0; index < size; index++) {\n    var point = input.charCodeAt(index)\n\n    if (point >= 0xD800 && point <= 0xDBFF && size > index + 1) {\n      var second = input.charCodeAt(index + 1)\n\n      if (second >= 0xDC00 && second <= 0xDFFF) {\n        // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        point = (point - 0xD800) * 0x400 + second - 0xDC00 + 0x10000\n        index += 1\n      }\n    }\n\n    // US-ASCII\n    if (point < 0x80) {\n      result.push(point)\n      continue\n    }\n\n    // 2-byte UTF-8\n    if (point < 0x800) {\n      result.push((point >> 6) | 192)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 3-byte UTF-8\n    if (point < 0xD800 || (point >= 0xE000 && point < 0x10000)) {\n      result.push((point >> 12) | 224)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 4-byte UTF-8\n    if (point >= 0x10000 && point <= 0x10FFFF) {\n      result.push((point >> 18) | 240)\n      result.push(((point >> 12) & 63) | 128)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // Invalid character\n    result.push(0xEF, 0xBF, 0xBD)\n  }\n\n  return new Uint8Array(result).buffer\n}\n", "const encodeUtf8 = require('encode-utf8')\nconst Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    data = encodeUtf8(data)\n  }\n  this.data = new Uint8Array(data)\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n", "const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n", "'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function(graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n\n    var closest,\n        u, v,\n        cost_of_s_to_u,\n        adjacent_nodes,\n        cost_of_e,\n        cost_of_s_to_u_plus_cost_of_e,\n        cost_of_s_to_v,\n        first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = (typeof costs[v] === 'undefined');\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n\n    return predecessors;\n  },\n\n  extract_shortest_path_from_predecessor_list: function(predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n\n  find_path: function(graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(\n      predecessors, d);\n  },\n\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n          t = {},\n          key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {value: value, cost: cost};\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}\n", "const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n", "const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n", "function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n", "const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n", "const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n", "\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n", "import QRCodeUtil from 'qrcode';\nimport { svg } from 'lit';\nconst CONNECTING_ERROR_MARGIN = 0.1;\nconst CIRCLE_SIZE_MODIFIER = 2.5;\nconst QRCODE_MATRIX_MARGIN = 7;\nfunction isAdjecentDots(cy, otherCy, cellSize) {\n    if (cy === otherCy) {\n        return false;\n    }\n    const diff = cy - otherCy < 0 ? otherCy - cy : cy - otherCy;\n    return diff <= cellSize + CONNECTING_ERROR_MARGIN;\n}\nfunction getMatrix(value, errorCorrectionLevel) {\n    const arr = Array.prototype.slice.call(QRCodeUtil.create(value, { errorCorrectionLevel }).modules.data, 0);\n    const sqrt = Math.sqrt(arr.length);\n    return arr.reduce((rows, key, index) => (index % sqrt === 0 ? rows.push([key]) : rows[rows.length - 1].push(key)) && rows, []);\n}\nexport const QrCodeUtil = {\n    generate({ uri, size, logoSize, dotColor = '#141414' }) {\n        const edgeColor = 'transparent';\n        const strokeWidth = 5;\n        const dots = [];\n        const matrix = getMatrix(uri, 'Q');\n        const cellSize = size / matrix.length;\n        const qrList = [\n            { x: 0, y: 0 },\n            { x: 1, y: 0 },\n            { x: 0, y: 1 }\n        ];\n        qrList.forEach(({ x, y }) => {\n            const x1 = (matrix.length - QRCODE_MATRIX_MARGIN) * cellSize * x;\n            const y1 = (matrix.length - QRCODE_MATRIX_MARGIN) * cellSize * y;\n            const borderRadius = 0.45;\n            for (let i = 0; i < qrList.length; i += 1) {\n                const dotSize = cellSize * (QRCODE_MATRIX_MARGIN - i * 2);\n                dots.push(svg `\n            <rect\n              fill=${i === 2 ? dotColor : edgeColor}\n              width=${i === 0 ? dotSize - strokeWidth : dotSize}\n              rx= ${i === 0 ? (dotSize - strokeWidth) * borderRadius : dotSize * borderRadius}\n              ry= ${i === 0 ? (dotSize - strokeWidth) * borderRadius : dotSize * borderRadius}\n              stroke=${dotColor}\n              stroke-width=${i === 0 ? strokeWidth : 0}\n              height=${i === 0 ? dotSize - strokeWidth : dotSize}\n              x= ${i === 0 ? y1 + cellSize * i + strokeWidth / 2 : y1 + cellSize * i}\n              y= ${i === 0 ? x1 + cellSize * i + strokeWidth / 2 : x1 + cellSize * i}\n            />\n          `);\n            }\n        });\n        const clearArenaSize = Math.floor((logoSize + 25) / cellSize);\n        const matrixMiddleStart = matrix.length / 2 - clearArenaSize / 2;\n        const matrixMiddleEnd = matrix.length / 2 + clearArenaSize / 2 - 1;\n        const circles = [];\n        matrix.forEach((row, i) => {\n            row.forEach((_, j) => {\n                if (matrix[i][j]) {\n                    if (!((i < QRCODE_MATRIX_MARGIN && j < QRCODE_MATRIX_MARGIN) ||\n                        (i > matrix.length - (QRCODE_MATRIX_MARGIN + 1) && j < QRCODE_MATRIX_MARGIN) ||\n                        (i < QRCODE_MATRIX_MARGIN && j > matrix.length - (QRCODE_MATRIX_MARGIN + 1)))) {\n                        if (!(i > matrixMiddleStart &&\n                            i < matrixMiddleEnd &&\n                            j > matrixMiddleStart &&\n                            j < matrixMiddleEnd)) {\n                            const cx = i * cellSize + cellSize / 2;\n                            const cy = j * cellSize + cellSize / 2;\n                            circles.push([cx, cy]);\n                        }\n                    }\n                }\n            });\n        });\n        const circlesToConnect = {};\n        circles.forEach(([cx, cy]) => {\n            if (circlesToConnect[cx]) {\n                circlesToConnect[cx]?.push(cy);\n            }\n            else {\n                circlesToConnect[cx] = [cy];\n            }\n        });\n        Object.entries(circlesToConnect)\n            .map(([cx, cys]) => {\n            const newCys = cys.filter(cy => cys.every(otherCy => !isAdjecentDots(cy, otherCy, cellSize)));\n            return [Number(cx), newCys];\n        })\n            .forEach(([cx, cys]) => {\n            cys.forEach(cy => {\n                dots.push(svg `<circle cx=${cx} cy=${cy} fill=${dotColor} r=${cellSize / CIRCLE_SIZE_MODIFIER} />`);\n            });\n        });\n        Object.entries(circlesToConnect)\n            .filter(([_, cys]) => cys.length > 1)\n            .map(([cx, cys]) => {\n            const newCys = cys.filter(cy => cys.some(otherCy => isAdjecentDots(cy, otherCy, cellSize)));\n            return [Number(cx), newCys];\n        })\n            .map(([cx, cys]) => {\n            cys.sort((a, b) => (a < b ? -1 : 1));\n            const groups = [];\n            for (const cy of cys) {\n                const group = groups.find(item => item.some(otherCy => isAdjecentDots(cy, otherCy, cellSize)));\n                if (group) {\n                    group.push(cy);\n                }\n                else {\n                    groups.push([cy]);\n                }\n            }\n            return [cx, groups.map(item => [item[0], item[item.length - 1]])];\n        })\n            .forEach(([cx, groups]) => {\n            groups.forEach(([y1, y2]) => {\n                dots.push(svg `\n              <line\n                x1=${cx}\n                x2=${cx}\n                y1=${y1}\n                y2=${y2}\n                stroke=${dotColor}\n                stroke-width=${cellSize / (CIRCLE_SIZE_MODIFIER / 2)}\n                stroke-linecap=\"round\"\n              />\n            `);\n            });\n        });\n        return dots;\n    }\n};\n//# sourceMappingURL=QrCode.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    position: relative;\n    user-select: none;\n    display: block;\n    overflow: hidden;\n    aspect-ratio: 1 / 1;\n    width: var(--local-size);\n  }\n\n  :host([data-theme='dark']) {\n    border-radius: clamp(0px, var(--wui-border-radius-l), 40px);\n    background-color: var(--wui-color-inverse-100);\n    padding: var(--wui-spacing-l);\n  }\n\n  :host([data-theme='light']) {\n    box-shadow: 0 0 0 1px var(--wui-color-bg-125);\n    background-color: var(--wui-color-bg-125);\n  }\n\n  :host([data-clear='true']) > wui-icon {\n    display: none;\n  }\n\n  svg:first-child,\n  wui-image,\n  wui-icon {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translateY(-50%) translateX(-50%);\n  }\n\n  wui-image {\n    width: 25%;\n    height: 25%;\n    border-radius: var(--wui-border-radius-xs);\n  }\n\n  wui-icon {\n    width: 100%;\n    height: 100%;\n    color: var(--local-icon-color) !important;\n    transform: translateY(-50%) translateX(-50%) scale(0.25);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html, svg } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-image/index.js';\nimport { QrCodeUtil } from '../../utils/QrCode.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nconst DEFAULT_ICON_COLOR = '#3396ff';\nlet WuiQrCode = class WuiQrCode extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.uri = '';\n        this.size = 0;\n        this.theme = 'dark';\n        this.imageSrc = undefined;\n        this.alt = undefined;\n        this.arenaClear = undefined;\n        this.farcaster = undefined;\n    }\n    render() {\n        this.dataset['theme'] = this.theme;\n        this.dataset['clear'] = String(this.arenaClear);\n        this.style.cssText = `\n     --local-size: ${this.size}px;\n     --local-icon-color: ${this.color ?? DEFAULT_ICON_COLOR}\n    `;\n        return html `${this.templateVisual()} ${this.templateSvg()}`;\n    }\n    templateSvg() {\n        const size = this.theme === 'light' ? this.size : this.size - 16 * 2;\n        return svg `\n      <svg height=${size} width=${size}>\n        ${QrCodeUtil.generate({\n            uri: this.uri,\n            size,\n            logoSize: this.arenaClear ? 0 : size / 4,\n            dotColor: this.color\n        })}\n      </svg>\n    `;\n    }\n    templateVisual() {\n        if (this.imageSrc) {\n            return html `<wui-image src=${this.imageSrc} alt=${this.alt ?? 'logo'}></wui-image>`;\n        }\n        if (this.farcaster) {\n            return html `<wui-icon\n        class=\"farcaster\"\n        size=\"inherit\"\n        color=\"inherit\"\n        name=\"farcaster\"\n      ></wui-icon>`;\n        }\n        return html `<wui-icon size=\"inherit\" color=\"inherit\" name=\"walletConnect\"></wui-icon>`;\n    }\n};\nWuiQrCode.styles = [resetStyles, styles];\n__decorate([\n    property()\n], WuiQrCode.prototype, \"uri\", void 0);\n__decorate([\n    property({ type: Number })\n], WuiQrCode.prototype, \"size\", void 0);\n__decorate([\n    property()\n], WuiQrCode.prototype, \"theme\", void 0);\n__decorate([\n    property()\n], WuiQrCode.prototype, \"imageSrc\", void 0);\n__decorate([\n    property()\n], WuiQrCode.prototype, \"alt\", void 0);\n__decorate([\n    property()\n], WuiQrCode.prototype, \"color\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiQrCode.prototype, \"arenaClear\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiQrCode.prototype, \"farcaster\", void 0);\nWuiQrCode = __decorate([\n    customElement('wui-qr-code')\n], WuiQrCode);\nexport { WuiQrCode };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: block;\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-005);\n    background: linear-gradient(\n      120deg,\n      var(--wui-color-bg-200) 5%,\n      var(--wui-color-bg-200) 48%,\n      var(--wui-color-bg-300) 55%,\n      var(--wui-color-bg-300) 60%,\n      var(--wui-color-bg-300) calc(60% + 10px),\n      var(--wui-color-bg-200) calc(60% + 12px),\n      var(--wui-color-bg-200) 100%\n    );\n    background-size: 250%;\n    animation: shimmer 3s linear infinite reverse;\n  }\n\n  :host([variant='light']) {\n    background: linear-gradient(\n      120deg,\n      var(--wui-color-bg-150) 5%,\n      var(--wui-color-bg-150) 48%,\n      var(--wui-color-bg-200) 55%,\n      var(--wui-color-bg-200) 60%,\n      var(--wui-color-bg-200) calc(60% + 10px),\n      var(--wui-color-bg-150) calc(60% + 12px),\n      var(--wui-color-bg-150) 100%\n    );\n    background-size: 250%;\n  }\n\n  @keyframes shimmer {\n    from {\n      background-position: -250% 0;\n    }\n    to {\n      background-position: 250% 0;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiShimmer = class WuiShimmer extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.width = '';\n        this.height = '';\n        this.borderRadius = 'm';\n        this.variant = 'default';\n    }\n    render() {\n        this.style.cssText = `\n      width: ${this.width};\n      height: ${this.height};\n      border-radius: ${`clamp(0px,var(--wui-border-radius-${this.borderRadius}), 40px)`};\n    `;\n        return html `<slot></slot>`;\n    }\n};\nWuiShimmer.styles = [styles];\n__decorate([\n    property()\n], WuiShimmer.prototype, \"width\", void 0);\n__decorate([\n    property()\n], WuiShimmer.prototype, \"height\", void 0);\n__decorate([\n    property()\n], WuiShimmer.prototype, \"borderRadius\", void 0);\n__decorate([\n    property()\n], WuiShimmer.prototype, \"variant\", void 0);\nWuiShimmer = __decorate([\n    customElement('wui-shimmer')\n], WuiShimmer);\nexport { WuiShimmer };\n//# sourceMappingURL=index.js.map", "export const specialCharactersRegex = /[.*+?^${}()|[\\]\\\\]/gu;\nexport const numbersRegex = /[0-9,.]/u;\nexport const REOWN_URL = 'https://reown.com';\n//# sourceMappingURL=ConstantsUtil.js.map", "import { css } from 'lit';\nexport default css `\n  .reown-logo {\n    height: var(--wui-spacing-xxl);\n  }\n\n  a {\n    text-decoration: none;\n    cursor: pointer;\n  }\n\n  a:hover {\n    opacity: 0.9;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-text/index.js';\nimport '../../layout/wui-flex/index.js';\nimport { REOWN_URL } from '../../utils/ConstantsUtil.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiUxByReown = class WuiUxByReown extends LitElement {\n    render() {\n        return html `\n      <a\n        data-testid=\"ux-branding-reown\"\n        href=${REOWN_URL}\n        rel=\"noreferrer\"\n        target=\"_blank\"\n        style=\"text-decoration: none;\"\n      >\n        <wui-flex\n          justifyContent=\"center\"\n          alignItems=\"center\"\n          gap=\"xs\"\n          .padding=${['0', '0', 'l', '0']}\n        >\n          <wui-text variant=\"small-500\" color=\"fg-100\"> UX by </wui-text>\n          <wui-icon name=\"reown\" size=\"xxxl\" class=\"reown-logo\"></wui-icon>\n        </wui-flex>\n      </a>\n    `;\n    }\n};\nWuiUxByReown.styles = [resetStyles, elementStyles, styles];\nWuiUxByReown = __decorate([\n    customElement('wui-ux-by-reown')\n], WuiUxByReown);\nexport { WuiUxByReown };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  @keyframes fadein {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  wui-shimmer {\n    width: 100%;\n    aspect-ratio: 1 / 1;\n    border-radius: clamp(0px, var(--wui-border-radius-l), 40px) !important;\n  }\n\n  wui-qr-code {\n    opacity: 0;\n    animation-duration: 200ms;\n    animation-timing-function: ease;\n    animation-name: fadein;\n    animation-fill-mode: forwards;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { html } from 'lit';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, ConnectionController, EventsController, ThemeController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon';\nimport '@reown/appkit-ui/wui-link';\nimport '@reown/appkit-ui/wui-qr-code';\nimport '@reown/appkit-ui/wui-shimmer';\nimport '@reown/appkit-ui/wui-text';\nimport '@reown/appkit-ui/wui-ux-by-reown';\nimport { W3mConnectingWidget } from '../../utils/w3m-connecting-widget/index.js';\nimport '../w3m-mobile-download-links/index.js';\nimport styles from './styles.js';\nlet W3mConnectingWcQrcode = class W3mConnectingWcQrcode extends W3mConnectingWidget {\n    constructor() {\n        super();\n        this.forceUpdate = () => {\n            this.requestUpdate();\n        };\n        window.addEventListener('resize', this.forceUpdate);\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet?.name ?? 'WalletConnect', platform: 'qrcode' }\n        });\n    }\n    disconnectedCallback() {\n        super.disconnectedCallback();\n        this.unsubscribe?.forEach(unsub => unsub());\n        window.removeEventListener('resize', this.forceUpdate);\n    }\n    render() {\n        this.onRenderProxy();\n        return html `\n      <wui-flex\n        flexDirection=\"column\"\n        alignItems=\"center\"\n        .padding=${['0', 'xl', 'xl', 'xl']}\n        gap=\"xl\"\n      >\n        <wui-shimmer borderRadius=\"l\" width=\"100%\"> ${this.qrCodeTemplate()} </wui-shimmer>\n\n        <wui-text variant=\"paragraph-500\" color=\"fg-100\">\n          Scan this QR Code with your phone\n        </wui-text>\n        ${this.copyTemplate()}\n      </wui-flex>\n      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>\n    `;\n    }\n    onRenderProxy() {\n        if (!this.ready && this.uri) {\n            this.timeout = setTimeout(() => {\n                this.ready = true;\n            }, 200);\n        }\n    }\n    qrCodeTemplate() {\n        if (!this.uri || !this.ready) {\n            return null;\n        }\n        const size = this.getBoundingClientRect().width - 40;\n        const alt = this.wallet ? this.wallet.name : undefined;\n        ConnectionController.setWcLinking(undefined);\n        ConnectionController.setRecentWallet(this.wallet);\n        return html ` <wui-qr-code\n      size=${size}\n      theme=${ThemeController.state.themeMode}\n      uri=${this.uri}\n      imageSrc=${ifDefined(AssetUtil.getWalletImage(this.wallet))}\n      color=${ifDefined(ThemeController.state.themeVariables['--w3m-qr-color'])}\n      alt=${ifDefined(alt)}\n      data-testid=\"wui-qr-code\"\n    ></wui-qr-code>`;\n    }\n    copyTemplate() {\n        const inactive = !this.uri || !this.ready;\n        return html `<wui-link\n      .disabled=${inactive}\n      @click=${this.onCopyUri}\n      color=\"fg-200\"\n      data-testid=\"copy-wc2-uri\"\n    >\n      <wui-icon size=\"xs\" color=\"fg-200\" slot=\"iconLeft\" name=\"copy\"></wui-icon>\n      Copy link\n    </wui-link>`;\n    }\n};\nW3mConnectingWcQrcode.styles = styles;\nW3mConnectingWcQrcode = __decorate([\n    customElement('w3m-connecting-wc-qrcode')\n], W3mConnectingWcQrcode);\nexport { W3mConnectingWcQrcode };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil, EventsController, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-text';\nimport '@reown/appkit-ui/wui-wallet-image';\nimport '../w3m-mobile-download-links/index.js';\nlet W3mConnectingWcUnsupported = class W3mConnectingWcUnsupported extends LitElement {\n    constructor() {\n        super();\n        this.wallet = RouterController.state.data?.wallet;\n        if (!this.wallet) {\n            throw new Error('w3m-connecting-wc-unsupported: No wallet provided');\n        }\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet.name, platform: 'browser' }\n        });\n    }\n    render() {\n        return html `\n      <wui-flex\n        flexDirection=\"column\"\n        alignItems=\"center\"\n        .padding=${['3xl', 'xl', 'xl', 'xl']}\n        gap=\"xl\"\n      >\n        <wui-wallet-image\n          size=\"lg\"\n          imageSrc=${ifDefined(AssetUtil.getWalletImage(this.wallet))}\n        ></wui-wallet-image>\n\n        <wui-text variant=\"paragraph-500\" color=\"fg-100\">Not Detected</wui-text>\n      </wui-flex>\n\n      <w3m-mobile-download-links .wallet=${this.wallet}></w3m-mobile-download-links>\n    `;\n    }\n};\nW3mConnectingWcUnsupported = __decorate([\n    customElement('w3m-connecting-wc-unsupported')\n], W3mConnectingWcUnsupported);\nexport { W3mConnectingWcUnsupported };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { state } from 'lit/decorators.js';\nimport { ConnectionController, ConstantsUtil, CoreHelperUtil, EventsController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport { W3mConnectingWidget } from '../../utils/w3m-connecting-widget/index.js';\nlet W3mConnectingWcWeb = class W3mConnectingWcWeb extends W3mConnectingWidget {\n    constructor() {\n        super();\n        this.isLoading = true;\n        if (!this.wallet) {\n            throw new Error('w3m-connecting-wc-web: No wallet provided');\n        }\n        this.onConnect = this.onConnectProxy.bind(this);\n        this.secondaryBtnLabel = 'Open';\n        this.secondaryLabel = ConstantsUtil.CONNECT_LABELS.MOBILE;\n        this.secondaryBtnIcon = 'externalLink';\n        this.updateLoadingState();\n        this.unsubscribe.push(ConnectionController.subscribeKey('wcUri', () => {\n            this.updateLoadingState();\n        }));\n        EventsController.sendEvent({\n            type: 'track',\n            event: 'SELECT_WALLET',\n            properties: { name: this.wallet.name, platform: 'web' }\n        });\n    }\n    updateLoadingState() {\n        this.isLoading = !this.uri;\n    }\n    onConnectProxy() {\n        if (this.wallet?.webapp_link && this.uri) {\n            try {\n                this.error = false;\n                const { webapp_link, name } = this.wallet;\n                const { redirect, href } = CoreHelperUtil.formatUniversalUrl(webapp_link, this.uri);\n                ConnectionController.setWcLinking({ name, href });\n                ConnectionController.setRecentWallet(this.wallet);\n                CoreHelperUtil.openHref(redirect, '_blank');\n            }\n            catch {\n                this.error = true;\n            }\n        }\n    }\n};\n__decorate([\n    state()\n], W3mConnectingWcWeb.prototype, \"isLoading\", void 0);\nW3mConnectingWcWeb = __decorate([\n    customElement('w3m-connecting-wc-web')\n], W3mConnectingWcWeb);\nexport { W3mConnectingWcWeb };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { ChainController, ConnectionController, CoreHelperUtil, EventsController, ModalController, OptionsController, RouterController, SnackController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '../../partials/w3m-connecting-header/index.js';\nimport '../../partials/w3m-connecting-wc-browser/index.js';\nimport '../../partials/w3m-connecting-wc-desktop/index.js';\nimport '../../partials/w3m-connecting-wc-mobile/index.js';\nimport '../../partials/w3m-connecting-wc-qrcode/index.js';\nimport '../../partials/w3m-connecting-wc-unsupported/index.js';\nimport '../../partials/w3m-connecting-wc-web/index.js';\nlet W3mConnectingWcView = class W3mConnectingWcView extends LitElement {\n    constructor() {\n        super();\n        this.wallet = RouterController.state.data?.wallet;\n        this.unsubscribe = [];\n        this.platform = undefined;\n        this.platforms = [];\n        this.isSiwxEnabled = Boolean(OptionsController.state.siwx);\n        this.remoteFeatures = OptionsController.state.remoteFeatures;\n        this.determinePlatforms();\n        this.initializeConnection();\n        this.unsubscribe.push(OptionsController.subscribeKey('remoteFeatures', val => (this.remoteFeatures = val)));\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n    }\n    render() {\n        return html `\n      ${this.headerTemplate()}\n      <div>${this.platformTemplate()}</div>\n      ${this.reownBrandingTemplate()}\n    `;\n    }\n    reownBrandingTemplate() {\n        if (!this.remoteFeatures?.reownBranding) {\n            return null;\n        }\n        return html `<wui-ux-by-reown></wui-ux-by-reown>`;\n    }\n    async initializeConnection(retry = false) {\n        if (this.platform === 'browser' || (OptionsController.state.manualWCControl && !retry)) {\n            return;\n        }\n        try {\n            const { wcPairingExpiry, status } = ConnectionController.state;\n            if (retry ||\n                OptionsController.state.enableEmbedded ||\n                CoreHelperUtil.isPairingExpired(wcPairingExpiry) ||\n                status === 'connecting') {\n                await ConnectionController.connectWalletConnect();\n                if (!this.isSiwxEnabled) {\n                    ModalController.close();\n                }\n            }\n        }\n        catch (error) {\n            EventsController.sendEvent({\n                type: 'track',\n                event: 'CONNECT_ERROR',\n                properties: { message: error?.message ?? 'Unknown' }\n            });\n            ConnectionController.setWcError(true);\n            SnackController.showError(error.message ?? 'Connection error');\n            ConnectionController.resetWcConnection();\n            RouterController.goBack();\n        }\n    }\n    determinePlatforms() {\n        if (!this.wallet) {\n            this.platforms.push('qrcode');\n            this.platform = 'qrcode';\n            return;\n        }\n        if (this.platform) {\n            return;\n        }\n        const { mobile_link, desktop_link, webapp_link, injected, rdns } = this.wallet;\n        const injectedIds = injected?.map(({ injected_id }) => injected_id).filter(Boolean);\n        const browserIds = [...(rdns ? [rdns] : (injectedIds ?? []))];\n        const isBrowser = OptionsController.state.isUniversalProvider ? false : browserIds.length;\n        const hasMobileWCLink = mobile_link;\n        const isWebWc = webapp_link;\n        const isBrowserInstalled = ConnectionController.checkInstalled(browserIds);\n        const isBrowserWc = isBrowser && isBrowserInstalled;\n        const isDesktopWc = desktop_link && !CoreHelperUtil.isMobile();\n        if (isBrowserWc && !ChainController.state.noAdapters) {\n            this.platforms.push('browser');\n        }\n        if (hasMobileWCLink) {\n            this.platforms.push(CoreHelperUtil.isMobile() ? 'mobile' : 'qrcode');\n        }\n        if (isWebWc) {\n            this.platforms.push('web');\n        }\n        if (isDesktopWc) {\n            this.platforms.push('desktop');\n        }\n        if (!isBrowserWc && isBrowser && !ChainController.state.noAdapters) {\n            this.platforms.push('unsupported');\n        }\n        this.platform = this.platforms[0];\n    }\n    platformTemplate() {\n        switch (this.platform) {\n            case 'browser':\n                return html `<w3m-connecting-wc-browser></w3m-connecting-wc-browser>`;\n            case 'web':\n                return html `<w3m-connecting-wc-web></w3m-connecting-wc-web>`;\n            case 'desktop':\n                return html `\n          <w3m-connecting-wc-desktop .onRetry=${() => this.initializeConnection(true)}>\n          </w3m-connecting-wc-desktop>\n        `;\n            case 'mobile':\n                return html `\n          <w3m-connecting-wc-mobile isMobile .onRetry=${() => this.initializeConnection(true)}>\n          </w3m-connecting-wc-mobile>\n        `;\n            case 'qrcode':\n                return html `<w3m-connecting-wc-qrcode></w3m-connecting-wc-qrcode>`;\n            default:\n                return html `<w3m-connecting-wc-unsupported></w3m-connecting-wc-unsupported>`;\n        }\n    }\n    headerTemplate() {\n        const multiPlatform = this.platforms.length > 1;\n        if (!multiPlatform) {\n            return null;\n        }\n        return html `\n      <w3m-connecting-header\n        .platforms=${this.platforms}\n        .onSelectPlatfrom=${this.onSelectPlatform.bind(this)}\n      >\n      </w3m-connecting-header>\n    `;\n    }\n    async onSelectPlatform(platform) {\n        const container = this.shadowRoot?.querySelector('div');\n        if (container) {\n            await container.animate([{ opacity: 1 }, { opacity: 0 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            }).finished;\n            this.platform = platform;\n            container.animate([{ opacity: 0 }, { opacity: 1 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n    }\n};\n__decorate([\n    state()\n], W3mConnectingWcView.prototype, \"platform\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcView.prototype, \"platforms\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcView.prototype, \"isSiwxEnabled\", void 0);\n__decorate([\n    state()\n], W3mConnectingWcView.prototype, \"remoteFeatures\", void 0);\nW3mConnectingWcView = __decorate([\n    customElement('w3m-connecting-wc-view')\n], W3mConnectingWcView);\nexport { W3mConnectingWcView };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { ApiController, CoreHelperUtil, OptionsController, StorageUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '../../partials/w3m-all-wallets-widget/index.js';\nimport '../../partials/w3m-connector-list/index.js';\nimport '../w3m-connecting-wc-view/index.js';\nlet W3mConnectingWcBasicView = class W3mConnectingWcBasicView extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.isMobile = CoreHelperUtil.isMobile();\n    }\n    render() {\n        if (this.isMobile) {\n            const { featured, recommended } = ApiController.state;\n            const { customWallets } = OptionsController.state;\n            const recent = StorageUtil.getRecentWallets();\n            const showConnectors = featured.length || recommended.length || customWallets?.length || recent.length;\n            return html `<wui-flex\n        flexDirection=\"column\"\n        gap=\"xs\"\n        .margin=${['3xs', 's', 's', 's']}\n      >\n        ${showConnectors ? html `<w3m-connector-list></w3m-connector-list>` : null}\n        <w3m-all-wallets-widget></w3m-all-wallets-widget>\n      </wui-flex>`;\n        }\n        return html `<wui-flex flexDirection=\"column\" .padding=${['0', '0', 'l', '0']}>\n      <w3m-connecting-wc-view></w3m-connecting-wc-view>\n      <wui-flex flexDirection=\"column\" .padding=${['0', 'm', '0', 'm']}>\n        <w3m-all-wallets-widget></w3m-all-wallets-widget> </wui-flex\n    ></wui-flex>`;\n    }\n};\n__decorate([\n    state()\n], W3mConnectingWcBasicView.prototype, \"isMobile\", void 0);\nW3mConnectingWcBasicView = __decorate([\n    customElement('w3m-connecting-wc-basic-view')\n], W3mConnectingWcBasicView);\nexport { W3mConnectingWcBasicView };\n//# sourceMappingURL=index.js.map", "import{nothing as t}from\"../lit-html.js\";import{AsyncDirective as i}from\"../async-directive.js\";import{directive as s}from\"../directive.js\";\n/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const e=()=>new h;class h{}const o=new WeakMap,n=s(class extends i{render(i){return t}update(i,[s]){const e=s!==this.G;return e&&void 0!==this.G&&this.rt(void 0),(e||this.lt!==this.ct)&&(this.G=s,this.ht=i.options?.host,this.rt(this.ct=i.element)),t}rt(t){if(this.isConnected||(t=void 0),\"function\"==typeof this.G){const i=this.ht??globalThis;let s=o.get(i);void 0===s&&(s=new WeakMap,o.set(i,s)),void 0!==s.get(this.G)&&this.G.call(this.ht,void 0),s.set(this.G,t),void 0!==t&&this.G.call(this.ht,t)}else this.G.value=t}get lt(){return\"function\"==typeof this.G?o.get(this.ht??globalThis)?.get(this.G):this.G?.value}disconnected(){this.lt===this.ct&&this.rt(void 0)}reconnected(){this.rt(this.ct)}});export{e as createRef,n as ref};\n//# sourceMappingURL=ref.js.map\n", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n\n  label {\n    position: relative;\n    display: inline-block;\n    width: 32px;\n    height: 22px;\n  }\n\n  input {\n    width: 0;\n    height: 0;\n    opacity: 0;\n  }\n\n  span {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: var(--wui-color-blue-100);\n    border-width: 1px;\n    border-style: solid;\n    border-color: var(--wui-color-gray-glass-002);\n    border-radius: 999px;\n    transition:\n      background-color var(--wui-ease-inout-power-1) var(--wui-duration-md),\n      border-color var(--wui-ease-inout-power-1) var(--wui-duration-md);\n    will-change: background-color, border-color;\n  }\n\n  span:before {\n    position: absolute;\n    content: '';\n    height: 16px;\n    width: 16px;\n    left: 3px;\n    top: 2px;\n    background-color: var(--wui-color-inverse-100);\n    transition: transform var(--wui-ease-inout-power-1) var(--wui-duration-lg);\n    will-change: transform;\n    border-radius: 50%;\n  }\n\n  input:checked + span {\n    border-color: var(--wui-color-gray-glass-005);\n    background-color: var(--wui-color-blue-100);\n  }\n\n  input:not(:checked) + span {\n    background-color: var(--wui-color-gray-glass-010);\n  }\n\n  input:checked + span:before {\n    transform: translateX(calc(100% - 7px));\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { createRef, ref } from 'lit/directives/ref.js';\nimport { colorStyles, elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiSwitch = class WuiSwitch extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.inputElementRef = createRef();\n        this.checked = undefined;\n    }\n    render() {\n        return html `\n      <label>\n        <input\n          ${ref(this.inputElementRef)}\n          type=\"checkbox\"\n          ?checked=${ifDefined(this.checked)}\n          @change=${this.dispatchChangeEvent.bind(this)}\n        />\n        <span></span>\n      </label>\n    `;\n    }\n    dispatchChangeEvent() {\n        this.dispatchEvent(new CustomEvent('switchChange', {\n            detail: this.inputElementRef.value?.checked,\n            bubbles: true,\n            composed: true\n        }));\n    }\n};\nWuiSwitch.styles = [resetStyles, elementStyles, colorStyles, styles];\n__decorate([\n    property({ type: Boolean })\n], WuiSwitch.prototype, \"checked\", void 0);\nWuiSwitch = __decorate([\n    customElement('wui-switch')\n], WuiSwitch);\nexport { WuiSwitch };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    height: 100%;\n  }\n\n  button {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    column-gap: var(--wui-spacing-1xs);\n    padding: var(--wui-spacing-xs) var(--wui-spacing-s);\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: var(--wui-border-radius-xs);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);\n    transition: background-color var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: background-color;\n    cursor: pointer;\n  }\n\n  wui-switch {\n    pointer-events: none;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport '../../components/wui-icon/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-switch/index.js';\nimport styles from './styles.js';\nlet WuiCertifiedSwitch = class WuiCertifiedSwitch extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.checked = undefined;\n    }\n    render() {\n        return html `\n      <button>\n        <wui-icon size=\"xl\" name=\"walletConnectBrown\"></wui-icon>\n        <wui-switch ?checked=${ifDefined(this.checked)}></wui-switch>\n      </button>\n    `;\n    }\n};\nWuiCertifiedSwitch.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property({ type: Boolean })\n], WuiCertifiedSwitch.prototype, \"checked\", void 0);\nWuiCertifiedSwitch = __decorate([\n    customElement('wui-certified-switch')\n], WuiCertifiedSwitch);\nexport { WuiCertifiedSwitch };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    background-color: var(--wui-color-fg-300);\n    border-radius: var(--wui-border-radius-4xs);\n    width: 16px;\n    height: 16px;\n  }\n\n  button:disabled {\n    background-color: var(--wui-color-bg-300);\n  }\n\n  wui-icon {\n    color: var(--wui-color-bg-200) !important;\n  }\n\n  button:focus-visible {\n    background-color: var(--wui-color-fg-250);\n    border: 1px solid var(--wui-color-accent-100);\n  }\n\n  @media (hover: hover) and (pointer: fine) {\n    button:hover:enabled {\n      background-color: var(--wui-color-fg-250);\n    }\n\n    button:active:enabled {\n      background-color: var(--wui-color-fg-225);\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport '../../components/wui-icon/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiInputElement = class WuiInputElement extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.icon = 'copy';\n    }\n    render() {\n        return html `\n      <button>\n        <wui-icon color=\"inherit\" size=\"xxs\" name=${this.icon}></wui-icon>\n      </button>\n    `;\n    }\n};\nWuiInputElement.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiInputElement.prototype, \"icon\", void 0);\nWuiInputElement = __decorate([\n    customElement('wui-input-element')\n], WuiInputElement);\nexport { WuiInputElement };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    position: relative;\n    width: 100%;\n    display: inline-block;\n    color: var(--wui-color-fg-275);\n  }\n\n  input {\n    width: 100%;\n    border-radius: var(--wui-border-radius-xs);\n    box-shadow: inset 0 0 0 1px var(--wui-color-gray-glass-002);\n    background: var(--wui-color-gray-glass-002);\n    font-size: var(--wui-font-size-paragraph);\n    letter-spacing: var(--wui-letter-spacing-paragraph);\n    color: var(--wui-color-fg-100);\n    transition:\n      background-color var(--wui-ease-inout-power-1) var(--wui-duration-md),\n      border-color var(--wui-ease-inout-power-1) var(--wui-duration-md),\n      box-shadow var(--wui-ease-inout-power-1) var(--wui-duration-md);\n    will-change: background-color, border-color, box-shadow;\n    caret-color: var(--wui-color-accent-100);\n  }\n\n  input:disabled {\n    cursor: not-allowed;\n    border: 1px solid var(--wui-color-gray-glass-010);\n  }\n\n  input:disabled::placeholder,\n  input:disabled + wui-icon {\n    color: var(--wui-color-fg-300);\n  }\n\n  input::placeholder {\n    color: var(--wui-color-fg-275);\n  }\n\n  input:focus:enabled {\n    background-color: var(--wui-color-gray-glass-005);\n    -webkit-box-shadow:\n      inset 0 0 0 1px var(--wui-color-accent-100),\n      0px 0px 0px 4px var(--wui-box-shadow-blue);\n    -moz-box-shadow:\n      inset 0 0 0 1px var(--wui-color-accent-100),\n      0px 0px 0px 4px var(--wui-box-shadow-blue);\n    box-shadow:\n      inset 0 0 0 1px var(--wui-color-accent-100),\n      0px 0px 0px 4px var(--wui-box-shadow-blue);\n  }\n\n  input:hover:enabled {\n    background-color: var(--wui-color-gray-glass-005);\n  }\n\n  wui-icon {\n    position: absolute;\n    top: 50%;\n    transform: translateY(-50%);\n    pointer-events: none;\n  }\n\n  .wui-size-sm {\n    padding: 9px var(--wui-spacing-m) 10px var(--wui-spacing-s);\n  }\n\n  wui-icon + .wui-size-sm {\n    padding: 9px var(--wui-spacing-m) 10px 36px;\n  }\n\n  wui-icon[data-input='sm'] {\n    left: var(--wui-spacing-s);\n  }\n\n  .wui-size-md {\n    padding: 15px var(--wui-spacing-m) var(--wui-spacing-l) var(--wui-spacing-m);\n  }\n\n  wui-icon + .wui-size-md,\n  wui-loading-spinner + .wui-size-md {\n    padding: 10.5px var(--wui-spacing-3xl) 10.5px var(--wui-spacing-3xl);\n  }\n\n  wui-icon[data-input='md'] {\n    left: var(--wui-spacing-l);\n  }\n\n  .wui-size-lg {\n    padding: var(--wui-spacing-s) var(--wui-spacing-s) var(--wui-spacing-s) var(--wui-spacing-l);\n    letter-spacing: var(--wui-letter-spacing-medium-title);\n    font-size: var(--wui-font-size-medium-title);\n    font-weight: var(--wui-font-weight-light);\n    line-height: 130%;\n    color: var(--wui-color-fg-100);\n    height: 64px;\n  }\n\n  .wui-padding-right-xs {\n    padding-right: var(--wui-spacing-xs);\n  }\n\n  .wui-padding-right-s {\n    padding-right: var(--wui-spacing-s);\n  }\n\n  .wui-padding-right-m {\n    padding-right: var(--wui-spacing-m);\n  }\n\n  .wui-padding-right-l {\n    padding-right: var(--wui-spacing-l);\n  }\n\n  .wui-padding-right-xl {\n    padding-right: var(--wui-spacing-xl);\n  }\n\n  .wui-padding-right-2xl {\n    padding-right: var(--wui-spacing-2xl);\n  }\n\n  .wui-padding-right-3xl {\n    padding-right: var(--wui-spacing-3xl);\n  }\n\n  .wui-padding-right-4xl {\n    padding-right: var(--wui-spacing-4xl);\n  }\n\n  .wui-padding-right-5xl {\n    padding-right: var(--wui-spacing-5xl);\n  }\n\n  wui-icon + .wui-size-lg,\n  wui-loading-spinner + .wui-size-lg {\n    padding-left: 50px;\n  }\n\n  wui-icon[data-input='lg'] {\n    left: var(--wui-spacing-l);\n  }\n\n  .wui-size-mdl {\n    padding: 17.25px var(--wui-spacing-m) 17.25px var(--wui-spacing-m);\n  }\n  wui-icon + .wui-size-mdl,\n  wui-loading-spinner + .wui-size-mdl {\n    padding: 17.25px var(--wui-spacing-3xl) 17.25px 40px;\n  }\n  wui-icon[data-input='mdl'] {\n    left: var(--wui-spacing-m);\n  }\n\n  input:placeholder-shown ~ ::slotted(wui-input-element),\n  input:placeholder-shown ~ ::slotted(wui-icon) {\n    opacity: 0;\n    pointer-events: none;\n  }\n\n  input::-webkit-outer-spin-button,\n  input::-webkit-inner-spin-button {\n    -webkit-appearance: none;\n    margin: 0;\n  }\n\n  input[type='number'] {\n    -moz-appearance: textfield;\n  }\n\n  ::slotted(wui-input-element),\n  ::slotted(wui-icon) {\n    position: absolute;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n\n  ::slotted(wui-input-element) {\n    right: var(--wui-spacing-m);\n  }\n\n  ::slotted(wui-icon) {\n    right: 0px;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { classMap } from 'lit/directives/class-map.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { createRef, ref } from 'lit/directives/ref.js';\nimport '../../components/wui-icon/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiInputText = class WuiInputText extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.inputElementRef = createRef();\n        this.size = 'md';\n        this.disabled = false;\n        this.placeholder = '';\n        this.type = 'text';\n        this.value = '';\n    }\n    render() {\n        const inputClass = `wui-padding-right-${this.inputRightPadding}`;\n        const sizeClass = `wui-size-${this.size}`;\n        const classes = {\n            [sizeClass]: true,\n            [inputClass]: Boolean(this.inputRightPadding)\n        };\n        return html `${this.templateIcon()}\n      <input\n        data-testid=\"wui-input-text\"\n        ${ref(this.inputElementRef)}\n        class=${classMap(classes)}\n        type=${this.type}\n        enterkeyhint=${ifDefined(this.enterKeyHint)}\n        ?disabled=${this.disabled}\n        placeholder=${this.placeholder}\n        @input=${this.dispatchInputChangeEvent.bind(this)}\n        .value=${this.value || ''}\n        tabindex=${ifDefined(this.tabIdx)}\n      />\n      <slot></slot>`;\n    }\n    templateIcon() {\n        if (this.icon) {\n            return html `<wui-icon\n        data-input=${this.size}\n        size=${this.size}\n        color=\"inherit\"\n        name=${this.icon}\n      ></wui-icon>`;\n        }\n        return null;\n    }\n    dispatchInputChangeEvent() {\n        this.dispatchEvent(new CustomEvent('inputChange', {\n            detail: this.inputElementRef.value?.value,\n            bubbles: true,\n            composed: true\n        }));\n    }\n};\nWuiInputText.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiInputText.prototype, \"size\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"icon\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiInputText.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"placeholder\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"type\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"keyHint\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"value\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"inputRightPadding\", void 0);\n__decorate([\n    property()\n], WuiInputText.prototype, \"tabIdx\", void 0);\nWuiInputText = __decorate([\n    customElement('wui-input-text')\n], WuiInputText);\nexport { WuiInputText };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { createRef, ref } from 'lit/directives/ref.js';\nimport '../../composites/wui-input-element/index.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-input-text/index.js';\nimport styles from './styles.js';\nlet WuiSearchBar = class WuiSearchBar extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.inputComponentRef = createRef();\n    }\n    render() {\n        return html `\n      <wui-input-text\n        ${ref(this.inputComponentRef)}\n        placeholder=\"Search wallet\"\n        icon=\"search\"\n        type=\"search\"\n        enterKeyHint=\"search\"\n        size=\"sm\"\n      >\n        <wui-input-element @click=${this.clearValue} icon=\"close\"></wui-input-element>\n      </wui-input-text>\n    `;\n    }\n    clearValue() {\n        const inputComponent = this.inputComponentRef.value;\n        const inputElement = inputComponent?.inputElementRef.value;\n        if (inputElement) {\n            inputElement.value = '';\n            inputElement.focus();\n            inputElement.dispatchEvent(new Event('input'));\n        }\n    }\n};\nWuiSearchBar.styles = [resetStyles, styles];\nWuiSearchBar = __decorate([\n    customElement('wui-search-bar')\n], WuiSearchBar);\nexport { WuiSearchBar };\n//# sourceMappingURL=index.js.map", "import { svg } from 'lit';\nexport const networkSvgMd = svg `<svg  viewBox=\"0 0 48 54\" fill=\"none\">\n  <path\n    d=\"M43.4605 10.7248L28.0485 1.61089C25.5438 0.129705 22.4562 0.129705 19.9515 1.61088L4.53951 10.7248C2.03626 12.2051 0.5 14.9365 0.5 17.886V36.1139C0.5 39.0635 2.03626 41.7949 4.53951 43.2752L19.9515 52.3891C22.4562 53.8703 25.5438 53.8703 28.0485 52.3891L43.4605 43.2752C45.9637 41.7949 47.5 39.0635 47.5 36.114V17.8861C47.5 14.9365 45.9637 12.2051 43.4605 10.7248Z\"\n  />\n</svg>`;\n//# sourceMappingURL=networkMd.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    width: 104px;\n    row-gap: var(--wui-spacing-xs);\n    padding: var(--wui-spacing-xs) 10px;\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: clamp(0px, var(--wui-border-radius-xs), 20px);\n    position: relative;\n  }\n\n  wui-shimmer[data-type='network'] {\n    border: none;\n    -webkit-clip-path: var(--wui-path-network);\n    clip-path: var(--wui-path-network);\n  }\n\n  svg {\n    position: absolute;\n    width: 48px;\n    height: 54px;\n    z-index: 1;\n  }\n\n  svg > path {\n    stroke: var(--wui-color-gray-glass-010);\n    stroke-width: 1px;\n  }\n\n  @media (max-width: 350px) {\n    :host {\n      width: 100%;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { networkSvgMd } from '../../assets/svg/networkMd.js';\nimport '../../components/wui-shimmer/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiCardSelectLoader = class WuiCardSelectLoader extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.type = 'wallet';\n    }\n    render() {\n        return html `\n      ${this.shimmerTemplate()}\n      <wui-shimmer width=\"56px\" height=\"20px\" borderRadius=\"xs\"></wui-shimmer>\n    `;\n    }\n    shimmerTemplate() {\n        if (this.type === 'network') {\n            return html ` <wui-shimmer\n          data-type=${this.type}\n          width=\"48px\"\n          height=\"54px\"\n          borderRadius=\"xs\"\n        ></wui-shimmer>\n        ${networkSvgMd}`;\n        }\n        return html `<wui-shimmer width=\"56px\" height=\"56px\" borderRadius=\"xs\"></wui-shimmer>`;\n    }\n};\nWuiCardSelectLoader.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiCardSelectLoader.prototype, \"type\", void 0);\nWuiCardSelectLoader = __decorate([\n    customElement('wui-card-select-loader')\n], WuiCardSelectLoader);\nexport { WuiCardSelectLoader };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  :host {\n    display: grid;\n    width: inherit;\n    height: inherit;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { resetStyles } from '../../utils/ThemeUtil.js';\nimport { UiHelperUtil } from '../../utils/UiHelperUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport styles from './styles.js';\nlet WuiGrid = class WuiGrid extends LitElement {\n    render() {\n        this.style.cssText = `\n      grid-template-rows: ${this.gridTemplateRows};\n      grid-template-columns: ${this.gridTemplateColumns};\n      justify-items: ${this.justifyItems};\n      align-items: ${this.alignItems};\n      justify-content: ${this.justifyContent};\n      align-content: ${this.alignContent};\n      column-gap: ${this.columnGap && `var(--wui-spacing-${this.columnGap})`};\n      row-gap: ${this.rowGap && `var(--wui-spacing-${this.rowGap})`};\n      gap: ${this.gap && `var(--wui-spacing-${this.gap})`};\n      padding-top: ${this.padding && UiHelperUtil.getSpacingStyles(this.padding, 0)};\n      padding-right: ${this.padding && UiHelperUtil.getSpacingStyles(this.padding, 1)};\n      padding-bottom: ${this.padding && UiHelperUtil.getSpacingStyles(this.padding, 2)};\n      padding-left: ${this.padding && UiHelperUtil.getSpacingStyles(this.padding, 3)};\n      margin-top: ${this.margin && UiHelperUtil.getSpacingStyles(this.margin, 0)};\n      margin-right: ${this.margin && UiHelperUtil.getSpacingStyles(this.margin, 1)};\n      margin-bottom: ${this.margin && UiHelperUtil.getSpacingStyles(this.margin, 2)};\n      margin-left: ${this.margin && UiHelperUtil.getSpacingStyles(this.margin, 3)};\n    `;\n        return html `<slot></slot>`;\n    }\n};\nWuiGrid.styles = [resetStyles, styles];\n__decorate([\n    property()\n], WuiGrid.prototype, \"gridTemplateRows\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"gridTemplateColumns\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"justifyItems\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"alignItems\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"justifyContent\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"alignContent\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"columnGap\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"rowGap\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"gap\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"padding\", void 0);\n__decorate([\n    property()\n], WuiGrid.prototype, \"margin\", void 0);\nWuiGrid = __decorate([\n    customElement('wui-grid')\n], WuiGrid);\nexport { WuiGrid };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    width: 104px;\n    row-gap: var(--wui-spacing-xs);\n    padding: var(--wui-spacing-s) var(--wui-spacing-0);\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: clamp(0px, var(--wui-border-radius-xs), 20px);\n    transition:\n      color var(--wui-duration-lg) var(--wui-ease-out-power-1),\n      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1),\n      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1);\n    will-change: background-color, color, border-radius;\n    outline: none;\n    border: none;\n  }\n\n  button > wui-flex > wui-text {\n    color: var(--wui-color-fg-100);\n    max-width: 86px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    justify-content: center;\n  }\n\n  button > wui-flex > wui-text.certified {\n    max-width: 66px;\n  }\n\n  button:hover:enabled {\n    background-color: var(--wui-color-gray-glass-005);\n  }\n\n  button:disabled > wui-flex > wui-text {\n    color: var(--wui-color-gray-glass-015);\n  }\n\n  [data-selected='true'] {\n    background-color: var(--wui-color-accent-glass-020);\n  }\n\n  @media (hover: hover) and (pointer: fine) {\n    [data-selected='true']:hover:enabled {\n      background-color: var(--wui-color-accent-glass-015);\n    }\n  }\n\n  [data-selected='true']:active:enabled {\n    background-color: var(--wui-color-accent-glass-010);\n  }\n\n  @media (max-width: 350px) {\n    button {\n      width: 100%;\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { AssetUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon';\nimport '@reown/appkit-ui/wui-shimmer';\nimport '@reown/appkit-ui/wui-text';\nimport '@reown/appkit-ui/wui-wallet-image';\nimport styles from './styles.js';\nlet W3mAllWalletsListItem = class W3mAllWalletsListItem extends LitElement {\n    constructor() {\n        super();\n        this.observer = new IntersectionObserver(() => undefined);\n        this.visible = false;\n        this.imageSrc = undefined;\n        this.imageLoading = false;\n        this.wallet = undefined;\n        this.observer = new IntersectionObserver(entries => {\n            entries.forEach(entry => {\n                if (entry.isIntersecting) {\n                    this.visible = true;\n                    this.fetchImageSrc();\n                }\n                else {\n                    this.visible = false;\n                }\n            });\n        }, { threshold: 0.01 });\n    }\n    firstUpdated() {\n        this.observer.observe(this);\n    }\n    disconnectedCallback() {\n        this.observer.disconnect();\n    }\n    render() {\n        const certified = this.wallet?.badge_type === 'certified';\n        return html `\n      <button>\n        ${this.imageTemplate()}\n        <wui-flex flexDirection=\"row\" alignItems=\"center\" justifyContent=\"center\" gap=\"3xs\">\n          <wui-text\n            variant=\"tiny-500\"\n            color=\"inherit\"\n            class=${ifDefined(certified ? 'certified' : undefined)}\n            >${this.wallet?.name}</wui-text\n          >\n          ${certified ? html `<wui-icon size=\"sm\" name=\"walletConnectBrown\"></wui-icon>` : null}\n        </wui-flex>\n      </button>\n    `;\n    }\n    imageTemplate() {\n        if ((!this.visible && !this.imageSrc) || this.imageLoading) {\n            return this.shimmerTemplate();\n        }\n        return html `\n      <wui-wallet-image\n        size=\"md\"\n        imageSrc=${ifDefined(this.imageSrc)}\n        name=${this.wallet?.name}\n        .installed=${this.wallet?.installed}\n        badgeSize=\"sm\"\n      >\n      </wui-wallet-image>\n    `;\n    }\n    shimmerTemplate() {\n        return html `<wui-shimmer width=\"56px\" height=\"56px\" borderRadius=\"xs\"></wui-shimmer>`;\n    }\n    async fetchImageSrc() {\n        if (!this.wallet) {\n            return;\n        }\n        this.imageSrc = AssetUtil.getWalletImage(this.wallet);\n        if (this.imageSrc) {\n            return;\n        }\n        this.imageLoading = true;\n        this.imageSrc = await AssetUtil.fetchWalletImage(this.wallet.image_id);\n        this.imageLoading = false;\n    }\n};\nW3mAllWalletsListItem.styles = styles;\n__decorate([\n    state()\n], W3mAllWalletsListItem.prototype, \"visible\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsListItem.prototype, \"imageSrc\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsListItem.prototype, \"imageLoading\", void 0);\n__decorate([\n    property()\n], W3mAllWalletsListItem.prototype, \"wallet\", void 0);\nW3mAllWalletsListItem = __decorate([\n    customElement('w3m-all-wallets-list-item')\n], W3mAllWalletsListItem);\nexport { W3mAllWalletsListItem };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  wui-grid {\n    max-height: clamp(360px, 400px, 80vh);\n    overflow: scroll;\n    scrollbar-width: none;\n    grid-auto-rows: min-content;\n    grid-template-columns: repeat(auto-fill, 104px);\n  }\n\n  @media (max-width: 350px) {\n    wui-grid {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n\n  wui-grid[data-scroll='false'] {\n    overflow: hidden;\n  }\n\n  wui-grid::-webkit-scrollbar {\n    display: none;\n  }\n\n  wui-loading-spinner {\n    padding-top: var(--wui-spacing-l);\n    padding-bottom: var(--wui-spacing-l);\n    justify-content: center;\n    grid-column: 1 / span 4;\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { ApiController, ConnectorController, CoreHelperUtil } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-card-select-loader';\nimport '@reown/appkit-ui/wui-grid';\nimport { WalletUtil } from '../../utils/WalletUtil.js';\nimport '../w3m-all-wallets-list-item/index.js';\nimport styles from './styles.js';\nconst PAGINATOR_ID = 'local-paginator';\nlet W3mAllWalletsList = class W3mAllWalletsList extends LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.paginationObserver = undefined;\n        this.loading = !ApiController.state.wallets.length;\n        this.wallets = ApiController.state.wallets;\n        this.recommended = ApiController.state.recommended;\n        this.featured = ApiController.state.featured;\n        this.filteredWallets = ApiController.state.filteredWallets;\n        this.unsubscribe.push(...[\n            ApiController.subscribeKey('wallets', val => (this.wallets = val)),\n            ApiController.subscribeKey('recommended', val => (this.recommended = val)),\n            ApiController.subscribeKey('featured', val => (this.featured = val)),\n            ApiController.subscribeKey('filteredWallets', val => (this.filteredWallets = val))\n        ]);\n    }\n    firstUpdated() {\n        this.initialFetch();\n        this.createPaginationObserver();\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n        this.paginationObserver?.disconnect();\n    }\n    render() {\n        return html `\n      <wui-grid\n        data-scroll=${!this.loading}\n        .padding=${['0', 's', 's', 's']}\n        columnGap=\"xxs\"\n        rowGap=\"l\"\n        justifyContent=\"space-between\"\n      >\n        ${this.loading ? this.shimmerTemplate(16) : this.walletsTemplate()}\n        ${this.paginationLoaderTemplate()}\n      </wui-grid>\n    `;\n    }\n    async initialFetch() {\n        this.loading = true;\n        const gridEl = this.shadowRoot?.querySelector('wui-grid');\n        if (gridEl) {\n            await ApiController.fetchWalletsByPage({ page: 1 });\n            await gridEl.animate([{ opacity: 1 }, { opacity: 0 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            }).finished;\n            this.loading = false;\n            gridEl.animate([{ opacity: 0 }, { opacity: 1 }], {\n                duration: 200,\n                fill: 'forwards',\n                easing: 'ease'\n            });\n        }\n    }\n    shimmerTemplate(items, id) {\n        return [...Array(items)].map(() => html `\n        <wui-card-select-loader type=\"wallet\" id=${ifDefined(id)}></wui-card-select-loader>\n      `);\n    }\n    walletsTemplate() {\n        const wallets = this.filteredWallets?.length > 0\n            ? CoreHelperUtil.uniqueBy([...this.featured, ...this.recommended, ...this.filteredWallets], 'id')\n            : CoreHelperUtil.uniqueBy([...this.featured, ...this.recommended, ...this.wallets], 'id');\n        const walletsWithInstalled = WalletUtil.markWalletsAsInstalled(wallets);\n        return walletsWithInstalled.map(wallet => html `\n        <w3m-all-wallets-list-item\n          @click=${() => this.onConnectWallet(wallet)}\n          .wallet=${wallet}\n        ></w3m-all-wallets-list-item>\n      `);\n    }\n    paginationLoaderTemplate() {\n        const { wallets, recommended, featured, count } = ApiController.state;\n        const columns = window.innerWidth < 352 ? 3 : 4;\n        const currentWallets = wallets.length + recommended.length;\n        const minimumRows = Math.ceil(currentWallets / columns);\n        let shimmerCount = minimumRows * columns - currentWallets + columns;\n        shimmerCount -= wallets.length ? featured.length % columns : 0;\n        if (count === 0 && featured.length > 0) {\n            return null;\n        }\n        if (count === 0 || [...featured, ...wallets, ...recommended].length < count) {\n            return this.shimmerTemplate(shimmerCount, PAGINATOR_ID);\n        }\n        return null;\n    }\n    createPaginationObserver() {\n        const loaderEl = this.shadowRoot?.querySelector(`#${PAGINATOR_ID}`);\n        if (loaderEl) {\n            this.paginationObserver = new IntersectionObserver(([element]) => {\n                if (element?.isIntersecting && !this.loading) {\n                    const { page, count, wallets } = ApiController.state;\n                    if (wallets.length < count) {\n                        ApiController.fetchWalletsByPage({ page: page + 1 });\n                    }\n                }\n            });\n            this.paginationObserver.observe(loaderEl);\n        }\n    }\n    onConnectWallet(wallet) {\n        ConnectorController.selectWalletConnector(wallet);\n    }\n};\nW3mAllWalletsList.styles = styles;\n__decorate([\n    state()\n], W3mAllWalletsList.prototype, \"loading\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsList.prototype, \"wallets\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsList.prototype, \"recommended\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsList.prototype, \"featured\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsList.prototype, \"filteredWallets\", void 0);\nW3mAllWalletsList = __decorate([\n    customElement('w3m-all-wallets-list')\n], W3mAllWalletsList);\nexport { W3mAllWalletsList };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  wui-grid,\n  wui-loading-spinner,\n  wui-flex {\n    height: 360px;\n  }\n\n  wui-grid {\n    overflow: scroll;\n    scrollbar-width: none;\n    grid-auto-rows: min-content;\n    grid-template-columns: repeat(auto-fill, 104px);\n  }\n\n  wui-grid[data-scroll='false'] {\n    overflow: hidden;\n  }\n\n  wui-grid::-webkit-scrollbar {\n    display: none;\n  }\n\n  wui-loading-spinner {\n    justify-content: center;\n    align-items: center;\n  }\n\n  @media (max-width: 350px) {\n    wui-grid {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property, state } from 'lit/decorators.js';\nimport { ApiController, ConnectorController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-grid';\nimport '@reown/appkit-ui/wui-icon-box';\nimport '@reown/appkit-ui/wui-loading-spinner';\nimport '@reown/appkit-ui/wui-text';\nimport { WalletUtil } from '../../utils/WalletUtil.js';\nimport '../w3m-all-wallets-list-item/index.js';\nimport styles from './styles.js';\nlet W3mAllWalletsSearch = class W3mAllWalletsSearch extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.prevQuery = '';\n        this.prevBadge = undefined;\n        this.loading = true;\n        this.query = '';\n    }\n    render() {\n        this.onSearch();\n        return this.loading\n            ? html `<wui-loading-spinner color=\"accent-100\"></wui-loading-spinner>`\n            : this.walletsTemplate();\n    }\n    async onSearch() {\n        if (this.query.trim() !== this.prevQuery.trim() || this.badge !== this.prevBadge) {\n            this.prevQuery = this.query;\n            this.prevBadge = this.badge;\n            this.loading = true;\n            await ApiController.searchWallet({ search: this.query, badge: this.badge });\n            this.loading = false;\n        }\n    }\n    walletsTemplate() {\n        const { search } = ApiController.state;\n        const wallets = WalletUtil.markWalletsAsInstalled(search);\n        if (!search.length) {\n            return html `\n        <wui-flex\n          data-testid=\"no-wallet-found\"\n          justifyContent=\"center\"\n          alignItems=\"center\"\n          gap=\"s\"\n          flexDirection=\"column\"\n        >\n          <wui-icon-box\n            size=\"lg\"\n            iconColor=\"fg-200\"\n            backgroundColor=\"fg-300\"\n            icon=\"wallet\"\n            background=\"transparent\"\n          ></wui-icon-box>\n          <wui-text data-testid=\"no-wallet-found-text\" color=\"fg-200\" variant=\"paragraph-500\">\n            No Wallet found\n          </wui-text>\n        </wui-flex>\n      `;\n        }\n        return html `\n      <wui-grid\n        data-testid=\"wallet-list\"\n        .padding=${['0', 's', 's', 's']}\n        rowGap=\"l\"\n        columnGap=\"xs\"\n        justifyContent=\"space-between\"\n      >\n        ${wallets.map(wallet => html `\n            <w3m-all-wallets-list-item\n              @click=${() => this.onConnectWallet(wallet)}\n              .wallet=${wallet}\n              data-testid=\"wallet-search-item-${wallet.id}\"\n            ></w3m-all-wallets-list-item>\n          `)}\n      </wui-grid>\n    `;\n    }\n    onConnectWallet(wallet) {\n        ConnectorController.selectWalletConnector(wallet);\n    }\n};\nW3mAllWalletsSearch.styles = styles;\n__decorate([\n    state()\n], W3mAllWalletsSearch.prototype, \"loading\", void 0);\n__decorate([\n    property()\n], W3mAllWalletsSearch.prototype, \"query\", void 0);\n__decorate([\n    property()\n], W3mAllWalletsSearch.prototype, \"badge\", void 0);\nW3mAllWalletsSearch = __decorate([\n    customElement('w3m-all-wallets-search')\n], W3mAllWalletsSearch);\nexport { W3mAllWalletsSearch };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { state } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport { CoreHelperUtil, RouterController, SnackController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-certified-switch';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-icon-box';\nimport '@reown/appkit-ui/wui-search-bar';\nimport '../../partials/w3m-all-wallets-list/index.js';\nimport '../../partials/w3m-all-wallets-search/index.js';\nlet W3mAllWalletsView = class W3mAllWalletsView extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.search = '';\n        this.onDebouncedSearch = CoreHelperUtil.debounce((value) => {\n            this.search = value;\n        });\n    }\n    render() {\n        const isSearch = this.search.length >= 2;\n        return html `\n      <wui-flex .padding=${['0', 's', 's', 's']} gap=\"xs\">\n        <wui-search-bar @inputChange=${this.onInputChange.bind(this)}></wui-search-bar>\n        <wui-certified-switch\n          ?checked=${this.badge}\n          @click=${this.onClick.bind(this)}\n          data-testid=\"wui-certified-switch\"\n        ></wui-certified-switch>\n        ${this.qrButtonTemplate()}\n      </wui-flex>\n      ${isSearch || this.badge\n            ? html `<w3m-all-wallets-search\n            query=${this.search}\n            badge=${ifDefined(this.badge)}\n          ></w3m-all-wallets-search>`\n            : html `<w3m-all-wallets-list badge=${ifDefined(this.badge)}></w3m-all-wallets-list>`}\n    `;\n    }\n    onInputChange(event) {\n        this.onDebouncedSearch(event.detail);\n    }\n    onClick() {\n        if (this.badge === 'certified') {\n            this.badge = undefined;\n            return;\n        }\n        this.badge = 'certified';\n        SnackController.showSvg('Only WalletConnect certified', {\n            icon: 'walletConnectBrown',\n            iconColor: 'accent-100'\n        });\n    }\n    qrButtonTemplate() {\n        if (CoreHelperUtil.isMobile()) {\n            return html `\n        <wui-icon-box\n          size=\"lg\"\n          iconSize=\"xl\"\n          iconColor=\"accent-100\"\n          backgroundColor=\"accent-100\"\n          icon=\"qrCode\"\n          background=\"transparent\"\n          border\n          borderColor=\"wui-accent-glass-010\"\n          @click=${this.onWalletConnectQr.bind(this)}\n        ></wui-icon-box>\n      `;\n        }\n        return null;\n    }\n    onWalletConnectQr() {\n        RouterController.push('ConnectingWalletConnect');\n    }\n};\n__decorate([\n    state()\n], W3mAllWalletsView.prototype, \"search\", void 0);\n__decorate([\n    state()\n], W3mAllWalletsView.prototype, \"badge\", void 0);\nW3mAllWalletsView = __decorate([\n    customElement('w3m-all-wallets-view')\n], W3mAllWalletsView);\nexport { W3mAllWalletsView };\n//# sourceMappingURL=index.js.map", "import { css } from 'lit';\nexport default css `\n  button {\n    column-gap: var(--wui-spacing-s);\n    padding: 11px 18px 11px var(--wui-spacing-s);\n    width: 100%;\n    background-color: var(--wui-color-gray-glass-002);\n    border-radius: var(--wui-border-radius-xs);\n    color: var(--wui-color-fg-250);\n    transition:\n      color var(--wui-ease-out-power-1) var(--wui-duration-md),\n      background-color var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: color, background-color;\n  }\n\n  button[data-iconvariant='square'],\n  button[data-iconvariant='square-blue'] {\n    padding: 6px 18px 6px 9px;\n  }\n\n  button > wui-flex {\n    flex: 1;\n  }\n\n  button > wui-image {\n    width: 32px;\n    height: 32px;\n    box-shadow: 0 0 0 2px var(--wui-color-gray-glass-005);\n    border-radius: var(--wui-border-radius-3xl);\n  }\n\n  button > wui-icon {\n    width: 36px;\n    height: 36px;\n    transition: opacity var(--wui-ease-out-power-1) var(--wui-duration-md);\n    will-change: opacity;\n  }\n\n  button > wui-icon-box[data-variant='blue'] {\n    box-shadow: 0 0 0 2px var(--wui-color-accent-glass-005);\n  }\n\n  button > wui-icon-box[data-variant='overlay'] {\n    box-shadow: 0 0 0 2px var(--wui-color-gray-glass-005);\n  }\n\n  button > wui-icon-box[data-variant='square-blue'] {\n    border-radius: var(--wui-border-radius-3xs);\n    position: relative;\n    border: none;\n    width: 36px;\n    height: 36px;\n  }\n\n  button > wui-icon-box[data-variant='square-blue']::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    border-radius: inherit;\n    border: 1px solid var(--wui-color-accent-glass-010);\n    pointer-events: none;\n  }\n\n  button > wui-icon:last-child {\n    width: 14px;\n    height: 14px;\n  }\n\n  button:disabled {\n    color: var(--wui-color-gray-glass-020);\n  }\n\n  button[data-loading='true'] > wui-icon {\n    opacity: 0;\n  }\n\n  wui-loading-spinner {\n    position: absolute;\n    right: 18px;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n`;\n//# sourceMappingURL=styles.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { property } from 'lit/decorators.js';\nimport { ifDefined } from 'lit/directives/if-defined.js';\nimport '../../components/wui-icon/index.js';\nimport '../../components/wui-image/index.js';\nimport '../../components/wui-loading-spinner/index.js';\nimport '../../components/wui-text/index.js';\nimport '../../layout/wui-flex/index.js';\nimport { elementStyles, resetStyles } from '../../utils/ThemeUtil.js';\nimport { customElement } from '../../utils/WebComponentsUtil.js';\nimport '../wui-icon-box/index.js';\nimport styles from './styles.js';\nlet WuiListItem = class WuiListItem extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.tabIdx = undefined;\n        this.variant = 'icon';\n        this.disabled = false;\n        this.imageSrc = undefined;\n        this.alt = undefined;\n        this.chevron = false;\n        this.loading = false;\n    }\n    render() {\n        return html `\n      <button\n        ?disabled=${this.loading ? true : Boolean(this.disabled)}\n        data-loading=${this.loading}\n        data-iconvariant=${ifDefined(this.iconVariant)}\n        tabindex=${ifDefined(this.tabIdx)}\n      >\n        ${this.loadingTemplate()} ${this.visualTemplate()}\n        <wui-flex gap=\"3xs\">\n          <slot></slot>\n        </wui-flex>\n        ${this.chevronTemplate()}\n      </button>\n    `;\n    }\n    visualTemplate() {\n        if (this.variant === 'image' && this.imageSrc) {\n            return html `<wui-image src=${this.imageSrc} alt=${this.alt ?? 'list item'}></wui-image>`;\n        }\n        if (this.iconVariant === 'square' && this.icon && this.variant === 'icon') {\n            return html `<wui-icon name=${this.icon}></wui-icon>`;\n        }\n        if (this.variant === 'icon' && this.icon && this.iconVariant) {\n            const color = ['blue', 'square-blue'].includes(this.iconVariant) ? 'accent-100' : 'fg-200';\n            const size = this.iconVariant === 'square-blue' ? 'mdl' : 'md';\n            const iconSize = this.iconSize ? this.iconSize : size;\n            return html `\n        <wui-icon-box\n          data-variant=${this.iconVariant}\n          icon=${this.icon}\n          iconSize=${iconSize}\n          background=\"transparent\"\n          iconColor=${color}\n          backgroundColor=${color}\n          size=${size}\n        ></wui-icon-box>\n      `;\n        }\n        return null;\n    }\n    loadingTemplate() {\n        if (this.loading) {\n            return html `<wui-loading-spinner\n        data-testid=\"wui-list-item-loading-spinner\"\n        color=\"fg-300\"\n      ></wui-loading-spinner>`;\n        }\n        return html ``;\n    }\n    chevronTemplate() {\n        if (this.chevron) {\n            return html `<wui-icon size=\"inherit\" color=\"fg-200\" name=\"chevronRight\"></wui-icon>`;\n        }\n        return null;\n    }\n};\nWuiListItem.styles = [resetStyles, elementStyles, styles];\n__decorate([\n    property()\n], WuiListItem.prototype, \"icon\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"iconSize\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"tabIdx\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"variant\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"iconVariant\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListItem.prototype, \"disabled\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"imageSrc\", void 0);\n__decorate([\n    property()\n], WuiListItem.prototype, \"alt\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListItem.prototype, \"chevron\", void 0);\n__decorate([\n    property({ type: Boolean })\n], WuiListItem.prototype, \"loading\", void 0);\nWuiListItem = __decorate([\n    customElement('wui-list-item')\n], WuiListItem);\nexport { WuiListItem };\n//# sourceMappingURL=index.js.map", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { LitElement, html } from 'lit';\nimport { CoreHelperUtil, RouterController } from '@reown/appkit-controllers';\nimport { customElement } from '@reown/appkit-ui';\nimport '@reown/appkit-ui/wui-flex';\nimport '@reown/appkit-ui/wui-list-item';\nimport '@reown/appkit-ui/wui-text';\nlet W3mDownloadsView = class W3mDownloadsView extends LitElement {\n    constructor() {\n        super(...arguments);\n        this.wallet = RouterController.state.data?.wallet;\n    }\n    render() {\n        if (!this.wallet) {\n            throw new Error('w3m-downloads-view');\n        }\n        return html `\n      <wui-flex gap=\"xs\" flexDirection=\"column\" .padding=${['s', 's', 'l', 's']}>\n        ${this.chromeTemplate()} ${this.iosTemplate()} ${this.androidTemplate()}\n        ${this.homepageTemplate()}\n      </wui-flex>\n    `;\n    }\n    chromeTemplate() {\n        if (!this.wallet?.chrome_store) {\n            return null;\n        }\n        return html `<wui-list-item\n      variant=\"icon\"\n      icon=\"chromeStore\"\n      iconVariant=\"square\"\n      @click=${this.onChromeStore.bind(this)}\n      chevron\n    >\n      <wui-text variant=\"paragraph-500\" color=\"fg-100\">Chrome Extension</wui-text>\n    </wui-list-item>`;\n    }\n    iosTemplate() {\n        if (!this.wallet?.app_store) {\n            return null;\n        }\n        return html `<wui-list-item\n      variant=\"icon\"\n      icon=\"appStore\"\n      iconVariant=\"square\"\n      @click=${this.onAppStore.bind(this)}\n      chevron\n    >\n      <wui-text variant=\"paragraph-500\" color=\"fg-100\">iOS App</wui-text>\n    </wui-list-item>`;\n    }\n    androidTemplate() {\n        if (!this.wallet?.play_store) {\n            return null;\n        }\n        return html `<wui-list-item\n      variant=\"icon\"\n      icon=\"playStore\"\n      iconVariant=\"square\"\n      @click=${this.onPlayStore.bind(this)}\n      chevron\n    >\n      <wui-text variant=\"paragraph-500\" color=\"fg-100\">Android App</wui-text>\n    </wui-list-item>`;\n    }\n    homepageTemplate() {\n        if (!this.wallet?.homepage) {\n            return null;\n        }\n        return html `\n      <wui-list-item\n        variant=\"icon\"\n        icon=\"browser\"\n        iconVariant=\"square-blue\"\n        @click=${this.onHomePage.bind(this)}\n        chevron\n      >\n        <wui-text variant=\"paragraph-500\" color=\"fg-100\">Website</wui-text>\n      </wui-list-item>\n    `;\n    }\n    onChromeStore() {\n        if (this.wallet?.chrome_store) {\n            CoreHelperUtil.openHref(this.wallet.chrome_store, '_blank');\n        }\n    }\n    onAppStore() {\n        if (this.wallet?.app_store) {\n            CoreHelperUtil.openHref(this.wallet.app_store, '_blank');\n        }\n    }\n    onPlayStore() {\n        if (this.wallet?.play_store) {\n            CoreHelperUtil.openHref(this.wallet.play_store, '_blank');\n        }\n    }\n    onHomePage() {\n        if (this.wallet?.homepage) {\n            CoreHelperUtil.openHref(this.wallet.homepage, '_blank');\n        }\n    }\n};\nW3mDownloadsView = __decorate([\n    customElement('w3m-downloads-view')\n], W3mDownloadsView);\nexport { W3mDownloadsView };\n//# sourceMappingURL=index.js.map"], "names": ["styles$q", "css", "__decorate", "decorators", "target", "key", "desc", "c", "r", "d", "i", "WuiWalletImage", "LitElement", "borderRadius", "html", "elementStyles", "resetStyles", "styles", "property", "customElement", "styles$p", "TOTAL_IMAGES", "WuiAllWalletsImage", "isPlaceholders", "src", "walletName", "ifDefined", "styles$o", "WuiListWallet", "W3mAllWalletsWidget", "ConnectorController", "ApiController", "val", "unsubscribe", "wcConnector", "allWallets", "OptionsController", "CoreHelperUtil", "featuredCount", "rawCount", "roundedCount", "count", "tagLabel", "EventsController", "RouterController", "state", "W3mConnectAnnouncedWidget", "announcedConnectors", "connector", "ConnectorUtil", "<PERSON><PERSON><PERSON><PERSON>", "W3mConnectCustomWidget", "ConnectionController", "customWallets", "wallets", "wallet", "recent", "StorageUtil", "connectorRDNSs", "_a", "recentRDNSs", "allRDNSs", "index", "W3mConnectExternalWidget", "filteredOutCoinbaseConnectors", "Constants<PERSON><PERSON>", "W3mConnectFeaturedWidget", "W3mConnectInjectedWidget", "injectedConnectors", "W3mConnectMultiChainWidget", "multiChainConnectors", "W3mConnectRecentWidget", "filteredRecentWallets", "<PERSON><PERSON><PERSON><PERSON>", "currentNamespace", "ChainController", "chainNamespace", "W3mConnectRecommendedWidget", "connectors", "featuredWalletIds", "recentWallets", "injectedWallets", "override<PERSON>ength", "maxR<PERSON>ommended", "W3mConnectWalletConnectWidget", "AssetController", "connectorImage", "styles$n", "W3mConnectorList", "custom", "announced", "injected", "multi<PERSON>hain", "recommended", "featured", "external", "type", "styles$m", "WuiTabs", "tab", "isActive", "initialAnimation", "passiveBtn", "activeBtn", "passiveBtnText", "activeBtnText", "activeBtnBounds", "activeBtnTextBounds", "W3mConnectingHeader", "tabs", "platform", "styles$l", "SPINNER_COLOR_BY_VARIANT", "TEXT_VARIANT_BY_SIZE", "SPINNER_SIZE_BY_SIZE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textVariant", "size", "color", "styles$k", "WuiLink", "styles$j", "WuiLoading<PERSON><PERSON><PERSON><PERSON>", "radius", "radiusFactor", "dashArrayStart", "dashArrayEnd", "dashOffset", "styles$i", "WuiChipButton", "styles$h", "WuiCtaButton", "styles$g", "W3mMobileDownloadLinks", "name", "app_store", "play_store", "chrome_store", "homepage", "isMobile", "isIos", "isAndroid", "isMultiple", "shortName", "UiHelperUtil", "styles$f", "W3mConnectingWidget", "_b", "_c", "_d", "_e", "subLabel", "label", "retryButton", "borderRadiusMaster", "ThemeController", "SnackController", "W3mConnectingWcBrowser", "ModalController", "error", "W3mConnectingWcDesktop", "desktop_link", "redirect", "href", "W3mConnectingWcMobile", "mobile_link", "link_mode", "redirectUniversalLink", "e", "canPromise", "toSJISFunction", "CODEWORDS_COUNT", "utils", "version", "data", "digit", "f", "kanji", "exports", "fromString", "string", "level", "value", "defaultValue", "BitBuffer", "bufIndex", "num", "length", "bit", "bitBuffer", "BitMatrix", "row", "col", "reserved", "bitMatrix", "getSymbolSize", "require$$0", "posCount", "intervals", "positions", "coords", "pos", "pos<PERSON><PERSON><PERSON>", "j", "FINDER_PATTERN_SIZE", "finderPattern", "PenaltyScores", "mask", "points", "sameCountCol", "sameCountRow", "lastCol", "lastRow", "module", "last", "bitsCol", "bitsRow", "darkCount", "modulesCount", "getMaskAt", "maskPattern", "pattern", "setupFormatFunc", "numPatterns", "bestPattern", "lowerPenalty", "p", "penalty", "ECLevel", "EC_BLOCKS_TABLE", "EC_CODEWORDS_TABLE", "errorCorrectionCode", "errorCorrectionLevel", "EXP_TABLE", "LOG_TABLE", "x", "galois<PERSON>ield", "n", "y", "GF", "p1", "p2", "coeff", "divident", "divisor", "result", "offset", "degree", "poly", "Polynomial", "ReedSolomonEncoder", "paddedData", "remainder", "start", "buff", "reedS<PERSON><PERSON>der", "versionCheck", "numeric", "alphanumeric", "byte", "regex", "TEST_KANJI", "TEST_NUMERIC", "TEST_ALPHANUMERIC", "str", "VersionCheck", "Regex", "require$$1", "mode", "dataStr", "Utils", "ECCode", "require$$2", "Mode", "require$$3", "require$$4", "G18", "G18_BCH", "getBestVersionForDataLength", "currentVersion", "getReservedBitsCount", "getTotalBitsFromDataArray", "segments", "totalBits", "reservedBits", "getBestVersionForMixedData", "totalCodewords", "ecTotalCodewords", "dataTotalCodewordsBits", "usableBits", "seg", "ecl", "G15", "G15_MASK", "G15_BCH", "formatInfo", "NumericData", "group", "remainingNum", "numericData", "ALPHA_NUM_CHARS", "AlphanumericData", "alphanumericData", "encodeUtf8", "input", "point", "second", "ByteData", "l", "byteData", "KanjiData", "kanjiData", "<PERSON><PERSON><PERSON>", "graph", "s", "predecessors", "costs", "open", "closest", "u", "v", "cost_of_s_to_u", "adjacent_nodes", "cost_of_e", "cost_of_s_to_u_plus_cost_of_e", "cost_of_s_to_v", "first_visit", "msg", "nodes", "opts", "T", "t", "a", "b", "cost", "item", "require$$5", "require$$6", "require$$7", "getStringByteLength", "getSegments", "getSegmentsFromString", "numSegs", "alphaNumSegs", "byteSegs", "kanjiSegs", "s1", "s2", "obj", "getSegmentBitsLength", "mergeSegments", "segs", "acc", "curr", "prevSeg", "buildNodes", "buildGraph", "table", "prevNodeIds", "nodeGroup", "currentNodeIds", "node", "prevNodeId", "buildSingleSegment", "modesHint", "bestMode", "array", "path", "optimizedSegs", "AlignmentPattern", "FinderPattern", "MaskPattern", "require$$8", "Version", "require$$9", "FormatInfo", "require$$10", "require$$11", "Segments", "require$$12", "setupFinderPattern", "matrix", "setupTimingPattern", "setupAlignmentPattern", "setupVersionInfo", "bits", "mod", "setupFormatInfo", "setupData", "inc", "bitIndex", "byteIndex", "dark", "createData", "buffer", "remainingByte", "createCodewords", "dataTotalCodewords", "ecTotalBlocks", "blocksInGroup2", "blocksInGroup1", "totalCodewordsInGroup1", "dataCodewordsInGroup1", "dataCodewordsInGroup2", "ecCount", "rs", "dcData", "ecData", "maxDataSize", "dataSize", "createSymbol", "estimatedVersion", "rawSegments", "bestVersion", "dataBits", "moduleCount", "modules", "qrcode", "options", "hex2rgba", "hex", "hexCode", "hexValue", "margin", "width", "scale", "qrSize", "imgData", "qr", "symbolSize", "<PERSON><PERSON><PERSON><PERSON>", "palette", "posDst", "pxColor", "iSrc", "jSrc", "clearCanvas", "ctx", "canvas", "getCanvasElement", "qrData", "canvasEl", "image", "rendererOpts", "getColorAttrib", "attrib", "alpha", "svgCmd", "cmd", "qrTo<PERSON><PERSON>", "moveBy", "newRow", "lineLength", "svgTag", "cb", "qrcodesize", "bg", "viewBox", "QRCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Svg<PERSON><PERSON><PERSON>", "renderCanvas", "renderFunc", "text", "args", "argsNum", "isLastArgCb", "resolve", "reject", "browser", "_", "CONNECTING_ERROR_MARGIN", "CIRCLE_SIZE_MODIFIER", "QRCODE_MATRIX_MARGIN", "isAdjecentDots", "cy", "otherCy", "cellSize", "getMatrix", "arr", "QRCodeUtil", "sqrt", "rows", "QrCodeUtil", "uri", "logoSize", "dotColor", "edgeColor", "dots", "qrList", "x1", "y1", "dotSize", "svg", "clearArenaSize", "matrixMiddleStart", "matrixMiddleEnd", "circles", "cx", "circlesToConnect", "cys", "newCys", "groups", "y2", "styles$e", "DEFAULT_ICON_COLOR", "WuiQrCode", "styles$d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REOWN_URL", "styles$c", "WuiUxByReown", "styles$b", "W3mConnectingWcQrcode", "unsub", "alt", "inactive", "W3mConnectingWcUnsupported", "W3mConnectingWcWeb", "webapp_link", "W3mConnectingWcView", "retry", "wcPairingExpiry", "status", "rdns", "injectedIds", "injected_id", "browserIds", "<PERSON><PERSON><PERSON><PERSON>", "hasMobileWCLink", "isWebWc", "isBrowserInstalled", "isBrowserWc", "isDesktopWc", "container", "W3mConnectingWcBasicView", "showConnectors", "h", "o", "styles$a", "WuiSwitch", "createRef", "ref", "colorStyles", "styles$9", "WuiCertifiedSwitch", "styles$8", "WuiInputElement", "styles$7", "WuiInputText", "inputClass", "classes", "classMap", "styles$6", "WuiSearchBar", "inputComponent", "inputElement", "networkSvgMd", "styles$5", "WuiCardSelectLoader", "styles$4", "Wu<PERSON><PERSON><PERSON>", "styles$3", "W3mAllWalletsListItem", "entries", "entry", "certified", "styles$2", "PAGINATOR_ID", "W3mAllWalletsList", "gridEl", "items", "id", "columns", "currentWallets", "shimmerCount", "loaderEl", "element", "page", "styles$1", "W3mAllWalletsSearch", "search", "W3mAllWalletsView", "isSearch", "event", "WuiListItem", "iconSize", "W3mDownloadsView"], "mappings": "+aACA,MAAAA,GAAeC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAUA,IAAIG,GAAiB,cAA6BC,CAAW,CACzD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,KACZ,KAAK,KAAO,GACZ,KAAK,UAAY,GACjB,KAAK,UAAY,IACrB,CACA,QAAS,CACL,IAAIC,EAAe,MACnB,OAAI,KAAK,OAAS,KACdA,EAAe,IAEV,KAAK,OAAS,KACnBA,EAAe,KAGfA,EAAe,MAEnB,KAAK,MAAM,QAAU;AAAA,wDAC2BA,CAAY;AAAA,mDACjB,KAAK,IAAI;AAAA,KAEhD,KAAK,aACL,KAAK,QAAQ,WAAgB,KAAK,YAE/BC;AAAAA,+DACgD,KAAK,gBAAgB;AAAA,KAEhF,CACA,gBAAiB,CACb,OAAI,KAAK,SACEA,mBAAuB,KAAK,QAAQ,QAAQ,KAAK,IAAI,gBAEvD,KAAK,WACHA;AAAAA;AAAAA;AAAAA;AAAAA,eAIJ,KAAK,UAAU;AAAA,oBAGfA;AAAAA,yBACU,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,iBAK9B,CACJ,EACAH,GAAe,OAAS,CAACI,EAAeC,EAAaC,EAAM,EAC3Df,GAAW,CACPgB,EAAA,CACJ,EAAGP,GAAe,UAAW,OAAQ,MAAM,EAC3CT,GAAW,CACPgB,EAAA,CACJ,EAAGP,GAAe,UAAW,OAAQ,MAAM,EAC3CT,GAAW,CACPgB,EAAA,CACJ,EAAGP,GAAe,UAAW,WAAY,MAAM,EAC/CT,GAAW,CACPgB,EAAA,CACJ,EAAGP,GAAe,UAAW,aAAc,MAAM,EACjDT,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGP,GAAe,UAAW,YAAa,MAAM,EAChDT,GAAW,CACPgB,EAAA,CACJ,EAAGP,GAAe,UAAW,YAAa,MAAM,EAChDA,GAAiBT,GAAW,CACxBiB,EAAc,kBAAkB,CACpC,EAAGR,EAAc,ECrFjB,MAAAS,GAAenB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAUA,MAAMa,GAAe,EACrB,IAAIC,GAAqB,cAAiCV,CAAW,CACjE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,aAAe,CAAA,CACxB,CACA,QAAS,CACL,MAAMW,EAAiB,KAAK,aAAa,OAASF,GAClD,OAAOP,IAAQ,KAAK,aACf,MAAM,EAAGO,EAAY,EACrB,IAAI,CAAC,CAAE,IAAAG,EAAK,WAAAC,CAAA,IAAiBX;AAAAA;AAAAA;AAAAA,yBAGjBU,CAAG;AAAA,qBACPE,EAAUD,CAAU,CAAC;AAAA;AAAA,WAE/B,CAAC;AAAA,QACJF,EACM,CAAC,GAAG,MAAMF,GAAe,KAAK,aAAa,MAAM,CAAC,EAAE,IAAI,IAAMP,gEAAoE,EAClI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAWd,CACJ,EACAQ,GAAmB,OAAS,CAACN,EAAaC,EAAM,EAChDf,GAAW,CACPgB,EAAS,CAAE,KAAM,KAAA,CAAO,CAC5B,EAAGI,GAAmB,UAAW,eAAgB,MAAM,EACvDA,GAAqBpB,GAAW,CAC5BiB,EAAc,uBAAuB,CACzC,EAAGG,EAAkB,ECpDrB,MAAAK,GAAe1B;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAaA,IAAIoB,EAAgB,cAA4BhB,CAAW,CACvD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,aAAe,CAAA,EACpB,KAAK,SAAW,GAChB,KAAK,KAAO,GACZ,KAAK,OAAS,OACd,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,eAAiB,GACtB,KAAK,QAAU,GACf,KAAK,oBAAsB,YAC/B,CACA,QAAS,CACL,OAAOE;AAAAA,0BACW,KAAK,QAAQ,aAAaY,EAAU,KAAK,MAAM,CAAC;AAAA,UAChE,KAAK,mBAAA,CAAoB,IAAI,KAAK,qBAAqB;AAAA,4DACL,KAAK,IAAI;AAAA,UAC3D,KAAK,gBAAgB;AAAA;AAAA,KAG3B,CACA,oBAAqB,CACjB,OAAI,KAAK,gBAAkB,KAAK,SACrBZ,uCAA2C,KAAK,QAAQ,8BAE1D,KAAK,gBAAkB,KAAK,WAC1BA,mCAAuC,KAAK,UAAU,mCAE1D,IACX,CACA,qBAAsB,CAClB,MAAI,CAAC,KAAK,gBAAkB,KAAK,SACtBA;AAAAA;AAAAA,mBAEA,KAAK,QAAQ;AAAA,eACjB,KAAK,IAAI;AAAA,qBACH,KAAK,SAAS;AAAA,4BAGlB,CAAC,KAAK,gBAAkB,CAAC,KAAK,SAC5BA,qCAAyC,KAAK,IAAI,uBAEtD,IACX,CACA,gBAAiB,CACb,OAAI,KAAK,QACEA;AAAAA;AAAAA,gBAEH,KAAK,mBAAmB;AAAA,+BAGvB,KAAK,UAAY,KAAK,WACpBA,qBAAyB,KAAK,UAAU,IAAI,KAAK,QAAQ,aAE3D,KAAK,KACHA,6CAAiD,KAAK,IAAI,eAE9D,IACX,CACJ,EACAc,EAAc,OAAS,CAACZ,EAAaD,EAAeE,EAAM,EAC1Df,EAAW,CACPgB,EAAS,CAAE,KAAM,KAAA,CAAO,CAC5B,EAAGU,EAAc,UAAW,eAAgB,MAAM,EAClD1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,WAAY,MAAM,EAC9C1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,OAAQ,MAAM,EAC1C1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,WAAY,MAAM,EAC9C1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,aAAc,MAAM,EAChD1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,OAAQ,MAAM,EAC1C1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,aAAc,MAAM,EAChD1B,EAAW,CACPgB,EAAA,CACJ,EAAGU,EAAc,UAAW,SAAU,MAAM,EAC5C1B,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGU,EAAc,UAAW,YAAa,MAAM,EAC/C1B,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGU,EAAc,UAAW,WAAY,MAAM,EAC9C1B,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGU,EAAc,UAAW,iBAAkB,MAAM,EACpD1B,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGU,EAAc,UAAW,UAAW,MAAM,EAC7C1B,EAAW,CACPgB,EAAS,CAAE,KAAM,MAAA,CAAQ,CAC7B,EAAGU,EAAc,UAAW,sBAAuB,MAAM,EACzDA,EAAgB1B,EAAW,CACvBiB,EAAc,iBAAiB,CACnC,EAAGS,CAAa,ECzHhB,IAAI1B,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAIqB,GAAsB,cAAkCjB,CAAW,CACnE,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,MAAQC,EAAc,MAAM,MACjC,KAAK,cAAgBA,EAAc,MAAM,gBAAgB,OACzD,KAAK,6BAA+BA,EAAc,MAAM,6BACxD,KAAK,YAAY,KAAKD,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,EAAGD,EAAc,aAAa,QAASC,GAAQ,KAAK,MAAQA,CAAI,EAAGD,EAAc,aAAa,kBAAmBC,GAAQ,KAAK,cAAgBA,EAAI,MAAO,EAAGD,EAAc,aAAa,+BAAgCC,GAAQ,KAAK,6BAA+BA,CAAI,CAAC,CAC/W,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,MAAMC,EAAc,KAAK,WAAW,KAAK3B,GAAKA,EAAE,KAAO,eAAe,EAChE,CAAE,WAAA4B,GAAeC,EAAkB,MAIzC,GAHI,CAACF,GAAeC,IAAe,QAG/BA,IAAe,eAAiB,CAACE,EAAe,WAChD,OAAO,KAEX,MAAMC,EAAgBP,EAAc,MAAM,SAAS,OAC7CQ,EAAW,KAAK,MAAQD,EACxBE,EAAeD,EAAW,GAAKA,EAAW,KAAK,MAAMA,EAAW,EAAE,EAAI,GACtEE,EAAQ,KAAK,cAAgB,EAAI,KAAK,cAAgBD,EAC5D,IAAIE,EAAW,GAAGD,CAAK,GACvB,OAAI,KAAK,cAAgB,EACrBC,EAAW,GAAG,KAAK,aAAa,GAE3BD,EAAQF,IACbG,EAAW,GAAGD,CAAK,KAEhB3B;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,iBAKE,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,mBAC1B4B,CAAQ;AAAA;AAAA;AAAA,iBAGVhB,EAAU,KAAK,MAAM,CAAC;AAAA,mBACpB,KAAK,4BAA4B;AAAA,8BACtB,KAAK,6BAA+B,SAAW,YAAY;AAAA;AAAA,KAGrF,CACA,cAAe,CACXiB,EAAiB,UAAU,CAAE,KAAM,QAAS,MAAO,oBAAqB,EACxEC,EAAiB,KAAK,YAAY,CACtC,CACJ,EACA1C,GAAW,CACPgB,EAAA,CACJ,EAAGW,GAAoB,UAAW,SAAU,MAAM,EAClD3B,GAAW,CACP2C,EAAA,CACJ,EAAGhB,GAAoB,UAAW,aAAc,MAAM,EACtD3B,GAAW,CACP2C,EAAA,CACJ,EAAGhB,GAAoB,UAAW,QAAS,MAAM,EACjD3B,GAAW,CACP2C,EAAA,CACJ,EAAGhB,GAAoB,UAAW,gBAAiB,MAAM,EACzD3B,GAAW,CACP2C,EAAA,CACJ,EAAGhB,GAAoB,UAAW,+BAAgC,MAAM,EACxEA,GAAsB3B,GAAW,CAC7BiB,EAAc,wBAAwB,CAC1C,EAAGU,EAAmB,ECnFtB,IAAI3B,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAIsC,GAA4B,cAAwClC,CAAW,CAC/E,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,YAAY,KAAKA,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,CAAC,CACxG,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,MAAMc,EAAsB,KAAK,WAAW,OAAOC,GAAaA,EAAU,OAAS,WAAW,EAC9F,OAAKD,GAAA,MAAAA,EAAqB,OAInBjC;AAAAA;AAAAA,UAELiC,EACG,OAAOE,GAAc,aAAa,EAClC,IAAID,GAAalC;AAAAA;AAAAA,2BAEHY,EAAUwB,EAAU,kBAAkBF,CAAS,CAAC,CAAC;AAAA,uBACrDA,EAAU,MAAQ,SAAS;AAAA,yBACzB,IAAM,KAAK,YAAYA,CAAS,CAAC;AAAA;AAAA;AAAA,8BAG5B,mBAAmBA,EAAU,EAAE,EAAE;AAAA,6BAClC,EAAI;AAAA,yBACRtB,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA,aAGlC,CAAC;AAAA;AAAA,OAnBF,KAAK,MAAM,QAAU,gBACd,KAqBf,CACA,YAAYsB,EAAW,CACfA,EAAU,KAAO,gBACbX,EAAe,WACfO,EAAiB,KAAK,YAAY,EAGlCA,EAAiB,KAAK,yBAAyB,EAInDA,EAAiB,KAAK,qBAAsB,CAAE,UAAAI,CAAA,CAAW,CAEjE,CACJ,EACA9C,GAAW,CACPgB,EAAA,CACJ,EAAG4B,GAA0B,UAAW,SAAU,MAAM,EACxD5C,GAAW,CACP2C,EAAA,CACJ,EAAGC,GAA0B,UAAW,aAAc,MAAM,EAC5DA,GAA4B5C,GAAW,CACnCiB,EAAc,8BAA8B,CAChD,EAAG2B,EAAyB,ECzE5B,IAAI5C,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAI2C,GAAyB,cAAqCvC,CAAW,CACzE,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,QAAU,GACf,KAAK,YAAY,KAAKA,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,CAAC,EAChGK,EAAe,WAAA,GAAgBA,EAAe,UAC9C,KAAK,QAAU,CAACe,EAAqB,MAAM,MAC3C,KAAK,YAAY,KAAKA,EAAqB,aAAa,WAAiB,KAAK,QAAU,CAACpB,CAAI,CAAC,EAEtG,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,KAAM,CAAE,cAAAoB,GAAkBjB,EAAkB,MAC5C,GAAI,EAACiB,GAAA,MAAAA,EAAe,QAChB,YAAK,MAAM,QAAU,gBACd,KAEX,MAAMC,EAAU,KAAK,0BAA0BD,CAAa,EAC5D,OAAOvC;AAAAA,QACPwC,EAAQ,IAAIC,GAAUzC;AAAAA;AAAAA,uBAEPY,EAAUwB,EAAU,eAAeK,CAAM,CAAC,CAAC;AAAA,mBAC/CA,EAAO,MAAQ,SAAS;AAAA,qBACtB,IAAM,KAAK,gBAAgBA,CAAM,CAAC;AAAA,0BAC7B,mBAAmBA,EAAO,EAAE,EAAE;AAAA,qBACnC7B,EAAU,KAAK,MAAM,CAAC;AAAA,uBACpB,KAAK,OAAO;AAAA;AAAA;AAAA,SAG1B,CAAC;AAAA,gBAEN,CACA,0BAA0B4B,EAAS,CAC/B,MAAME,EAASC,GAAY,iBAAA,EACrBC,EAAiB,KAAK,WACvB,IAAIV,UAAa,OAAAW,EAAAX,EAAU,OAAV,YAAAW,EAAgB,KAAI,EACrC,OAAO,OAAO,EACbC,EAAcJ,EAAO,IAAID,GAAUA,EAAO,IAAI,EAAE,OAAO,OAAO,EAC9DM,EAAWH,EAAe,OAAOE,CAAW,EAClD,GAAIC,EAAS,SAAS,oBAAoB,GAAKxB,EAAe,WAAY,CACtE,MAAMyB,EAAQD,EAAS,QAAQ,oBAAoB,EACnDA,EAASC,CAAK,EAAI,aACtB,CAEA,OADiBR,EAAQ,OAAOC,GAAU,CAACM,EAAS,SAAS,OAAON,GAAA,YAAAA,EAAQ,IAAI,CAAC,CAAC,CAEtF,CACA,gBAAgBA,EAAQ,CAChB,KAAK,SAGTX,EAAiB,KAAK,0BAA2B,CAAE,OAAAW,CAAA,CAAQ,CAC/D,CACJ,EACArD,GAAW,CACPgB,EAAA,CACJ,EAAGiC,GAAuB,UAAW,SAAU,MAAM,EACrDjD,GAAW,CACP2C,EAAA,CACJ,EAAGM,GAAuB,UAAW,aAAc,MAAM,EACzDjD,GAAW,CACP2C,EAAA,CACJ,EAAGM,GAAuB,UAAW,UAAW,MAAM,EACtDA,GAAyBjD,GAAW,CAChCiB,EAAc,2BAA2B,CAC7C,EAAGgC,EAAsB,EClFzB,IAAIjD,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAUA,IAAIuD,GAA2B,cAAuCnD,CAAW,CAC7E,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,YAAY,KAAKA,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,CAAC,CACxG,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CAGL,MAAM+B,EAFqB,KAAK,WAAW,OAAOhB,GAAaA,EAAU,OAAS,UAAU,EACnC,OAAOC,GAAc,aAAa,EACvB,OAAOD,GAAaA,EAAU,KAAOiB,GAAc,aAAa,YAAY,EAChJ,OAAKD,GAAA,MAAAA,EAA+B,OAI7BlD;AAAAA;AAAAA,UAELkD,EAA8B,IAAIhB,GAAalC;AAAAA;AAAAA,yBAEhCY,EAAUwB,EAAU,kBAAkBF,CAAS,CAAC,CAAC;AAAA,2BAC/C,EAAI;AAAA,qBACVA,EAAU,MAAQ,SAAS;AAAA,4BACpB,4BAA4BA,EAAU,EAAE,EAAE;AAAA,uBAC/C,IAAM,KAAK,YAAYA,CAAS,CAAC;AAAA,uBACjCtB,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA,WAGlC,CAAC;AAAA;AAAA,OAfA,KAAK,MAAM,QAAU,gBACd,KAiBf,CACA,YAAYsB,EAAW,CACnBJ,EAAiB,KAAK,qBAAsB,CAAE,UAAAI,CAAA,CAAW,CAC7D,CACJ,EACA9C,GAAW,CACPgB,EAAA,CACJ,EAAG6C,GAAyB,UAAW,SAAU,MAAM,EACvD7D,GAAW,CACP2C,EAAA,CACJ,EAAGkB,GAAyB,UAAW,aAAc,MAAM,EAC3DA,GAA2B7D,GAAW,CAClCiB,EAAc,6BAA6B,CAC/C,EAAG4C,EAAwB,EC9D3B,IAAI7D,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAI0D,GAA2B,cAAuCtD,CAAW,CAC7E,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,OACd,KAAK,QAAU,CAAA,CACnB,CACA,QAAS,CACL,OAAK,KAAK,QAAQ,OAIXE;AAAAA;AAAAA,UAEL,KAAK,QAAQ,IAAIyC,GAAUzC;AAAAA;AAAAA,4BAET,4BAA4ByC,EAAO,EAAE,EAAE;AAAA,yBAC1C7B,EAAUwB,EAAU,eAAeK,CAAM,CAAC,CAAC;AAAA,qBAC/CA,EAAO,MAAQ,SAAS;AAAA,uBACtB,IAAM,KAAK,gBAAgBA,CAAM,CAAC;AAAA,uBAClC7B,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA,WAGlC,CAAC;AAAA;AAAA,OAdA,KAAK,MAAM,QAAU,gBACd,KAgBf,CACA,gBAAgB6B,EAAQ,CACpBzB,EAAoB,sBAAsByB,CAAM,CACpD,CACJ,EACArD,GAAW,CACPgB,EAAA,CACJ,EAAGgD,GAAyB,UAAW,SAAU,MAAM,EACvDhE,GAAW,CACPgB,EAAA,CACJ,EAAGgD,GAAyB,UAAW,UAAW,MAAM,EACxDA,GAA2BhE,GAAW,CAClCiB,EAAc,6BAA6B,CAC/C,EAAG+C,EAAwB,ECnD3B,IAAIhE,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAI2D,GAA2B,cAAuCvD,CAAW,CAC7E,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,OACd,KAAK,WAAa,CAAA,CACtB,CACA,QAAS,CACL,MAAMwD,EAAqB,KAAK,WAAW,OAAOnB,GAAc,aAAa,EAC7E,OAAImB,EAAmB,SAAW,GAC9B,KAAK,MAAM,QAAU,gBACd,MAEJtD;AAAAA;AAAAA,UAELsD,EAAmB,IAAIpB,GAAalC;AAAAA;AAAAA,yBAErBY,EAAUwB,EAAU,kBAAkBF,CAAS,CAAC,CAAC;AAAA,2BAC/C,EAAI;AAAA,qBACVA,EAAU,MAAQ,SAAS;AAAA;AAAA;AAAA,4BAGpB,mBAAmBA,EAAU,EAAE,EAAE;AAAA,uBACtC,IAAM,KAAK,YAAYA,CAAS,CAAC;AAAA,uBACjCtB,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA,WAGlC,CAAC;AAAA;AAAA,KAGR,CACA,YAAYsB,EAAW,CACnBlB,EAAoB,mBAAmBkB,CAAS,EAChDJ,EAAiB,KAAK,qBAAsB,CAAE,UAAAI,CAAA,CAAW,CAC7D,CACJ,EACA9C,GAAW,CACPgB,EAAA,CACJ,EAAGiD,GAAyB,UAAW,SAAU,MAAM,EACvDjE,GAAW,CACPgB,EAAA,CACJ,EAAGiD,GAAyB,UAAW,aAAc,MAAM,EAC3DA,GAA2BjE,GAAW,CAClCiB,EAAc,6BAA6B,CAC/C,EAAGgD,EAAwB,ECzD3B,IAAIjE,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAI6D,GAA6B,cAAyCzD,CAAW,CACjF,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,YAAY,KAAKA,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,CAAC,CACxG,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,MAAMqC,EAAuB,KAAK,WAAW,OAAOtB,GAAaA,EAAU,OAAS,eAAiBA,EAAU,OAAS,eAAe,EACvI,OAAKsB,GAAA,MAAAA,EAAsB,OAIpBxD;AAAAA;AAAAA,UAELwD,EAAqB,IAAItB,GAAalC;AAAAA;AAAAA,yBAEvBY,EAAUwB,EAAU,kBAAkBF,CAAS,CAAC,CAAC;AAAA,2BAC/C,EAAI;AAAA,qBACVA,EAAU,MAAQ,SAAS;AAAA;AAAA;AAAA,4BAGpB,mBAAmBA,EAAU,EAAE,EAAE;AAAA,uBACtC,IAAM,KAAK,YAAYA,CAAS,CAAC;AAAA,uBACjCtB,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA,WAGlC,CAAC;AAAA;AAAA,OAjBA,KAAK,MAAM,QAAU,gBACd,KAmBf,CACA,YAAYsB,EAAW,CACnBlB,EAAoB,mBAAmBkB,CAAS,EAChDJ,EAAiB,KAAK,sBAAsB,CAChD,CACJ,EACA1C,GAAW,CACPgB,EAAA,CACJ,EAAGmD,GAA2B,UAAW,SAAU,MAAM,EACzDnE,GAAW,CACP2C,EAAA,CACJ,EAAGwB,GAA2B,UAAW,aAAc,MAAM,EAC7DA,GAA6BnE,GAAW,CACpCiB,EAAc,gCAAgC,CAClD,EAAGkD,EAA0B,EC7D7B,IAAInE,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAI+D,GAAyB,cAAqC3D,CAAW,CACzE,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,QAAU,GACf,KAAK,YAAY,KAAKA,EAAoB,aAAa,aAAcE,GAAQ,KAAK,WAAaA,CAAI,CAAC,EAChGK,EAAe,WAAA,GAAgBA,EAAe,UAC9C,KAAK,QAAU,CAACe,EAAqB,MAAM,MAC3C,KAAK,YAAY,KAAKA,EAAqB,aAAa,WAAiB,KAAK,QAAU,CAACpB,CAAI,CAAC,EAEtG,CACA,QAAS,CAEL,MAAMwC,EADgBf,GAAY,iBAAA,EAE7B,OAAOF,GAAU,CAACkB,GAAW,WAAWlB,CAAM,CAAC,EAC/C,UAAiB,CAAC,KAAK,mBAAmBA,CAAM,CAAC,EACjD,OAAOA,GAAU,KAAK,mCAAmCA,CAAM,CAAC,EACrE,OAAKiB,EAAsB,OAIpB1D;AAAAA;AAAAA,UAEL0D,EAAsB,IAAIjB,GAAUzC;AAAAA;AAAAA,yBAErBY,EAAUwB,EAAU,eAAeK,CAAM,CAAC,CAAC;AAAA,qBAC/CA,EAAO,MAAQ,SAAS;AAAA,uBACtB,IAAM,KAAK,gBAAgBA,CAAM,CAAC;AAAA;AAAA;AAAA,uBAGlC7B,EAAU,KAAK,MAAM,CAAC;AAAA,yBACpB,KAAK,OAAO;AAAA;AAAA;AAAA,WAG1B,CAAC;AAAA;AAAA,OAhBA,KAAK,MAAM,QAAU,gBACd,KAkBf,CACA,gBAAgB6B,EAAQ,CAChB,KAAK,SAGTzB,EAAoB,sBAAsByB,CAAM,CACpD,CACA,mBAAmBA,EAAQ,CACvB,OAAO,KAAK,WAAW,KAAKP,GAAaA,EAAU,KAAOO,EAAO,IAAMP,EAAU,OAASO,EAAO,IAAI,CACzG,CACA,mCAAmCA,EAAQ,CACvC,MAAMmB,EAAmBC,GAAgB,MAAM,YAC/C,OAAID,GAAoBnB,EAAO,OACpBA,EAAO,OAAO,KAAKhD,GAAK,CAC3B,MAAMqE,EAAiBrE,EAAE,MAAM,GAAG,EAAE,CAAC,EACrC,OAAOmE,IAAqBE,CAChC,CAAC,EAEE,EACX,CACJ,EACA1E,GAAW,CACPgB,EAAA,CACJ,EAAGqD,GAAuB,UAAW,SAAU,MAAM,EACrDrE,GAAW,CACP2C,EAAA,CACJ,EAAG0B,GAAuB,UAAW,aAAc,MAAM,EACzDrE,GAAW,CACP2C,EAAA,CACJ,EAAG0B,GAAuB,UAAW,UAAW,MAAM,EACtDA,GAAyBrE,GAAW,CAChCiB,EAAc,2BAA2B,CAC7C,EAAGoD,EAAsB,ECrFzB,IAAIrE,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAIqE,GAA8B,cAA0CjE,CAAW,CACnF,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,QAAU,CAAA,EACf,KAAK,QAAU,GACXyB,EAAe,WAAA,GAAgBA,EAAe,UAC9C,KAAK,QAAU,CAACe,EAAqB,MAAM,MAC3C,KAAK,YAAY,KAAKA,EAAqB,aAAa,WAAiB,KAAK,QAAU,CAACpB,CAAI,CAAC,EAEtG,CACA,QAAS,CACL,KAAM,CAAE,WAAA8C,GAAehD,EAAoB,MACrC,CAAE,cAAAuB,EAAe,kBAAA0B,CAAA,EAAsB3C,EAAkB,MACzD4C,EAAgBvB,GAAY,iBAAA,EAC5BvB,EAAc4C,EAAW,KAAKvE,GAAKA,EAAE,KAAO,eAAe,EAE3D0E,EADqBH,EAAW,OAAOvE,GAAKA,EAAE,OAAS,YAAcA,EAAE,OAAS,aAAeA,EAAE,OAAS,aAAa,EAClF,OAAOG,GAAKA,EAAE,OAAS,gBAAgB,EAClF,GAAI,CAACwB,EACD,OAAO,KAEX,GAAI6C,GAAqB1B,GAAiB,CAAC,KAAK,QAAQ,OACpD,YAAK,MAAM,QAAU,gBACd,KAEX,MAAM6B,EAAiBD,EAAgB,OAASD,EAAc,OACxDG,EAAiB,KAAK,IAAI,EAAG,EAAID,CAAc,EAC/C5B,EAAUmB,GAAW,0BAA0B,KAAK,OAAO,EAAE,MAAM,EAAGU,CAAc,EAC1F,OAAK7B,EAAQ,OAINxC;AAAAA;AAAAA,UAELwC,EAAQ,IAAIC,GAAUzC;AAAAA;AAAAA,yBAEPY,EAAUwB,EAAU,eAAeK,CAAM,CAAC,CAAC;AAAA,sBAC/CA,GAAA,YAAAA,EAAQ,OAAQ,SAAS;AAAA,uBACvB,IAAM,KAAK,gBAAgBA,CAAM,CAAC;AAAA,uBAClC7B,EAAU,KAAK,MAAM,CAAC;AAAA,yBACpB,KAAK,OAAO;AAAA;AAAA;AAAA,WAG1B,CAAC;AAAA;AAAA,OAdA,KAAK,MAAM,QAAU,gBACd,KAgBf,CACA,gBAAgB6B,EAAQ,CACpB,GAAI,KAAK,QACL,OAEJ,MAAMP,EAAYlB,EAAoB,aAAayB,EAAO,GAAIA,EAAO,IAAI,EACrEP,EACAJ,EAAiB,KAAK,qBAAsB,CAAE,UAAAI,CAAA,CAAW,EAGzDJ,EAAiB,KAAK,0BAA2B,CAAE,OAAAW,CAAA,CAAQ,CAEnE,CACJ,EACArD,GAAW,CACPgB,EAAA,CACJ,EAAG2D,GAA4B,UAAW,SAAU,MAAM,EAC1D3E,GAAW,CACPgB,EAAA,CACJ,EAAG2D,GAA4B,UAAW,UAAW,MAAM,EAC3D3E,GAAW,CACP2C,EAAA,CACJ,EAAGgC,GAA4B,UAAW,UAAW,MAAM,EAC3DA,GAA8B3E,GAAW,CACrCiB,EAAc,gCAAgC,CAClD,EAAG0D,EAA2B,ECtF9B,IAAI3E,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAI4E,GAAgC,cAA4CxE,CAAW,CACvF,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,gBAAkBuD,GAAgB,MAAM,gBAC7C,KAAK,YAAY,KAAKvD,EAAoB,aAAa,gBAAsB,KAAK,WAAaE,CAAI,EAAGqD,GAAgB,aAAa,qBAA2B,KAAK,gBAAkBrD,CAAI,CAAC,CAC9L,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,GAAII,EAAe,WACf,YAAK,MAAM,QAAU,gBACd,KAEX,MAAMW,EAAY,KAAK,WAAW,KAAKzC,GAAKA,EAAE,KAAO,eAAe,EACpE,GAAI,CAACyC,EACD,YAAK,MAAM,QAAU,gBACd,KAEX,MAAMsC,EAAiBtC,EAAU,UAAY,KAAK,iBAAgBA,GAAA,YAAAA,EAAW,UAAW,EAAE,EAC1F,OAAOlC;AAAAA;AAAAA,mBAEIY,EAAU4D,CAAc,CAAC;AAAA,eAC7BtC,EAAU,MAAQ,SAAS;AAAA,iBACzB,IAAM,KAAK,YAAYA,CAAS,CAAC;AAAA;AAAA;AAAA,iBAGjCtB,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,KAKnC,CACA,YAAYsB,EAAW,CACnBlB,EAAoB,mBAAmBkB,CAAS,EAChDJ,EAAiB,KAAK,yBAAyB,CACnD,CACJ,EACA1C,GAAW,CACPgB,EAAA,CACJ,EAAGkE,GAA8B,UAAW,SAAU,MAAM,EAC5DlF,GAAW,CACP2C,EAAA,CACJ,EAAGuC,GAA8B,UAAW,aAAc,MAAM,EAChElF,GAAW,CACP2C,EAAA,CACJ,EAAGuC,GAA8B,UAAW,kBAAmB,MAAM,EACrEA,GAAgClF,GAAW,CACvCiB,EAAc,kCAAkC,CACpD,EAAGiE,EAA6B,EC/DhC,MAAAG,GAAetF;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAkBA,IAAIgF,GAAmB,cAA+B5E,CAAW,CAC7D,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,OAAS,OACd,KAAK,WAAakB,EAAoB,MAAM,WAC5C,KAAK,YAAcC,EAAc,MAAM,YACvC,KAAK,SAAWA,EAAc,MAAM,SACpC,KAAK,YAAY,KAAKD,EAAoB,aAAa,gBAAsB,KAAK,WAAaE,CAAI,EAAGD,EAAc,aAAa,cAAeC,GAAQ,KAAK,YAAcA,CAAI,EAAGD,EAAc,aAAa,WAAYC,GAAQ,KAAK,SAAWA,CAAI,CAAC,CAC1P,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,OAAOnB;AAAAA,mDACoC,KAAK,uBAAuB;AAAA,KAE3E,CACA,uBAAwB,CACpB,KAAM,CAAE,OAAA2E,EAAQ,OAAAjC,EAAQ,UAAAkC,EAAW,SAAAC,EAAU,WAAAC,EAAY,YAAAC,EAAa,SAAAC,EAAU,SAAAC,CAAA,EAAa9C,GAAc,oBAAoB,KAAK,WAAY,KAAK,YAAa,KAAK,QAAQ,EAW/K,OAV2BA,GAAc,sBAAsB,CAC3D,OAAAwC,EACA,OAAAjC,EACA,UAAAkC,EACA,SAAAC,EACA,WAAAC,EACA,YAAAC,EACA,SAAAC,EACA,SAAAC,CAAA,CACH,EACyB,IAAIC,GAAQ,CAClC,OAAQA,EAAA,CACJ,IAAK,WACD,OAAOlF;AAAAA,cACb8E,EAAW,OACC9E;AAAAA,2BACCY,EAAU,KAAK,MAAM,CAAC;AAAA,oDAEvB,IAAI;AAAA,cAChBgE,EAAU,OACE5E;AAAAA,2BACCY,EAAU,KAAK,MAAM,CAAC;AAAA,kDAEvB,IAAI;AAAA,cAChBiE,EAAS,OACG7E;AAAAA,gCACM6E,CAAQ;AAAA,2BACbjE,EAAU,KAAK,MAAM,CAAC;AAAA,iDAEvB,IAAI;AAAA,YAEd,IAAK,gBACD,OAAOZ;AAAAA,qBACNY,EAAU,KAAK,MAAM,CAAC;AAAA,gDAE3B,IAAK,SACD,OAAOZ;AAAAA,qBACNY,EAAU,KAAK,MAAM,CAAC;AAAA,yCAE3B,IAAK,WACD,OAAOZ;AAAAA,uBACJgF,CAAQ;AAAA,qBACVpE,EAAU,KAAK,MAAM,CAAC;AAAA,2CAE3B,IAAK,SACD,OAAOZ;AAAAA,qBACNY,EAAU,KAAK,MAAM,CAAC;AAAA,yCAE3B,IAAK,WACD,OAAOZ;AAAAA,qBACNY,EAAU,KAAK,MAAM,CAAC;AAAA,2CAE3B,IAAK,cACD,OAAOZ;AAAAA,uBACJ+E,CAAW;AAAA,qBACbnE,EAAU,KAAK,MAAM,CAAC;AAAA,8CAE3B,QACI,eAAQ,KAAK,2BAA2BsE,CAAI,EAAE,EACvC,IAAA,CAEnB,CAAC,CACL,CACJ,EACAR,GAAiB,OAASvE,GAC1Bf,GAAW,CACPgB,EAAA,CACJ,EAAGsE,GAAiB,UAAW,SAAU,MAAM,EAC/CtF,GAAW,CACP2C,EAAA,CACJ,EAAG2C,GAAiB,UAAW,aAAc,MAAM,EACnDtF,GAAW,CACP2C,EAAA,CACJ,EAAG2C,GAAiB,UAAW,cAAe,MAAM,EACpDtF,GAAW,CACP2C,EAAA,CACJ,EAAG2C,GAAiB,UAAW,WAAY,MAAM,EACjDA,GAAmBtF,GAAW,CAC1BiB,EAAc,oBAAoB,CACtC,EAAGqE,EAAgB,ECzHnB,MAAAS,GAAehG;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAI0F,GAAU,cAAsBtF,CAAW,CAC3C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,CAAA,EACZ,KAAK,YAAc,IAAM,KACzB,KAAK,QAAU,CAAA,EACf,KAAK,SAAW,GAChB,KAAK,cAAgB,QACrB,KAAK,UAAY,EACjB,KAAK,QAAU,EACnB,CACA,QAAS,CACL,YAAK,QAAU,KAAK,KAAK,OAAS,EAClC,KAAK,MAAM,QAAU;AAAA,qBACR,KAAK,SAAS;AAAA,2BACR,KAAK,aAAa;AAAA,MAErC,KAAK,QAAQ,KAAU,KAAK,QAAU,OAAS,QACxC,KAAK,KAAK,IAAI,CAACuF,EAAKrC,IAAU,OACjC,MAAMsC,EAAWtC,IAAU,KAAK,UAChC,OAAOhD;AAAAA;AAAAA,sBAEG,KAAK,QAAQ;AAAA,mBAChB,IAAM,KAAK,WAAWgD,CAAK,CAAC;AAAA,wBACvBsC,CAAQ;AAAA,8BACHzC,EAAAwC,EAAI,QAAJ,YAAAxC,EAAW,aAAa;AAAA;AAAA,YAEzC,KAAK,aAAawC,CAAG,CAAC;AAAA,2DACyBA,EAAI,KAAK;AAAA;AAAA,OAG5D,CAAC,CACL,CACA,cAAe,CACP,KAAK,YAAc,KAAK,UACxB,KAAK,QAAU,CAAC,GAAG,KAAK,WAAW,iBAAiB,QAAQ,CAAC,EAC7D,WAAW,IAAM,CACb,KAAK,YAAY,EAAG,EAAI,CAC5B,EAAG,CAAC,EAEZ,CACA,aAAaA,EAAK,CACd,OAAIA,EAAI,KACGrF,6CAAiDqF,EAAI,IAAI,eAE7D,IACX,CACA,WAAWrC,EAAO,CACV,KAAK,SACL,KAAK,YAAYA,EAAO,EAAK,EAEjC,KAAK,UAAYA,EACjB,KAAK,YAAYA,CAAK,CAC1B,CACA,YAAYA,EAAOuC,EAAkB,CACjC,MAAMC,EAAa,KAAK,QAAQ,KAAK,SAAS,EACxCC,EAAY,KAAK,QAAQzC,CAAK,EAC9B0C,EAAiBF,GAAA,YAAAA,EAAY,cAAc,YAC3CG,EAAgBF,GAAA,YAAAA,EAAW,cAAc,YACzCG,EAAkBH,GAAA,YAAAA,EAAW,wBAC7BI,EAAsBF,GAAA,YAAAA,EAAe,wBACvCH,GAAcE,GAAkB,CAACH,GAAoBvC,IAAU,KAAK,YACpE0C,EAAe,QAAQ,CAAC,CAAE,QAAS,CAAA,CAAG,EAAG,CACrC,SAAU,GACV,OAAQ,OACR,KAAM,UAAA,CACT,EACDF,EAAW,QAAQ,CAAC,CAAE,MAAO,MAAA,CAAQ,EAAG,CACpC,SAAU,IACV,OAAQ,OACR,KAAM,UAAA,CACT,GAEDC,GAAaG,GAAmBC,GAAuBF,IACnD3C,IAAU,KAAK,WAAauC,KAC5B,KAAK,cAAgB,GAAG,KAAK,MAAMK,EAAgB,MAAQC,EAAoB,KAAK,EAAI,CAAC,KACzFJ,EAAU,QAAQ,CAAC,CAAE,MAAO,GAAGG,EAAgB,MAAQC,EAAoB,KAAK,IAAA,CAAM,EAAG,CACrF,SAAUN,EAAmB,EAAI,IACjC,KAAM,WACN,OAAQ,MAAA,CACX,EACDI,EAAc,QAAQ,CAAC,CAAE,QAAS,CAAA,CAAG,EAAG,CACpC,SAAUJ,EAAmB,EAAI,IACjC,MAAOA,EAAmB,EAAI,IAC9B,KAAM,WACN,OAAQ,MAAA,CACX,EAGb,CACJ,EACAH,GAAQ,OAAS,CAAClF,EAAaD,EAAeE,EAAM,EACpDf,GAAW,CACPgB,EAAS,CAAE,KAAM,KAAA,CAAO,CAC5B,EAAGgF,GAAQ,UAAW,OAAQ,MAAM,EACpChG,GAAW,CACPgB,EAAA,CACJ,EAAGgF,GAAQ,UAAW,cAAe,MAAM,EAC3ChG,GAAW,CACPgB,EAAS,CAAE,KAAM,KAAA,CAAO,CAC5B,EAAGgF,GAAQ,UAAW,UAAW,MAAM,EACvChG,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGgF,GAAQ,UAAW,WAAY,MAAM,EACxChG,GAAW,CACPgB,EAAA,CACJ,EAAGgF,GAAQ,UAAW,gBAAiB,MAAM,EAC7ChG,GAAW,CACP2C,EAAA,CACJ,EAAGqD,GAAQ,UAAW,YAAa,MAAM,EACzChG,GAAW,CACP2C,EAAA,CACJ,EAAGqD,GAAQ,UAAW,UAAW,MAAM,EACvCA,GAAUhG,GAAW,CACjBiB,EAAc,UAAU,CAC5B,EAAG+E,EAAO,EChIV,IAAIhG,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAMA,IAAIoG,GAAsB,cAAkChG,CAAW,CACnE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,aAAe,CAAA,EACpB,KAAK,YAAc,CAAA,EACnB,KAAK,UAAY,CAAA,EACjB,KAAK,iBAAmB,MAC5B,CACA,oBAAqB,CACjB,KAAK,YAAY,QAAQqB,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,MAAM4E,EAAO,KAAK,aAAA,EAClB,OAAO/F;AAAAA,mDACoC,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA,0BAC7C+F,CAAI,iBAAiB,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA;AAAA,KAGtE,CACA,cAAe,CACX,MAAMA,EAAO,KAAK,UAAU,IAAIC,GACxBA,IAAa,UACN,CAAE,MAAO,UAAW,KAAM,YAAa,SAAU,SAAA,EAEnDA,IAAa,SACX,CAAE,MAAO,SAAU,KAAM,SAAU,SAAU,QAAA,EAE/CA,IAAa,SACX,CAAE,MAAO,SAAU,KAAM,SAAU,SAAU,QAAA,EAE/CA,IAAa,MACX,CAAE,MAAO,SAAU,KAAM,UAAW,SAAU,KAAA,EAEhDA,IAAa,UACX,CAAE,MAAO,UAAW,KAAM,UAAW,SAAU,SAAA,EAEnD,CAAE,MAAO,UAAW,KAAM,YAAa,SAAU,aAAA,CAC3D,EACD,YAAK,aAAeD,EAAK,IAAI,CAAC,CAAE,SAAAC,CAAA,IAAeA,CAAQ,EAChDD,CACX,CACA,YAAY/C,EAAO,OACf,MAAMqC,EAAM,KAAK,aAAarC,CAAK,EAC/BqC,KACAxC,EAAA,KAAK,mBAAL,MAAAA,EAAA,UAAwBwC,GAEhC,CACJ,EACAjG,GAAW,CACPgB,EAAS,CAAE,KAAM,KAAA,CAAO,CAC5B,EAAG0F,GAAoB,UAAW,YAAa,MAAM,EACrD1G,GAAW,CACPgB,EAAA,CACJ,EAAG0F,GAAoB,UAAW,mBAAoB,MAAM,EAC5DA,GAAsB1G,GAAW,CAC7BiB,EAAc,uBAAuB,CACzC,EAAGyF,EAAmB,EClEtB,MAAAG,GAAe9G;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,MAAMwG,GAA2B,CAC7B,KAAM,cACN,QAAS,cACT,OAAQ,aACR,eAAgB,YAChB,iBAAkB,cAClB,QAAS,SACT,SAAU,gBACd,EACMC,GAAuB,CACzB,GAAI,gBACJ,GAAI,WACR,EACMC,GAAuB,CACzB,GAAI,KACJ,GAAI,IACR,EACA,IAAIC,EAAY,cAAwBvG,CAAW,CAC/C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,KACZ,KAAK,SAAW,GAChB,KAAK,UAAY,GACjB,KAAK,QAAU,GACf,KAAK,QAAU,OACf,KAAK,YAAc,GACnB,KAAK,aAAe,GACpB,KAAK,aAAe,GACxB,CACA,QAAS,CACL,KAAK,MAAM,QAAU;AAAA,qBACR,KAAK,UAAY,OAAS,MAAM;AAAA,2BAC1B,KAAK,QAAU,EAAI,CAAC;AAAA,2BACpB,KAAK,QAAU,EAAI,CAAC;AAAA,qDACM,KAAK,YAAY;AAAA,MAE9D,MAAMwG,EAAc,KAAK,aAAeH,GAAqB,KAAK,IAAI,EACtE,OAAOnG;AAAAA;AAAAA,uBAEQ,KAAK,OAAO;AAAA,yBACV,KAAK,WAAW;AAAA,0BACf,KAAK,YAAY;AAAA,oBACvB,KAAK,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA;AAAA,UAEvB,KAAK,iBAAiB;AAAA,4CACY,IAAM,KAAK,sBAAsB;AAAA,4BACjDsG,CAAW;AAAA;AAAA;AAAA,6CAGM,IAAM,KAAK,uBAAuB;AAAA;AAAA,KAG3E,CACA,sBAAuB,CACnB,KAAK,YAAc,EACvB,CACA,uBAAwB,CACpB,KAAK,aAAe,EACxB,CACA,iBAAkB,CACd,GAAI,KAAK,QAAS,CACd,MAAMC,EAAOH,GAAqB,KAAK,IAAI,EACrCI,EAAQ,KAAK,SACbN,GAAyB,SACzBA,GAAyB,KAAK,OAAO,EAC3C,OAAOlG,+BAAmCwG,CAAK,SAASD,CAAI,yBAChE,CACA,OAAOvG,GACX,CACJ,EACAqG,EAAU,OAAS,CAACnG,EAAaD,EAAeE,EAAM,EACtDf,GAAW,CACPgB,EAAA,CACJ,EAAGiG,EAAU,UAAW,OAAQ,MAAM,EACtCjH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiG,EAAU,UAAW,WAAY,MAAM,EAC1CjH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiG,EAAU,UAAW,YAAa,MAAM,EAC3CjH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiG,EAAU,UAAW,UAAW,MAAM,EACzCjH,GAAW,CACPgB,EAAA,CACJ,EAAGiG,EAAU,UAAW,UAAW,MAAM,EACzCjH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiG,EAAU,UAAW,cAAe,MAAM,EAC7CjH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiG,EAAU,UAAW,eAAgB,MAAM,EAC9CjH,GAAW,CACPgB,EAAA,CACJ,EAAGiG,EAAU,UAAW,eAAgB,MAAM,EAC9CjH,GAAW,CACPgB,EAAA,CACJ,EAAGiG,EAAU,UAAW,cAAe,MAAM,EAC7CA,EAAYjH,GAAW,CACnBiB,EAAc,YAAY,CAC9B,EAAGgG,CAAS,ECjHZ,MAAAI,GAAetH;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAIgH,GAAU,cAAsB5G,CAAW,CAC3C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,OACd,KAAK,SAAW,GAChB,KAAK,MAAQ,SACjB,CACA,QAAS,CACL,OAAOE;AAAAA,0BACW,KAAK,QAAQ,aAAaY,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA,8CAE5B,KAAK,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,KAMpD,CACJ,EACA8F,GAAQ,OAAS,CAACxG,EAAaD,EAAeE,EAAM,EACpDf,GAAW,CACPgB,EAAA,CACJ,EAAGsG,GAAQ,UAAW,SAAU,MAAM,EACtCtH,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGsG,GAAQ,UAAW,WAAY,MAAM,EACxCtH,GAAW,CACPgB,EAAA,CACJ,EAAGsG,GAAQ,UAAW,QAAS,MAAM,EACrCA,GAAUtH,GAAW,CACjBiB,EAAc,UAAU,CAC5B,EAAGqG,EAAO,EC3CV,MAAAC,GAAexH;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAMA,IAAIkH,GAAsB,cAAkC9G,CAAW,CACnE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,EAClB,CACA,QAAS,CACL,OAAO,KAAK,kBAAA,CAChB,CACA,mBAAoB,CAChB,MAAM+G,EAAS,KAAK,OAAS,GAAK,GAAK,KAAK,OAEtCC,EADgB,GACeD,EAC/BE,EAAiB,IAAMD,EACvBE,EAAe,IAAMF,EACrBG,EAAa,IAAMH,EAAe,KACxC,OAAO9G;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,eAOA6G,CAAM;AAAA,8BACSE,CAAc,IAAIC,CAAY;AAAA,8BAC9BC,CAAU;AAAA;AAAA;AAAA,KAIpC,CACJ,EACAL,GAAoB,OAAS,CAAC1G,EAAaC,EAAM,EACjDf,GAAW,CACPgB,EAAS,CAAE,KAAM,MAAA,CAAQ,CAC7B,EAAGwG,GAAoB,UAAW,SAAU,MAAM,EAClDA,GAAsBxH,GAAW,CAC7BiB,EAAc,uBAAuB,CACzC,EAAGuG,EAAmB,EC9CtB,MAAAM,GAAe/H;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAIyH,GAAgB,cAA4BrH,CAAW,CACvD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,QAAU,SACf,KAAK,SAAW,GAChB,KAAK,SAAW,GAChB,KAAK,KAAO,eACZ,KAAK,KAAO,KACZ,KAAK,KAAO,EAChB,CACA,QAAS,CACL,MAAMwG,EAAc,KAAK,OAAS,KAAO,YAAc,gBACvD,OAAOtG;AAAAA;AAAAA,gBAEC,KAAK,SAAW,WAAa,EAAE;AAAA,uBACxB,KAAK,OAAO;AAAA,oBACf,KAAK,IAAI;AAAA;AAAA,UAEnB,KAAK,SAAWA,mBAAuB,KAAK,QAAQ,gBAAkB,IAAI;AAAA,4BACxDsG,CAAW,qBAAqB,KAAK,IAAI;AAAA,yBAC5C,KAAK,IAAI;AAAA;AAAA,KAG9B,CACJ,EACAa,GAAc,OAAS,CAACjH,EAAaD,EAAeE,EAAM,EAC1Df,GAAW,CACPgB,EAAA,CACJ,EAAG+G,GAAc,UAAW,UAAW,MAAM,EAC7C/H,GAAW,CACPgB,EAAA,CACJ,EAAG+G,GAAc,UAAW,WAAY,MAAM,EAC9C/H,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAG+G,GAAc,UAAW,WAAY,MAAM,EAC9C/H,GAAW,CACPgB,EAAA,CACJ,EAAG+G,GAAc,UAAW,OAAQ,MAAM,EAC1C/H,GAAW,CACPgB,EAAA,CACJ,EAAG+G,GAAc,UAAW,OAAQ,MAAM,EAC1C/H,GAAW,CACPgB,EAAA,CACJ,EAAG+G,GAAc,UAAW,OAAQ,MAAM,EAC1CA,GAAgB/H,GAAW,CACvBiB,EAAc,iBAAiB,CACnC,EAAG8G,EAAa,EC3DhB,MAAAC,GAAejI;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAI2H,GAAe,cAA2BvH,CAAW,CACrD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,SAAW,GAChB,KAAK,MAAQ,GACb,KAAK,YAAc,EACvB,CACA,QAAS,CACL,OAAOE;AAAAA;AAAAA;AAAAA;AAAAA,mBAII,CAAC,MAAO,KAAM,MAAO,IAAI,CAAC;AAAA;AAAA,2DAEc,KAAK,KAAK;AAAA,0DACX,KAAK,WAAW;AAAA;AAAA;AAAA,KAItE,CACJ,EACAqH,GAAa,OAAS,CAACnH,EAAaD,EAAeE,EAAM,EACzDf,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGiH,GAAa,UAAW,WAAY,MAAM,EAC7CjI,GAAW,CACPgB,EAAA,CACJ,EAAGiH,GAAa,UAAW,QAAS,MAAM,EAC1CjI,GAAW,CACPgB,EAAA,CACJ,EAAGiH,GAAa,UAAW,cAAe,MAAM,EAChDA,GAAejI,GAAW,CACtBiB,EAAc,gBAAgB,CAClC,EAAGgH,EAAY,EC9Cf,MAAAC,GAAenI;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAI6H,GAAyB,cAAqCzH,CAAW,CACzE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,MAClB,CACA,QAAS,CACL,GAAI,CAAC,KAAK,OACN,YAAK,MAAM,QAAU,OACd,KAEX,KAAM,CAAE,KAAA0H,EAAM,UAAAC,EAAW,WAAAC,EAAY,aAAAC,EAAc,SAAAC,CAAA,EAAa,KAAK,OAC/DC,EAAWtG,EAAe,SAAA,EAC1BuG,EAAQvG,EAAe,MAAA,EACvBwG,EAAYxG,EAAe,UAAA,EAC3ByG,EAAa,CAACP,EAAWC,EAAYE,EAAUD,CAAY,EAAE,OAAO,OAAO,EAAE,OAAS,EACtFM,EAAYC,GAAa,kBAAkB,CAC7C,OAAQV,EACR,WAAY,GACZ,SAAU,EACV,SAAU,KAAA,CACb,EACD,OAAIQ,GAAc,CAACH,EACR7H;AAAAA;AAAAA,kBAED,cAAciI,CAAS,GAAG;AAAA;AAAA,mBAEzB,IAAMnG,EAAiB,KAAK,YAAa,CAAE,OAAQ,KAAK,OAAQ,CAAC;AAAA;AAAA,QAIxE,CAACkG,GAAcJ,EACR5H;AAAAA;AAAAA,kBAED,cAAciI,CAAS,GAAG;AAAA;AAAA,mBAEzB,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,QAIjCR,GAAaK,EACN9H;AAAAA;AAAAA,kBAED,cAAciI,CAAS,GAAG;AAAA;AAAA,mBAEzB,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,QAIjCP,GAAcK,EACP/H;AAAAA;AAAAA,kBAED,cAAciI,CAAS,GAAG;AAAA;AAAA,mBAEzB,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA;AAAA,SAItC,KAAK,MAAM,QAAU,OACd,KACX,CACA,YAAa,QACLpF,EAAA,KAAK,SAAL,MAAAA,EAAa,WACbtB,EAAe,SAAS,KAAK,OAAO,UAAW,QAAQ,CAE/D,CACA,aAAc,QACNsB,EAAA,KAAK,SAAL,MAAAA,EAAa,YACbtB,EAAe,SAAS,KAAK,OAAO,WAAY,QAAQ,CAEhE,CACA,YAAa,QACLsB,EAAA,KAAK,SAAL,MAAAA,EAAa,UACbtB,EAAe,SAAS,KAAK,OAAO,SAAU,QAAQ,CAE9D,CACJ,EACAgG,GAAuB,OAAS,CAACpH,EAAM,EACvCf,GAAW,CACPgB,EAAS,CAAE,KAAM,MAAA,CAAQ,CAC7B,EAAGmH,GAAuB,UAAW,SAAU,MAAM,EACrDA,GAAyBnI,GAAW,CAChCiB,EAAc,2BAA2B,CAC7C,EAAGkH,EAAsB,EC7FzB,MAAAY,GAAehJ;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAeO,MAAM0I,UAA4BtI,CAAW,CAChD,aAAc,eACV,MAAA,EACA,KAAK,QAAS+C,EAAAf,EAAiB,MAAM,OAAvB,YAAAe,EAA6B,OAC3C,KAAK,WAAYwF,EAAAvG,EAAiB,MAAM,OAAvB,YAAAuG,EAA6B,UAC9C,KAAK,QAAU,OACf,KAAK,iBAAmB,UACxB,KAAK,UAAY,OACjB,KAAK,SAAW,OAChB,KAAK,cAAgB,OACrB,KAAK,gBAAkB,GACvB,KAAK,YAAc,CAAA,EACnB,KAAK,SAAWjG,EAAU,eAAe,KAAK,MAAM,GAAKA,EAAU,kBAAkB,KAAK,SAAS,EACnG,KAAK,OAAOkG,EAAA,KAAK,SAAL,YAAAA,EAAa,SAAQC,EAAA,KAAK,YAAL,YAAAA,EAAgB,OAAQ,SACzD,KAAK,WAAa,GAClB,KAAK,IAAMjG,EAAqB,MAAM,MACtC,KAAK,MAAQA,EAAqB,MAAM,QACxC,KAAK,MAAQ,GACb,KAAK,UAAY,GACjB,KAAK,kBAAoB,YACzB,KAAK,eAAiB,0CACtB,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,QAAU,OACf,KAAK,YAAY,KACbA,EAAqB,aAAa,QAASpB,GAAO,OAC9C,KAAK,IAAMA,EACP,KAAK,YAAc,KAAK,UACxB,KAAK,WAAa,IAClB2B,EAAA,KAAK,YAAL,MAAAA,EAAA,WAER,CAAC,EACDP,EAAqB,aAAa,UAAWpB,GAAQ,KAAK,MAAQA,CAAI,CACzE,GACIK,EAAe,WAAA,GAAgBA,EAAe,SAAA,IAC/CA,EAAe,MAAA,GACfe,EAAqB,MAAM,SAC3BkG,EAAA,KAAK,YAAL,MAAAA,EAAA,WAER,CACA,cAAe,QACX3F,EAAA,KAAK,gBAAL,MAAAA,EAAA,WACA,KAAK,UAAY,CAAC,KAAK,aAC3B,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQ1B,GAAeA,EAAA,CAAa,EACrDmB,EAAqB,WAAW,EAAK,EACrC,aAAa,KAAK,OAAO,CAC7B,CACA,QAAS,QACLO,EAAA,KAAK,WAAL,MAAAA,EAAA,WACA,KAAK,YAAA,EACL,MAAM4F,EAAW,KAAK,MAChB,mEACA,KAAK,eACX,IAAIC,EAAQ,eAAe,KAAK,IAAI,GACpC,OAAI,KAAK,QACLA,EAAQ,uBAEL1I;AAAAA;AAAAA,qBAEMY,EAAU,KAAK,KAAK,CAAC;AAAA,qBACrB,KAAK,SAAS;AAAA;AAAA;AAAA,mBAGhB,CAAC,MAAO,KAAM,KAAM,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,iDAIKA,EAAU,KAAK,QAAQ,CAAC;AAAA;AAAA,YAE7D,KAAK,MAAQ,KAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oDAcD,KAAK,MAAQ,YAAc,QAAQ;AAAA,cACzE8H,CAAK;AAAA;AAAA,wEAEqDD,CAAQ;AAAA;AAAA;AAAA,UAGtE,KAAK,kBACDzI;AAAAA;AAAAA;AAAAA;AAAAA,4BAIc,KAAK,YAAc,KAAK,SAAS;AAAA,yBACpC,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,iEAGc,KAAK,gBAAgB;AAAA,kBACpE,KAAK,iBAAiB;AAAA;AAAA,cAG1B,IAAI;AAAA;AAAA;AAAA,QAGV,KAAK,gBACCA;AAAAA,iCACmB,CAAC,IAAK,KAAM,KAAM,IAAI,CAAC;AAAA,iCACvB,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,YAMjC,IAAI;AAAA;AAAA,2CAEyB,KAAK,MAAM;AAAA,KAElD,CACA,aAAc,OACV,GAAI,KAAK,OAAS,CAAC,KAAK,UAAW,CAC/B,KAAK,UAAY,GACjB,MAAM2I,GAAc9F,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,cACnD8F,GAAA,MAAAA,EAAa,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CACnD,KAAM,WACN,OAAQ,MAAA,EAEhB,CACJ,CACA,YAAa,SACTrG,EAAqB,WAAW,EAAK,EACjC,KAAK,SACL,KAAK,WAAa,IAClBO,EAAA,KAAK,UAAL,MAAAA,EAAA,aAGAwF,EAAA,KAAK,YAAL,MAAAA,EAAA,UAER,CACA,gBAAiB,CACb,MAAMO,EAAqBC,GAAgB,MAAM,eAAe,4BAA4B,EACtFhC,EAAS+B,EAAqB,SAASA,EAAmB,QAAQ,KAAM,EAAE,EAAG,EAAE,EAAI,EACzF,OAAO5I,kCAAsC6G,EAAS,CAAC,2BAC3D,CACA,WAAY,CACR,GAAI,CACI,KAAK,MACLtF,EAAe,gBAAgB,KAAK,GAAG,EACvCuH,GAAgB,YAAY,aAAa,EAEjD,MACM,CACFA,GAAgB,UAAU,gBAAgB,CAC9C,CACJ,CACJ,CACAV,EAAoB,OAASjI,GAC7Bf,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,aAAc,MAAM,EACtDhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,MAAO,MAAM,EAC/ChJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,QAAS,MAAM,EACjDhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,QAAS,MAAM,EACjDhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,YAAa,MAAM,EACrDhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,oBAAqB,MAAM,EAC7DhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,iBAAkB,MAAM,EAC1DhJ,GAAW,CACP2C,EAAA,CACJ,EAAGqG,EAAoB,UAAW,YAAa,MAAM,EACrDhJ,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGgI,EAAoB,UAAW,WAAY,MAAM,EACpDhJ,GAAW,CACPgB,EAAA,CACJ,EAAGgI,EAAoB,UAAW,UAAW,MAAM,EChNnD,IAAIhJ,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAIA,IAAIqJ,GAAyB,cAAqCX,CAAoB,CAClF,aAAc,CAEV,GADA,MAAA,EACI,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,+CAA+C,EAEnE,KAAK,UAAY,KAAK,eAAe,KAAK,IAAI,EAC9C,KAAK,cAAgB,KAAK,eAAe,KAAK,IAAI,EAClDvG,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,KAAM,KAAK,OAAO,KAAM,SAAU,SAAA,CAAU,CAC7D,CACL,CACA,MAAM,gBAAiB,OACnB,GAAI,CACA,KAAK,MAAQ,GACb,KAAM,CAAE,WAAAmC,GAAehD,EAAoB,MACrCkB,EAAY8B,EAAW,KAAKvE,cAAM,OAAAA,EAAE,OAAS,eAAeoD,EAAApD,EAAE,OAAF,YAAAoD,EAAQ,UAASwF,EAAA,KAAK,SAAL,YAAAA,EAAa,OAC5F5I,EAAE,OAAS,YACXA,EAAE,SAAS6I,EAAA,KAAK,SAAL,YAAAA,EAAa,MAAI,EAChC,GAAIpG,EACA,MAAMI,EAAqB,gBAAgBJ,EAAWA,EAAU,KAAK,MAGrE,OAAM,IAAI,MAAM,+CAA+C,EAEnE8G,GAAgB,MAAA,EAChBnH,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,kBACP,WAAY,CAAE,OAAQ,UAAW,OAAMgB,EAAA,KAAK,SAAL,YAAAA,EAAa,OAAQ,SAAA,CAAU,CACzE,CACL,OACOoG,EAAO,CACVpH,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,SAASoH,GAAA,YAAAA,EAAO,UAAW,SAAA,CAAU,CACtD,EACD,KAAK,MAAQ,EACjB,CACJ,CACJ,EACAF,GAAyB3J,GAAW,CAChCiB,EAAc,2BAA2B,CAC7C,EAAG0I,EAAsB,ECvDzB,IAAI3J,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAIA,IAAIwJ,GAAyB,cAAqCd,CAAoB,CAClF,aAAc,CAEV,GADA,MAAA,EACI,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,+CAA+C,EAEnE,KAAK,UAAY,KAAK,eAAe,KAAK,IAAI,EAC9C,KAAK,SAAW,KAAK,cAAc,KAAK,IAAI,EAC5CvG,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,KAAM,KAAK,OAAO,KAAM,SAAU,SAAA,CAAU,CAC7D,CACL,CACA,eAAgB,OACR,CAAC,KAAK,OAAS,KAAK,MACpB,KAAK,MAAQ,IACbgB,EAAA,KAAK,YAAL,MAAAA,EAAA,WAER,CACA,gBAAiB,OACb,IAAIA,EAAA,KAAK,SAAL,MAAAA,EAAa,cAAgB,KAAK,IAClC,GAAI,CACA,KAAK,MAAQ,GACb,KAAM,CAAE,aAAAsG,EAAc,KAAA3B,CAAA,EAAS,KAAK,OAC9B,CAAE,SAAA4B,EAAU,KAAAC,GAAS9H,EAAe,gBAAgB4H,EAAc,KAAK,GAAG,EAChF7G,EAAqB,aAAa,CAAE,KAAAkF,EAAM,KAAA6B,CAAA,CAAM,EAChD/G,EAAqB,gBAAgB,KAAK,MAAM,EAChDf,EAAe,SAAS6H,EAAU,QAAQ,CAC9C,MACM,CACF,KAAK,MAAQ,EACjB,CAER,CACJ,EACAF,GAAyB9J,GAAW,CAChCiB,EAAc,2BAA2B,CAC7C,EAAG6I,EAAsB,EC/CzB,IAAI9J,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAKA,IAAI4J,GAAwB,cAAoClB,CAAoB,CAChF,aAAc,CAyCV,GAxCA,MAAA,EACA,KAAK,gBAAkB,OACvB,KAAK,iBAAmB,OACxB,KAAK,sBAAwB,OAC7B,KAAK,OAAS,OACd,KAAK,qBAAuB9G,EAAkB,MAAM,kCACpD,KAAK,UAAY,GACjB,KAAK,UAAY,IAAM,OACnB,IAAIuB,EAAA,KAAK,SAAL,MAAAA,EAAa,aAAe,KAAK,IACjC,GAAI,CACA,KAAK,MAAQ,GACb,KAAM,CAAE,YAAA0G,EAAa,UAAAC,EAAW,KAAAhC,CAAA,EAAS,KAAK,OACxC,CAAE,SAAA4B,EAAU,sBAAAK,EAAuB,KAAAJ,GAAS9H,EAAe,gBAAgBgI,EAAa,KAAK,IAAKC,CAAS,EACjH,KAAK,iBAAmBJ,EACxB,KAAK,sBAAwBK,EAC7B,KAAK,OAASlI,EAAe,SAAA,EAAa,OAAS,QACnDe,EAAqB,aAAa,CAAE,KAAAkF,EAAM,KAAA6B,CAAA,CAAM,EAChD/G,EAAqB,gBAAgB,KAAK,MAAM,EAC5C,KAAK,sBAAwB,KAAK,sBAClCf,EAAe,SAAS,KAAK,sBAAuB,KAAK,MAAM,EAG/DA,EAAe,SAAS,KAAK,iBAAkB,KAAK,MAAM,CAElE,OACOmI,EAAG,CACN7H,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,sBACP,WAAY,CACR,QAAS6H,aAAa,MAAQA,EAAE,QAAU,6BAC1C,IAAK,KAAK,IACV,YAAa,KAAK,OAAO,YACzB,KAAM,KAAK,OAAO,IAAA,CACtB,CACH,EACD,KAAK,MAAQ,EACjB,CAER,EACI,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,8CAA8C,EAElE,KAAK,kBAAoB,OACzB,KAAK,eAAiBvG,GAAc,eAAe,OACnD,KAAK,iBAAmB,eACxB,KAAK,YAAA,EACL,KAAK,YAAY,KAAKb,EAAqB,aAAa,QAAS,IAAM,CACnE,KAAK,YAAA,CACT,CAAC,CAAC,EACFT,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,KAAM,KAAK,OAAO,KAAM,SAAU,QAAA,CAAS,CAC5D,CACL,CACA,sBAAuB,CACnB,MAAM,qBAAA,EACN,aAAa,KAAK,eAAe,CACrC,CACA,aAAc,OACV,KAAK,UAAY,CAAC,KAAK,IACnB,CAAC,KAAK,OAAS,KAAK,MACpB,KAAK,MAAQ,IACbgB,EAAA,KAAK,YAAL,MAAAA,EAAA,WAER,CACA,YAAa,OACTP,EAAqB,WAAW,EAAK,GACrCO,EAAA,KAAK,YAAL,MAAAA,EAAA,UACJ,CACJ,EACAzD,GAAW,CACP2C,EAAA,CACJ,EAAGuH,GAAsB,UAAW,mBAAoB,MAAM,EAC9DlK,GAAW,CACP2C,EAAA,CACJ,EAAGuH,GAAsB,UAAW,wBAAyB,MAAM,EACnElK,GAAW,CACP2C,EAAA,CACJ,EAAGuH,GAAsB,UAAW,SAAU,MAAM,EACpDlK,GAAW,CACP2C,EAAA,CACJ,EAAGuH,GAAsB,UAAW,uBAAwB,MAAM,EAClElK,GAAW,CACP2C,EAAA,CACJ,EAAGuH,GAAsB,UAAW,YAAa,MAAM,EACvDA,GAAwBlK,GAAW,CAC/BiB,EAAc,0BAA0B,CAC5C,EAAGiJ,EAAqB,YCjGxBK,GAAiB,UAAY,CAC3B,OAAO,OAAO,SAAY,YAAc,QAAQ,WAAa,QAAQ,UAAU,IACjF,aCNA,IAAIC,GACJ,MAAMC,GAAkB,CACtB,EACA,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1C,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAC7C,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KACtD,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IACxD,EAQAC,EAAA,cAAwB,SAAwBC,EAAS,CACvD,GAAI,CAACA,EAAS,MAAM,IAAI,MAAM,uCAAuC,EACrE,GAAIA,EAAU,GAAKA,EAAU,GAAI,MAAM,IAAI,MAAM,2CAA2C,EAC5F,OAAOA,EAAU,EAAI,EACvB,EAQAD,EAAA,wBAAkC,SAAkCC,EAAS,CAC3E,OAAOF,GAAgBE,CAAO,CAChC,EAQAD,EAAA,YAAsB,SAAUE,EAAM,CACpC,IAAIC,EAAQ,EAEZ,KAAOD,IAAS,GACdC,IACAD,KAAU,EAGZ,OAAOC,CACT,EAEAH,EAAA,kBAA4B,SAA4BI,EAAG,CACzD,GAAI,OAAOA,GAAM,WACf,MAAM,IAAI,MAAM,uCAAuC,EAGzDN,GAAiBM,CACnB,EAEAJ,EAAA,mBAA6B,UAAY,CACvC,OAAO,OAAOF,GAAmB,GACnC,EAEAE,EAAA,OAAiB,SAAiBK,EAAO,CACvC,OAAOP,GAAeO,CAAK,CAC7B,yBC9DAC,EAAA,EAAY,CAAE,IAAK,CAAC,EACpBA,EAAA,EAAY,CAAE,IAAK,CAAC,EACpBA,EAAA,EAAY,CAAE,IAAK,CAAC,EACpBA,EAAA,EAAY,CAAE,IAAK,CAAC,EAEpB,SAASC,EAAYC,EAAQ,CAC3B,GAAI,OAAOA,GAAW,SACpB,MAAM,IAAI,MAAM,uBAAuB,EAKzC,OAFcA,EAAO,YAAW,EAEnB,CACX,IAAK,IACL,IAAK,MACH,OAAOF,EAAQ,EAEjB,IAAK,IACL,IAAK,SACH,OAAOA,EAAQ,EAEjB,IAAK,IACL,IAAK,WACH,OAAOA,EAAQ,EAEjB,IAAK,IACL,IAAK,OACH,OAAOA,EAAQ,EAEjB,QACE,MAAM,IAAI,MAAM,qBAAuBE,CAAM,EAEnD,CAEAF,EAAA,QAAkB,SAAkBG,EAAO,CACzC,OAAOA,GAAS,OAAOA,EAAM,IAAQ,KACnCA,EAAM,KAAO,GAAKA,EAAM,IAAM,CAClC,EAEAH,EAAA,KAAe,SAAeI,EAAOC,EAAc,CACjD,GAAIL,EAAQ,QAAQI,CAAK,EACvB,OAAOA,EAGT,GAAI,CACF,OAAOH,EAAWG,CAAK,CAC3B,MAAc,CACV,OAAOC,CACX,CACA,QCjDA,SAASC,IAAa,CACpB,KAAK,OAAS,CAAA,EACd,KAAK,OAAS,CAChB,CAEAA,GAAU,UAAY,CAEpB,IAAK,SAAU1H,EAAO,CACpB,MAAM2H,EAAW,KAAK,MAAM3H,EAAQ,CAAC,EACrC,OAAS,KAAK,OAAO2H,CAAQ,IAAO,EAAI3H,EAAQ,EAAM,KAAO,CACjE,EAEE,IAAK,SAAU4H,EAAKC,EAAQ,CAC1B,QAAS,EAAI,EAAG,EAAIA,EAAQ,IAC1B,KAAK,QAASD,IAASC,EAAS,EAAI,EAAM,KAAO,CAAC,CAExD,EAEE,gBAAiB,UAAY,CAC3B,OAAO,KAAK,MAChB,EAEE,OAAQ,SAAUC,EAAK,CACrB,MAAMH,EAAW,KAAK,MAAM,KAAK,OAAS,CAAC,EACvC,KAAK,OAAO,QAAUA,GACxB,KAAK,OAAO,KAAK,CAAC,EAGhBG,IACF,KAAK,OAAOH,CAAQ,GAAM,MAAU,KAAK,OAAS,GAGpD,KAAK,QACT,CACA,EAEA,IAAAI,GAAiBL,GC/BjB,SAASM,GAAWzE,EAAM,CACxB,GAAI,CAACA,GAAQA,EAAO,EAClB,MAAM,IAAI,MAAM,mDAAmD,EAGrE,KAAK,KAAOA,EACZ,KAAK,KAAO,IAAI,WAAWA,EAAOA,CAAI,EACtC,KAAK,YAAc,IAAI,WAAWA,EAAOA,CAAI,CAC/C,CAWAyE,GAAU,UAAU,IAAM,SAAUC,EAAKC,EAAKV,EAAOW,EAAU,CAC7D,MAAMnI,EAAQiI,EAAM,KAAK,KAAOC,EAChC,KAAK,KAAKlI,CAAK,EAAIwH,EACfW,IAAU,KAAK,YAAYnI,CAAK,EAAI,GAC1C,EASAgI,GAAU,UAAU,IAAM,SAAUC,EAAKC,EAAK,CAC5C,OAAO,KAAK,KAAKD,EAAM,KAAK,KAAOC,CAAG,CACxC,EAUAF,GAAU,UAAU,IAAM,SAAUC,EAAKC,EAAKV,EAAO,CACnD,KAAK,KAAKS,EAAM,KAAK,KAAOC,CAAG,GAAKV,CACtC,EASAQ,GAAU,UAAU,WAAa,SAAUC,EAAKC,EAAK,CACnD,OAAO,KAAK,YAAYD,EAAM,KAAK,KAAOC,CAAG,CAC/C,EAEA,IAAAE,GAAiBJ,sBCtDjB,MAAMK,EAAgBC,EAAmB,cAgBzClB,EAAA,gBAA0B,SAA0BL,EAAS,CAC3D,GAAIA,IAAY,EAAG,MAAO,CAAA,EAE1B,MAAMwB,EAAW,KAAK,MAAMxB,EAAU,CAAC,EAAI,EACrCxD,EAAO8E,EAActB,CAAO,EAC5ByB,EAAYjF,IAAS,IAAM,GAAK,KAAK,MAAMA,EAAO,KAAO,EAAIgF,EAAW,EAAE,EAAI,EAC9EE,EAAY,CAAClF,EAAO,CAAC,EAE3B,QAAS3G,EAAI,EAAGA,EAAI2L,EAAW,EAAG3L,IAChC6L,EAAU7L,CAAC,EAAI6L,EAAU7L,EAAI,CAAC,EAAI4L,EAGpC,OAAAC,EAAU,KAAK,CAAC,EAETA,EAAU,QAAO,CAC1B,EAsBArB,EAAA,aAAuB,SAAuBL,EAAS,CACrD,MAAM2B,EAAS,CAAA,EACTC,EAAMvB,EAAQ,gBAAgBL,CAAO,EACrC6B,EAAYD,EAAI,OAEtB,QAAS/L,EAAI,EAAGA,EAAIgM,EAAWhM,IAC7B,QAASiM,EAAI,EAAGA,EAAID,EAAWC,IAExBjM,IAAM,GAAKiM,IAAM,GACjBjM,IAAM,GAAKiM,IAAMD,EAAY,GAC7BhM,IAAMgM,EAAY,GAAKC,IAAM,GAIlCH,EAAO,KAAK,CAACC,EAAI/L,CAAC,EAAG+L,EAAIE,CAAC,CAAC,CAAC,EAIhC,OAAOH,CACT,kBClFA,MAAML,GAAgBC,EAAmB,cACnCQ,GAAsB,EAS5BC,GAAA,aAAuB,SAAuBhC,EAAS,CACrD,MAAMxD,EAAO8E,GAActB,CAAO,EAElC,MAAO,CAEL,CAAC,EAAG,CAAC,EAEL,CAACxD,EAAOuF,GAAqB,CAAC,EAE9B,CAAC,EAAGvF,EAAOuF,EAAmB,CAClC,CACA,yBCjBA1B,EAAA,SAAmB,CACjB,WAAY,EACZ,WAAY,EACZ,WAAY,EACZ,WAAY,EACZ,WAAY,EACZ,WAAY,EACZ,WAAY,EACZ,WAAY,GAOd,MAAM4B,EAAgB,CACpB,GAAI,EACJ,GAAI,EACJ,GAAI,GACJ,GAAI,IASN5B,EAAA,QAAkB,SAAkB6B,EAAM,CACxC,OAAOA,GAAQ,MAAQA,IAAS,IAAM,CAAC,MAAMA,CAAI,GAAKA,GAAQ,GAAKA,GAAQ,CAC7E,EASA7B,EAAA,KAAe,SAAeI,EAAO,CACnC,OAAOJ,EAAQ,QAAQI,CAAK,EAAI,SAASA,EAAO,EAAE,EAAI,MACxD,EASAJ,EAAA,aAAuB,SAAuBJ,EAAM,CAClD,MAAMzD,EAAOyD,EAAK,KAClB,IAAIkC,EAAS,EACTC,EAAe,EACfC,EAAe,EACfC,EAAU,KACVC,EAAU,KAEd,QAASrB,EAAM,EAAGA,EAAM1E,EAAM0E,IAAO,CACnCkB,EAAeC,EAAe,EAC9BC,EAAUC,EAAU,KAEpB,QAASpB,EAAM,EAAGA,EAAM3E,EAAM2E,IAAO,CACnC,IAAIqB,EAASvC,EAAK,IAAIiB,EAAKC,CAAG,EAC1BqB,IAAWF,EACbF,KAEIA,GAAgB,IAAGD,GAAUF,EAAc,IAAMG,EAAe,IACpEE,EAAUE,EACVJ,EAAe,GAGjBI,EAASvC,EAAK,IAAIkB,EAAKD,CAAG,EACtBsB,IAAWD,EACbF,KAEIA,GAAgB,IAAGF,GAAUF,EAAc,IAAMI,EAAe,IACpEE,EAAUC,EACVH,EAAe,EAEvB,CAEQD,GAAgB,IAAGD,GAAUF,EAAc,IAAMG,EAAe,IAChEC,GAAgB,IAAGF,GAAUF,EAAc,IAAMI,EAAe,GACxE,CAEE,OAAOF,CACT,EAOA9B,EAAA,aAAuB,SAAuBJ,EAAM,CAClD,MAAMzD,EAAOyD,EAAK,KAClB,IAAIkC,EAAS,EAEb,QAASjB,EAAM,EAAGA,EAAM1E,EAAO,EAAG0E,IAChC,QAASC,EAAM,EAAGA,EAAM3E,EAAO,EAAG2E,IAAO,CACvC,MAAMsB,EAAOxC,EAAK,IAAIiB,EAAKC,CAAG,EAC5BlB,EAAK,IAAIiB,EAAKC,EAAM,CAAC,EACrBlB,EAAK,IAAIiB,EAAM,EAAGC,CAAG,EACrBlB,EAAK,IAAIiB,EAAM,EAAGC,EAAM,CAAC,GAEvBsB,IAAS,GAAKA,IAAS,IAAGN,GACpC,CAGE,OAAOA,EAASF,EAAc,EAChC,EAQA5B,EAAA,aAAuB,SAAuBJ,EAAM,CAClD,MAAMzD,EAAOyD,EAAK,KAClB,IAAIkC,EAAS,EACTO,EAAU,EACVC,EAAU,EAEd,QAASzB,EAAM,EAAGA,EAAM1E,EAAM0E,IAAO,CACnCwB,EAAUC,EAAU,EACpB,QAASxB,EAAM,EAAGA,EAAM3E,EAAM2E,IAC5BuB,EAAYA,GAAW,EAAK,KAASzC,EAAK,IAAIiB,EAAKC,CAAG,EAClDA,GAAO,KAAOuB,IAAY,MAASA,IAAY,KAAQP,IAE3DQ,EAAYA,GAAW,EAAK,KAAS1C,EAAK,IAAIkB,EAAKD,CAAG,EAClDC,GAAO,KAAOwB,IAAY,MAASA,IAAY,KAAQR,GAEjE,CAEE,OAAOA,EAASF,EAAc,EAChC,EAUA5B,EAAA,aAAuB,SAAuBJ,EAAM,CAClD,IAAI2C,EAAY,EAChB,MAAMC,EAAe5C,EAAK,KAAK,OAE/B,QAASpK,EAAI,EAAGA,EAAIgN,EAAchN,IAAK+M,GAAa3C,EAAK,KAAKpK,CAAC,EAI/D,OAFU,KAAK,IAAI,KAAK,KAAM+M,EAAY,IAAMC,EAAgB,CAAC,EAAI,EAAE,EAE5DZ,EAAc,EAC3B,EAUA,SAASa,EAAWC,EAAalN,EAAGiM,EAAG,CACrC,OAAQiB,EAAW,CACjB,KAAK1C,EAAQ,SAAS,WAAY,OAAQxK,EAAIiM,GAAK,IAAM,EACzD,KAAKzB,EAAQ,SAAS,WAAY,OAAOxK,EAAI,IAAM,EACnD,KAAKwK,EAAQ,SAAS,WAAY,OAAOyB,EAAI,IAAM,EACnD,KAAKzB,EAAQ,SAAS,WAAY,OAAQxK,EAAIiM,GAAK,IAAM,EACzD,KAAKzB,EAAQ,SAAS,WAAY,OAAQ,KAAK,MAAMxK,EAAI,CAAC,EAAI,KAAK,MAAMiM,EAAI,CAAC,GAAK,IAAM,EACzF,KAAKzB,EAAQ,SAAS,WAAY,OAAQxK,EAAIiM,EAAK,EAAKjM,EAAIiM,EAAK,IAAM,EACvE,KAAKzB,EAAQ,SAAS,WAAY,OAASxK,EAAIiM,EAAK,EAAKjM,EAAIiM,EAAK,GAAK,IAAM,EAC7E,KAAKzB,EAAQ,SAAS,WAAY,OAASxK,EAAIiM,EAAK,GAAKjM,EAAIiM,GAAK,GAAK,IAAM,EAE7E,QAAS,MAAM,IAAI,MAAM,mBAAqBiB,CAAW,EAE7D,CAQA1C,EAAA,UAAoB,SAAoB2C,EAAS/C,EAAM,CACrD,MAAMzD,EAAOyD,EAAK,KAElB,QAASkB,EAAM,EAAGA,EAAM3E,EAAM2E,IAC5B,QAASD,EAAM,EAAGA,EAAM1E,EAAM0E,IACxBjB,EAAK,WAAWiB,EAAKC,CAAG,GAC5BlB,EAAK,IAAIiB,EAAKC,EAAK2B,EAAUE,EAAS9B,EAAKC,CAAG,CAAC,CAGrD,EAQAd,EAAA,YAAsB,SAAsBJ,EAAMgD,EAAiB,CACjE,MAAMC,EAAc,OAAO,KAAK7C,EAAQ,QAAQ,EAAE,OAClD,IAAI8C,EAAc,EACdC,EAAe,IAEnB,QAASC,EAAI,EAAGA,EAAIH,EAAaG,IAAK,CACpCJ,EAAgBI,CAAC,EACjBhD,EAAQ,UAAUgD,EAAGpD,CAAI,EAGzB,MAAMqD,EACJjD,EAAQ,aAAaJ,CAAI,EACzBI,EAAQ,aAAaJ,CAAI,EACzBI,EAAQ,aAAaJ,CAAI,EACzBI,EAAQ,aAAaJ,CAAI,EAG3BI,EAAQ,UAAUgD,EAAGpD,CAAI,EAErBqD,EAAUF,IACZA,EAAeE,EACfH,EAAcE,EAEpB,CAEE,OAAOF,CACT,kBCzOA,MAAMI,GAAUhC,GAEViC,GAAkB,CAEtB,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,EACT,EAAG,EAAG,EAAG,GACT,EAAG,EAAG,GAAI,GACV,EAAG,EAAG,GAAI,GACV,EAAG,EAAG,GAAI,GACV,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,EAAG,GAAI,GAAI,GACX,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,EACd,EAEMC,GAAqB,CAEzB,EAAG,GAAI,GAAI,GACX,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,GACZ,GAAI,GAAI,GAAI,IACZ,GAAI,GAAI,IAAK,IACb,GAAI,GAAI,IAAK,IACb,GAAI,IAAK,IAAK,IACd,GAAI,IAAK,IAAK,IACd,GAAI,IAAK,IAAK,IACd,GAAI,IAAK,IAAK,IACd,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,KACf,IAAK,IAAK,IAAK,KACf,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,IAAK,KAAM,KAChB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,KACjB,IAAK,KAAM,KAAM,IACnB,EAUAC,GAAA,eAAyB,SAAyB1D,EAAS2D,EAAsB,CAC/E,OAAQA,EAAoB,CAC1B,KAAKJ,GAAQ,EACX,OAAOC,IAAiBxD,EAAU,GAAK,EAAI,CAAC,EAC9C,KAAKuD,GAAQ,EACX,OAAOC,IAAiBxD,EAAU,GAAK,EAAI,CAAC,EAC9C,KAAKuD,GAAQ,EACX,OAAOC,IAAiBxD,EAAU,GAAK,EAAI,CAAC,EAC9C,KAAKuD,GAAQ,EACX,OAAOC,IAAiBxD,EAAU,GAAK,EAAI,CAAC,EAC9C,QACE,MACN,CACA,EAUA0D,GAAA,uBAAiC,SAAiC1D,EAAS2D,EAAsB,CAC/F,OAAQA,EAAoB,CAC1B,KAAKJ,GAAQ,EACX,OAAOE,IAAoBzD,EAAU,GAAK,EAAI,CAAC,EACjD,KAAKuD,GAAQ,EACX,OAAOE,IAAoBzD,EAAU,GAAK,EAAI,CAAC,EACjD,KAAKuD,GAAQ,EACX,OAAOE,IAAoBzD,EAAU,GAAK,EAAI,CAAC,EACjD,KAAKuD,GAAQ,EACX,OAAOE,IAAoBzD,EAAU,GAAK,EAAI,CAAC,EACjD,QACE,MACN,CACA,kBCtIA,MAAM4D,GAAY,IAAI,WAAW,GAAG,EAC9BC,GAAY,IAAI,WAAW,GAAG,GASlC,UAAuB,CACvB,IAAIC,EAAI,EACR,QAAS,EAAI,EAAG,EAAI,IAAK,IACvBF,GAAU,CAAC,EAAIE,EACfD,GAAUC,CAAC,EAAI,EAEfA,IAAM,EAIFA,EAAI,MACNA,GAAK,KAQT,QAAS,EAAI,IAAK,EAAI,IAAK,IACzBF,GAAU,CAAC,EAAIA,GAAU,EAAI,GAAG,CAEpC,GAAC,EAQDG,GAAA,IAAc,SAAcC,EAAG,CAC7B,GAAIA,EAAI,EAAG,MAAM,IAAI,MAAM,OAASA,EAAI,GAAG,EAC3C,OAAOH,GAAUG,CAAC,CACpB,EAQAD,GAAA,IAAc,SAAcC,EAAG,CAC7B,OAAOJ,GAAUI,CAAC,CACpB,EASAD,GAAA,IAAc,SAAcD,EAAGG,EAAG,CAChC,OAAIH,IAAM,GAAKG,IAAM,EAAU,EAIxBL,GAAUC,GAAUC,CAAC,EAAID,GAAUI,CAAC,CAAC,CAC9C,eCpEA,MAAMC,EAAK3C,GASXlB,EAAA,IAAc,SAAc8D,EAAIC,EAAI,CAClC,MAAMC,EAAQ,IAAI,WAAWF,EAAG,OAASC,EAAG,OAAS,CAAC,EAEtD,QAASvO,EAAI,EAAGA,EAAIsO,EAAG,OAAQtO,IAC7B,QAASiM,EAAI,EAAGA,EAAIsC,EAAG,OAAQtC,IAC7BuC,EAAMxO,EAAIiM,CAAC,GAAKoC,EAAG,IAAIC,EAAGtO,CAAC,EAAGuO,EAAGtC,CAAC,CAAC,EAIvC,OAAOuC,CACT,EASAhE,EAAA,IAAc,SAAciE,EAAUC,EAAS,CAC7C,IAAIC,EAAS,IAAI,WAAWF,CAAQ,EAEpC,KAAQE,EAAO,OAASD,EAAQ,QAAW,GAAG,CAC5C,MAAMF,EAAQG,EAAO,CAAC,EAEtB,QAAS3O,EAAI,EAAGA,EAAI0O,EAAQ,OAAQ1O,IAClC2O,EAAO3O,CAAC,GAAKqO,EAAG,IAAIK,EAAQ1O,CAAC,EAAGwO,CAAK,EAIvC,IAAII,EAAS,EACb,KAAOA,EAASD,EAAO,QAAUA,EAAOC,CAAM,IAAM,GAAGA,IACvDD,EAASA,EAAO,MAAMC,CAAM,CAChC,CAEE,OAAOD,CACT,EASAnE,EAAA,qBAA+B,SAA+BqE,EAAQ,CACpE,IAAIC,EAAO,IAAI,WAAW,CAAC,CAAC,CAAC,EAC7B,QAAS9O,EAAI,EAAGA,EAAI6O,EAAQ7O,IAC1B8O,EAAOtE,EAAQ,IAAIsE,EAAM,IAAI,WAAW,CAAC,EAAGT,EAAG,IAAIrO,CAAC,CAAC,CAAC,CAAC,EAGzD,OAAO8O,CACT,QC7DA,MAAMC,GAAarD,GAEnB,SAASsD,GAAoBH,EAAQ,CACnC,KAAK,QAAU,OACf,KAAK,OAASA,EAEV,KAAK,QAAQ,KAAK,WAAW,KAAK,MAAM,CAC9C,CAQAG,GAAmB,UAAU,WAAa,SAAqBH,EAAQ,CAErE,KAAK,OAASA,EACd,KAAK,QAAUE,GAAW,qBAAqB,KAAK,MAAM,CAC5D,EAQAC,GAAmB,UAAU,OAAS,SAAiB5E,EAAM,CAC3D,GAAI,CAAC,KAAK,QACR,MAAM,IAAI,MAAM,yBAAyB,EAK3C,MAAM6E,EAAa,IAAI,WAAW7E,EAAK,OAAS,KAAK,MAAM,EAC3D6E,EAAW,IAAI7E,CAAI,EAInB,MAAM8E,EAAYH,GAAW,IAAIE,EAAY,KAAK,OAAO,EAKnDE,EAAQ,KAAK,OAASD,EAAU,OACtC,GAAIC,EAAQ,EAAG,CACb,MAAMC,EAAO,IAAI,WAAW,KAAK,MAAM,EACvC,OAAAA,EAAK,IAAIF,EAAWC,CAAK,EAElBC,CACX,CAEE,OAAOF,CACT,EAEA,IAAAG,GAAiBL,qBCjDjBM,GAAA,QAAkB,SAAkBnF,EAAS,CAC3C,MAAO,CAAC,MAAMA,CAAO,GAAKA,GAAW,GAAKA,GAAW,EACvD,YCRA,MAAMoF,GAAU,SACVC,GAAe,oBACrB,IAAIjF,GAAQ,mNAIZA,GAAQA,GAAM,QAAQ,KAAM,KAAK,EAEjC,MAAMkF,GAAO,6BAA+BlF,GAAQ;AAAA,MAEpDmF,GAAA,MAAgB,IAAI,OAAOnF,GAAO,GAAG,EACrCmF,GAAA,WAAqB,IAAI,OAAO,wBAAyB,GAAG,EAC5DA,GAAA,KAAe,IAAI,OAAOD,GAAM,GAAG,EACnCC,GAAA,QAAkB,IAAI,OAAOH,GAAS,GAAG,EACzCG,GAAA,aAAuB,IAAI,OAAOF,GAAc,GAAG,EAEnD,MAAMG,GAAa,IAAI,OAAO,IAAMpF,GAAQ,GAAG,EACzCqF,GAAe,IAAI,OAAO,IAAML,GAAU,GAAG,EAC7CM,GAAoB,IAAI,OAAO,wBAAwB,EAE7DH,GAAA,UAAoB,SAAoBI,EAAK,CAC3C,OAAOH,GAAW,KAAKG,CAAG,CAC5B,EAEAJ,GAAA,YAAsB,SAAsBI,EAAK,CAC/C,OAAOF,GAAa,KAAKE,CAAG,CAC9B,EAEAJ,GAAA,iBAA2B,SAA2BI,EAAK,CACzD,OAAOD,GAAkB,KAAKC,CAAG,CACnC,eC9BA,MAAMC,EAAerE,GACfsE,EAAQC,GASdzF,EAAA,QAAkB,CAChB,GAAI,UACJ,IAAK,EACL,OAAQ,CAAC,GAAI,GAAI,EAAE,GAYrBA,EAAA,aAAuB,CACrB,GAAI,eACJ,IAAK,EACL,OAAQ,CAAC,EAAG,GAAI,EAAE,GAQpBA,EAAA,KAAe,CACb,GAAI,OACJ,IAAK,EACL,OAAQ,CAAC,EAAG,GAAI,EAAE,GAYpBA,EAAA,MAAgB,CACd,GAAI,QACJ,IAAK,EACL,OAAQ,CAAC,EAAG,GAAI,EAAE,GASpBA,EAAA,MAAgB,CACd,IAAK,IAWPA,EAAA,sBAAgC,SAAgC0F,EAAM/F,EAAS,CAC7E,GAAI,CAAC+F,EAAK,OAAQ,MAAM,IAAI,MAAM,iBAAmBA,CAAI,EAEzD,GAAI,CAACH,EAAa,QAAQ5F,CAAO,EAC/B,MAAM,IAAI,MAAM,oBAAsBA,CAAO,EAG/C,OAAIA,GAAW,GAAKA,EAAU,GAAW+F,EAAK,OAAO,CAAC,EAC7C/F,EAAU,GAAW+F,EAAK,OAAO,CAAC,EACpCA,EAAK,OAAO,CAAC,CACtB,EAQA1F,EAAA,mBAA6B,SAA6B2F,EAAS,CACjE,OAAIH,EAAM,YAAYG,CAAO,EAAU3F,EAAQ,QACtCwF,EAAM,iBAAiBG,CAAO,EAAU3F,EAAQ,aAChDwF,EAAM,UAAUG,CAAO,EAAU3F,EAAQ,MACtCA,EAAQ,IACtB,EAQAA,EAAA,SAAmB,SAAmB0F,EAAM,CAC1C,GAAIA,GAAQA,EAAK,GAAI,OAAOA,EAAK,GACjC,MAAM,IAAI,MAAM,cAAc,CAChC,EAQA1F,EAAA,QAAkB,SAAkB0F,EAAM,CACxC,OAAOA,GAAQA,EAAK,KAAOA,EAAK,MAClC,EAQA,SAASzF,EAAYC,EAAQ,CAC3B,GAAI,OAAOA,GAAW,SACpB,MAAM,IAAI,MAAM,uBAAuB,EAKzC,OAFcA,EAAO,YAAW,EAEnB,CACX,IAAK,UACH,OAAOF,EAAQ,QACjB,IAAK,eACH,OAAOA,EAAQ,aACjB,IAAK,QACH,OAAOA,EAAQ,MACjB,IAAK,OACH,OAAOA,EAAQ,KACjB,QACE,MAAM,IAAI,MAAM,iBAAmBE,CAAM,EAE/C,CAUAF,EAAA,KAAe,SAAeI,EAAOC,EAAc,CACjD,GAAIL,EAAQ,QAAQI,CAAK,EACvB,OAAOA,EAGT,GAAI,CACF,OAAOH,EAAWG,CAAK,CAC3B,MAAc,CACV,OAAOC,CACX,CACA,qBCtKA,MAAMuF,EAAQ1E,EACR2E,EAASJ,GACTvC,EAAU4C,GACVC,EAAOC,GACPT,EAAeU,GAGfC,EAAO,KACPC,EAAUP,EAAM,YAAYM,CAAG,EAErC,SAASE,EAA6BV,EAAMjF,EAAQ6C,EAAsB,CACxE,QAAS+C,EAAiB,EAAGA,GAAkB,GAAIA,IACjD,GAAI5F,GAAUT,EAAQ,YAAYqG,EAAgB/C,EAAsBoC,CAAI,EAC1E,OAAOW,CAKb,CAEA,SAASC,EAAsBZ,EAAM/F,EAAS,CAE5C,OAAOoG,EAAK,sBAAsBL,EAAM/F,CAAO,EAAI,CACrD,CAEA,SAAS4G,EAA2BC,EAAU7G,EAAS,CACrD,IAAI8G,EAAY,EAEhB,OAAAD,EAAS,QAAQ,SAAU5G,EAAM,CAC/B,MAAM8G,EAAeJ,EAAqB1G,EAAK,KAAMD,CAAO,EAC5D8G,GAAaC,EAAe9G,EAAK,cAAa,CAClD,CAAG,EAEM6G,CACT,CAEA,SAASE,EAA4BH,EAAUlD,EAAsB,CACnE,QAAS+C,EAAiB,EAAGA,GAAkB,GAAIA,IAEjD,GADeE,EAA0BC,EAAUH,CAAc,GACnDrG,EAAQ,YAAYqG,EAAgB/C,EAAsByC,EAAK,KAAK,EAChF,OAAOM,CAKb,CAUArG,EAAA,KAAe,SAAeI,EAAOC,EAAc,CACjD,OAAIkF,EAAa,QAAQnF,CAAK,EACrB,SAASA,EAAO,EAAE,EAGpBC,CACT,EAWAL,EAAA,YAAsB,SAAsBL,EAAS2D,EAAsBoC,EAAM,CAC/E,GAAI,CAACH,EAAa,QAAQ5F,CAAO,EAC/B,MAAM,IAAI,MAAM,yBAAyB,EAIvC,OAAO+F,EAAS,MAAaA,EAAOK,EAAK,MAG7C,MAAMa,EAAiBhB,EAAM,wBAAwBjG,CAAO,EAGtDkH,EAAmBhB,EAAO,uBAAuBlG,EAAS2D,CAAoB,EAG9EwD,GAA0BF,EAAiBC,GAAoB,EAErE,GAAInB,IAASK,EAAK,MAAO,OAAOe,EAEhC,MAAMC,EAAaD,EAAyBR,EAAqBZ,EAAM/F,CAAO,EAG9E,OAAQ+F,EAAI,CACV,KAAKK,EAAK,QACR,OAAO,KAAK,MAAOgB,EAAa,GAAM,CAAC,EAEzC,KAAKhB,EAAK,aACR,OAAO,KAAK,MAAOgB,EAAa,GAAM,CAAC,EAEzC,KAAKhB,EAAK,MACR,OAAO,KAAK,MAAMgB,EAAa,EAAE,EAEnC,KAAKhB,EAAK,KACV,QACE,OAAO,KAAK,MAAMgB,EAAa,CAAC,EAEtC,EAUA/G,EAAA,sBAAgC,SAAgCJ,EAAM0D,EAAsB,CAC1F,IAAI0D,EAEJ,MAAMC,EAAM/D,EAAQ,KAAKI,EAAsBJ,EAAQ,CAAC,EAExD,GAAI,MAAM,QAAQtD,CAAI,EAAG,CACvB,GAAIA,EAAK,OAAS,EAChB,OAAO+G,EAA2B/G,EAAMqH,CAAG,EAG7C,GAAIrH,EAAK,SAAW,EAClB,MAAO,GAGToH,EAAMpH,EAAK,CAAC,CAChB,MACIoH,EAAMpH,EAGR,OAAOwG,EAA4BY,EAAI,KAAMA,EAAI,UAAS,EAAIC,CAAG,CACnE,EAYAjH,EAAA,eAAyB,SAAyBL,EAAS,CACzD,GAAI,CAAC4F,EAAa,QAAQ5F,CAAO,GAAKA,EAAU,EAC9C,MAAM,IAAI,MAAM,yBAAyB,EAG3C,IAAIpK,EAAIoK,GAAW,GAEnB,KAAOiG,EAAM,YAAYrQ,CAAC,EAAI4Q,GAAW,GACvC5Q,GAAM2Q,GAAQN,EAAM,YAAYrQ,CAAC,EAAI4Q,EAGvC,OAAQxG,GAAW,GAAMpK,CAC3B,kBClKA,MAAMqQ,GAAQ1E,EAERgG,GAAO,KACPC,GAAY,MACZC,GAAUxB,GAAM,YAAYsB,EAAG,EAYrCG,GAAA,eAAyB,SAAyB/D,EAAsBzB,EAAM,CAC5E,MAAMjC,EAAS0D,EAAqB,KAAO,EAAKzB,EAChD,IAAItM,EAAIqK,GAAQ,GAEhB,KAAOgG,GAAM,YAAYrQ,CAAC,EAAI6R,IAAW,GACvC7R,GAAM2R,IAAQtB,GAAM,YAAYrQ,CAAC,EAAI6R,GAMvC,OAASxH,GAAQ,GAAMrK,GAAK4R,EAC9B,YC5BA,MAAMpB,GAAO7E,GAEb,SAASoG,GAAa1H,EAAM,CAC1B,KAAK,KAAOmG,GAAK,QACjB,KAAK,KAAOnG,EAAK,SAAQ,CAC3B,CAEA0H,GAAY,cAAgB,SAAwB7G,EAAQ,CAC1D,MAAO,IAAK,KAAK,MAAMA,EAAS,CAAC,GAAMA,EAAS,EAAOA,EAAS,EAAK,EAAI,EAAK,EAChF,EAEA6G,GAAY,UAAU,UAAY,UAAsB,CACtD,OAAO,KAAK,KAAK,MACnB,EAEAA,GAAY,UAAU,cAAgB,UAA0B,CAC9D,OAAOA,GAAY,cAAc,KAAK,KAAK,MAAM,CACnD,EAEAA,GAAY,UAAU,MAAQ,SAAgB3G,EAAW,CACvD,IAAI,EAAG4G,EAAOnH,EAId,IAAK,EAAI,EAAG,EAAI,GAAK,KAAK,KAAK,OAAQ,GAAK,EAC1CmH,EAAQ,KAAK,KAAK,OAAO,EAAG,CAAC,EAC7BnH,EAAQ,SAASmH,EAAO,EAAE,EAE1B5G,EAAU,IAAIP,EAAO,EAAE,EAKzB,MAAMoH,EAAe,KAAK,KAAK,OAAS,EACpCA,EAAe,IACjBD,EAAQ,KAAK,KAAK,OAAO,CAAC,EAC1BnH,EAAQ,SAASmH,EAAO,EAAE,EAE1B5G,EAAU,IAAIP,EAAOoH,EAAe,EAAI,CAAC,EAE7C,EAEA,IAAAC,GAAiBH,GC1CjB,MAAMvB,GAAO7E,GAWPwG,GAAkB,CACtB,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7C,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAC1C,EAEA,SAASC,GAAkB/H,EAAM,CAC/B,KAAK,KAAOmG,GAAK,aACjB,KAAK,KAAOnG,CACd,CAEA+H,GAAiB,cAAgB,SAAwBlH,EAAQ,CAC/D,MAAO,IAAK,KAAK,MAAMA,EAAS,CAAC,EAAI,GAAKA,EAAS,EACrD,EAEAkH,GAAiB,UAAU,UAAY,UAAsB,CAC3D,OAAO,KAAK,KAAK,MACnB,EAEAA,GAAiB,UAAU,cAAgB,UAA0B,CACnE,OAAOA,GAAiB,cAAc,KAAK,KAAK,MAAM,CACxD,EAEAA,GAAiB,UAAU,MAAQ,SAAgBhH,EAAW,CAC5D,IAAI,EAIJ,IAAK,EAAI,EAAG,EAAI,GAAK,KAAK,KAAK,OAAQ,GAAK,EAAG,CAE7C,IAAIP,EAAQsH,GAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,EAAI,GAGpDtH,GAASsH,GAAgB,QAAQ,KAAK,KAAK,EAAI,CAAC,CAAC,EAGjD/G,EAAU,IAAIP,EAAO,EAAE,CAC3B,CAIM,KAAK,KAAK,OAAS,GACrBO,EAAU,IAAI+G,GAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,EAAG,CAAC,CAE1D,EAEA,IAAAE,GAAiBD,GCxDjBE,GAAiB,SAAqBC,EAAO,CAI3C,QAHI3D,EAAS,CAAA,EACThI,EAAO2L,EAAM,OAERlP,EAAQ,EAAGA,EAAQuD,EAAMvD,IAAS,CACzC,IAAImP,EAAQD,EAAM,WAAWlP,CAAK,EAElC,GAAImP,GAAS,OAAUA,GAAS,OAAU5L,EAAOvD,EAAQ,EAAG,CAC1D,IAAIoP,EAASF,EAAM,WAAWlP,EAAQ,CAAC,EAEnCoP,GAAU,OAAUA,GAAU,QAEhCD,GAASA,EAAQ,OAAU,KAAQC,EAAS,MAAS,MACrDpP,GAAS,EAEjB,CAGI,GAAImP,EAAQ,IAAM,CAChB5D,EAAO,KAAK4D,CAAK,EACjB,QACN,CAGI,GAAIA,EAAQ,KAAO,CACjB5D,EAAO,KAAM4D,GAAS,EAAK,GAAG,EAC9B5D,EAAO,KAAM4D,EAAQ,GAAM,GAAG,EAC9B,QACN,CAGI,GAAIA,EAAQ,OAAWA,GAAS,OAAUA,EAAQ,MAAU,CAC1D5D,EAAO,KAAM4D,GAAS,GAAM,GAAG,EAC/B5D,EAAO,KAAO4D,GAAS,EAAK,GAAM,GAAG,EACrC5D,EAAO,KAAM4D,EAAQ,GAAM,GAAG,EAC9B,QACN,CAGI,GAAIA,GAAS,OAAWA,GAAS,QAAU,CACzC5D,EAAO,KAAM4D,GAAS,GAAM,GAAG,EAC/B5D,EAAO,KAAO4D,GAAS,GAAM,GAAM,GAAG,EACtC5D,EAAO,KAAO4D,GAAS,EAAK,GAAM,GAAG,EACrC5D,EAAO,KAAM4D,EAAQ,GAAM,GAAG,EAC9B,QACN,CAGI5D,EAAO,KAAK,IAAM,IAAM,GAAI,CAChC,CAEE,OAAO,IAAI,WAAWA,CAAM,EAAE,MAChC,ECtDA,MAAM0D,GAAa3G,GACb6E,GAAON,GAEb,SAASwC,GAAUrI,EAAM,CACvB,KAAK,KAAOmG,GAAK,KACb,OAAQnG,GAAU,WACpBA,EAAOiI,GAAWjI,CAAI,GAExB,KAAK,KAAO,IAAI,WAAWA,CAAI,CACjC,CAEAqI,GAAS,cAAgB,SAAwBxH,EAAQ,CACvD,OAAOA,EAAS,CAClB,EAEAwH,GAAS,UAAU,UAAY,UAAsB,CACnD,OAAO,KAAK,KAAK,MACnB,EAEAA,GAAS,UAAU,cAAgB,UAA0B,CAC3D,OAAOA,GAAS,cAAc,KAAK,KAAK,MAAM,CAChD,EAEAA,GAAS,UAAU,MAAQ,SAAUtH,EAAW,CAC9C,QAASnL,EAAI,EAAG0S,EAAI,KAAK,KAAK,OAAQ1S,EAAI0S,EAAG1S,IAC3CmL,EAAU,IAAI,KAAK,KAAKnL,CAAC,EAAG,CAAC,CAEjC,EAEA,IAAA2S,GAAiBF,GC7BjB,MAAMlC,GAAO7E,GACP0E,GAAQH,EAEd,SAAS2C,GAAWxI,EAAM,CACxB,KAAK,KAAOmG,GAAK,MACjB,KAAK,KAAOnG,CACd,CAEAwI,GAAU,cAAgB,SAAwB3H,EAAQ,CACxD,OAAOA,EAAS,EAClB,EAEA2H,GAAU,UAAU,UAAY,UAAsB,CACpD,OAAO,KAAK,KAAK,MACnB,EAEAA,GAAU,UAAU,cAAgB,UAA0B,CAC5D,OAAOA,GAAU,cAAc,KAAK,KAAK,MAAM,CACjD,EAEAA,GAAU,UAAU,MAAQ,SAAUzH,EAAW,CAC/C,IAAInL,EAKJ,IAAKA,EAAI,EAAGA,EAAI,KAAK,KAAK,OAAQA,IAAK,CACrC,IAAI4K,EAAQwF,GAAM,OAAO,KAAK,KAAKpQ,CAAC,CAAC,EAGrC,GAAI4K,GAAS,OAAUA,GAAS,MAE9BA,GAAS,cAGAA,GAAS,OAAUA,GAAS,MAErCA,GAAS,UAET,OAAM,IAAI,MACR,2BAA6B,KAAK,KAAK5K,CAAC,EAAI;AAAA,gCACX,EAKrC4K,GAAWA,IAAU,EAAK,KAAQ,KAASA,EAAQ,KAGnDO,EAAU,IAAIP,EAAO,EAAE,CAC3B,CACA,EAEA,IAAAiI,GAAiBD,gCC9BjB,IAAIE,EAAW,CACb,6BAA8B,SAASC,EAAOC,EAAGjT,EAAG,CAGlD,IAAIkT,EAAe,CAAA,EAIfC,EAAQ,CAAA,EACZA,EAAMF,CAAC,EAAI,EAMX,IAAIG,EAAOL,EAAS,cAAc,KAAI,EACtCK,EAAK,KAAKH,EAAG,CAAC,EAUd,QARII,EACAC,EAAGC,EACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACG,CAACT,EAAK,SAAS,CAGpBC,EAAUD,EAAK,IAAG,EAClBE,EAAID,EAAQ,MACZG,EAAiBH,EAAQ,KAGzBI,EAAiBT,EAAMM,CAAC,GAAK,CAAA,EAK7B,IAAKC,KAAKE,EACJA,EAAe,eAAeF,CAAC,IAEjCG,EAAYD,EAAeF,CAAC,EAK5BI,EAAgCH,EAAiBE,EAMjDE,EAAiBT,EAAMI,CAAC,EACxBM,EAAe,OAAOV,EAAMI,CAAC,EAAM,KAC/BM,GAAeD,EAAiBD,KAClCR,EAAMI,CAAC,EAAII,EACXP,EAAK,KAAKG,EAAGI,CAA6B,EAC1CT,EAAaK,CAAC,EAAID,GAI9B,CAEI,GAAI,OAAOtT,EAAM,KAAe,OAAOmT,EAAMnT,CAAC,EAAM,IAAa,CAC/D,IAAI8T,EAAM,CAAC,8BAA+Bb,EAAG,OAAQjT,EAAG,GAAG,EAAE,KAAK,EAAE,EACpE,MAAM,IAAI,MAAM8T,CAAG,CACzB,CAEI,OAAOZ,CACX,EAEE,4CAA6C,SAASA,EAAclT,EAAG,CAIrE,QAHI+T,EAAQ,CAAA,EACRT,EAAItT,EAEDsT,GACLS,EAAM,KAAKT,CAAC,EACEJ,EAAaI,CAAC,EAC5BA,EAAIJ,EAAaI,CAAC,EAEpB,OAAAS,EAAM,QAAO,EACNA,CACX,EAEE,UAAW,SAASf,EAAOC,EAAGjT,EAAG,CAC/B,IAAIkT,EAAeH,EAAS,6BAA6BC,EAAOC,EAAGjT,CAAC,EACpE,OAAO+S,EAAS,4CACdG,EAAclT,CAAC,CACrB,EAKE,cAAe,CACb,KAAM,SAAUgU,EAAM,CACpB,IAAIC,EAAIlB,EAAS,cACbmB,EAAI,CAAA,EACJtU,EACJoU,EAAOA,GAAQ,CAAA,EACf,IAAKpU,KAAOqU,EACNA,EAAE,eAAerU,CAAG,IACtBsU,EAAEtU,CAAG,EAAIqU,EAAErU,CAAG,GAGlB,OAAAsU,EAAE,MAAQ,CAAA,EACVA,EAAE,OAASF,EAAK,QAAUC,EAAE,eACrBC,CACb,EAEI,eAAgB,SAAUC,EAAGC,EAAG,CAC9B,OAAOD,EAAE,KAAOC,EAAE,IACxB,EAMI,KAAM,SAAUvJ,EAAOwJ,EAAM,CAC3B,IAAIC,EAAO,CAAC,MAAOzJ,EAAO,KAAMwJ,CAAI,EACpC,KAAK,MAAM,KAAKC,CAAI,EACpB,KAAK,MAAM,KAAK,KAAK,MAAM,CACjC,EAKI,IAAK,UAAY,CACf,OAAO,KAAK,MAAM,MAAK,CAC7B,EAEI,MAAO,UAAY,CACjB,OAAO,KAAK,MAAM,SAAW,CACnC,EAEA,EAKE1H,EAAA,QAAiBmG,uCCnKnB,MAAMvC,EAAO7E,GACPoG,EAAc7B,GACdkC,EAAmB7B,GACnBmC,EAAWjC,GACXoC,EAAYnC,GACZT,EAAQsE,GACRlE,EAAQmE,EACRzB,EAAW0B,GAQjB,SAASC,EAAqB3E,EAAK,CACjC,OAAO,SAAS,mBAAmBA,CAAG,CAAC,EAAE,MAC3C,CAUA,SAAS4E,EAAahF,EAAOQ,EAAMJ,EAAK,CACtC,MAAMkB,EAAW,CAAA,EACjB,IAAIrC,EAEJ,MAAQA,EAASe,EAAM,KAAKI,CAAG,KAAO,MACpCkB,EAAS,KAAK,CACZ,KAAMrC,EAAO,CAAC,EACd,MAAOA,EAAO,MACd,KAAMuB,EACN,OAAQvB,EAAO,CAAC,EAAE,MACxB,CAAK,EAGH,OAAOqC,CACT,CASA,SAAS2D,EAAuBxE,EAAS,CACvC,MAAMyE,EAAUF,EAAY1E,EAAM,QAASO,EAAK,QAASJ,CAAO,EAC1D0E,EAAeH,EAAY1E,EAAM,aAAcO,EAAK,aAAcJ,CAAO,EAC/E,IAAI2E,EACAC,EAEJ,OAAI3E,EAAM,sBACR0E,EAAWJ,EAAY1E,EAAM,KAAMO,EAAK,KAAMJ,CAAO,EACrD4E,EAAYL,EAAY1E,EAAM,MAAOO,EAAK,MAAOJ,CAAO,IAExD2E,EAAWJ,EAAY1E,EAAM,WAAYO,EAAK,KAAMJ,CAAO,EAC3D4E,EAAY,CAAA,GAGDH,EAAQ,OAAOC,EAAcC,EAAUC,CAAS,EAG1D,KAAK,SAAUC,EAAIC,EAAI,CACtB,OAAOD,EAAG,MAAQC,EAAG,KAC3B,CAAK,EACA,IAAI,SAAUC,EAAK,CAClB,MAAO,CACL,KAAMA,EAAI,KACV,KAAMA,EAAI,KACV,OAAQA,EAAI,OAEpB,CAAK,CACL,CAUA,SAASC,EAAsBlK,EAAQiF,EAAM,CAC3C,OAAQA,EAAI,CACV,KAAKK,EAAK,QACR,OAAOuB,EAAY,cAAc7G,CAAM,EACzC,KAAKsF,EAAK,aACR,OAAO4B,EAAiB,cAAclH,CAAM,EAC9C,KAAKsF,EAAK,MACR,OAAOqC,EAAU,cAAc3H,CAAM,EACvC,KAAKsF,EAAK,KACR,OAAOkC,EAAS,cAAcxH,CAAM,EAE1C,CAQA,SAASmK,EAAeC,EAAM,CAC5B,OAAOA,EAAK,OAAO,SAAUC,EAAKC,EAAM,CACtC,MAAMC,EAAUF,EAAI,OAAS,GAAK,EAAIA,EAAIA,EAAI,OAAS,CAAC,EAAI,KAC5D,OAAIE,GAAWA,EAAQ,OAASD,EAAK,MACnCD,EAAIA,EAAI,OAAS,CAAC,EAAE,MAAQC,EAAK,KAC1BD,IAGTA,EAAI,KAAKC,CAAI,EACND,EACX,EAAK,CAAA,CAAE,CACP,CAkBA,SAASG,EAAYJ,EAAM,CACzB,MAAMvB,EAAQ,CAAA,EACd,QAAS9T,EAAI,EAAGA,EAAIqV,EAAK,OAAQrV,IAAK,CACpC,MAAMwR,EAAM6D,EAAKrV,CAAC,EAElB,OAAQwR,EAAI,KAAI,CACd,KAAKjB,EAAK,QACRuD,EAAM,KAAK,CAACtC,EACV,CAAE,KAAMA,EAAI,KAAM,KAAMjB,EAAK,aAAc,OAAQiB,EAAI,MAAM,EAC7D,CAAE,KAAMA,EAAI,KAAM,KAAMjB,EAAK,KAAM,OAAQiB,EAAI,MAAM,CAC/D,CAAS,EACD,MACF,KAAKjB,EAAK,aACRuD,EAAM,KAAK,CAACtC,EACV,CAAE,KAAMA,EAAI,KAAM,KAAMjB,EAAK,KAAM,OAAQiB,EAAI,MAAM,CAC/D,CAAS,EACD,MACF,KAAKjB,EAAK,MACRuD,EAAM,KAAK,CAACtC,EACV,CAAE,KAAMA,EAAI,KAAM,KAAMjB,EAAK,KAAM,OAAQkE,EAAoBjD,EAAI,IAAI,CAAC,CAClF,CAAS,EACD,MACF,KAAKjB,EAAK,KACRuD,EAAM,KAAK,CACT,CAAE,KAAMtC,EAAI,KAAM,KAAMjB,EAAK,KAAM,OAAQkE,EAAoBjD,EAAI,IAAI,CAAC,CAClF,CAAS,EAET,CAEE,OAAOsC,CACT,CAcA,SAAS4B,EAAY5B,EAAO3J,EAAS,CACnC,MAAMwL,EAAQ,CAAA,EACR5C,EAAQ,CAAE,MAAO,CAAA,CAAE,EACzB,IAAI6C,EAAc,CAAC,OAAO,EAE1B,QAAS5V,EAAI,EAAGA,EAAI8T,EAAM,OAAQ9T,IAAK,CACrC,MAAM6V,EAAY/B,EAAM9T,CAAC,EACnB8V,EAAiB,CAAA,EAEvB,QAAS7J,GAAI,EAAGA,GAAI4J,EAAU,OAAQ5J,KAAK,CACzC,MAAM8J,EAAOF,EAAU5J,EAAC,EAClBtM,GAAM,GAAKK,EAAIiM,GAErB6J,EAAe,KAAKnW,EAAG,EACvBgW,EAAMhW,EAAG,EAAI,CAAE,KAAMoW,EAAM,UAAW,CAAC,EACvChD,EAAMpT,EAAG,EAAI,CAAA,EAEb,QAASwO,GAAI,EAAGA,GAAIyH,EAAY,OAAQzH,KAAK,CAC3C,MAAM6H,GAAaJ,EAAYzH,EAAC,EAE5BwH,EAAMK,EAAU,GAAKL,EAAMK,EAAU,EAAE,KAAK,OAASD,EAAK,MAC5DhD,EAAMiD,EAAU,EAAErW,EAAG,EACnBwV,EAAqBQ,EAAMK,EAAU,EAAE,UAAYD,EAAK,OAAQA,EAAK,IAAI,EACzEZ,EAAqBQ,EAAMK,EAAU,EAAE,UAAWD,EAAK,IAAI,EAE7DJ,EAAMK,EAAU,EAAE,WAAaD,EAAK,SAEhCJ,EAAMK,EAAU,IAAGL,EAAMK,EAAU,EAAE,UAAYD,EAAK,QAE1DhD,EAAMiD,EAAU,EAAErW,EAAG,EAAIwV,EAAqBY,EAAK,OAAQA,EAAK,IAAI,EAClE,EAAIxF,EAAK,sBAAsBwF,EAAK,KAAM5L,CAAO,EAE7D,CACA,CAEIyL,EAAcE,CAClB,CAEE,QAAS3H,EAAI,EAAGA,EAAIyH,EAAY,OAAQzH,IACtC4E,EAAM6C,EAAYzH,CAAC,CAAC,EAAE,IAAM,EAG9B,MAAO,CAAE,IAAK4E,EAAO,MAAO4C,CAAK,CACnC,CAUA,SAASM,EAAoB7L,EAAM8L,EAAW,CAC5C,IAAIhG,EACJ,MAAMiG,EAAW5F,EAAK,mBAAmBnG,CAAI,EAK7C,GAHA8F,EAAOK,EAAK,KAAK2F,EAAWC,CAAQ,EAGhCjG,IAASK,EAAK,MAAQL,EAAK,IAAMiG,EAAS,IAC5C,MAAM,IAAI,MAAM,IAAM/L,EAAO,iCACOmG,EAAK,SAASL,CAAI,EACpD;AAAA,sBAA4BK,EAAK,SAAS4F,CAAQ,CAAC,EAQvD,OAJIjG,IAASK,EAAK,OAAS,CAACH,EAAM,mBAAkB,IAClDF,EAAOK,EAAK,MAGNL,EAAI,CACV,KAAKK,EAAK,QACR,OAAO,IAAIuB,EAAY1H,CAAI,EAE7B,KAAKmG,EAAK,aACR,OAAO,IAAI4B,EAAiB/H,CAAI,EAElC,KAAKmG,EAAK,MACR,OAAO,IAAIqC,EAAUxI,CAAI,EAE3B,KAAKmG,EAAK,KACR,OAAO,IAAIkC,EAASrI,CAAI,EAE9B,CAiBAI,EAAA,UAAoB,SAAoB4L,EAAO,CAC7C,OAAOA,EAAM,OAAO,SAAUd,EAAK9D,EAAK,CACtC,OAAI,OAAOA,GAAQ,SACjB8D,EAAI,KAAKW,EAAmBzE,EAAK,IAAI,CAAC,EAC7BA,EAAI,MACb8D,EAAI,KAAKW,EAAmBzE,EAAI,KAAMA,EAAI,IAAI,CAAC,EAG1C8D,CACX,EAAK,CAAA,CAAE,CACP,EAUA9K,EAAA,WAAqB,SAAqBJ,EAAMD,EAAS,CACvD,MAAMkL,EAAOV,EAAsBvK,EAAMgG,EAAM,mBAAkB,CAAE,EAE7D0D,EAAQ2B,EAAWJ,CAAI,EACvBtC,EAAQ2C,EAAW5B,EAAO3J,CAAO,EACjCkM,EAAOvD,EAAS,UAAUC,EAAM,IAAK,QAAS,KAAK,EAEnDuD,EAAgB,CAAA,EACtB,QAAStW,GAAI,EAAGA,GAAIqW,EAAK,OAAS,EAAGrW,KACnCsW,EAAc,KAAKvD,EAAM,MAAMsD,EAAKrW,EAAC,CAAC,EAAE,IAAI,EAG9C,OAAOwK,EAAQ,UAAU4K,EAAckB,CAAa,CAAC,CACvD,EAYA9L,EAAA,SAAmB,SAAmBJ,EAAM,CAC1C,OAAOI,EAAQ,UACbmK,EAAsBvK,EAAMgG,EAAM,mBAAkB,CAAE,EAE1D,QCzUA,MAAMA,GAAQ1E,EACRgC,GAAUuC,GACVnF,GAAYwF,GACZlF,GAAYoF,GACZ+F,GAAmB9F,GACnB+F,GAAgBlC,GAChBmC,GAAclC,GACdlE,GAASmE,GACTxF,GAAqB0H,GACrBC,GAAUC,GACVC,GAAaC,GACbvG,GAAOwG,GACPC,GAAWC,GAkCjB,SAASC,GAAoBC,EAAQhN,EAAS,CAC5C,MAAMxD,EAAOwQ,EAAO,KACdpL,EAAMyK,GAAc,aAAarM,CAAO,EAE9C,QAASnK,EAAI,EAAGA,EAAI+L,EAAI,OAAQ/L,IAAK,CACnC,MAAMqL,EAAMU,EAAI/L,CAAC,EAAE,CAAC,EACdsL,EAAMS,EAAI/L,CAAC,EAAE,CAAC,EAEpB,QAASF,EAAI,GAAIA,GAAK,EAAGA,IACvB,GAAI,EAAAuL,EAAMvL,GAAK,IAAM6G,GAAQ0E,EAAMvL,GAEnC,QAASD,EAAI,GAAIA,GAAK,EAAGA,IACnByL,EAAMzL,GAAK,IAAM8G,GAAQ2E,EAAMzL,IAE9BC,GAAK,GAAKA,GAAK,IAAMD,IAAM,GAAKA,IAAM,IACxCA,GAAK,GAAKA,GAAK,IAAMC,IAAM,GAAKA,IAAM,IACtCA,GAAK,GAAKA,GAAK,GAAKD,GAAK,GAAKA,GAAK,EACpCsX,EAAO,IAAI9L,EAAMvL,EAAGwL,EAAMzL,EAAG,GAAM,EAAI,EAEvCsX,EAAO,IAAI9L,EAAMvL,EAAGwL,EAAMzL,EAAG,GAAO,EAAI,EAIlD,CACA,CASA,SAASuX,GAAoBD,EAAQ,CACnC,MAAMxQ,EAAOwQ,EAAO,KAEpB,QAASrX,EAAI,EAAGA,EAAI6G,EAAO,EAAG7G,IAAK,CACjC,MAAM8K,EAAQ9K,EAAI,IAAM,EACxBqX,EAAO,IAAIrX,EAAG,EAAG8K,EAAO,EAAI,EAC5BuM,EAAO,IAAI,EAAGrX,EAAG8K,EAAO,EAAI,CAChC,CACA,CAUA,SAASyM,GAAuBF,EAAQhN,EAAS,CAC/C,MAAM4B,EAAMwK,GAAiB,aAAapM,CAAO,EAEjD,QAASnK,EAAI,EAAGA,EAAI+L,EAAI,OAAQ/L,IAAK,CACnC,MAAMqL,EAAMU,EAAI/L,CAAC,EAAE,CAAC,EACdsL,EAAMS,EAAI/L,CAAC,EAAE,CAAC,EAEpB,QAASF,EAAI,GAAIA,GAAK,EAAGA,IACvB,QAASD,EAAI,GAAIA,GAAK,EAAGA,IACnBC,IAAM,IAAMA,IAAM,GAAKD,IAAM,IAAMA,IAAM,GAC1CC,IAAM,GAAKD,IAAM,EAClBsX,EAAO,IAAI9L,EAAMvL,EAAGwL,EAAMzL,EAAG,GAAM,EAAI,EAEvCsX,EAAO,IAAI9L,EAAMvL,EAAGwL,EAAMzL,EAAG,GAAO,EAAI,CAIlD,CACA,CAQA,SAASyX,GAAkBH,EAAQhN,EAAS,CAC1C,MAAMxD,EAAOwQ,EAAO,KACdI,EAAOZ,GAAQ,eAAexM,CAAO,EAC3C,IAAIkB,EAAKC,EAAKkM,EAEd,QAASxX,EAAI,EAAGA,EAAI,GAAIA,IACtBqL,EAAM,KAAK,MAAMrL,EAAI,CAAC,EACtBsL,EAAMtL,EAAI,EAAI2G,EAAO,EAAI,EACzB6Q,GAAQD,GAAQvX,EAAK,KAAO,EAE5BmX,EAAO,IAAI9L,EAAKC,EAAKkM,EAAK,EAAI,EAC9BL,EAAO,IAAI7L,EAAKD,EAAKmM,EAAK,EAAI,CAElC,CASA,SAASC,GAAiBN,EAAQrJ,EAAsBZ,EAAa,CACnE,MAAMvG,EAAOwQ,EAAO,KACdI,EAAOV,GAAW,eAAe/I,EAAsBZ,CAAW,EACxE,IAAIlN,EAAGwX,EAEP,IAAKxX,EAAI,EAAGA,EAAI,GAAIA,IAClBwX,GAAQD,GAAQvX,EAAK,KAAO,EAGxBA,EAAI,EACNmX,EAAO,IAAInX,EAAG,EAAGwX,EAAK,EAAI,EACjBxX,EAAI,EACbmX,EAAO,IAAInX,EAAI,EAAG,EAAGwX,EAAK,EAAI,EAE9BL,EAAO,IAAIxQ,EAAO,GAAK3G,EAAG,EAAGwX,EAAK,EAAI,EAIpCxX,EAAI,EACNmX,EAAO,IAAI,EAAGxQ,EAAO3G,EAAI,EAAGwX,EAAK,EAAI,EAC5BxX,EAAI,EACbmX,EAAO,IAAI,EAAG,GAAKnX,EAAI,EAAI,EAAGwX,EAAK,EAAI,EAEvCL,EAAO,IAAI,EAAG,GAAKnX,EAAI,EAAGwX,EAAK,EAAI,EAKvCL,EAAO,IAAIxQ,EAAO,EAAG,EAAG,EAAG,EAAI,CACjC,CAQA,SAAS+Q,GAAWP,EAAQ/M,EAAM,CAChC,MAAMzD,EAAOwQ,EAAO,KACpB,IAAIQ,EAAM,GACNtM,EAAM1E,EAAO,EACbiR,EAAW,EACXC,EAAY,EAEhB,QAASvM,EAAM3E,EAAO,EAAG2E,EAAM,EAAGA,GAAO,EAGvC,IAFIA,IAAQ,GAAGA,MAEF,CACX,QAASzL,EAAI,EAAGA,EAAI,EAAGA,IACrB,GAAI,CAACsX,EAAO,WAAW9L,EAAKC,EAAMzL,CAAC,EAAG,CACpC,IAAIiY,EAAO,GAEPD,EAAYzN,EAAK,SACnB0N,GAAU1N,EAAKyN,CAAS,IAAMD,EAAY,KAAO,GAGnDT,EAAO,IAAI9L,EAAKC,EAAMzL,EAAGiY,CAAI,EAC7BF,IAEIA,IAAa,KACfC,IACAD,EAAW,EAEvB,CAKM,GAFAvM,GAAOsM,EAEHtM,EAAM,GAAK1E,GAAQ0E,EAAK,CAC1BA,GAAOsM,EACPA,EAAM,CAACA,EACP,KACR,CACA,CAEA,CAUA,SAASI,GAAY5N,EAAS2D,EAAsBkD,EAAU,CAE5D,MAAMgH,EAAS,IAAIlN,GAEnBkG,EAAS,QAAQ,SAAU5G,EAAM,CAE/B4N,EAAO,IAAI5N,EAAK,KAAK,IAAK,CAAC,EAS3B4N,EAAO,IAAI5N,EAAK,UAAS,EAAImG,GAAK,sBAAsBnG,EAAK,KAAMD,CAAO,CAAC,EAG3EC,EAAK,MAAM4N,CAAM,CACrB,CAAG,EAGD,MAAM5G,EAAiBhB,GAAM,wBAAwBjG,CAAO,EACtDkH,EAAmBhB,GAAO,uBAAuBlG,EAAS2D,CAAoB,EAC9EwD,GAA0BF,EAAiBC,GAAoB,EAgBrE,IATI2G,EAAO,kBAAoB,GAAK1G,GAClC0G,EAAO,IAAI,EAAG,CAAC,EAQVA,EAAO,kBAAoB,IAAM,GACtCA,EAAO,OAAO,CAAC,EAOjB,MAAMC,GAAiB3G,EAAyB0G,EAAO,gBAAe,GAAM,EAC5E,QAAShY,EAAI,EAAGA,EAAIiY,EAAejY,IACjCgY,EAAO,IAAIhY,EAAI,EAAI,GAAO,IAAM,CAAC,EAGnC,OAAOkY,GAAgBF,EAAQ7N,EAAS2D,CAAoB,CAC9D,CAWA,SAASoK,GAAiB/M,EAAWhB,EAAS2D,EAAsB,CAElE,MAAMsD,EAAiBhB,GAAM,wBAAwBjG,CAAO,EAGtDkH,EAAmBhB,GAAO,uBAAuBlG,EAAS2D,CAAoB,EAG9EqK,EAAqB/G,EAAiBC,EAGtC+G,EAAgB/H,GAAO,eAAelG,EAAS2D,CAAoB,EAGnEuK,EAAiBjH,EAAiBgH,EAClCE,EAAiBF,EAAgBC,EAEjCE,EAAyB,KAAK,MAAMnH,EAAiBgH,CAAa,EAElEI,EAAwB,KAAK,MAAML,EAAqBC,CAAa,EACrEK,EAAwBD,EAAwB,EAGhDE,EAAUH,EAAyBC,EAGnCG,EAAK,IAAI3J,GAAmB0J,CAAO,EAEzC,IAAI9J,EAAS,EACb,MAAMgK,EAAS,IAAI,MAAMR,CAAa,EAChCS,EAAS,IAAI,MAAMT,CAAa,EACtC,IAAIU,EAAc,EAClB,MAAMd,EAAS,IAAI,WAAW7M,EAAU,MAAM,EAG9C,QAASgJ,EAAI,EAAGA,EAAIiE,EAAejE,IAAK,CACtC,MAAM4E,EAAW5E,EAAImE,EAAiBE,EAAwBC,EAG9DG,EAAOzE,CAAC,EAAI6D,EAAO,MAAMpJ,EAAQA,EAASmK,CAAQ,EAGlDF,EAAO1E,CAAC,EAAIwE,EAAG,OAAOC,EAAOzE,CAAC,CAAC,EAE/BvF,GAAUmK,EACVD,EAAc,KAAK,IAAIA,EAAaC,CAAQ,CAChD,CAIE,MAAM3O,EAAO,IAAI,WAAWgH,CAAc,EAC1C,IAAIhO,EAAQ,EACRpD,EAAGF,EAGP,IAAKE,EAAI,EAAGA,EAAI8Y,EAAa9Y,IAC3B,IAAKF,EAAI,EAAGA,EAAIsY,EAAetY,IACzBE,EAAI4Y,EAAO9Y,CAAC,EAAE,SAChBsK,EAAKhH,GAAO,EAAIwV,EAAO9Y,CAAC,EAAEE,CAAC,GAMjC,IAAKA,EAAI,EAAGA,EAAI0Y,EAAS1Y,IACvB,IAAKF,EAAI,EAAGA,EAAIsY,EAAetY,IAC7BsK,EAAKhH,GAAO,EAAIyV,EAAO/Y,CAAC,EAAEE,CAAC,EAI/B,OAAOoK,CACT,CAWA,SAAS4O,GAAc5O,EAAMD,EAAS2D,EAAsBZ,EAAa,CACvE,IAAI8D,EAEJ,GAAI,MAAM,QAAQ5G,CAAI,EACpB4G,EAAWgG,GAAS,UAAU5M,CAAI,UACzB,OAAOA,GAAS,SAAU,CACnC,IAAI6O,EAAmB9O,EAEvB,GAAI,CAAC8O,EAAkB,CACrB,MAAMC,EAAclC,GAAS,SAAS5M,CAAI,EAG1C6O,EAAmBtC,GAAQ,sBAAsBuC,EAAapL,CAAoB,CACxF,CAIIkD,EAAWgG,GAAS,WAAW5M,EAAM6O,GAAoB,EAAE,CAC/D,KACI,OAAM,IAAI,MAAM,cAAc,EAIhC,MAAME,EAAcxC,GAAQ,sBAAsB3F,EAAUlD,CAAoB,EAGhF,GAAI,CAACqL,EACH,MAAM,IAAI,MAAM,yDAAyD,EAI3E,GAAI,CAAChP,EACHA,EAAUgP,UAGDhP,EAAUgP,EACnB,MAAM,IAAI,MAAM;AAAA;AAAA,qDAE0CA,EAAc;AAAA,CAC5E,EAGE,MAAMC,EAAWrB,GAAW5N,EAAS2D,EAAsBkD,CAAQ,EAG7DqI,EAAcjJ,GAAM,cAAcjG,CAAO,EACzCmP,EAAU,IAAIlO,GAAUiO,CAAW,EAGzC,OAAAnC,GAAmBoC,EAASnP,CAAO,EACnCiN,GAAmBkC,CAAO,EAC1BjC,GAAsBiC,EAASnP,CAAO,EAMtCsN,GAAgB6B,EAASxL,EAAsB,CAAC,EAE5C3D,GAAW,GACbmN,GAAiBgC,EAASnP,CAAO,EAInCuN,GAAU4B,EAASF,CAAQ,EAEvB,MAAMlM,CAAW,IAEnBA,EAAcuJ,GAAY,YAAY6C,EACpC7B,GAAgB,KAAK,KAAM6B,EAASxL,CAAoB,CAAC,GAI7D2I,GAAY,UAAUvJ,EAAaoM,CAAO,EAG1C7B,GAAgB6B,EAASxL,EAAsBZ,CAAW,EAEnD,CACL,QAASoM,EACT,QAASnP,EACT,qBAAsB2D,EACtB,YAAaZ,EACb,SAAU8D,CACd,CACA,CAWAuI,GAAA,OAAiB,SAAiBnP,EAAMoP,EAAS,CAC/C,GAAI,OAAOpP,EAAS,KAAeA,IAAS,GAC1C,MAAM,IAAI,MAAM,eAAe,EAGjC,IAAI0D,EAAuBJ,GAAQ,EAC/BvD,EACAkC,EAEJ,OAAI,OAAOmN,EAAY,MAErB1L,EAAuBJ,GAAQ,KAAK8L,EAAQ,qBAAsB9L,GAAQ,CAAC,EAC3EvD,EAAUwM,GAAQ,KAAK6C,EAAQ,OAAO,EACtCnN,EAAOoK,GAAY,KAAK+C,EAAQ,WAAW,EAEvCA,EAAQ,YACVpJ,GAAM,kBAAkBoJ,EAAQ,UAAU,GAIvCR,GAAa5O,EAAMD,EAAS2D,EAAsBzB,CAAI,CAC/D,+BC9eA,SAASoN,EAAUC,EAAK,CAKtB,GAJI,OAAOA,GAAQ,WACjBA,EAAMA,EAAI,SAAQ,GAGhB,OAAOA,GAAQ,SACjB,MAAM,IAAI,MAAM,uCAAuC,EAGzD,IAAIC,EAAUD,EAAI,MAAK,EAAG,QAAQ,IAAK,EAAE,EAAE,MAAM,EAAE,EACnD,GAAIC,EAAQ,OAAS,GAAKA,EAAQ,SAAW,GAAKA,EAAQ,OAAS,EACjE,MAAM,IAAI,MAAM,sBAAwBD,CAAG,GAIzCC,EAAQ,SAAW,GAAKA,EAAQ,SAAW,KAC7CA,EAAU,MAAM,UAAU,OAAO,MAAM,CAAA,EAAIA,EAAQ,IAAI,SAAU9Z,EAAG,CAClE,MAAO,CAACA,EAAGA,CAAC,CAClB,CAAK,CAAC,GAIA8Z,EAAQ,SAAW,GAAGA,EAAQ,KAAK,IAAK,GAAG,EAE/C,MAAMC,EAAW,SAASD,EAAQ,KAAK,EAAE,EAAG,EAAE,EAE9C,MAAO,CACL,EAAIC,GAAY,GAAM,IACtB,EAAIA,GAAY,GAAM,IACtB,EAAIA,GAAY,EAAK,IACrB,EAAGA,EAAW,IACd,IAAK,IAAMD,EAAQ,MAAM,EAAG,CAAC,EAAE,KAAK,EAAE,EAE1C,CAEAnP,EAAA,WAAqB,SAAqBgP,EAAS,CAC5CA,IAASA,EAAU,CAAA,GACnBA,EAAQ,QAAOA,EAAQ,MAAQ,CAAA,GAEpC,MAAMK,EAAS,OAAOL,EAAQ,OAAW,KACvCA,EAAQ,SAAW,MACnBA,EAAQ,OAAS,EACf,EACAA,EAAQ,OAENM,EAAQN,EAAQ,OAASA,EAAQ,OAAS,GAAKA,EAAQ,MAAQ,OAC/DO,EAAQP,EAAQ,OAAS,EAE/B,MAAO,CACL,MAAOM,EACP,MAAOA,EAAQ,EAAIC,EACnB,OAAQF,EACR,MAAO,CACL,KAAMJ,EAASD,EAAQ,MAAM,MAAQ,WAAW,EAChD,MAAOC,EAASD,EAAQ,MAAM,OAAS,WAAW,GAEpD,KAAMA,EAAQ,KACd,aAAcA,EAAQ,cAAgB,CAAA,EAE1C,EAEAhP,EAAA,SAAmB,SAAmBwP,EAAQjG,EAAM,CAClD,OAAOA,EAAK,OAASA,EAAK,OAASiG,EAASjG,EAAK,OAAS,EACtDA,EAAK,OAASiG,EAASjG,EAAK,OAAS,GACrCA,EAAK,KACX,EAEAvJ,EAAA,cAAwB,SAAwBwP,EAAQjG,EAAM,CAC5D,MAAMgG,EAAQvP,EAAQ,SAASwP,EAAQjG,CAAI,EAC3C,OAAO,KAAK,OAAOiG,EAASjG,EAAK,OAAS,GAAKgG,CAAK,CACtD,EAEAvP,EAAA,cAAwB,SAAwByP,EAASC,EAAInG,EAAM,CACjE,MAAMpN,EAAOuT,EAAG,QAAQ,KAClB9P,EAAO8P,EAAG,QAAQ,KAClBH,EAAQvP,EAAQ,SAAS7D,EAAMoN,CAAI,EACnCoG,EAAa,KAAK,OAAOxT,EAAOoN,EAAK,OAAS,GAAKgG,CAAK,EACxDK,EAAerG,EAAK,OAASgG,EAC7BM,EAAU,CAACtG,EAAK,MAAM,MAAOA,EAAK,MAAM,IAAI,EAElD,QAAS/T,EAAI,EAAGA,EAAIma,EAAYna,IAC9B,QAASiM,EAAI,EAAGA,EAAIkO,EAAYlO,IAAK,CACnC,IAAIqO,GAAUta,EAAIma,EAAalO,GAAK,EAChCsO,EAAUxG,EAAK,MAAM,MAEzB,GAAI/T,GAAKoa,GAAgBnO,GAAKmO,GAC5Bpa,EAAIma,EAAaC,GAAgBnO,EAAIkO,EAAaC,EAAc,CAChE,MAAMI,EAAO,KAAK,OAAOxa,EAAIoa,GAAgBL,CAAK,EAC5CU,EAAO,KAAK,OAAOxO,EAAImO,GAAgBL,CAAK,EAClDQ,EAAUF,EAAQjQ,EAAKoQ,EAAO7T,EAAO8T,CAAI,EAAI,EAAI,CAAC,CAC1D,CAEMR,EAAQK,GAAQ,EAAIC,EAAQ,EAC5BN,EAAQK,GAAQ,EAAIC,EAAQ,EAC5BN,EAAQK,GAAQ,EAAIC,EAAQ,EAC5BN,EAAQK,CAAM,EAAIC,EAAQ,CAChC,CAEA,qBClGA,MAAMnK,EAAQ1E,GAEd,SAASgP,EAAaC,EAAKC,EAAQjU,EAAM,CACvCgU,EAAI,UAAU,EAAG,EAAGC,EAAO,MAAOA,EAAO,MAAM,EAE1CA,EAAO,QAAOA,EAAO,MAAQ,CAAA,GAClCA,EAAO,OAASjU,EAChBiU,EAAO,MAAQjU,EACfiU,EAAO,MAAM,OAASjU,EAAO,KAC7BiU,EAAO,MAAM,MAAQjU,EAAO,IAC9B,CAEA,SAASkU,GAAoB,CAC3B,GAAI,CACF,OAAO,SAAS,cAAc,QAAQ,CAC1C,MAAc,CACV,MAAM,IAAI,MAAM,sCAAsC,CAC1D,CACA,CAEArQ,EAAA,OAAiB,SAAiBsQ,EAAQF,EAAQpB,EAAS,CACzD,IAAIzF,EAAOyF,EACPuB,EAAWH,EAEX,OAAO7G,EAAS,MAAgB,CAAC6G,GAAU,CAACA,EAAO,cACrD7G,EAAO6G,EACPA,EAAS,QAGNA,IACHG,EAAWF,EAAgB,GAG7B9G,EAAO3D,EAAM,WAAW2D,CAAI,EAC5B,MAAMpN,EAAOyJ,EAAM,cAAc0K,EAAO,QAAQ,KAAM/G,CAAI,EAEpD4G,EAAMI,EAAS,WAAW,IAAI,EAC9BC,EAAQL,EAAI,gBAAgBhU,EAAMA,CAAI,EAC5C,OAAAyJ,EAAM,cAAc4K,EAAM,KAAMF,EAAQ/G,CAAI,EAE5C2G,EAAYC,EAAKI,EAAUpU,CAAI,EAC/BgU,EAAI,aAAaK,EAAO,EAAG,CAAC,EAErBD,CACT,EAEAvQ,EAAA,gBAA0B,SAA0BsQ,EAAQF,EAAQpB,EAAS,CAC3E,IAAIzF,EAAOyF,EAEP,OAAOzF,EAAS,MAAgB,CAAC6G,GAAU,CAACA,EAAO,cACrD7G,EAAO6G,EACPA,EAAS,QAGN7G,IAAMA,EAAO,CAAA,GAElB,MAAMgH,EAAWvQ,EAAQ,OAAOsQ,EAAQF,EAAQ7G,CAAI,EAE9CzO,EAAOyO,EAAK,MAAQ,YACpBkH,EAAelH,EAAK,cAAgB,CAAA,EAE1C,OAAOgH,EAAS,UAAUzV,EAAM2V,EAAa,OAAO,CACtD,kBC9DA,MAAM7K,GAAQ1E,GAEd,SAASwP,GAAgBtU,EAAOuU,EAAQ,CACtC,MAAMC,EAAQxU,EAAM,EAAI,IAClBkJ,EAAMqL,EAAS,KAAOvU,EAAM,IAAM,IAExC,OAAOwU,EAAQ,EACXtL,EAAM,IAAMqL,EAAS,aAAeC,EAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAI,IAChEtL,CACN,CAEA,SAASuL,GAAQC,EAAKrN,EAAGG,EAAG,CAC1B,IAAI0B,EAAMwL,EAAMrN,EAChB,OAAI,OAAOG,EAAM,MAAa0B,GAAO,IAAM1B,GAEpC0B,CACT,CAEA,SAASyL,GAAUnR,EAAMzD,EAAMkT,EAAQ,CACrC,IAAIxD,EAAO,GACPmF,EAAS,EACTC,EAAS,GACTC,EAAa,EAEjB,QAAS1b,EAAI,EAAGA,EAAIoK,EAAK,OAAQpK,IAAK,CACpC,MAAMsL,EAAM,KAAK,MAAMtL,EAAI2G,CAAI,EACzB0E,EAAM,KAAK,MAAMrL,EAAI2G,CAAI,EAE3B,CAAC2E,GAAO,CAACmQ,IAAQA,EAAS,IAE1BrR,EAAKpK,CAAC,GACR0b,IAEM1b,EAAI,GAAKsL,EAAM,GAAKlB,EAAKpK,EAAI,CAAC,IAClCqW,GAAQoF,EACJJ,GAAO,IAAK/P,EAAMuO,EAAQ,GAAMxO,EAAMwO,CAAM,EAC5CwB,GAAO,IAAKG,EAAQ,CAAC,EAEzBA,EAAS,EACTC,EAAS,IAGLnQ,EAAM,EAAI3E,GAAQyD,EAAKpK,EAAI,CAAC,IAChCqW,GAAQgF,GAAO,IAAKK,CAAU,EAC9BA,EAAa,IAGfF,GAEN,CAEE,OAAOnF,CACT,CAEAsF,GAAA,OAAiB,SAAiBb,EAAQtB,EAASoC,EAAI,CACrD,MAAM7H,EAAO3D,GAAM,WAAWoJ,CAAO,EAC/B7S,EAAOmU,EAAO,QAAQ,KACtB1Q,EAAO0Q,EAAO,QAAQ,KACtBe,EAAalV,EAAOoN,EAAK,OAAS,EAElC+H,EAAM/H,EAAK,MAAM,MAAM,EAEzB,SAAWmH,GAAenH,EAAK,MAAM,MAAO,MAAM,EAClD,YAAc8H,EAAa,IAAMA,EAAa,SAF9C,GAIExF,EACJ,SAAW6E,GAAenH,EAAK,MAAM,KAAM,QAAQ,EACnD,OAASwH,GAASnR,EAAMzD,EAAMoN,EAAK,MAAM,EAAI,MAEzCgI,EAAU,gBAAuBF,EAAa,IAAMA,EAAa,IAIjEF,EAAS,4CAFA5H,EAAK,MAAa,UAAYA,EAAK,MAAQ,aAAeA,EAAK,MAAQ,KAA1D,IAEwCgI,EAAU,iCAAmCD,EAAKzF,EAAO;AAAA,EAE7H,OAAI,OAAOuF,GAAO,YAChBA,EAAG,KAAMD,CAAM,EAGVA,CACT,EC/EA,MAAM5R,GAAa2B,GAEbsQ,GAAS/L,GACTgM,GAAiB3L,GACjB4L,GAAc1L,GAEpB,SAAS2L,GAAcC,EAAYxB,EAAQyB,EAAMtI,EAAM6H,EAAI,CACzD,MAAMU,EAAO,CAAA,EAAG,MAAM,KAAK,UAAW,CAAC,EACjCC,EAAUD,EAAK,OACfE,EAAc,OAAOF,EAAKC,EAAU,CAAC,GAAM,WAEjD,GAAI,CAACC,GAAe,CAACzS,KACnB,MAAM,IAAI,MAAM,oCAAoC,EAGtD,GAAIyS,EAAa,CACf,GAAID,EAAU,EACZ,MAAM,IAAI,MAAM,4BAA4B,EAG1CA,IAAY,GACdX,EAAKS,EACLA,EAAOzB,EACPA,EAAS7G,EAAO,QACPwI,IAAY,IACjB3B,EAAO,YAAc,OAAOgB,EAAO,KACrCA,EAAK7H,EACLA,EAAO,SAEP6H,EAAK7H,EACLA,EAAOsI,EACPA,EAAOzB,EACPA,EAAS,QAGjB,KAAS,CACL,GAAI2B,EAAU,EACZ,MAAM,IAAI,MAAM,4BAA4B,EAG9C,OAAIA,IAAY,GACdF,EAAOzB,EACPA,EAAS7G,EAAO,QACPwI,IAAY,GAAK,CAAC3B,EAAO,aAClC7G,EAAOsI,EACPA,EAAOzB,EACPA,EAAS,QAGJ,IAAI,QAAQ,SAAU6B,EAASC,EAAQ,CAC5C,GAAI,CACF,MAAMtS,EAAO4R,GAAO,OAAOK,EAAMtI,CAAI,EACrC0I,EAAQL,EAAWhS,EAAMwQ,EAAQ7G,CAAI,CAAC,CAC9C,OAAejK,EAAG,CACV4S,EAAO5S,CAAC,CAChB,CACA,CAAK,CACL,CAEE,GAAI,CACF,MAAMM,EAAO4R,GAAO,OAAOK,EAAMtI,CAAI,EACrC6H,EAAG,KAAMQ,EAAWhS,EAAMwQ,EAAQ7G,CAAI,CAAC,CAC3C,OAAWjK,EAAG,CACV8R,EAAG9R,CAAC,CACR,CACA,CAEA6S,GAAA,OAAiBX,GAAO,OACxBW,GAAA,SAAmBR,GAAa,KAAK,KAAMF,GAAe,MAAM,EAChEU,GAAA,UAAoBR,GAAa,KAAK,KAAMF,GAAe,eAAe,EAG1EU,GAAA,SAAmBR,GAAa,KAAK,KAAM,SAAU/R,EAAMwS,EAAG7I,EAAM,CAClE,OAAOmI,GAAY,OAAO9R,EAAM2J,CAAI,CACtC,CAAC,ECzED,MAAM8I,GAA0B,GAC1BC,GAAuB,IACvBC,GAAuB,EAC7B,SAASC,GAAeC,EAAIC,EAASC,EAAU,CAC3C,OAAIF,IAAOC,EACA,IAEED,EAAKC,EAAU,EAAIA,EAAUD,EAAKA,EAAKC,IACrCC,EAAWN,EAC9B,CACA,SAASO,GAAUxS,EAAOkD,EAAsB,CAC5C,MAAMuP,EAAM,MAAM,UAAU,MAAM,KAAKC,GAAW,OAAO1S,EAAO,CAAE,qBAAAkD,CAAA,CAAsB,EAAE,QAAQ,KAAM,CAAC,EACnGyP,EAAO,KAAK,KAAKF,EAAI,MAAM,EACjC,OAAOA,EAAI,OAAO,CAACG,EAAM7d,EAAKyD,KAAWA,EAAQma,IAAS,EAAIC,EAAK,KAAK,CAAC7d,CAAG,CAAC,EAAI6d,EAAKA,EAAK,OAAS,CAAC,EAAE,KAAK7d,CAAG,IAAM6d,EAAM,CAAA,CAAE,CACjI,CACO,MAAMC,GAAa,CACtB,SAAS,CAAE,IAAAC,EAAK,KAAA/W,EAAM,SAAAgX,EAAU,SAAAC,EAAW,WAAa,CACpD,MAAMC,EAAY,cAEZC,EAAO,CAAA,EACP3G,EAASiG,GAAUM,EAAK,GAAG,EAC3BP,EAAWxW,EAAOwQ,EAAO,OACzB4G,EAAS,CACX,CAAE,EAAG,EAAG,EAAG,CAAA,EACX,CAAE,EAAG,EAAG,EAAG,CAAA,EACX,CAAE,EAAG,EAAG,EAAG,CAAA,CAAE,EAEjBA,EAAO,QAAQ,CAAC,CAAE,EAAA9P,EAAG,EAAAG,KAAQ,CACzB,MAAM4P,GAAM7G,EAAO,OAAS4F,IAAwBI,EAAWlP,EACzDgQ,GAAM9G,EAAO,OAAS4F,IAAwBI,EAAW/O,EACzDjO,EAAe,IACrB,QAASH,EAAI,EAAGA,EAAI+d,EAAO,OAAQ/d,GAAK,EAAG,CACvC,MAAMke,EAAUf,GAAYJ,GAAuB/c,EAAI,GACvD8d,EAAK,KAAKK;AAAAA;AAAAA,qBAELne,IAAM,EAAI4d,EAAWC,CAAS;AAAA,sBAC7B7d,IAAM,EAAIke,EAAU,EAAcA,CAAO;AAAA,oBAC3Cle,IAAM,GAAKke,EAAU,GAAe/d,EAAe+d,EAAU/d,CAAY;AAAA,oBACzEH,IAAM,GAAKke,EAAU,GAAe/d,EAAe+d,EAAU/d,CAAY;AAAA,uBACtEyd,CAAQ;AAAA,6BACF5d,IAAM,EAAI,EAAc,CAAC;AAAA,uBAC/BA,IAAM,EAAIke,EAAU,EAAcA,CAAO;AAAA,mBAC7Cle,IAAM,EAAIie,EAAKd,EAAWnd,EAAI,EAAc,EAAIie,EAAKd,EAAWnd,CAAC;AAAA,mBACjEA,IAAM,EAAIge,EAAKb,EAAWnd,EAAI,EAAc,EAAIge,EAAKb,EAAWnd,CAAC;AAAA;AAAA,WAEzE,CACC,CACJ,CAAC,EACD,MAAMoe,EAAiB,KAAK,OAAOT,EAAW,IAAMR,CAAQ,EACtDkB,EAAoBlH,EAAO,OAAS,EAAIiH,EAAiB,EACzDE,EAAkBnH,EAAO,OAAS,EAAIiH,EAAiB,EAAI,EAC3DG,EAAU,CAAA,EAChBpH,EAAO,QAAQ,CAAC9L,EAAKrL,IAAM,CACvBqL,EAAI,QAAQ,CAACuR,EAAG3Q,IAAM,CAClB,GAAIkL,EAAOnX,CAAC,EAAEiM,CAAC,GACP,EAAGjM,EAAI+c,IAAwB9Q,EAAI8Q,IAClC/c,EAAImX,EAAO,QAAU4F,GAAuB,IAAM9Q,EAAI8Q,IACtD/c,EAAI+c,IAAwB9Q,EAAIkL,EAAO,QAAU4F,GAAuB,KACrE,EAAE/c,EAAIqe,GACNre,EAAIse,GACJrS,EAAIoS,GACJpS,EAAIqS,GAAkB,CACtB,MAAME,EAAKxe,EAAImd,EAAWA,EAAW,EAC/BF,EAAKhR,EAAIkR,EAAWA,EAAW,EACrCoB,EAAQ,KAAK,CAACC,EAAIvB,CAAE,CAAC,CACzB,CAGZ,CAAC,CACL,CAAC,EACD,MAAMwB,EAAmB,CAAA,EACzB,OAAAF,EAAQ,QAAQ,CAAC,CAACC,EAAIvB,CAAE,IAAM,OACtBwB,EAAiBD,CAAE,GACnBvb,EAAAwb,EAAiBD,CAAE,IAAnB,MAAAvb,EAAsB,KAAKga,GAG3BwB,EAAiBD,CAAE,EAAI,CAACvB,CAAE,CAElC,CAAC,EACD,OAAO,QAAQwB,CAAgB,EAC1B,IAAI,CAAC,CAACD,EAAIE,CAAG,IAAM,CACpB,MAAMC,EAASD,EAAI,OAAOzB,GAAMyB,EAAI,MAAMxB,GAAW,CAACF,GAAeC,EAAIC,EAASC,CAAQ,CAAC,CAAC,EAC5F,MAAO,CAAC,OAAOqB,CAAE,EAAGG,CAAM,CAC9B,CAAC,EACI,QAAQ,CAAC,CAACH,EAAIE,CAAG,IAAM,CACxBA,EAAI,QAAQzB,GAAM,CACda,EAAK,KAAKK,gBAAkBK,CAAE,OAAOvB,CAAE,SAASW,CAAQ,MAAMT,EAAWL,EAAoB,KAAK,CACtG,CAAC,CACL,CAAC,EACD,OAAO,QAAQ2B,CAAgB,EAC1B,OAAO,CAAC,CAAC7B,EAAG8B,CAAG,IAAMA,EAAI,OAAS,CAAC,EACnC,IAAI,CAAC,CAACF,EAAIE,CAAG,IAAM,CACpB,MAAMC,EAASD,EAAI,OAAOzB,GAAMyB,EAAI,KAAKxB,GAAWF,GAAeC,EAAIC,EAASC,CAAQ,CAAC,CAAC,EAC1F,MAAO,CAAC,OAAOqB,CAAE,EAAGG,CAAM,CAC9B,CAAC,EACI,IAAI,CAAC,CAACH,EAAIE,CAAG,IAAM,CACpBA,EAAI,KAAK,CAACxK,EAAGC,IAAOD,EAAIC,EAAI,GAAK,CAAE,EACnC,MAAMyK,EAAS,CAAA,EACf,UAAW3B,KAAMyB,EAAK,CAClB,MAAM3M,EAAQ6M,EAAO,KAAKvK,GAAQA,EAAK,KAAK6I,GAAWF,GAAeC,EAAIC,EAASC,CAAQ,CAAC,CAAC,EACzFpL,EACAA,EAAM,KAAKkL,CAAE,EAGb2B,EAAO,KAAK,CAAC3B,CAAE,CAAC,CAExB,CACA,MAAO,CAACuB,EAAII,EAAO,OAAY,CAACvK,EAAK,CAAC,EAAGA,EAAKA,EAAK,OAAS,CAAC,CAAC,CAAC,CAAC,CACpE,CAAC,EACI,QAAQ,CAAC,CAACmK,EAAII,CAAM,IAAM,CAC3BA,EAAO,QAAQ,CAAC,CAACX,EAAIY,CAAE,IAAM,CACzBf,EAAK,KAAKK;AAAAA;AAAAA,qBAELK,CAAE;AAAA,qBACFA,CAAE;AAAA,qBACFP,CAAE;AAAA,qBACFY,CAAE;AAAA,yBACEjB,CAAQ;AAAA,+BACFT,GAAYL,GAAuB,EAAE;AAAA;AAAA;AAAA,aAGvD,CACD,CAAC,CACL,CAAC,EACMgB,CACX,CACJ,EC/HAgB,GAAevf;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,MAAMif,GAAqB,UAC3B,IAAIC,EAAY,cAAwB9e,CAAW,CAC/C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,IAAM,GACX,KAAK,KAAO,EACZ,KAAK,MAAQ,OACb,KAAK,SAAW,OAChB,KAAK,IAAM,OACX,KAAK,WAAa,OAClB,KAAK,UAAY,MACrB,CACA,QAAS,CACL,YAAK,QAAQ,MAAW,KAAK,MAC7B,KAAK,QAAQ,MAAW,OAAO,KAAK,UAAU,EAC9C,KAAK,MAAM,QAAU;AAAA,qBACR,KAAK,IAAI;AAAA,2BACH,KAAK,OAAS6e,EAAkB;AAAA,MAE5C3e,IAAQ,KAAK,eAAA,CAAgB,IAAI,KAAK,aAAa,EAC9D,CACA,aAAc,CACV,MAAMuG,EAAO,KAAK,QAAU,QAAU,KAAK,KAAO,KAAK,KAAO,GAC9D,OAAOwX;AAAAA,oBACKxX,CAAI,UAAUA,CAAI;AAAA,UAC5B8W,GAAW,SAAS,CAClB,IAAK,KAAK,IACV,KAAA9W,EACA,SAAU,KAAK,WAAa,EAAIA,EAAO,EACvC,SAAU,KAAK,KAAA,CAClB,CAAC;AAAA;AAAA,KAGN,CACA,gBAAiB,CACb,OAAI,KAAK,SACEvG,mBAAuB,KAAK,QAAQ,QAAQ,KAAK,KAAO,MAAM,gBAErE,KAAK,UACEA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,oBAOJA,4EACX,CACJ,EACA4e,EAAU,OAAS,CAAC1e,EAAaC,EAAM,EACvCf,GAAW,CACPgB,EAAA,CACJ,EAAGwe,EAAU,UAAW,MAAO,MAAM,EACrCxf,GAAW,CACPgB,EAAS,CAAE,KAAM,MAAA,CAAQ,CAC7B,EAAGwe,EAAU,UAAW,OAAQ,MAAM,EACtCxf,GAAW,CACPgB,EAAA,CACJ,EAAGwe,EAAU,UAAW,QAAS,MAAM,EACvCxf,GAAW,CACPgB,EAAA,CACJ,EAAGwe,EAAU,UAAW,WAAY,MAAM,EAC1Cxf,GAAW,CACPgB,EAAA,CACJ,EAAGwe,EAAU,UAAW,MAAO,MAAM,EACrCxf,GAAW,CACPgB,EAAA,CACJ,EAAGwe,EAAU,UAAW,QAAS,MAAM,EACvCxf,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGwe,EAAU,UAAW,aAAc,MAAM,EAC5Cxf,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGwe,EAAU,UAAW,YAAa,MAAM,EAC3CA,EAAYxf,GAAW,CACnBiB,EAAc,aAAa,CAC/B,EAAGue,CAAS,ECzFZ,MAAAC,GAAe1f;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAKA,IAAIof,GAAa,cAAyBhf,CAAW,CACjD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,aAAe,IACpB,KAAK,QAAU,SACnB,CACA,QAAS,CACL,YAAK,MAAM,QAAU;AAAA,eACd,KAAK,KAAK;AAAA,gBACT,KAAK,MAAM;AAAA,uBACJ,qCAAqC,KAAK,YAAY,UAAU;AAAA,MAExEE,gBACX,CACJ,EACA8e,GAAW,OAAS,CAAC3e,EAAM,EAC3Bf,GAAW,CACPgB,EAAA,CACJ,EAAG0e,GAAW,UAAW,QAAS,MAAM,EACxC1f,GAAW,CACPgB,EAAA,CACJ,EAAG0e,GAAW,UAAW,SAAU,MAAM,EACzC1f,GAAW,CACPgB,EAAA,CACJ,EAAG0e,GAAW,UAAW,eAAgB,MAAM,EAC/C1f,GAAW,CACPgB,EAAA,CACJ,EAAG0e,GAAW,UAAW,UAAW,MAAM,EAC1CA,GAAa1f,GAAW,CACpBiB,EAAc,aAAa,CAC/B,EAAGye,EAAU,ECxCN,MAAMC,GAAY,oBCDzBC,GAAe7f;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAIuf,GAAe,cAA2Bnf,CAAW,CACrD,QAAS,CACL,OAAOE;AAAAA;AAAAA;AAAAA,eAGA+e,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBASH,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAOrC,CACJ,EACAE,GAAa,OAAS,CAAC/e,EAAaD,EAAeE,EAAM,EACzD8e,GAAe7f,GAAW,CACtBiB,EAAc,iBAAiB,CACnC,EAAG4e,EAAY,ECvCf,MAAAC,GAAe/f;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAeA,IAAIyf,GAAwB,cAAoC/W,CAAoB,CAChF,aAAc,OACV,MAAA,EACA,KAAK,YAAc,IAAM,CACrB,KAAK,cAAA,CACT,EACA,OAAO,iBAAiB,SAAU,KAAK,WAAW,EAClDvG,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,OAAMgB,EAAA,KAAK,SAAL,YAAAA,EAAa,OAAQ,gBAAiB,SAAU,QAAA,CAAS,CAChF,CACL,CACA,sBAAuB,OACnB,MAAM,qBAAA,GACNA,EAAA,KAAK,cAAL,MAAAA,EAAkB,QAAQuc,GAASA,EAAA,GACnC,OAAO,oBAAoB,SAAU,KAAK,WAAW,CACzD,CACA,QAAS,CACL,YAAK,cAAA,EACEpf;AAAAA;AAAAA;AAAAA;AAAAA,mBAII,CAAC,IAAK,KAAM,KAAM,IAAI,CAAC;AAAA;AAAA;AAAA,sDAGY,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,UAKjE,KAAK,cAAc;AAAA;AAAA,2CAEc,KAAK,MAAM;AAAA,KAElD,CACA,eAAgB,CACR,CAAC,KAAK,OAAS,KAAK,MACpB,KAAK,QAAU,WAAW,IAAM,CAC5B,KAAK,MAAQ,EACjB,EAAG,GAAG,EAEd,CACA,gBAAiB,CACb,GAAI,CAAC,KAAK,KAAO,CAAC,KAAK,MACnB,OAAO,KAEX,MAAMuG,EAAO,KAAK,sBAAA,EAAwB,MAAQ,GAC5C8Y,EAAM,KAAK,OAAS,KAAK,OAAO,KAAO,OAC7C,OAAA/c,EAAqB,aAAa,MAAS,EAC3CA,EAAqB,gBAAgB,KAAK,MAAM,EACzCtC;AAAAA,aACFuG,CAAI;AAAA,cACHsC,GAAgB,MAAM,SAAS;AAAA,YACjC,KAAK,GAAG;AAAA,iBACHjI,EAAUwB,EAAU,eAAe,KAAK,MAAM,CAAC,CAAC;AAAA,cACnDxB,EAAUiI,GAAgB,MAAM,eAAe,gBAAgB,CAAC,CAAC;AAAA,YACnEjI,EAAUye,CAAG,CAAC;AAAA;AAAA,oBAGtB,CACA,cAAe,CACX,MAAMC,EAAW,CAAC,KAAK,KAAO,CAAC,KAAK,MACpC,OAAOtf;AAAAA,kBACGsf,CAAQ;AAAA,eACX,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAOzB,CACJ,EACAH,GAAsB,OAAShf,GAC/Bgf,GAAwB/f,GAAW,CAC/BiB,EAAc,0BAA0B,CAC5C,EAAG8e,EAAqB,EClGxB,IAAI/f,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAI6f,GAA6B,cAAyCzf,CAAW,CACjF,aAAc,OAGV,GAFA,MAAA,EACA,KAAK,QAAS+C,EAAAf,EAAiB,MAAM,OAAvB,YAAAe,EAA6B,OACvC,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,mDAAmD,EAEvEhB,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,KAAM,KAAK,OAAO,KAAM,SAAU,SAAA,CAAU,CAC7D,CACL,CACA,QAAS,CACL,OAAO7B;AAAAA;AAAAA;AAAAA;AAAAA,mBAII,CAAC,MAAO,KAAM,KAAM,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKvBY,EAAUwB,EAAU,eAAe,KAAK,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAM1B,KAAK,MAAM;AAAA,KAElD,CACJ,EACAmd,GAA6BngB,GAAW,CACpCiB,EAAc,+BAA+B,CACjD,EAAGkf,EAA0B,ECjD7B,IAAIngB,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAKA,IAAI8f,GAAqB,cAAiCpX,CAAoB,CAC1E,aAAc,CAGV,GAFA,MAAA,EACA,KAAK,UAAY,GACb,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,2CAA2C,EAE/D,KAAK,UAAY,KAAK,eAAe,KAAK,IAAI,EAC9C,KAAK,kBAAoB,OACzB,KAAK,eAAiBjF,GAAc,eAAe,OACnD,KAAK,iBAAmB,eACxB,KAAK,mBAAA,EACL,KAAK,YAAY,KAAKb,EAAqB,aAAa,QAAS,IAAM,CACnE,KAAK,mBAAA,CACT,CAAC,CAAC,EACFT,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,KAAM,KAAK,OAAO,KAAM,SAAU,KAAA,CAAM,CACzD,CACL,CACA,oBAAqB,CACjB,KAAK,UAAY,CAAC,KAAK,GAC3B,CACA,gBAAiB,OACb,IAAIgB,EAAA,KAAK,SAAL,MAAAA,EAAa,aAAe,KAAK,IACjC,GAAI,CACA,KAAK,MAAQ,GACb,KAAM,CAAE,YAAA4c,EAAa,KAAAjY,CAAA,EAAS,KAAK,OAC7B,CAAE,SAAA4B,EAAU,KAAAC,GAAS9H,EAAe,mBAAmBke,EAAa,KAAK,GAAG,EAClFnd,EAAqB,aAAa,CAAE,KAAAkF,EAAM,KAAA6B,CAAA,CAAM,EAChD/G,EAAqB,gBAAgB,KAAK,MAAM,EAChDf,EAAe,SAAS6H,EAAU,QAAQ,CAC9C,MACM,CACF,KAAK,MAAQ,EACjB,CAER,CACJ,EACAhK,GAAW,CACP2C,EAAA,CACJ,EAAGyd,GAAmB,UAAW,YAAa,MAAM,EACpDA,GAAqBpgB,GAAW,CAC5BiB,EAAc,uBAAuB,CACzC,EAAGmf,EAAkB,ECvDrB,IAAIpgB,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAYA,IAAIggB,GAAsB,cAAkC5f,CAAW,CACnE,aAAc,OACV,MAAA,EACA,KAAK,QAAS+C,EAAAf,EAAiB,MAAM,OAAvB,YAAAe,EAA6B,OAC3C,KAAK,YAAc,CAAA,EACnB,KAAK,SAAW,OAChB,KAAK,UAAY,CAAA,EACjB,KAAK,cAAgB,EAAQvB,EAAkB,MAAM,KACrD,KAAK,eAAiBA,EAAkB,MAAM,eAC9C,KAAK,mBAAA,EACL,KAAK,qBAAA,EACL,KAAK,YAAY,KAAKA,EAAkB,aAAa,iBAAkBJ,GAAQ,KAAK,eAAiBA,CAAI,CAAC,CAC9G,CACA,sBAAuB,CACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,CACzD,CACA,QAAS,CACL,OAAOnB;AAAAA,QACP,KAAK,gBAAgB;AAAA,aAChB,KAAK,kBAAkB;AAAA,QAC5B,KAAK,uBAAuB;AAAA,KAEhC,CACA,uBAAwB,OACpB,OAAK6C,EAAA,KAAK,iBAAL,MAAAA,EAAqB,cAGnB7C,uCAFI,IAGf,CACA,MAAM,qBAAqB2f,EAAQ,GAAO,CACtC,GAAI,OAAK,WAAa,WAAcre,EAAkB,MAAM,iBAAmB,CAACqe,GAGhF,GAAI,CACA,KAAM,CAAE,gBAAAC,EAAiB,OAAAC,CAAA,EAAWvd,EAAqB,OACrDqd,GACAre,EAAkB,MAAM,gBACxBC,EAAe,iBAAiBqe,CAAe,GAC/CC,IAAW,gBACX,MAAMvd,EAAqB,qBAAA,EACtB,KAAK,eACN0G,GAAgB,MAAA,EAG5B,OACOC,EAAO,CACVpH,EAAiB,UAAU,CACvB,KAAM,QACN,MAAO,gBACP,WAAY,CAAE,SAASoH,GAAA,YAAAA,EAAO,UAAW,SAAA,CAAU,CACtD,EACD3G,EAAqB,WAAW,EAAI,EACpCwG,GAAgB,UAAUG,EAAM,SAAW,kBAAkB,EAC7D3G,EAAqB,kBAAA,EACrBR,EAAiB,OAAA,CACrB,CACJ,CACA,oBAAqB,CACjB,GAAI,CAAC,KAAK,OAAQ,CACd,KAAK,UAAU,KAAK,QAAQ,EAC5B,KAAK,SAAW,SAChB,MACJ,CACA,GAAI,KAAK,SACL,OAEJ,KAAM,CAAE,YAAAyH,EAAa,aAAAJ,EAAc,YAAAsW,EAAa,SAAA5a,EAAU,KAAAib,CAAA,EAAS,KAAK,OAClEC,EAAclb,GAAA,YAAAA,EAAU,IAAI,CAAC,CAAE,YAAAmb,KAAkBA,GAAa,OAAO,SACrEC,EAAa,CAAC,GAAIH,EAAO,CAACA,CAAI,EAAKC,GAAe,EAAI,EACtDG,EAAY5e,EAAkB,MAAM,oBAAsB,GAAQ2e,EAAW,OAC7EE,EAAkB5W,EAClB6W,EAAUX,EACVY,EAAqB/d,EAAqB,eAAe2d,CAAU,EACnEK,EAAcJ,GAAaG,EAC3BE,EAAcpX,GAAgB,CAAC5H,EAAe,SAAA,EAChD+e,GAAe,CAACzc,GAAgB,MAAM,YACtC,KAAK,UAAU,KAAK,SAAS,EAE7Bsc,GACA,KAAK,UAAU,KAAK5e,EAAe,SAAA,EAAa,SAAW,QAAQ,EAEnE6e,GACA,KAAK,UAAU,KAAK,KAAK,EAEzBG,GACA,KAAK,UAAU,KAAK,SAAS,EAE7B,CAACD,GAAeJ,GAAa,CAACrc,GAAgB,MAAM,YACpD,KAAK,UAAU,KAAK,aAAa,EAErC,KAAK,SAAW,KAAK,UAAU,CAAC,CACpC,CACA,kBAAmB,CACf,OAAQ,KAAK,SAAA,CACT,IAAK,UACD,OAAO7D,2DACX,IAAK,MACD,OAAOA,mDACX,IAAK,UACD,OAAOA;AAAAA,gDACyB,IAAM,KAAK,qBAAqB,EAAI,CAAC;AAAA;AAAA,UAGzE,IAAK,SACD,OAAOA;AAAAA,wDACiC,IAAM,KAAK,qBAAqB,EAAI,CAAC;AAAA;AAAA,UAGjF,IAAK,SACD,OAAOA,yDACX,QACI,OAAOA,kEAAA,CAEnB,CACA,gBAAiB,CAEb,OADsB,KAAK,UAAU,OAAS,EAIvCA;AAAAA;AAAAA,qBAEM,KAAK,SAAS;AAAA,4BACP,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,MALzC,IASf,CACA,MAAM,iBAAiBgG,EAAU,OAC7B,MAAMwa,GAAY3d,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,OAC7C2d,IACA,MAAMA,EAAU,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CACtD,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAAE,SACH,KAAK,SAAWxa,EAChBwa,EAAU,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CAChD,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAET,CACJ,EACAphB,GAAW,CACP2C,EAAA,CACJ,EAAG2d,GAAoB,UAAW,WAAY,MAAM,EACpDtgB,GAAW,CACP2C,EAAA,CACJ,EAAG2d,GAAoB,UAAW,YAAa,MAAM,EACrDtgB,GAAW,CACP2C,EAAA,CACJ,EAAG2d,GAAoB,UAAW,gBAAiB,MAAM,EACzDtgB,GAAW,CACP2C,EAAA,CACJ,EAAG2d,GAAoB,UAAW,iBAAkB,MAAM,EAC1DA,GAAsBtgB,GAAW,CAC7BiB,EAAc,wBAAwB,CAC1C,EAAGqf,EAAmB,EC/KtB,IAAItgB,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAI+gB,GAA2B,cAAuC3gB,CAAW,CAC7E,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,SAAWyB,EAAe,SAAA,CACnC,CACA,QAAS,CACL,GAAI,KAAK,SAAU,CACf,KAAM,CAAE,SAAAyD,EAAU,YAAAD,CAAA,EAAgB9D,EAAc,MAC1C,CAAE,cAAAsB,GAAkBjB,EAAkB,MACtCoB,EAASC,GAAY,iBAAA,EACrB+d,EAAiB1b,EAAS,QAAUD,EAAY,SAAUxC,GAAA,YAAAA,EAAe,SAAUG,EAAO,OAChG,OAAO1C;AAAAA;AAAAA;AAAAA,kBAGD,CAAC,MAAO,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA,UAE9B0gB,EAAiB1gB,6CAAmD,IAAI;AAAA;AAAA,kBAG1E,CACA,OAAOA,8CAAkD,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA,kDAEnC,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA,iBAGlE,CACJ,EACAZ,GAAW,CACP2C,EAAA,CACJ,EAAG0e,GAAyB,UAAW,WAAY,MAAM,EACzDA,GAA2BrhB,GAAW,CAClCiB,EAAc,8BAA8B,CAChD,EAAGogB,EAAwB,EC7C3B;AAAA;AAAA;AAAA;AAAA,GAIG,MAAM/W,GAAE,IAAI,IAAIiX,GAAE,MAAMA,EAAC,CAAA,CAAE,MAAMC,GAAE,IAAI,QAAQ7S,GAAE6E,GAAE,cAAchT,EAAC,CAAC,OAAOA,EAAE,CAAC,OAAOiU,EAAC,CAAC,OAAOjU,EAAE,CAACgT,CAAC,EAAE,OAAC,MAAMlJ,EAAEkJ,IAAI,KAAK,EAAE,OAAOlJ,GAAY,KAAK,IAAd,QAAiB,KAAK,GAAG,MAAM,GAAGA,GAAG,KAAK,KAAK,KAAK,MAAM,KAAK,EAAEkJ,EAAE,KAAK,IAAG/P,EAAAjD,EAAE,UAAF,YAAAiD,EAAW,KAAK,KAAK,GAAG,KAAK,GAAGjD,EAAE,OAAO,GAAGiU,EAAC,CAAC,GAAGA,EAAE,CAAC,GAAG,KAAK,cAAcA,EAAE,QAAoB,OAAO,KAAK,GAAxB,WAA0B,CAAC,MAAMjU,EAAE,KAAK,IAAI,WAAW,IAAIgT,EAAEgO,GAAE,IAAIhhB,CAAC,EAAWgT,IAAT,SAAaA,EAAE,IAAI,QAAQgO,GAAE,IAAIhhB,EAAEgT,CAAC,GAAYA,EAAE,IAAI,KAAK,CAAC,IAArB,QAAwB,KAAK,EAAE,KAAK,KAAK,GAAG,MAAM,EAAEA,EAAE,IAAI,KAAK,EAAEiB,CAAC,EAAWA,IAAT,QAAY,KAAK,EAAE,KAAK,KAAK,GAAGA,CAAC,CAAC,MAAM,KAAK,EAAE,MAAMA,CAAC,CAAC,IAAI,IAAI,SAAC,OAAkB,OAAO,KAAK,GAAxB,YAA0BhR,EAAA+d,GAAE,IAAI,KAAK,IAAI,UAAU,IAAzB,YAAA/d,EAA4B,IAAI,KAAK,IAAGwF,EAAA,KAAK,IAAL,YAAAA,EAAQ,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,ECJ5rBwY,GAAe1hB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAIohB,GAAY,cAAwBhhB,CAAW,CAC/C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,gBAAkBihB,GAAA,EACvB,KAAK,QAAU,MACnB,CACA,QAAS,CACL,OAAO/gB;AAAAA;AAAAA;AAAAA,YAGHghB,GAAI,KAAK,eAAe,CAAC;AAAA;AAAA,qBAEhBpgB,EAAU,KAAK,OAAO,CAAC;AAAA,oBACxB,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,KAKnD,CACA,qBAAsB,OAClB,KAAK,cAAc,IAAI,YAAY,eAAgB,CAC/C,QAAQiC,EAAA,KAAK,gBAAgB,QAArB,YAAAA,EAA4B,QACpC,QAAS,GACT,SAAU,EAAA,CACb,CAAC,CACN,CACJ,EACAie,GAAU,OAAS,CAAC5gB,EAAaD,EAAeghB,GAAa9gB,EAAM,EACnEf,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAG0gB,GAAU,UAAW,UAAW,MAAM,EACzCA,GAAY1hB,GAAW,CACnBiB,EAAc,YAAY,CAC9B,EAAGygB,EAAS,EC7CZ,MAAAI,GAAe/hB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EASA,IAAIyhB,GAAqB,cAAiCrhB,CAAW,CACjE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,QAAU,MACnB,CACA,QAAS,CACL,OAAOE;AAAAA;AAAAA;AAAAA,+BAGgBY,EAAU,KAAK,OAAO,CAAC;AAAA;AAAA,KAGlD,CACJ,EACAugB,GAAmB,OAAS,CAACjhB,EAAaD,EAAeE,EAAM,EAC/Df,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAG+gB,GAAmB,UAAW,UAAW,MAAM,EAClDA,GAAqB/hB,GAAW,CAC5BiB,EAAc,sBAAsB,CACxC,EAAG8gB,EAAkB,ECjCrB,MAAAC,GAAejiB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAI2hB,GAAkB,cAA8BvhB,CAAW,CAC3D,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,MAChB,CACA,QAAS,CACL,OAAOE;AAAAA;AAAAA,oDAEqC,KAAK,IAAI;AAAA;AAAA,KAGzD,CACJ,EACAqhB,GAAgB,OAAS,CAACnhB,EAAaD,EAAeE,EAAM,EAC5Df,GAAW,CACPgB,EAAA,CACJ,EAAGihB,GAAgB,UAAW,OAAQ,MAAM,EAC5CA,GAAkBjiB,GAAW,CACzBiB,EAAc,mBAAmB,CACrC,EAAGghB,EAAe,EC9BlB,MAAAC,GAAeniB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAUA,IAAI6hB,EAAe,cAA2BzhB,CAAW,CACrD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,gBAAkBihB,GAAA,EACvB,KAAK,KAAO,KACZ,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,KAAO,OACZ,KAAK,MAAQ,EACjB,CACA,QAAS,CACL,MAAMS,EAAa,qBAAqB,KAAK,iBAAiB,GAExDC,EAAU,CACZ,CAFc,YAAY,KAAK,IAAI,EAEzB,EAAG,GACb,CAACD,CAAU,EAAG,EAAQ,KAAK,iBAAiB,EAEhD,OAAOxhB,IAAQ,KAAK,aAAA,CAAc;AAAA;AAAA;AAAA,UAGhCghB,GAAI,KAAK,eAAe,CAAC;AAAA,gBACnBU,GAASD,CAAO,CAAC;AAAA,eAClB,KAAK,IAAI;AAAA,uBACD7gB,EAAU,KAAK,YAAY,CAAC;AAAA,oBAC/B,KAAK,QAAQ;AAAA,sBACX,KAAK,WAAW;AAAA,iBACrB,KAAK,yBAAyB,KAAK,IAAI,CAAC;AAAA,iBACxC,KAAK,OAAS,EAAE;AAAA,mBACdA,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA,oBAGrC,CACA,cAAe,CACX,OAAI,KAAK,KACEZ;AAAAA,qBACE,KAAK,IAAI;AAAA,eACf,KAAK,IAAI;AAAA;AAAA,eAET,KAAK,IAAI;AAAA,oBAGT,IACX,CACA,0BAA2B,OACvB,KAAK,cAAc,IAAI,YAAY,cAAe,CAC9C,QAAQ6C,EAAA,KAAK,gBAAgB,QAArB,YAAAA,EAA4B,MACpC,QAAS,GACT,SAAU,EAAA,CACb,CAAC,CACN,CACJ,EACA0e,EAAa,OAAS,CAACrhB,EAAaD,EAAeE,EAAM,EACzDf,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,OAAQ,MAAM,EACzCniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,OAAQ,MAAM,EACzCniB,GAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGmhB,EAAa,UAAW,WAAY,MAAM,EAC7CniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,cAAe,MAAM,EAChDniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,OAAQ,MAAM,EACzCniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,UAAW,MAAM,EAC5CniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,QAAS,MAAM,EAC1CniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,oBAAqB,MAAM,EACtDniB,GAAW,CACPgB,EAAA,CACJ,EAAGmhB,EAAa,UAAW,SAAU,MAAM,EAC3CA,EAAeniB,GAAW,CACtBiB,EAAc,gBAAgB,CAClC,EAAGkhB,CAAY,EC/Ff,MAAAI,GAAexiB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAIkiB,GAAe,cAA2B9hB,CAAW,CACrD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,kBAAoBihB,GAAA,CAC7B,CACA,QAAS,CACL,OAAO/gB;AAAAA;AAAAA,UAELghB,GAAI,KAAK,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAOD,KAAK,UAAU;AAAA;AAAA,KAG/C,CACA,YAAa,CACT,MAAMa,EAAiB,KAAK,kBAAkB,MACxCC,EAAeD,GAAA,YAAAA,EAAgB,gBAAgB,MACjDC,IACAA,EAAa,MAAQ,GACrBA,EAAa,MAAA,EACbA,EAAa,cAAc,IAAI,MAAM,OAAO,CAAC,EAErD,CACJ,EACAF,GAAa,OAAS,CAAC1hB,EAAaC,EAAM,EAC1CyhB,GAAexiB,GAAW,CACtBiB,EAAc,gBAAgB,CAClC,EAAGuhB,EAAY,EC5CR,MAAMG,GAAehE;AAAAA;AAAAA;AAAAA;AAAAA,QCA5BiE,GAAe7iB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAQA,IAAIuiB,GAAsB,cAAkCniB,CAAW,CACnE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,KAAO,QAChB,CACA,QAAS,CACL,OAAOE;AAAAA,QACP,KAAK,iBAAiB;AAAA;AAAA,KAG1B,CACA,iBAAkB,CACd,OAAI,KAAK,OAAS,UACPA;AAAAA,sBACG,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAKrB+hB,EAAY,GAEP/hB,2EACX,CACJ,EACAiiB,GAAoB,OAAS,CAAC/hB,EAAaD,EAAeE,EAAM,EAChEf,GAAW,CACPgB,EAAA,CACJ,EAAG6hB,GAAoB,UAAW,OAAQ,MAAM,EAChDA,GAAsB7iB,GAAW,CAC7BiB,EAAc,wBAAwB,CAC1C,EAAG4hB,EAAmB,EC1CtB,MAAAC,GAAe/iB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAIyiB,EAAU,cAAsBriB,CAAW,CAC3C,QAAS,CACL,YAAK,MAAM,QAAU;AAAA,4BACD,KAAK,gBAAgB;AAAA,+BAClB,KAAK,mBAAmB;AAAA,uBAChC,KAAK,YAAY;AAAA,qBACnB,KAAK,UAAU;AAAA,yBACX,KAAK,cAAc;AAAA,uBACrB,KAAK,YAAY;AAAA,oBACpB,KAAK,WAAa,qBAAqB,KAAK,SAAS,GAAG;AAAA,iBAC3D,KAAK,QAAU,qBAAqB,KAAK,MAAM,GAAG;AAAA,aACtD,KAAK,KAAO,qBAAqB,KAAK,GAAG,GAAG;AAAA,qBACpC,KAAK,SAAWoI,GAAa,iBAAiB,KAAK,QAAS,CAAC,CAAC;AAAA,uBAC5D,KAAK,SAAWA,GAAa,iBAAiB,KAAK,QAAS,CAAC,CAAC;AAAA,wBAC7D,KAAK,SAAWA,GAAa,iBAAiB,KAAK,QAAS,CAAC,CAAC;AAAA,sBAChE,KAAK,SAAWA,GAAa,iBAAiB,KAAK,QAAS,CAAC,CAAC;AAAA,oBAChE,KAAK,QAAUA,GAAa,iBAAiB,KAAK,OAAQ,CAAC,CAAC;AAAA,sBAC1D,KAAK,QAAUA,GAAa,iBAAiB,KAAK,OAAQ,CAAC,CAAC;AAAA,uBAC3D,KAAK,QAAUA,GAAa,iBAAiB,KAAK,OAAQ,CAAC,CAAC;AAAA,qBAC9D,KAAK,QAAUA,GAAa,iBAAiB,KAAK,OAAQ,CAAC,CAAC;AAAA,MAElElI,gBACX,CACJ,EACAmiB,EAAQ,OAAS,CAACjiB,EAAaC,EAAM,EACrCf,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,mBAAoB,MAAM,EAChD/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,sBAAuB,MAAM,EACnD/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,eAAgB,MAAM,EAC5C/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,aAAc,MAAM,EAC1C/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,iBAAkB,MAAM,EAC9C/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,eAAgB,MAAM,EAC5C/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,YAAa,MAAM,EACzC/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,SAAU,MAAM,EACtC/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,MAAO,MAAM,EACnC/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,UAAW,MAAM,EACvC/iB,EAAW,CACPgB,EAAA,CACJ,EAAG+hB,EAAQ,UAAW,SAAU,MAAM,EACtCA,EAAU/iB,EAAW,CACjBiB,EAAc,UAAU,CAC5B,EAAG8hB,CAAO,ECvEV,MAAAC,GAAejjB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAYA,IAAI2iB,GAAwB,cAAoCviB,CAAW,CACvE,aAAc,CACV,MAAA,EACA,KAAK,SAAW,IAAI,qBAAqB,IAAA,EAAe,EACxD,KAAK,QAAU,GACf,KAAK,SAAW,OAChB,KAAK,aAAe,GACpB,KAAK,OAAS,OACd,KAAK,SAAW,IAAI,qBAAqBwiB,GAAW,CAChDA,EAAQ,QAAQC,GAAS,CACjBA,EAAM,gBACN,KAAK,QAAU,GACf,KAAK,cAAA,GAGL,KAAK,QAAU,EAEvB,CAAC,CACL,EAAG,CAAE,UAAW,IAAM,CAC1B,CACA,cAAe,CACX,KAAK,SAAS,QAAQ,IAAI,CAC9B,CACA,sBAAuB,CACnB,KAAK,SAAS,WAAA,CAClB,CACA,QAAS,SACL,MAAMC,IAAY3f,EAAA,KAAK,SAAL,YAAAA,EAAa,cAAe,YAC9C,OAAO7C;AAAAA;AAAAA,UAEL,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,oBAKVY,EAAU4hB,EAAY,YAAc,MAAS,CAAC;AAAA,gBACnDna,EAAA,KAAK,SAAL,YAAAA,EAAa,IAAI;AAAA;AAAA,YAEpBma,EAAYxiB,6DAAmE,IAAI;AAAA;AAAA;AAAA,KAI3F,CACA,eAAgB,SACZ,MAAK,CAAC,KAAK,SAAW,CAAC,KAAK,UAAa,KAAK,aACnC,KAAK,gBAAA,EAETA;AAAAA;AAAAA;AAAAA,mBAGIY,EAAU,KAAK,QAAQ,CAAC;AAAA,gBAC5BiC,EAAA,KAAK,SAAL,YAAAA,EAAa,IAAI;AAAA,sBACXwF,EAAA,KAAK,SAAL,YAAAA,EAAa,SAAS;AAAA;AAAA;AAAA;AAAA,KAKvC,CACA,iBAAkB,CACd,OAAOrI,2EACX,CACA,MAAM,eAAgB,CACb,KAAK,SAGV,KAAK,SAAWoC,EAAU,eAAe,KAAK,MAAM,EAChD,MAAK,WAGT,KAAK,aAAe,GACpB,KAAK,SAAW,MAAMA,EAAU,iBAAiB,KAAK,OAAO,QAAQ,EACrE,KAAK,aAAe,IACxB,CACJ,EACAigB,GAAsB,OAASliB,GAC/Bf,GAAW,CACP2C,EAAA,CACJ,EAAGsgB,GAAsB,UAAW,UAAW,MAAM,EACrDjjB,GAAW,CACP2C,EAAA,CACJ,EAAGsgB,GAAsB,UAAW,WAAY,MAAM,EACtDjjB,GAAW,CACP2C,EAAA,CACJ,EAAGsgB,GAAsB,UAAW,eAAgB,MAAM,EAC1DjjB,GAAW,CACPgB,EAAA,CACJ,EAAGiiB,GAAsB,UAAW,SAAU,MAAM,EACpDA,GAAwBjjB,GAAW,CAC/BiB,EAAc,2BAA2B,CAC7C,EAAGgiB,EAAqB,ECzGxB,MAAAI,GAAetjB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAWA,MAAMgjB,GAAe,kBACrB,IAAIC,GAAoB,cAAgC7iB,CAAW,CAC/D,aAAc,CACV,MAAA,EACA,KAAK,YAAc,CAAA,EACnB,KAAK,mBAAqB,OAC1B,KAAK,QAAU,CAACmB,EAAc,MAAM,QAAQ,OAC5C,KAAK,QAAUA,EAAc,MAAM,QACnC,KAAK,YAAcA,EAAc,MAAM,YACvC,KAAK,SAAWA,EAAc,MAAM,SACpC,KAAK,gBAAkBA,EAAc,MAAM,gBAC3C,KAAK,YAAY,KACbA,EAAc,aAAa,UAAWC,GAAQ,KAAK,QAAUA,CAAI,EACjED,EAAc,aAAa,cAAeC,GAAQ,KAAK,YAAcA,CAAI,EACzED,EAAc,aAAa,WAAYC,GAAQ,KAAK,SAAWA,CAAI,EACnED,EAAc,aAAa,kBAAmBC,GAAQ,KAAK,gBAAkBA,CAAI,CACpF,CACL,CACA,cAAe,CACX,KAAK,aAAA,EACL,KAAK,yBAAA,CACT,CACA,sBAAuB,OACnB,KAAK,YAAY,QAAQC,GAAeA,EAAA,CAAa,GACrD0B,EAAA,KAAK,qBAAL,MAAAA,EAAyB,YAC7B,CACA,QAAS,CACL,OAAO7C;AAAAA;AAAAA,sBAEO,CAAC,KAAK,OAAO;AAAA,mBAChB,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,UAK7B,KAAK,QAAU,KAAK,gBAAgB,EAAE,EAAI,KAAK,iBAAiB;AAAA,UAChE,KAAK,0BAA0B;AAAA;AAAA,KAGrC,CACA,MAAM,cAAe,OACjB,KAAK,QAAU,GACf,MAAM4iB,GAAS/f,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,YAC1C+f,IACA,MAAM3hB,EAAc,mBAAmB,CAAE,KAAM,EAAG,EAClD,MAAM2hB,EAAO,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CACnD,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAAE,SACH,KAAK,QAAU,GACfA,EAAO,QAAQ,CAAC,CAAE,QAAS,CAAA,EAAK,CAAE,QAAS,CAAA,CAAG,EAAG,CAC7C,SAAU,IACV,KAAM,WACN,OAAQ,MAAA,CACX,EAET,CACA,gBAAgBC,EAAOC,EAAI,CACvB,MAAO,CAAC,GAAG,MAAMD,CAAK,CAAC,EAAE,IAAI,IAAM7iB;AAAAA,mDACQY,EAAUkiB,CAAE,CAAC;AAAA,OACzD,CACH,CACA,iBAAkB,OACd,MAAMtgB,IAAUK,EAAA,KAAK,kBAAL,YAAAA,EAAsB,QAAS,EACzCtB,EAAe,SAAS,CAAC,GAAG,KAAK,SAAU,GAAG,KAAK,YAAa,GAAG,KAAK,eAAe,EAAG,IAAI,EAC9FA,EAAe,SAAS,CAAC,GAAG,KAAK,SAAU,GAAG,KAAK,YAAa,GAAG,KAAK,OAAO,EAAG,IAAI,EAE5F,OAD6BoC,GAAW,uBAAuBnB,CAAO,EAC1C,IAAIC,GAAUzC;AAAAA;AAAAA,mBAE/B,IAAM,KAAK,gBAAgByC,CAAM,CAAC;AAAA,oBACjCA,CAAM;AAAA;AAAA,OAEnB,CACH,CACA,0BAA2B,CACvB,KAAM,CAAE,QAAAD,EAAS,YAAAuC,EAAa,SAAAC,EAAU,MAAArD,CAAA,EAAUV,EAAc,MAC1D8hB,EAAU,OAAO,WAAa,IAAM,EAAI,EACxCC,EAAiBxgB,EAAQ,OAASuC,EAAY,OAEpD,IAAIke,EADgB,KAAK,KAAKD,EAAiBD,CAAO,EACrBA,EAAUC,EAAiBD,EAE5D,OADAE,GAAgBzgB,EAAQ,OAASwC,EAAS,OAAS+d,EAAU,EACzDphB,IAAU,GAAKqD,EAAS,OAAS,EAC1B,KAEPrD,IAAU,GAAK,CAAC,GAAGqD,EAAU,GAAGxC,EAAS,GAAGuC,CAAW,EAAE,OAASpD,EAC3D,KAAK,gBAAgBshB,EAAcP,EAAY,EAEnD,IACX,CACA,0BAA2B,OACvB,MAAMQ,GAAWrgB,EAAA,KAAK,aAAL,YAAAA,EAAiB,cAAc,IAAI6f,EAAY,IAC5DQ,IACA,KAAK,mBAAqB,IAAI,qBAAqB,CAAC,CAACC,CAAO,IAAM,CAC9D,GAAIA,GAAA,MAAAA,EAAS,gBAAkB,CAAC,KAAK,QAAS,CAC1C,KAAM,CAAE,KAAAC,EAAM,MAAAzhB,EAAO,QAAAa,CAAA,EAAYvB,EAAc,MAC3CuB,EAAQ,OAASb,GACjBV,EAAc,mBAAmB,CAAE,KAAMmiB,EAAO,EAAG,CAE3D,CACJ,CAAC,EACD,KAAK,mBAAmB,QAAQF,CAAQ,EAEhD,CACA,gBAAgBzgB,EAAQ,CACpBzB,EAAoB,sBAAsByB,CAAM,CACpD,CACJ,EACAkgB,GAAkB,OAASxiB,GAC3Bf,GAAW,CACP2C,EAAA,CACJ,EAAG4gB,GAAkB,UAAW,UAAW,MAAM,EACjDvjB,GAAW,CACP2C,EAAA,CACJ,EAAG4gB,GAAkB,UAAW,UAAW,MAAM,EACjDvjB,GAAW,CACP2C,EAAA,CACJ,EAAG4gB,GAAkB,UAAW,cAAe,MAAM,EACrDvjB,GAAW,CACP2C,EAAA,CACJ,EAAG4gB,GAAkB,UAAW,WAAY,MAAM,EAClDvjB,GAAW,CACP2C,EAAA,CACJ,EAAG4gB,GAAkB,UAAW,kBAAmB,MAAM,EACzDA,GAAoBvjB,GAAW,CAC3BiB,EAAc,sBAAsB,CACxC,EAAGsiB,EAAiB,EC7IpB,MAAAU,GAAelkB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAaA,IAAI4jB,GAAsB,cAAkCxjB,CAAW,CACnE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,UAAY,GACjB,KAAK,UAAY,OACjB,KAAK,QAAU,GACf,KAAK,MAAQ,EACjB,CACA,QAAS,CACL,YAAK,SAAA,EACE,KAAK,QACNE,kEACA,KAAK,gBAAA,CACf,CACA,MAAM,UAAW,EACT,KAAK,MAAM,KAAA,IAAW,KAAK,UAAU,KAAA,GAAU,KAAK,QAAU,KAAK,aACnE,KAAK,UAAY,KAAK,MACtB,KAAK,UAAY,KAAK,MACtB,KAAK,QAAU,GACf,MAAMiB,EAAc,aAAa,CAAE,OAAQ,KAAK,MAAO,MAAO,KAAK,MAAO,EAC1E,KAAK,QAAU,GAEvB,CACA,iBAAkB,CACd,KAAM,CAAE,OAAAsiB,GAAWtiB,EAAc,MAC3BuB,EAAUmB,GAAW,uBAAuB4f,CAAM,EACxD,OAAKA,EAAO,OAsBLvjB;AAAAA;AAAAA;AAAAA,mBAGI,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,UAK7BwC,EAAQ,IAAIC,GAAUzC;AAAAA;AAAAA,uBAET,IAAM,KAAK,gBAAgByC,CAAM,CAAC;AAAA,wBACjCA,CAAM;AAAA,gDACkBA,EAAO,EAAE;AAAA;AAAA,WAE9C,CAAC;AAAA;AAAA,MAnCOzC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,OAsCf,CACA,gBAAgByC,EAAQ,CACpBzB,EAAoB,sBAAsByB,CAAM,CACpD,CACJ,EACA6gB,GAAoB,OAASnjB,GAC7Bf,GAAW,CACP2C,EAAA,CACJ,EAAGuhB,GAAoB,UAAW,UAAW,MAAM,EACnDlkB,GAAW,CACPgB,EAAA,CACJ,EAAGkjB,GAAoB,UAAW,QAAS,MAAM,EACjDlkB,GAAW,CACPgB,EAAA,CACJ,EAAGkjB,GAAoB,UAAW,QAAS,MAAM,EACjDA,GAAsBlkB,GAAW,CAC7BiB,EAAc,wBAAwB,CAC1C,EAAGijB,EAAmB,ECpGtB,IAAIlkB,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAYA,IAAI8jB,GAAoB,cAAgC1jB,CAAW,CAC/D,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,GACd,KAAK,kBAAoByB,EAAe,SAAUiJ,GAAU,CACxD,KAAK,OAASA,CAClB,CAAC,CACL,CACA,QAAS,CACL,MAAMiZ,EAAW,KAAK,OAAO,QAAU,EACvC,OAAOzjB;AAAAA,2BACY,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA,uCACR,KAAK,cAAc,KAAK,IAAI,CAAC;AAAA;AAAA,qBAE/C,KAAK,KAAK;AAAA,mBACZ,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,UAGhC,KAAK,kBAAkB;AAAA;AAAA,QAEzByjB,GAAY,KAAK,MACXzjB;AAAAA,oBACM,KAAK,MAAM;AAAA,oBACXY,EAAU,KAAK,KAAK,CAAC;AAAA,sCAE3BZ,gCAAoCY,EAAU,KAAK,KAAK,CAAC,0BAA0B;AAAA,KAE7F,CACA,cAAc8iB,EAAO,CACjB,KAAK,kBAAkBA,EAAM,MAAM,CACvC,CACA,SAAU,CACN,GAAI,KAAK,QAAU,YAAa,CAC5B,KAAK,MAAQ,OACb,MACJ,CACA,KAAK,MAAQ,YACb5a,GAAgB,QAAQ,+BAAgC,CACpD,KAAM,qBACN,UAAW,YAAA,CACd,CACL,CACA,kBAAmB,CACf,OAAIvH,EAAe,WACRvB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,mBAUA,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA;AAAA,QAIrC,IACX,CACA,mBAAoB,CAChB8B,EAAiB,KAAK,yBAAyB,CACnD,CACJ,EACA1C,GAAW,CACP2C,EAAA,CACJ,EAAGyhB,GAAkB,UAAW,SAAU,MAAM,EAChDpkB,GAAW,CACP2C,EAAA,CACJ,EAAGyhB,GAAkB,UAAW,QAAS,MAAM,EAC/CA,GAAoBpkB,GAAW,CAC3BiB,EAAc,sBAAsB,CACxC,EAAGmjB,EAAiB,ECxFpB,MAAArjB,GAAehB;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,ECDf,IAAIC,EAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAA+EE,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAaA,IAAIikB,EAAc,cAA0B7jB,CAAW,CACnD,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,OAAS,OACd,KAAK,QAAU,OACf,KAAK,SAAW,GAChB,KAAK,SAAW,OAChB,KAAK,IAAM,OACX,KAAK,QAAU,GACf,KAAK,QAAU,EACnB,CACA,QAAS,CACL,OAAOE;AAAAA;AAAAA,oBAEK,KAAK,QAAU,GAAO,EAAQ,KAAK,QAAS;AAAA,uBACzC,KAAK,OAAO;AAAA,2BACRY,EAAU,KAAK,WAAW,CAAC;AAAA,mBACnCA,EAAU,KAAK,MAAM,CAAC;AAAA;AAAA,UAE/B,KAAK,gBAAA,CAAiB,IAAI,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA,UAI/C,KAAK,iBAAiB;AAAA;AAAA,KAG5B,CACA,gBAAiB,CACb,GAAI,KAAK,UAAY,SAAW,KAAK,SACjC,OAAOZ,mBAAuB,KAAK,QAAQ,QAAQ,KAAK,KAAO,WAAW,gBAE9E,GAAI,KAAK,cAAgB,UAAY,KAAK,MAAQ,KAAK,UAAY,OAC/D,OAAOA,mBAAuB,KAAK,IAAI,eAE3C,GAAI,KAAK,UAAY,QAAU,KAAK,MAAQ,KAAK,YAAa,CAC1D,MAAMwG,EAAQ,CAAC,OAAQ,aAAa,EAAE,SAAS,KAAK,WAAW,EAAI,aAAe,SAC5ED,EAAO,KAAK,cAAgB,cAAgB,MAAQ,KACpDqd,EAAW,KAAK,SAAW,KAAK,SAAWrd,EACjD,OAAOvG;AAAAA;AAAAA,yBAEM,KAAK,WAAW;AAAA,iBACxB,KAAK,IAAI;AAAA,qBACL4jB,CAAQ;AAAA;AAAA,sBAEPpd,CAAK;AAAA,4BACCA,CAAK;AAAA,iBAChBD,CAAI;AAAA;AAAA,OAGb,CACA,OAAO,IACX,CACA,iBAAkB,CACd,OAAI,KAAK,QACEvG;AAAAA;AAAAA;AAAAA,+BAKJA,GACX,CACA,iBAAkB,CACd,OAAI,KAAK,QACEA,2EAEJ,IACX,CACJ,EACA2jB,EAAY,OAAS,CAACzjB,EAAaD,EAAeE,EAAM,EACxDf,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,OAAQ,MAAM,EACxCvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,WAAY,MAAM,EAC5CvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,SAAU,MAAM,EAC1CvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,UAAW,MAAM,EAC3CvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,cAAe,MAAM,EAC/CvkB,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGujB,EAAY,UAAW,WAAY,MAAM,EAC5CvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,WAAY,MAAM,EAC5CvkB,EAAW,CACPgB,EAAA,CACJ,EAAGujB,EAAY,UAAW,MAAO,MAAM,EACvCvkB,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGujB,EAAY,UAAW,UAAW,MAAM,EAC3CvkB,EAAW,CACPgB,EAAS,CAAE,KAAM,OAAA,CAAS,CAC9B,EAAGujB,EAAY,UAAW,UAAW,MAAM,EAC3CA,EAAcvkB,EAAW,CACrBiB,EAAc,eAAe,CACjC,EAAGsjB,CAAW,ECvHd,IAAIvkB,GAA0C,SAAUC,EAAYC,EAAQC,EAAKC,EAAM,CACnF,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAIH,EAASE,IAAS,KAAOA,EAAO,OAAO,yBAAyBF,EAAQC,CAAG,EAAIC,EAAMG,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYD,EAAI,QAAQ,SAASL,EAAYC,EAAQC,EAAKC,CAAI,MACxH,SAASI,EAAIP,EAAW,OAAS,EAAGO,GAAK,EAAGA,KAASD,EAAIN,EAAWO,CAAC,KAAGF,GAAKD,EAAI,EAAIE,EAAED,CAAC,EAAID,EAAI,EAAIE,EAAEL,EAAQC,EAAKG,CAAC,EAAIC,EAAEL,EAAQC,CAAG,IAAMG,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAeJ,EAAQC,EAAKG,CAAC,EAAGA,CAChE,EAOA,IAAImkB,GAAmB,cAA+B/jB,CAAW,CAC7D,aAAc,OACV,MAAM,GAAG,SAAS,EAClB,KAAK,QAAS+C,EAAAf,EAAiB,MAAM,OAAvB,YAAAe,EAA6B,MAC/C,CACA,QAAS,CACL,GAAI,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,oBAAoB,EAExC,OAAO7C;AAAAA,2DAC4C,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC;AAAA,UACrE,KAAK,eAAA,CAAgB,IAAI,KAAK,aAAa,IAAI,KAAK,gBAAA,CAAiB;AAAA,UACrE,KAAK,kBAAkB;AAAA;AAAA,KAG7B,CACA,gBAAiB,OACb,OAAK6C,EAAA,KAAK,SAAL,MAAAA,EAAa,aAGX7C;AAAAA;AAAAA;AAAAA;AAAAA,eAIA,KAAK,cAAc,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,sBANzB,IAWf,CACA,aAAc,OACV,OAAK6C,EAAA,KAAK,SAAL,MAAAA,EAAa,UAGX7C;AAAAA;AAAAA;AAAAA;AAAAA,eAIA,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,sBANtB,IAWf,CACA,iBAAkB,OACd,OAAK6C,EAAA,KAAK,SAAL,MAAAA,EAAa,WAGX7C;AAAAA;AAAAA;AAAAA;AAAAA,eAIA,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,sBANvB,IAWf,CACA,kBAAmB,OACf,OAAK6C,EAAA,KAAK,SAAL,MAAAA,EAAa,SAGX7C;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,iBAKE,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAPxB,IAaf,CACA,eAAgB,QACR6C,EAAA,KAAK,SAAL,MAAAA,EAAa,cACbtB,EAAe,SAAS,KAAK,OAAO,aAAc,QAAQ,CAElE,CACA,YAAa,QACLsB,EAAA,KAAK,SAAL,MAAAA,EAAa,WACbtB,EAAe,SAAS,KAAK,OAAO,UAAW,QAAQ,CAE/D,CACA,aAAc,QACNsB,EAAA,KAAK,SAAL,MAAAA,EAAa,YACbtB,EAAe,SAAS,KAAK,OAAO,WAAY,QAAQ,CAEhE,CACA,YAAa,QACLsB,EAAA,KAAK,SAAL,MAAAA,EAAa,UACbtB,EAAe,SAAS,KAAK,OAAO,SAAU,QAAQ,CAE9D,CACJ,EACAsiB,GAAmBzkB,GAAW,CAC1BiB,EAAc,oBAAoB,CACtC,EAAGwjB,EAAgB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106]}