import{g as M}from"./index-2wea5Wgv.js";var v={exports:{}},w,I;function j(){if(I)return w;I=1;var c=1e3,s=c*60,n=s*60,l=n*24,F=l*7,d=l*365.25;w=function(e,r){r=r||{};var t=typeof e;if(t==="string"&&e.length>0)return h(e);if(t==="number"&&isFinite(e))return r.long?m(e):g(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function h(e){if(e=String(e),!(e.length>100)){var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(r){var t=parseFloat(r[1]),a=(r[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return t*d;case"weeks":case"week":case"w":return t*F;case"days":case"day":case"d":return t*l;case"hours":case"hour":case"hrs":case"hr":case"h":return t*n;case"minutes":case"minute":case"mins":case"min":case"m":return t*s;case"seconds":case"second":case"secs":case"sec":case"s":return t*c;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function g(e){var r=Math.abs(e);return r>=l?Math.round(e/l)+"d":r>=n?Math.round(e/n)+"h":r>=s?Math.round(e/s)+"m":r>=c?Math.round(e/c)+"s":e+"ms"}function m(e){var r=Math.abs(e);return r>=l?o(e,r,l,"day"):r>=n?o(e,r,n,"hour"):r>=s?o(e,r,s,"minute"):r>=c?o(e,r,c,"second"):e+" ms"}function o(e,r,t,a){var C=r>=t*1.5;return Math.round(e/t)+" "+a+(C?"s":"")}return w}function O(c){n.debug=n,n.default=n,n.coerce=m,n.disable=h,n.enable=F,n.enabled=g,n.humanize=j(),n.destroy=o,Object.keys(c).forEach(e=>{n[e]=c[e]}),n.names=[],n.skips=[],n.formatters={};function s(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return n.colors[Math.abs(r)%n.colors.length]}n.selectColor=s;function n(e){let r,t=null,a,C;function i(...u){if(!i.enabled)return;const f=i,p=Number(new Date),k=p-(r||p);f.diff=k,f.prev=r,f.curr=p,r=p,u[0]=n.coerce(u[0]),typeof u[0]!="string"&&u.unshift("%O");let y=0;u[0]=u[0].replace(/%([a-zA-Z%])/g,(b,x)=>{if(b==="%%")return"%";y++;const A=n.formatters[x];if(typeof A=="function"){const E=u[y];b=A.call(f,E),u.splice(y,1),y--}return b}),n.formatArgs.call(f,u),(f.log||n.log).apply(f,u)}return i.namespace=e,i.useColors=n.useColors(),i.color=n.selectColor(e),i.extend=l,i.destroy=n.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>t!==null?t:(a!==n.namespaces&&(a=n.namespaces,C=n.enabled(e)),C),set:u=>{t=u}}),typeof n.init=="function"&&n.init(i),i}function l(e,r){const t=n(this.namespace+(typeof r>"u"?":":r)+e);return t.log=this.log,t}function F(e){n.save(e),n.namespaces=e,n.names=[],n.skips=[];const r=(typeof e=="string"?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const t of r)t[0]==="-"?n.skips.push(t.slice(1)):n.names.push(t)}function d(e,r){let t=0,a=0,C=-1,i=0;for(;t<e.length;)if(a<r.length&&(r[a]===e[t]||r[a]==="*"))r[a]==="*"?(C=a,i=t,a++):(t++,a++);else if(C!==-1)a=C+1,i++,t=i;else return!1;for(;a<r.length&&r[a]==="*";)a++;return a===r.length}function h(){const e=[...n.names,...n.skips.map(r=>"-"+r)].join(",");return n.enable(""),e}function g(e){for(const r of n.skips)if(d(e,r))return!1;for(const r of n.names)if(d(e,r))return!0;return!1}function m(e){return e instanceof Error?e.stack||e.message:e}function o(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var D=O;(function(c,s){var n={};s.formatArgs=F,s.save=d,s.load=h,s.useColors=l,s.storage=g(),s.destroy=(()=>{let o=!1;return()=>{o||(o=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),s.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function l(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let o;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(o=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(o[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function F(o){if(o[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+o[0]+(this.useColors?"%c ":" ")+"+"+c.exports.humanize(this.diff),!this.useColors)return;const e="color: "+this.color;o.splice(1,0,e,"color: inherit");let r=0,t=0;o[0].replace(/%[a-zA-Z%]/g,a=>{a!=="%%"&&(r++,a==="%c"&&(t=r))}),o.splice(t,0,e)}s.log=console.debug||console.log||(()=>{});function d(o){try{o?s.storage.setItem("debug",o):s.storage.removeItem("debug")}catch{}}function h(){let o;try{o=s.storage.getItem("debug")||s.storage.getItem("DEBUG")}catch{}return!o&&typeof process<"u"&&"env"in process&&(o=n.DEBUG),o}function g(){try{return localStorage}catch{}}c.exports=D(s);const{formatters:m}=c.exports;m.j=function(o){try{return JSON.stringify(o)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}})(v,v.exports);var S=v.exports;const z=M(S);export{S as b,z as t};
//# sourceMappingURL=browser-B0UiaHO7.js.map
